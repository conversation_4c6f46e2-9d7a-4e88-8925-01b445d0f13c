'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

// أنواع البيانات المؤقتة
interface User {
  id: string
  username: string
  email: string
  full_name: string
  role: string
  permissions: UserPermissions
  is_active: boolean
  last_login: string | null
  created_at: string
}

interface UserPermissions {
  sales_view: boolean
  sales_create: boolean
  sales_edit: boolean
  sales_delete: boolean
  sales_print: boolean
  sales_view_prices: boolean
  purchases_view: boolean
  purchases_create: boolean
  purchases_edit: boolean
  purchases_delete: boolean
  purchases_print: boolean
  inventory_view: boolean
  inventory_create: boolean
  inventory_edit: boolean
  inventory_delete: boolean
  inventory_print: boolean
  customers_view: boolean
  customers_create: boolean
  customers_edit: boolean
  customers_delete: boolean
  suppliers_view: boolean
  suppliers_create: boolean
  suppliers_edit: boolean
  suppliers_delete: boolean
  reports_view: boolean
  reports_financial: boolean
  reports_detailed: boolean
  reports_export: boolean
  users_view: boolean
  users_create: boolean
  users_edit: boolean
  users_delete: boolean
  settings_view: boolean
  settings_edit: boolean
  cashbox_view: boolean
  cashbox_manage: boolean
  returns_view: boolean
  returns_create: boolean
  returns_edit: boolean
  returns_delete: boolean
}

// دوال مؤقتة للاختبار
const authenticateUser = async (username: string, password: string, rememberMe: boolean = false) => {
  // محاكاة تسجيل دخول ناجح
  if (username === 'admin' && password === 'admin123') {
    const mockUser: User = {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      full_name: 'مدير النظام',
      role: 'admin',
      permissions: {
        sales_view: true,
        sales_create: true,
        sales_edit: true,
        sales_delete: true,
        sales_print: true,
        sales_view_prices: true,
        purchases_view: true,
        purchases_create: true,
        purchases_edit: true,
        purchases_delete: true,
        purchases_print: true,
        inventory_view: true,
        inventory_create: true,
        inventory_edit: true,
        inventory_delete: true,
        inventory_print: true,
        customers_view: true,
        customers_create: true,
        customers_edit: true,
        customers_delete: true,
        suppliers_view: true,
        suppliers_create: true,
        suppliers_edit: true,
        suppliers_delete: true,
        reports_view: true,
        reports_financial: true,
        reports_detailed: true,
        reports_export: true,
        users_view: true,
        users_create: true,
        users_edit: true,
        users_delete: true,
        settings_view: true,
        settings_edit: true,
        cashbox_view: true,
        cashbox_manage: true,
        returns_view: true,
        returns_create: true,
        returns_edit: true,
        returns_delete: true,
      },
      is_active: true,
      last_login: new Date().toISOString(),
      created_at: '2024-01-01T00:00:00Z'
    }

    return {
      success: true,
      user: mockUser,
      session: { token: 'mock-session-token' }
    }
  }

  return {
    success: false,
    error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
  }
}

const logoutUser = async (token: string) => {
  return { success: true }
}

const validateSession = async (token: string) => {
  if (token === 'mock-session-token') {
    return {
      success: true,
      user: {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        full_name: 'مدير النظام',
        role: 'admin',
        permissions: {
          sales_view: true,
          sales_create: true,
          sales_edit: true,
          sales_delete: true,
          sales_print: true,
          sales_view_prices: true,
          purchases_view: true,
          purchases_create: true,
          purchases_edit: true,
          purchases_delete: true,
          purchases_print: true,
          inventory_view: true,
          inventory_create: true,
          inventory_edit: true,
          inventory_delete: true,
          inventory_print: true,
          customers_view: true,
          customers_create: true,
          customers_edit: true,
          customers_delete: true,
          suppliers_view: true,
          suppliers_create: true,
          suppliers_edit: true,
          suppliers_delete: true,
          reports_view: true,
          reports_financial: true,
          reports_detailed: true,
          reports_export: true,
          users_view: true,
          users_create: true,
          users_edit: true,
          users_delete: true,
          settings_view: true,
          settings_edit: true,
          cashbox_view: true,
          cashbox_manage: true,
          returns_view: true,
          returns_create: true,
          returns_edit: true,
          returns_delete: true,
        },
        is_active: true
      }
    }
  }

  return { success: false, error: 'جلسة غير صالحة' }
}

const logActivity = async (data: any) => {
  console.log('Activity logged:', data)
  return { success: true }
}

interface AuthContextType {
  user: User | null
  permissions: UserPermissions | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (username: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  hasPermission: (permission: keyof UserPermissions) => boolean
  hasAnyPermission: (permissions: (keyof UserPermissions)[]) => boolean
  hasRole: (role: string) => boolean
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [permissions, setPermissions] = useState<UserPermissions | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // التحقق من الجلسة عند تحميل التطبيق
  useEffect(() => {
    checkSession()
  }, [])

  const checkSession = async () => {
    try {
      const sessionToken = localStorage.getItem('sessionToken')
      if (!sessionToken) {
        setIsLoading(false)
        return
      }

      const result = await validateSession(sessionToken)
      if (result.success && result.user) {
        setUser(result.user as User)
        setPermissions(result.user.permissions as UserPermissions)
      } else {
        localStorage.removeItem('sessionToken')
      }
    } catch (error) {
      console.error('Error checking session:', error)
      localStorage.removeItem('sessionToken')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (username: string, password: string, rememberMe: boolean = false) => {
    try {
      setIsLoading(true)
      const result = await authenticateUser(username, password, rememberMe)
      
      if (result.success && result.user && result.session) {
        setUser(result.user as User)
        setPermissions(result.user.permissions as UserPermissions)
        localStorage.setItem('sessionToken', result.session.token)
        
        return { success: true }
      } else {
        return { success: false, error: result.error || 'فشل في تسجيل الدخول' }
      }
    } catch (error: any) {
      return { success: false, error: error.message || 'حدث خطأ غير متوقع' }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      const sessionToken = localStorage.getItem('sessionToken')
      if (sessionToken) {
        await logoutUser(sessionToken)
      }
      
      setUser(null)
      setPermissions(null)
      localStorage.removeItem('sessionToken')
    } catch (error) {
      console.error('Error during logout:', error)
      // حتى لو فشل في الخادم، نقوم بتنظيف البيانات المحلية
      setUser(null)
      setPermissions(null)
      localStorage.removeItem('sessionToken')
    }
  }

  const hasPermission = (permission: keyof UserPermissions): boolean => {
    if (!permissions) return false
    return permissions[permission] === true
  }

  const hasAnyPermission = (permissionsList: (keyof UserPermissions)[]): boolean => {
    if (!permissions) return false
    return permissionsList.some(permission => permissions[permission] === true)
  }

  const hasRole = (role: string): boolean => {
    if (!user) return false
    return user.role === role
  }

  const refreshUser = async () => {
    await checkSession()
  }

  const value: AuthContextType = {
    user,
    permissions,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    hasPermission,
    hasAnyPermission,
    hasRole,
    refreshUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook للتحقق من الصلاحيات
export const usePermissions = () => {
  const { permissions, hasPermission, hasAnyPermission } = useAuth()
  
  return {
    permissions,
    hasPermission,
    hasAnyPermission,
    
    // صلاحيات المبيعات
    canViewSales: hasPermission('sales_view'),
    canCreateSales: hasPermission('sales_create'),
    canEditSales: hasPermission('sales_edit'),
    canDeleteSales: hasPermission('sales_delete'),
    canPrintSales: hasPermission('sales_print'),
    canViewPrices: hasPermission('sales_view_prices'),
    
    // صلاحيات المشتريات
    canViewPurchases: hasPermission('purchases_view'),
    canCreatePurchases: hasPermission('purchases_create'),
    canEditPurchases: hasPermission('purchases_edit'),
    canDeletePurchases: hasPermission('purchases_delete'),
    canPrintPurchases: hasPermission('purchases_print'),
    
    // صلاحيات المخزون
    canViewInventory: hasPermission('inventory_view'),
    canCreateInventory: hasPermission('inventory_create'),
    canEditInventory: hasPermission('inventory_edit'),
    canDeleteInventory: hasPermission('inventory_delete'),
    canPrintInventory: hasPermission('inventory_print'),
    
    // صلاحيات العملاء
    canViewCustomers: hasPermission('customers_view'),
    canCreateCustomers: hasPermission('customers_create'),
    canEditCustomers: hasPermission('customers_edit'),
    canDeleteCustomers: hasPermission('customers_delete'),
    
    // صلاحيات الموردين
    canViewSuppliers: hasPermission('suppliers_view'),
    canCreateSuppliers: hasPermission('suppliers_create'),
    canEditSuppliers: hasPermission('suppliers_edit'),
    canDeleteSuppliers: hasPermission('suppliers_delete'),
    
    // صلاحيات التقارير
    canViewReports: hasPermission('reports_view'),
    canViewFinancialReports: hasPermission('reports_financial'),
    canViewDetailedReports: hasPermission('reports_detailed'),
    canExportReports: hasPermission('reports_export'),
    
    // صلاحيات المستخدمين
    canViewUsers: hasPermission('users_view'),
    canCreateUsers: hasPermission('users_create'),
    canEditUsers: hasPermission('users_edit'),
    canDeleteUsers: hasPermission('users_delete'),
    
    // صلاحيات الإعدادات
    canViewSettings: hasPermission('settings_view'),
    canEditSettings: hasPermission('settings_edit'),
    
    // صلاحيات الصندوق
    canViewCashbox: hasPermission('cashbox_view'),
    canManageCashbox: hasPermission('cashbox_manage'),
    
    // صلاحيات المرتجعات
    canViewReturns: hasPermission('returns_view'),
    canCreateReturns: hasPermission('returns_create'),
    canEditReturns: hasPermission('returns_edit'),
    canDeleteReturns: hasPermission('returns_delete'),
    
    // دوال مساعدة
    canAccessSalesModule: hasAnyPermission(['sales_view', 'sales_create', 'sales_edit']),
    canAccessPurchasesModule: hasAnyPermission(['purchases_view', 'purchases_create', 'purchases_edit']),
    canAccessInventoryModule: hasAnyPermission(['inventory_view', 'inventory_create', 'inventory_edit']),
    canAccessReportsModule: hasPermission('reports_view'),
    canAccessUsersModule: hasAnyPermission(['users_view', 'users_create', 'users_edit']),
    canAccessSettingsModule: hasAnyPermission(['settings_view', 'settings_edit']),
  }
}

// Hook لتسجيل النشاطات
export const useActivityLogger = () => {
  const { user } = useAuth()
  
  const logUserActivity = async (action: string, description: string, additionalData?: {
    table_name?: string
    record_id?: string
    old_values?: any
    new_values?: any
  }) => {
    if (!user) return
    
    try {
      await logActivity({
        user_id: user.id,
        action,
        description,
        ...additionalData
      })
    } catch (error) {
      console.error('Error logging activity:', error)
    }
  }
  
  return { logUserActivity }
}
