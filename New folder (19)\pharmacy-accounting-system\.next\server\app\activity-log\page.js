(()=>{var a={};a.id=1,a.ids=[1],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10349:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\activity-log\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\activity-log\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>j});var d=c(60687);c(43210);var e=c(16189),f=c(63213),g=c(41862),h=c(43649),i=c(99891);function j({children:a,requiredPermissions:b=[],requiredRole:c,fallback:j}){let{isAuthenticated:l,isLoading:m,user:n}=(0,f.As)(),{hasPermission:o,hasAnyPermission:p}=(0,f.Sk)(),q=(0,e.useRouter)();return m?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(g.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"جاري التحقق من الصلاحيات..."})]})}):l?c&&n?.role!==c?j||(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,d.jsx)(h.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"غير مصرح لك"}),(0,d.jsxs)("p",{className:"text-gray-600 mb-6",children:["هذه الصفحة مخصصة للمستخدمين من نوع: ",c]}),(0,d.jsx)("button",{onClick:()=>q.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة"})]})}):b.length>0&&!p(b)?j||(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,d.jsx)(i.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"صلاحيات غير كافية"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة"}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,d.jsx)("p",{className:"text-sm text-gray-700 font-medium mb-2",children:"الصلاحيات المطلوبة:"}),(0,d.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:b.map(a=>(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"w-2 h-2 bg-red-400 rounded-full"}),k(a)]},a))})]}),(0,d.jsx)("button",{onClick:()=>q.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة"})]})}):(0,d.jsx)(d.Fragment,{children:a}):null}let k=a=>({sales_view:"عرض المبيعات",sales_create:"إنشاء مبيعات",sales_edit:"تعديل المبيعات",sales_delete:"حذف المبيعات",sales_print:"طباعة المبيعات",sales_view_prices:"عرض الأسعار",purchases_view:"عرض المشتريات",purchases_create:"إنشاء مشتريات",purchases_edit:"تعديل المشتريات",purchases_delete:"حذف المشتريات",purchases_print:"طباعة المشتريات",inventory_view:"عرض المخزون",inventory_create:"إضافة للمخزون",inventory_edit:"تعديل المخزون",inventory_delete:"حذف من المخزون",inventory_print:"طباعة المخزون",customers_view:"عرض العملاء",customers_create:"إضافة عملاء",customers_edit:"تعديل العملاء",customers_delete:"حذف العملاء",suppliers_view:"عرض الموردين",suppliers_create:"إضافة موردين",suppliers_edit:"تعديل الموردين",suppliers_delete:"حذف الموردين",reports_view:"عرض التقارير",reports_financial:"التقارير المالية",reports_detailed:"التقارير المفصلة",reports_export:"تصدير التقارير",users_view:"عرض المستخدمين",users_create:"إضافة مستخدمين",users_edit:"تعديل المستخدمين",users_delete:"حذف المستخدمين",settings_view:"عرض الإعدادات",settings_edit:"تعديل الإعدادات",cashbox_view:"عرض الصندوق",cashbox_manage:"إدارة الصندوق",returns_view:"عرض المرتجعات",returns_create:"إنشاء مرتجعات",returns_edit:"تعديل المرتجعات",returns_delete:"حذف المرتجعات"})[a]||a},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},56089:(a,b,c)=>{Promise.resolve().then(c.bind(c,10349))},59007:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>t});var d=c(60687),e=c(43210),f=c(21979),g=c(20769),h=c(63213),i=c(99891),j=c(5336),k=c(96882),l=c(35071),m=c(58559),n=c(78122),o=c(31158),p=c(99270),q=c(58869),r=c(48730),s=c(13861);function t(){let[a,b]=(0,e.useState)([]),[c,t]=(0,e.useState)(!0),[u,v]=(0,e.useState)(""),[w,x]=(0,e.useState)("all"),[y,z]=(0,e.useState)("all"),[A,B]=(0,e.useState)("today"),[C,D]=(0,e.useState)(null),[E,F]=(0,e.useState)(!1),{user:G}=(0,h.As)(),H=async()=>{try{t(!0);let a=[{id:"1",user_id:"1",user_name:"مدير النظام",action:"LOGIN",description:"تسجيل دخول المستخدم: admin",table_name:"users",record_id:"1",old_values:null,new_values:null,ip_address:"*************",user_agent:"Mozilla/5.0...",created_at:new Date().toISOString()},{id:"2",user_id:"2",user_name:"أحمد الصيدلي",action:"CREATE_SALE",description:"إنشاء فاتورة مبيعات جديدة: INV-001",table_name:"sales_invoices",record_id:"inv-001",old_values:null,new_values:{invoice_number:"INV-001",total_amount:15e4},ip_address:"*************",user_agent:"Mozilla/5.0...",created_at:new Date(Date.now()-36e5).toISOString()},{id:"3",user_id:"1",user_name:"مدير النظام",action:"UPDATE_MEDICINE",description:"تعديل معلومات الدواء: باراسيتامول",table_name:"medicines",record_id:"med-001",old_values:{price:5e3},new_values:{price:5500},ip_address:"*************",user_agent:"Mozilla/5.0...",created_at:new Date(Date.now()-72e5).toISOString()},{id:"4",user_id:"3",user_name:"فاطمة الكاشير",action:"DELETE_CUSTOMER",description:"حذف عميل: أحمد محمد",table_name:"customers",record_id:"cust-001",old_values:{name:"أحمد محمد",phone:"07701234567"},new_values:null,ip_address:"*************",user_agent:"Mozilla/5.0...",created_at:new Date(Date.now()-108e5).toISOString()},{id:"5",user_id:"2",user_name:"أحمد الصيدلي",action:"PRINT_INVOICE",description:"طباعة فاتورة: INV-001",table_name:"sales_invoices",record_id:"inv-001",old_values:null,new_values:null,ip_address:"*************",user_agent:"Mozilla/5.0...",created_at:new Date(Date.now()-144e5).toISOString()}];b(a)}catch(a){console.error("Error loading activities:",a)}finally{t(!1)}},I=a.filter(a=>{let b=a.description.toLowerCase().includes(u.toLowerCase())||a.user_name.toLowerCase().includes(u.toLowerCase())||a.action.toLowerCase().includes(u.toLowerCase()),c="all"===w||a.user_id===w,d="all"===y||a.action.includes(y.toUpperCase());return b&&c&&d}),J=a=>({LOGIN:"تسجيل دخول",LOGOUT:"تسجيل خروج",CREATE_SALE:"إنشاء مبيعات",CREATE_PURCHASE:"إنشاء مشتريات",CREATE_CUSTOMER:"إضافة عميل",CREATE_SUPPLIER:"إضافة مورد",CREATE_MEDICINE:"إضافة دواء",CREATE_USER:"إضافة مستخدم",UPDATE_SALE:"تعديل مبيعات",UPDATE_PURCHASE:"تعديل مشتريات",UPDATE_CUSTOMER:"تعديل عميل",UPDATE_SUPPLIER:"تعديل مورد",UPDATE_MEDICINE:"تعديل دواء",UPDATE_USER:"تعديل مستخدم",DELETE_SALE:"حذف مبيعات",DELETE_PURCHASE:"حذف مشتريات",DELETE_CUSTOMER:"حذف عميل",DELETE_SUPPLIER:"حذف مورد",DELETE_MEDICINE:"حذف دواء",DELETE_USER:"حذف مستخدم",PRINT_INVOICE:"طباعة فاتورة",EXPORT_REPORT:"تصدير تقرير",ACTIVATE_USER:"تفعيل مستخدم",DEACTIVATE_USER:"تعطيل مستخدم"})[a]||a;return(0,d.jsx)(g.Ay,{requiredPermissions:["users_view"],children:(0,d.jsxs)(f.A,{children:[(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"سجل النشاطات"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"تتبع جميع العمليات والأنشطة في النظام"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:H,className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),"تحديث"]}),(0,d.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),"تصدير"]})]})]}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",placeholder:"البحث في النشاطات...",value:u,onChange:a=>v(a.target.value),className:"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsx)("div",{children:(0,d.jsxs)("select",{value:w,onChange:a=>x(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع المستخدمين"}),(0,d.jsx)("option",{value:"1",children:"مدير النظام"}),(0,d.jsx)("option",{value:"2",children:"أحمد الصيدلي"}),(0,d.jsx)("option",{value:"3",children:"فاطمة الكاشير"})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("select",{value:y,onChange:a=>z(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الأنشطة"}),(0,d.jsx)("option",{value:"login",children:"تسجيل الدخول/الخروج"}),(0,d.jsx)("option",{value:"create",children:"إنشاء"}),(0,d.jsx)("option",{value:"update",children:"تعديل"}),(0,d.jsx)("option",{value:"delete",children:"حذف"}),(0,d.jsx)("option",{value:"print",children:"طباعة"})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("select",{value:A,onChange:a=>B(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"today",children:"اليوم"}),(0,d.jsx)("option",{value:"yesterday",children:"أمس"}),(0,d.jsx)("option",{value:"week",children:"هذا الأسبوع"}),(0,d.jsx)("option",{value:"month",children:"هذا الشهر"}),(0,d.jsx)("option",{value:"all",children:"جميع الفترات"})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النشاط"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المستخدم"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الوصف"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الوقت"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"عنوان IP"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c?(0,d.jsx)("tr",{children:(0,d.jsx)("td",{colSpan:6,className:"px-6 py-12 text-center",children:(0,d.jsxs)("div",{className:"flex items-center justify-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,d.jsx)("span",{className:"mr-3 text-gray-600",children:"جاري التحميل..."})]})})}):0===I.length?(0,d.jsx)("tr",{children:(0,d.jsxs)("td",{colSpan:6,className:"px-6 py-12 text-center",children:[(0,d.jsx)(m.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500",children:"لا توجد نشاطات"})]})}):I.map(a=>{var b,c;return(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${(b=a.action).includes("LOGIN")||b.includes("LOGOUT")?"text-blue-600 bg-blue-100":b.includes("CREATE")?"text-green-600 bg-green-100":b.includes("UPDATE")||b.includes("EDIT")?"text-yellow-600 bg-yellow-100":b.includes("DELETE")?"text-red-600 bg-red-100":"text-gray-600 bg-gray-100"}`,children:(c=a.action).includes("LOGIN")||c.includes("LOGOUT")?(0,d.jsx)(i.A,{className:"h-4 w-4"}):c.includes("CREATE")?(0,d.jsx)(j.A,{className:"h-4 w-4"}):c.includes("UPDATE")||c.includes("EDIT")?(0,d.jsx)(k.A,{className:"h-4 w-4"}):c.includes("DELETE")?(0,d.jsx)(l.A,{className:"h-4 w-4"}):(0,d.jsx)(m.A,{className:"h-4 w-4"})}),(0,d.jsxs)("div",{className:"mr-3",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:J(a.action)}),a.table_name&&(0,d.jsx)("div",{className:"text-xs text-gray-500",children:a.table_name})]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 text-gray-400 ml-2"}),(0,d.jsx)("div",{className:"text-sm text-gray-900",children:a.user_name})]})}),(0,d.jsx)("td",{className:"px-6 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 max-w-xs truncate",children:a.description})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 ml-1"}),new Date(a.created_at).toLocaleString("ar-EG")]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.ip_address||"غير محدد"}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsx)("button",{onClick:()=>{D(a),F(!0)},className:"text-blue-600 hover:text-blue-900",title:"عرض التفاصيل",children:(0,d.jsx)(s.A,{className:"h-4 w-4"})})})]},a.id)})})]})})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(m.A,{className:"h-8 w-8 text-blue-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"إجمالي النشاطات"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.length})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(j.A,{className:"h-8 w-8 text-green-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"عمليات الإنشاء"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.filter(a=>a.action.includes("CREATE")).length})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(k.A,{className:"h-8 w-8 text-yellow-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"عمليات التعديل"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.filter(a=>a.action.includes("UPDATE")).length})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(l.A,{className:"h-8 w-8 text-red-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"عمليات الحذف"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.filter(a=>a.action.includes("DELETE")).length})]})})]})})]})]}),E&&C&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"تفاصيل النشاط"}),(0,d.jsx)("button",{onClick:()=>F(!1),className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(l.A,{className:"h-6 w-6"})})]}),(0,d.jsx)("div",{className:"p-6 overflow-auto max-h-[calc(90vh-120px)]",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"النشاط"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:J(C.action)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"المستخدم"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:C.user_name})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"الوصف"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:C.description})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"الوقت"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:new Date(C.created_at).toLocaleString("ar-EG")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"عنوان IP"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:C.ip_address||"غير محدد"})]}),C.old_values&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"القيم السابقة"}),(0,d.jsx)("pre",{className:"mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg overflow-auto",children:JSON.stringify(C.old_values,null,2)})]}),C.new_values&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"القيم الجديدة"}),(0,d.jsx)("pre",{className:"mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg overflow-auto",children:JSON.stringify(C.new_values,null,2)})]})]})})]})})]})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69157:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["activity-log",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,10349)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\activity-log\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\activity-log\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/activity-log/page",pathname:"/activity-log",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/activity-log/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92537:(a,b,c)=>{Promise.resolve().then(c.bind(c,59007))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,314,979],()=>b(b.s=69157));module.exports=c})();