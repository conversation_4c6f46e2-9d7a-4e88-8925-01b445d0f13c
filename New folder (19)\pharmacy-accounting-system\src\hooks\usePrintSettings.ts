'use client'

import { useState, useEffect } from 'react'

export interface PrintSettings {
  companyName: string
  companyNameEn: string
  companyAddress: string
  companyPhone: string
  companyEmail: string
  logo?: string
  showLogo: boolean
  showHeader: boolean
  showFooter: boolean
  footerText: string
  fontSize: 'small' | 'medium' | 'large'
  paperSize: 'A4' | 'A5' | 'thermal'
  showBorders: boolean
  showColors: boolean
  includeBarcode: boolean
  includeQRCode: boolean
  watermark?: string
  showWatermark: boolean
  headerColor: string
  accentColor: string
  textColor: string
  backgroundColor: string
}

const defaultSettings: PrintSettings = {
  companyName: 'مكتب لارين العلمي',
  companyNameEn: 'LAREN SCIENTIFIC BUREAU',
  companyAddress: 'بغداد - شارع فلسطين',
  companyPhone: '+964 ************',
  companyEmail: '<EMAIL>',
  showLogo: true,
  showHeader: true,
  showFooter: true,
  footerText: 'شكراً لتعاملكم معنا',
  fontSize: 'medium',
  paperSize: 'A4',
  showBorders: true,
  showColors: false,
  includeBarcode: false,
  includeQRCode: false,
  showWatermark: false,
  headerColor: '#1f2937',
  accentColor: '#3b82f6',
  textColor: '#374151',
  backgroundColor: '#ffffff'
}

export function usePrintSettings() {
  const [settings, setSettings] = useState<PrintSettings>(defaultSettings)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('printSettings')
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings)
        setSettings({ ...defaultSettings, ...parsed })
      }
    } catch (error) {
      console.error('Error loading print settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateSettings = (newSettings: Partial<PrintSettings>) => {
    const updatedSettings = { ...settings, ...newSettings }
    setSettings(updatedSettings)
    
    try {
      localStorage.setItem('printSettings', JSON.stringify(updatedSettings))
    } catch (error) {
      console.error('Error saving print settings:', error)
    }
  }

  const resetSettings = () => {
    setSettings(defaultSettings)
    try {
      localStorage.removeItem('printSettings')
    } catch (error) {
      console.error('Error resetting print settings:', error)
    }
  }

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'print-settings.json'
    link.click()
    URL.revokeObjectURL(url)
  }

  const importSettings = (file: File) => {
    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string)
          updateSettings(importedSettings)
          resolve()
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })
  }

  return {
    settings,
    printSettings: settings, // alias for compatibility
    loading,
    updateSettings,
    resetSettings,
    exportSettings,
    importSettings
  }
}

// Print utility functions
export const printInvoice = async (invoice: any, type: 'sales' | 'purchase' | 'return', settings: PrintSettings) => {
  try {
    console.log('🖨️ بدء طباعة الفاتورة:', invoice)

    // If we have an invoice ID, fetch fresh data from database for accurate printing
    let printData = invoice
    if (invoice.id && type === 'sales') {
      const { getSalesInvoiceForPrint } = require('@/lib/database')
      const result = await getSalesInvoiceForPrint(invoice.id)
      if (result.success && result.data) {
        printData = {
          ...result.data,
          // Preserve any additional data from the original invoice
          customerName: invoice.customerName || result.data.customer_name,
          customerPhone: invoice.customerPhone || result.data.customers?.phone,
          customerAddress: invoice.customerAddress || result.data.customers?.address,
        }
        console.log('✅ تم استرجاع بيانات فاتورة المبيعات من قاعدة البيانات:', printData)
      }
    } else if (invoice.id && type === 'purchase') {
      const { getPurchaseInvoiceForPrint } = require('@/lib/database')
      const result = await getPurchaseInvoiceForPrint(invoice.id)
      if (result.success && result.data) {
        printData = {
          ...result.data,
          // Preserve any additional data from the original invoice
          supplierName: invoice.supplierName || result.data.suppliers?.name,
          supplierPhone: invoice.supplierPhone || result.data.suppliers?.phone,
          supplierAddress: invoice.supplierAddress || result.data.suppliers?.address,
        }
        console.log('✅ تم استرجاع بيانات فاتورة المشتريات من قاعدة البيانات:', printData)
      }
    } else if (invoice.id && type === 'return') {
      const { getReturnForPrint } = require('@/lib/database')
      const result = await getReturnForPrint(invoice.id)
      if (result.success && result.data) {
        printData = {
          ...result.data,
          // Preserve any additional data from the original return
          customerName: invoice.customerName || result.data.customer_name,
          supplierName: invoice.supplierName || result.data.supplier_name,
        }
        console.log('✅ تم استرجاع بيانات المرتجع من قاعدة البيانات:', printData)
      }
    }

    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      console.error('❌ فشل في فتح نافذة الطباعة')
      return
    }

    // Use Laren template by default
    const { generateLarenInvoiceHTML } = require('@/utils/larenPrintTemplate')
    const invoiceHTML = generateLarenInvoiceHTML(printData, type, settings)

    printWindow.document.write(invoiceHTML)
    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
    printWindow.close()

    console.log('✅ تمت الطباعة بنجاح')
  } catch (error) {
    console.error('❌ خطأ في الطباعة:', error)

    // Fallback to original method
    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const { generateLarenInvoiceHTML } = require('@/utils/larenPrintTemplate')
    const invoiceHTML = generateLarenInvoiceHTML(invoice, type, settings)

    printWindow.document.write(invoiceHTML)
    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
    printWindow.close()
  }
}

export const printReport = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {
  const printWindow = window.open('', '_blank')
  if (!printWindow) return

  // Use Laren template by default
  const { generateLarenReportHTML } = require('@/utils/larenPrintTemplate')
  const reportHTML = generateLarenReportHTML(reportData, reportType, title, settings)

  printWindow.document.write(reportHTML)
  printWindow.document.close()
  printWindow.focus()
  printWindow.print()
  printWindow.close()
}

const generateInvoiceHTML = (invoice: any, type: 'sales' | 'purchase', settings: PrintSettings) => {
  const items = type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items
  const customerSupplier = type === 'sales' 
    ? (invoice.customers?.name || invoice.customer_name || 'عميل نقدي')
    : (invoice.suppliers?.name || 'غير محدد')

  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'} - ${invoice.invoice_number}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: 'Arial', sans-serif; 
          font-size: ${settings.fontSize === 'small' ? '12px' : settings.fontSize === 'large' ? '16px' : '14px'};
          color: ${settings.textColor};
          background-color: ${settings.backgroundColor};
          line-height: 1.6;
        }
        .container { 
          max-width: ${settings.paperSize === 'A5' ? '600px' : settings.paperSize === 'thermal' ? '300px' : '800px'}; 
          margin: 0 auto; 
          padding: 20px;
          ${settings.showBorders ? 'border: 1px solid #ddd;' : ''}
        }
        .header { 
          text-align: center; 
          margin-bottom: 30px; 
          ${settings.showHeader ? '' : 'display: none;'}
          border-bottom: 2px solid ${settings.accentColor};
          padding-bottom: 20px;
        }
        .logo { 
          width: 80px; 
          height: 80px; 
          background: ${settings.accentColor}; 
          border-radius: 50%; 
          margin: 0 auto 15px; 
          display: flex; 
          align-items: center; 
          justify-content: center; 
          color: white; 
          font-size: 24px; 
          font-weight: bold;
          ${settings.showLogo ? '' : 'display: none;'}
        }
        .company-name { 
          font-size: 24px; 
          font-weight: bold; 
          color: ${settings.headerColor}; 
          margin-bottom: 10px; 
        }
        .company-info { color: #666; margin-bottom: 5px; }
        .invoice-title { 
          text-align: center; 
          font-size: 20px; 
          font-weight: bold; 
          margin: 20px 0; 
          color: ${settings.headerColor};
        }
        .invoice-details { 
          display: flex; 
          justify-content: space-between; 
          margin-bottom: 30px; 
        }
        .invoice-info, .customer-info { flex: 1; }
        .customer-info { text-align: left; }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-bottom: 20px; 
        }
        th, td { 
          border: 1px solid #ddd; 
          padding: 10px; 
          text-align: center; 
        }
        th { 
          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'}; 
          color: ${settings.showColors ? 'white' : settings.textColor};
          font-weight: bold;
        }
        .totals { 
          width: 300px; 
          margin-left: auto; 
          border: 1px solid #ddd; 
        }
        .totals tr:last-child { 
          font-weight: bold; 
          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'};
          color: ${settings.showColors ? 'white' : settings.textColor};
        }
        .footer { 
          text-align: center; 
          margin-top: 30px; 
          padding-top: 20px; 
          border-top: 1px solid #ddd; 
          color: #666;
          ${settings.showFooter ? '' : 'display: none;'}
        }
        .notes { 
          margin-top: 20px; 
          padding: 15px; 
          background-color: #f9f9f9; 
          border-left: 4px solid ${settings.accentColor};
        }
        @media print {
          body { margin: 0; }
          .container { box-shadow: none; border: none; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">${settings.companyName.charAt(0)}</div>
          <div class="company-name">${settings.companyName}</div>
          <div class="company-info">${settings.companyAddress}</div>
          <div class="company-info">${settings.companyPhone}</div>
          <div class="company-info">${settings.companyEmail}</div>
        </div>

        <div class="invoice-title">${type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'}</div>

        <div class="invoice-details">
          <div class="invoice-info">
            <div><strong>رقم الفاتورة:</strong> ${invoice.invoice_number}</div>
            <div><strong>التاريخ:</strong> ${new Date(invoice.created_at).toLocaleDateString('ar-EG')}</div>
            <div><strong>طريقة الدفع:</strong> ${invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}</div>
            <div><strong>حالة الدفع:</strong> ${invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</div>
          </div>
          <div class="customer-info">
            <div><strong>${type === 'sales' ? 'بيانات العميل' : 'بيانات المورد'}:</strong></div>
            <div>${customerSupplier}</div>
            ${type === 'sales' && invoice.customers?.phone ? `<div>الهاتف: ${invoice.customers.phone}</div>` : ''}
            ${type === 'purchase' && invoice.suppliers?.phone ? `<div>الهاتف: ${invoice.suppliers.phone}</div>` : ''}
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th>اسم الدواء</th>
              <th>الكمية</th>
              <th>سعر الوحدة</th>
              <th>المجموع</th>
            </tr>
          </thead>
          <tbody>
            ${items?.map((item: any) => `
              <tr>
                <td>${type === 'sales'
                  ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || 'غير محدد')
                  : (item.medicine_name || item.medicineName || item.medicines?.name || 'غير محدد')
                }</td>
                <td>${item.quantity}</td>
                <td>${type === 'sales'
                  ? (item.unit_price || 0).toLocaleString()
                  : (item.unit_cost || item.unitCost || 0).toLocaleString()
                } د.ع</td>
                <td>${type === 'sales'
                  ? (item.total_price || 0).toLocaleString()
                  : (item.total_cost || item.totalCost || 0).toLocaleString()
                } د.ع</td>
              </tr>
            `).join('') || ''}
          </tbody>
        </table>

        <table class="totals">
          <tr>
            <td>المجموع الفرعي:</td>
            <td>${invoice.total_amount?.toLocaleString()} د.ع</td>
          </tr>
          <tr>
            <td>الخصم:</td>
            <td>${invoice.discount_amount?.toLocaleString() || 0} د.ع</td>
          </tr>
          <tr>
            <td>المجموع النهائي:</td>
            <td>${invoice.final_amount?.toLocaleString()} د.ع</td>
          </tr>
        </table>

        ${invoice.notes ? `
          <div class="notes">
            <strong>ملاحظات:</strong><br>
            ${invoice.notes}
          </div>
        ` : ''}

        <div class="footer">
          <div>${settings.footerText}</div>
          <div style="margin-top: 10px; font-size: 12px;">
            تم إنشاء هذا المستند بواسطة نظام إدارة الصيدلية - ${new Date().toLocaleDateString('ar-EG')}
          </div>
        </div>
      </div>
    </body>
    </html>
  `
}

const generateReportHTML = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: 'Arial', sans-serif; 
          font-size: ${settings.fontSize === 'small' ? '11px' : settings.fontSize === 'large' ? '15px' : '13px'};
          color: ${settings.textColor};
          background-color: ${settings.backgroundColor};
          line-height: 1.5;
        }
        .container { 
          max-width: 100%; 
          margin: 0 auto; 
          padding: 20px;
        }
        .header { 
          text-align: center; 
          margin-bottom: 30px; 
          ${settings.showHeader ? '' : 'display: none;'}
          border-bottom: 2px solid ${settings.accentColor};
          padding-bottom: 20px;
        }
        .company-name { 
          font-size: 20px; 
          font-weight: bold; 
          color: ${settings.headerColor}; 
          margin-bottom: 10px; 
        }
        .report-title { 
          text-align: center; 
          font-size: 18px; 
          font-weight: bold; 
          margin: 20px 0; 
          color: ${settings.headerColor};
        }
        .summary { 
          display: flex; 
          justify-content: space-around; 
          margin-bottom: 30px; 
          flex-wrap: wrap;
        }
        .summary-item { 
          text-align: center; 
          padding: 15px; 
          border: 1px solid #ddd; 
          border-radius: 5px; 
          margin: 5px;
          min-width: 150px;
          background-color: ${settings.showColors ? '#f8f9fa' : 'transparent'};
        }
        .summary-label { 
          font-size: 12px; 
          color: #666; 
          margin-bottom: 5px; 
        }
        .summary-value { 
          font-size: 18px; 
          font-weight: bold; 
          color: ${settings.accentColor};
        }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-bottom: 20px; 
          font-size: 11px;
        }
        th, td { 
          border: 1px solid #ddd; 
          padding: 8px; 
          text-align: center; 
        }
        th { 
          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'}; 
          color: ${settings.showColors ? 'white' : settings.textColor};
          font-weight: bold;
        }
        .footer { 
          text-align: center; 
          margin-top: 30px; 
          padding-top: 20px; 
          border-top: 1px solid #ddd; 
          color: #666;
          ${settings.showFooter ? '' : 'display: none;'}
        }
        @media print {
          body { margin: 0; }
          .container { box-shadow: none; }
          table { page-break-inside: auto; }
          tr { page-break-inside: avoid; page-break-after: auto; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="company-name">${settings.companyName}</div>
          <div>${settings.companyAddress}</div>
        </div>

        <div class="report-title">${title}</div>
        <div style="text-align: center; margin-bottom: 20px; color: #666;">
          تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}
        </div>

        ${Array.isArray(reportData) && reportData.length > 0 ? `
          <div class="summary">
            <div class="summary-item">
              <div class="summary-label">عدد السجلات</div>
              <div class="summary-value">${reportData.length}</div>
            </div>
            ${reportType.includes('sales') || reportType.includes('purchases') ? `
              <div class="summary-item">
                <div class="summary-label">إجمالي المبلغ</div>
                <div class="summary-value">${reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0).toLocaleString()} د.ع</div>
              </div>
            ` : ''}
          </div>

          <table>
            <thead>
              <tr>
                ${reportType.includes('sales') ? `
                  <th>رقم الفاتورة</th>
                  <th>العميل</th>
                  <th>المبلغ النهائي</th>
                  <th>حالة الدفع</th>
                  <th>التاريخ</th>
                ` : ''}
                ${reportType.includes('purchases') ? `
                  <th>رقم الفاتورة</th>
                  <th>المورد</th>
                  <th>المبلغ النهائي</th>
                  <th>حالة الدفع</th>
                  <th>التاريخ</th>
                ` : ''}
                ${reportType === 'inventory' ? `
                  <th>اسم الدواء</th>
                  <th>الفئة</th>
                  <th>الكمية</th>
                  <th>تاريخ الانتهاء</th>
                  <th>الحالة</th>
                ` : ''}
                ${reportType === 'cashbox' ? `
                  <th>النوع</th>
                  <th>الفئة</th>
                  <th>الوصف</th>
                  <th>المبلغ</th>
                  <th>التاريخ</th>
                ` : ''}
              </tr>
            </thead>
            <tbody>
              ${reportData.slice(0, 100).map((item: any) => `
                <tr>
                  ${reportType.includes('sales') ? `
                    <td>${item.invoice_number}</td>
                    <td>${item.customers?.name || item.customer_name || 'عميل نقدي'}</td>
                    <td>${item.final_amount?.toLocaleString()} د.ع</td>
                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>
                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>
                  ` : ''}
                  ${reportType.includes('purchases') ? `
                    <td>${item.invoice_number}</td>
                    <td>${item.suppliers?.name || 'غير محدد'}</td>
                    <td>${item.final_amount?.toLocaleString()} د.ع</td>
                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>
                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>
                  ` : ''}
                  ${reportType === 'inventory' ? `
                    <td>${item.medicines?.name || 'غير محدد'}</td>
                    <td>${item.medicines?.category || 'غير محدد'}</td>
                    <td>${item.quantity}</td>
                    <td>${new Date(item.expiry_date).toLocaleDateString('ar-EG')}</td>
                    <td>${item.quantity < 10 ? 'كمية قليلة' : 'طبيعي'}</td>
                  ` : ''}
                  ${reportType === 'cashbox' ? `
                    <td>${item.transaction_type === 'income' ? 'وارد' : 'مصروف'}</td>
                    <td>${item.category}</td>
                    <td>${item.description}</td>
                    <td>${item.transaction_type === 'income' ? '+' : '-'}${item.amount?.toLocaleString()} د.ع</td>
                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>
                  ` : ''}
                </tr>
              `).join('')}
            </tbody>
          </table>

          ${reportData.length > 100 ? `
            <div style="text-align: center; color: #666; margin-top: 20px;">
              تم عرض أول 100 سجل من أصل ${reportData.length} سجل
            </div>
          ` : ''}
        ` : `
          <div style="text-align: center; padding: 50px; color: #666;">
            لا توجد بيانات للعرض
          </div>
        `}

        <div class="footer">
          <div>${settings.footerText}</div>
          <div style="margin-top: 10px; font-size: 11px;">
            تم إنشاء هذا التقرير بواسطة نظام إدارة الصيدلية
          </div>
        </div>
      </div>
    </body>
    </html>
  `
}
