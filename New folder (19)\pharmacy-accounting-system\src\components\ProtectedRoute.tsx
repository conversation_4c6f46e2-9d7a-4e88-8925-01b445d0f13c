'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/contexts/AuthContext'
import { UserPermissions } from '@/lib/auth-database'
import { Loader2, Shield, AlertTriangle } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermissions?: (keyof UserPermissions)[]
  requiredRole?: string
  fallback?: React.ReactNode
}

export default function ProtectedRoute({ 
  children, 
  requiredPermissions = [], 
  requiredRole,
  fallback 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const { hasPermission, hasAnyPermission } = usePermissions()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  // عرض شاشة التحميل
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    )
  }

  // إعادة توجيه إذا لم يكن مسجل دخول
  if (!isAuthenticated) {
    return null
  }

  // التحقق من الدور المطلوب
  if (requiredRole && user?.role !== requiredRole) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            غير مصرح لك
          </h2>
          <p className="text-gray-600 mb-6">
            هذه الصفحة مخصصة للمستخدمين من نوع: {requiredRole}
          </p>
          <button
            onClick={() => router.back()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            العودة
          </button>
        </div>
      </div>
    )
  }

  // التحقق من الصلاحيات المطلوبة
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = hasAnyPermission(requiredPermissions)
    
    if (!hasRequiredPermissions) {
      return fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
            <Shield className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              صلاحيات غير كافية
            </h2>
            <p className="text-gray-600 mb-6">
              ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة
            </p>
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-700 font-medium mb-2">الصلاحيات المطلوبة:</p>
              <ul className="text-sm text-gray-600 space-y-1">
                {requiredPermissions.map((permission) => (
                  <li key={permission} className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                    {translatePermission(permission)}
                  </li>
                ))}
              </ul>
            </div>
            <button
              onClick={() => router.back()}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              العودة
            </button>
          </div>
        </div>
      )
    }
  }

  return <>{children}</>
}

// مكون للتحقق من صلاحية واحدة فقط
interface PermissionGuardProps {
  permission: keyof UserPermissions
  children: React.ReactNode
  fallback?: React.ReactNode
  showMessage?: boolean
}

export function PermissionGuard({ 
  permission, 
  children, 
  fallback,
  showMessage = false 
}: PermissionGuardProps) {
  const { hasPermission } = usePermissions()

  if (!hasPermission(permission)) {
    if (showMessage) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <p className="text-yellow-800 text-sm">
              ليس لديك صلاحية: {translatePermission(permission)}
            </p>
          </div>
        </div>
      )
    }
    return fallback || null
  }

  return <>{children}</>
}

// مكون للتحقق من عدة صلاحيات
interface MultiPermissionGuardProps {
  permissions: (keyof UserPermissions)[]
  requireAll?: boolean // true = يحتاج جميع الصلاحيات، false = يحتاج واحدة على الأقل
  children: React.ReactNode
  fallback?: React.ReactNode
  showMessage?: boolean
}

export function MultiPermissionGuard({ 
  permissions, 
  requireAll = false,
  children, 
  fallback,
  showMessage = false 
}: MultiPermissionGuardProps) {
  const { hasPermission, hasAnyPermission } = usePermissions()

  const hasAccess = requireAll 
    ? permissions.every(permission => hasPermission(permission))
    : hasAnyPermission(permissions)

  if (!hasAccess) {
    if (showMessage) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <div>
              <p className="text-yellow-800 text-sm font-medium">
                ليس لديك الصلاحيات المطلوبة
              </p>
              <p className="text-yellow-700 text-xs mt-1">
                {requireAll ? 'تحتاج جميع الصلاحيات التالية:' : 'تحتاج واحدة على الأقل من الصلاحيات التالية:'}
              </p>
              <ul className="text-xs text-yellow-600 mt-2 space-y-1">
                {permissions.map((permission) => (
                  <li key={permission}>• {translatePermission(permission)}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )
    }
    return fallback || null
  }

  return <>{children}</>
}

// مكون للتحقق من الدور
interface RoleGuardProps {
  role: string
  children: React.ReactNode
  fallback?: React.ReactNode
  showMessage?: boolean
}

export function RoleGuard({ 
  role, 
  children, 
  fallback,
  showMessage = false 
}: RoleGuardProps) {
  const { user } = useAuth()

  if (user?.role !== role) {
    if (showMessage) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <p className="text-yellow-800 text-sm">
              هذه الميزة مخصصة للمستخدمين من نوع: {translateRole(role)}
            </p>
          </div>
        </div>
      )
    }
    return fallback || null
  }

  return <>{children}</>
}

// دوال الترجمة
const translatePermission = (permission: keyof UserPermissions): string => {
  const translations: Record<keyof UserPermissions, string> = {
    sales_view: 'عرض المبيعات',
    sales_create: 'إنشاء مبيعات',
    sales_edit: 'تعديل المبيعات',
    sales_delete: 'حذف المبيعات',
    sales_print: 'طباعة المبيعات',
    sales_view_prices: 'عرض الأسعار',
    
    purchases_view: 'عرض المشتريات',
    purchases_create: 'إنشاء مشتريات',
    purchases_edit: 'تعديل المشتريات',
    purchases_delete: 'حذف المشتريات',
    purchases_print: 'طباعة المشتريات',
    
    inventory_view: 'عرض المخزون',
    inventory_create: 'إضافة للمخزون',
    inventory_edit: 'تعديل المخزون',
    inventory_delete: 'حذف من المخزون',
    inventory_print: 'طباعة المخزون',
    
    customers_view: 'عرض العملاء',
    customers_create: 'إضافة عملاء',
    customers_edit: 'تعديل العملاء',
    customers_delete: 'حذف العملاء',
    
    suppliers_view: 'عرض الموردين',
    suppliers_create: 'إضافة موردين',
    suppliers_edit: 'تعديل الموردين',
    suppliers_delete: 'حذف الموردين',
    
    reports_view: 'عرض التقارير',
    reports_financial: 'التقارير المالية',
    reports_detailed: 'التقارير المفصلة',
    reports_export: 'تصدير التقارير',
    
    users_view: 'عرض المستخدمين',
    users_create: 'إضافة مستخدمين',
    users_edit: 'تعديل المستخدمين',
    users_delete: 'حذف المستخدمين',
    
    settings_view: 'عرض الإعدادات',
    settings_edit: 'تعديل الإعدادات',
    
    cashbox_view: 'عرض الصندوق',
    cashbox_manage: 'إدارة الصندوق',
    
    returns_view: 'عرض المرتجعات',
    returns_create: 'إنشاء مرتجعات',
    returns_edit: 'تعديل المرتجعات',
    returns_delete: 'حذف المرتجعات',
  }
  
  return translations[permission] || permission
}

const translateRole = (role: string): string => {
  const translations: Record<string, string> = {
    admin: 'مدير النظام',
    manager: 'مدير',
    pharmacist: 'صيدلي',
    cashier: 'كاشير',
    viewer: 'مشاهد'
  }
  
  return translations[role] || role
}
