"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[501],{34137:(t,e,i)=>{i.r(e),i.d(e,{AElement:()=>tR,AnimateColorElement:()=>tO,AnimateElement:()=>tk,AnimateTransformElement:()=>tN,BoundingBox:()=>th,CB1:()=>N,CB2:()=>D,CB3:()=>B,CB4:()=>z,Canvg:()=>et,CircleElement:()=>tv,ClipPathElement:()=>tZ,DefsElement:()=>tA,DescElement:()=>t5,Document:()=>t8,Element:()=>tr,EllipseElement:()=>tm,FeColorMatrixElement:()=>tQ,FeCompositeElement:()=>t1,FeDropShadowElement:()=>tK,FeGaussianBlurElement:()=>t2,FeMorphologyElement:()=>t0,FilterElement:()=>tJ,Font:()=>tn,FontElement:()=>tD,FontFaceElement:()=>tB,GElement:()=>tC,GlyphElement:()=>tg,GradientElement:()=>tT,ImageElement:()=>tX,LineElement:()=>tx,LinearGradientElement:()=>tV,MarkerElement:()=>tP,MaskElement:()=>t$,Matrix:()=>J,MissingGlyphElement:()=>tz,Mouse:()=>X,PSEUDO_ZERO:()=>E,Parser:()=>Q,PathElement:()=>tu,PathParser:()=>to,PatternElement:()=>tw,Point:()=>_,PolygonElement:()=>tS,PolylineElement:()=>tb,Property:()=>F,QB1:()=>L,QB2:()=>R,QB3:()=>I,RadialGradientElement:()=>tE,RectElement:()=>tf,RenderedElement:()=>tl,Rotate:()=>Y,SVGElement:()=>ty,SVGFontLoader:()=>tW,Scale:()=>Z,Screen:()=>H,Skew:()=>K,SkewX:()=>tt,SkewY:()=>te,StopElement:()=>tM,StyleElement:()=>tH,SymbolElement:()=>tU,TRefElement:()=>tL,TSpanElement:()=>td,TextElement:()=>tc,TextPathElement:()=>tj,TitleElement:()=>t3,Transform:()=>ti,Translate:()=>$,UnknownElement:()=>ts,UseElement:()=>tq,ViewPort:()=>j,compressSpaces:()=>g,default:()=>et,getSelectorSpecificity:()=>V,normalizeAttributeName:()=>f,normalizeColor:()=>m,parseExternalUrl:()=>v,presets:()=>u,toNumbers:()=>p,trimLeft:()=>c,trimRight:()=>d,vectorMagnitude:()=>M,vectorsAngle:()=>O,vectorsRatio:()=>k}),i(24008);var r=i(94251);i(76509),i(89572),i(27380),i(56528),i(97755);var s=i(50488);i(96732),i(84061),i(51240);var a=i(56227);i(77648);var n=i(39022);i(92548),i(83997),i(59660);var h=i(44381);i(40545);var o=i(61733),l=i(49509),u=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>(0,r.A)(function*(){var e=yield fetch(t),i=yield e.blob();return yield createImageBitmap(i)})()};return("undefined"!=typeof DOMParser||void 0===t)&&Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:i,fetch:r}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:r,createCanvas:i.createCanvas,createImage:i.loadImage}}});function g(t){return t.replace(/(?!\u3000)\s+/gm," ")}function c(t){return t.replace(/^[\n \t]+/,"")}function d(t){return t.replace(/[\n \t]+$/,"")}function p(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var y=/^[A-Z-]+$/;function f(t){return y.test(t)?t.toLowerCase():t}function v(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function m(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,(t,i)=>e--&&i?String(Math.round(parseFloat(t))):t)}var x=/(\[[^\]]+\])/g,b=/(#[^\s+>~.[:]+)/g,S=/(\.[^\s+>~.[:]+)/g,w=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,P=/(:[\w-]+\([^)]*\))/gi,A=/(:[^\s+>~.[:]+)/g,C=/([^\s+>~.[:]+)/g;function T(t,e){var i=e.exec(t);return i?[t.replace(e," "),i.length]:[t,0]}function V(t){var e=[0,0,0],i=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),r=0;return[i,r]=T(i,x),e[1]+=r,[i,r]=T(i,b),e[0]+=r,[i,r]=T(i,S),e[1]+=r,[i,r]=T(i,w),e[2]+=r,[i,r]=T(i,P),e[1]+=r,[i,r]=T(i,A),e[1]+=r,i=i.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[i,r]=T(i,C),e[2]+=r,e.join("")}var E=1e-8;function M(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function k(t,e){return(t[0]*e[0]+t[1]*e[1])/(M(t)*M(e))}function O(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(k(t,e))}function N(t){return t*t*t}function D(t){return 3*t*t*(1-t)}function B(t){return 3*t*(1-t)*(1-t)}function z(t){return(1-t)*(1-t)*(1-t)}function L(t){return t*t}function R(t){return 2*t*(1-t)}function I(t){return(1-t)*(1-t)}class F{constructor(t,e,i){this.document=t,this.name=e,this.value=i,this.isNormalizedColor=!1}static empty(t){return new F(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:i}=this;return g(this.getString()).trim().split(t).map(t=>new F(e,i,t))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,i="string"==typeof e;return i&&t?t.test(e):i}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,i=parseFloat(e);return this.isString(/%$/)&&(i/=100),i}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=m(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[i,r]="boolean"==typeof t?[void 0,t]:[t],{viewPort:s}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(s.computeSize("x"),s.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(s.computeSize("x"),s.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*s.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*s.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&r:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*s.computeSize(i);default:var a=this.getNumber();if(e&&a<1)return a*s.computeSize(i);return a}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var i=this.getDefinition();if(!i)return null;if("function"==typeof i.createGradient)return i.createGradient(this.document.ctx,t,e);if("function"==typeof i.createPattern){if(i.getHrefAttribute().hasValue()){var r=i.getAttribute("patternTransform");i=i.getHrefAttribute().getDefinition(),r.hasValue()&&i.getAttribute("patternTransform",!0).setValue(r.value)}return i.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?F.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),i=e.length,r=0,s=0;s<i&&(","===e[s]&&r++,3!==r);s++);if(t.hasValue()&&this.isString()&&3!==r){var a=new n(e);a.ok&&(a.alpha=t.getNumber(),e=a.toRGBA())}return new F(this.document,this.name,e)}}F.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class j{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class _{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[i=e,r=e]=p(t);return new _(i,r)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[i=e,r=i]=p(t);return new _(i,r)}static parsePath(t){for(var e=p(t),i=e.length,r=[],s=0;s<i;s+=2)r.push(new _(e[s],e[s+1]));return r}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:i}=this,r=e*t[0]+i*t[2]+t[4],s=e*t[1]+i*t[3]+t[5];this.x=r,this.y=s}}class X{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:i}=this,r=t.ctx.canvas;r.onclick=e,r.onmousemove=i,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:i}=this,{style:r}=t.ctx.canvas;r&&(r.cursor=""),e.forEach((t,e)=>{for(var{run:r}=t,s=i[e];s;)r(s),s=s.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:i,eventElements:r}=this;i.forEach((i,s)=>{var{x:a,y:n}=i;!r[s]&&e.isPointInPath&&e.isPointInPath(a,n)&&(r[s]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:i,eventElements:r}=this;i.forEach((i,s)=>{var{x:a,y:n}=i;!r[s]&&e.isPointInBox(a,n)&&(r[s]=t)})}}mapXY(t,e){for(var{window:i,ctx:r}=this.screen,s=new _(t,e),a=r.canvas;a;)s.x-=a.offsetLeft,s.y-=a.offsetTop,a=a.offsetParent;return i.scrollX&&(s.x+=i.scrollX),i.scrollY&&(s.y+=i.scrollY),s}onClick(t){var{x:e,y:i}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:i,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:i}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:i,run(t){t.onMouseMove&&t.onMouseMove()}})}}var U="undefined"!=typeof window?window:null,W="undefined"!=typeof fetch?fetch.bind(void 0):null;class H{constructor(t){var{fetch:e=W,window:i=U}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new j,this.mouse=new X(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=i,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every(t=>t());return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:i,aspectRatio:r,width:s,desiredWidth:a,height:n,desiredHeight:h,minX:o=0,minY:l=0,refX:u,refY:c,clip:d=!1,clipX:p=0,clipY:y=0}=t,[f,v]=g(r).replace(/^defer\s/,"").split(" "),m=f||"xMidYMid",x=v||"meet",b=s/a,S=n/h,w=Math.min(b,S),P=Math.max(b,S),A=a,C=h;"meet"===x&&(A*=w,C*=w),"slice"===x&&(A*=P,C*=P);var T=new F(e,"refX",u),V=new F(e,"refY",c),E=T.hasValue()&&V.hasValue();if(E&&i.translate(-w*T.getPixels("x"),-w*V.getPixels("y")),d){var M=w*p,k=w*y;i.beginPath(),i.moveTo(M,k),i.lineTo(s,k),i.lineTo(s,n),i.lineTo(M,n),i.closePath(),i.clip()}if(!E){var O="meet"===x&&w===S,N="slice"===x&&P===S,D="meet"===x&&w===b,B="slice"===x&&P===b;m.startsWith("xMid")&&(O||N)&&i.translate(s/2-A/2,0),m.endsWith("YMid")&&(D||B)&&i.translate(0,n/2-C/2),m.startsWith("xMax")&&(O||N)&&i.translate(s-A,0),m.endsWith("YMax")&&(D||B)&&i.translate(0,n-C)}switch(!0){case"none"===m:i.scale(b,S);break;case"meet"===x:i.scale(w,w);break;case"slice"===x:i.scale(P,P)}i.translate(-o,-l)}start(t){var{enableRedraw:e=!1,ignoreMouse:i=!1,ignoreAnimation:r=!1,ignoreDimensions:s=!1,ignoreClear:n=!1,forceRedraw:h,scaleWidth:o,scaleHeight:l,offsetX:u,offsetY:g}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:c,mouse:d}=this,p=1e3/c;if(this.frameDuration=p,this.readyPromise=new Promise(t=>{this.resolveReady=t}),this.isReady()&&this.render(t,s,n,o,l,u,g),e){var y=Date.now(),f=y,v=0,m=()=>{(v=(y=Date.now())-f)>=p&&(f=y-v%p,this.shouldUpdate(r,h)&&(this.render(t,s,n,o,l,u,g),d.runEvents())),this.intervalId=a(m)};i||d.start(),this.intervalId=a(m)}}stop(){this.intervalId&&(a.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:i}=this;if(this.animations.reduce((t,e)=>e.update(i)||t,!1))return!0}return!!("function"==typeof e&&e()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(t,e,i,r,s,a,n){var{CLIENT_WIDTH:h,CLIENT_HEIGHT:o,viewPort:l,ctx:u,isFirstRender:g}=this,c=u.canvas;l.clear(),c.width&&c.height?l.setCurrent(c.width,c.height):l.setCurrent(h,o);var d=t.getStyle("width"),y=t.getStyle("height");!e&&(g||"number"!=typeof r&&"number"!=typeof s)&&(d.hasValue()&&(c.width=d.getPixels("x"),c.style&&(c.style.width="".concat(c.width,"px"))),y.hasValue()&&(c.height=y.getPixels("y"),c.style&&(c.style.height="".concat(c.height,"px"))));var f=c.clientWidth||c.width,v=c.clientHeight||c.height;if(e&&d.hasValue()&&y.hasValue()&&(f=d.getPixels("x"),v=y.getPixels("y")),l.setCurrent(f,v),"number"==typeof a&&t.getAttribute("x",!0).setValue(a),"number"==typeof n&&t.getAttribute("y",!0).setValue(n),"number"==typeof r||"number"==typeof s){var m=p(t.getAttribute("viewBox").getString()),x=0,b=0;if("number"==typeof r){var S=t.getStyle("width");S.hasValue()?x=S.getPixels("x")/r:isNaN(m[2])||(x=m[2]/r)}if("number"==typeof s){var w=t.getStyle("height");w.hasValue()?b=w.getPixels("y")/s:isNaN(m[3])||(b=m[3]/s)}x||(x=b),b||(b=x),t.getAttribute("width",!0).setValue(r),t.getAttribute("height",!0).setValue(s);var P=t.getStyle("transform",!0,!0);P.setValue("".concat(P.getString()," scale(").concat(1/x,", ").concat(1/b,")"))}i||u.clearRect(0,0,f,v),t.render(u),g&&(this.isFirstRender=!1)}}H.defaultWindow=U,H.defaultFetch=W;var{defaultFetch:q}=H,G="undefined"!=typeof DOMParser?DOMParser:null;class Q{constructor(){var{fetch:t=q,DOMParser:e=G}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return(0,r.A)(function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)})()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(i){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw Error(e.textContent);return t}load(t){var e=this;return(0,r.A)(function*(){var i=yield e.fetch(t),r=yield i.text();return e.parseFromString(r)})()}}class ${constructor(t,e){this.type="translate",this.point=null,this.point=_.parse(e)}apply(t){var{x:e,y:i}=this.point;t.translate(e||0,i||0)}unapply(t){var{x:e,y:i}=this.point;t.translate(-1*e||0,-1*i||0)}applyToPoint(t){var{x:e,y:i}=this.point;t.applyTransform([1,0,0,1,e||0,i||0])}}class Y{constructor(t,e,i){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var r=p(e);this.angle=new F(t,"angle",r[0]),this.originX=i[0],this.originY=i[1],this.cx=r[1]||0,this.cy=r[2]||0}apply(t){var{cx:e,cy:i,originX:r,originY:s,angle:a}=this,n=e+r.getPixels("x"),h=i+s.getPixels("y");t.translate(n,h),t.rotate(a.getRadians()),t.translate(-n,-h)}unapply(t){var{cx:e,cy:i,originX:r,originY:s,angle:a}=this,n=e+r.getPixels("x"),h=i+s.getPixels("y");t.translate(n,h),t.rotate(-1*a.getRadians()),t.translate(-n,-h)}applyToPoint(t){var{cx:e,cy:i,angle:r}=this,s=r.getRadians();t.applyTransform([1,0,0,1,e||0,i||0]),t.applyTransform([Math.cos(s),Math.sin(s),-Math.sin(s),Math.cos(s),0,0]),t.applyTransform([1,0,0,1,-e||0,-i||0])}}class Z{constructor(t,e,i){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var r=_.parseScale(e);(0===r.x||0===r.y)&&(r.x=E,r.y=E),this.scale=r,this.originX=i[0],this.originY=i[1]}apply(t){var{scale:{x:e,y:i},originX:r,originY:s}=this,a=r.getPixels("x"),n=s.getPixels("y");t.translate(a,n),t.scale(e,i||e),t.translate(-a,-n)}unapply(t){var{scale:{x:e,y:i},originX:r,originY:s}=this,a=r.getPixels("x"),n=s.getPixels("y");t.translate(a,n),t.scale(1/e,1/i||e),t.translate(-a,-n)}applyToPoint(t){var{x:e,y:i}=this.scale;t.applyTransform([e||0,0,0,i||0,0,0])}}class J{constructor(t,e,i){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=p(e),this.originX=i[0],this.originY=i[1]}apply(t){var{originX:e,originY:i,matrix:r}=this,s=e.getPixels("x"),a=i.getPixels("y");t.translate(s,a),t.transform(r[0],r[1],r[2],r[3],r[4],r[5]),t.translate(-s,-a)}unapply(t){var{originX:e,originY:i,matrix:r}=this,s=r[0],a=r[2],n=r[4],h=r[1],o=r[3],l=r[5],u=1/(s*(o-0*l)-a*(h-0*l)+n*(0*h-0*o)),g=e.getPixels("x"),c=i.getPixels("y");t.translate(g,c),t.transform(u*(o-0*l),u*(0*l-h),u*(0*n-a),u*(s-0*n),u*(a*l-n*o),u*(n*h-s*l)),t.translate(-g,-c)}applyToPoint(t){t.applyTransform(this.matrix)}}class K extends J{constructor(t,e,i){super(t,e,i),this.type="skew",this.angle=null,this.angle=new F(t,"angle",e)}}class tt extends K{constructor(t,e,i){super(t,e,i),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class te extends K{constructor(t,e,i){super(t,e,i),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class ti{constructor(t,e,i){this.document=t,this.transforms=[],g(e).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/).forEach(t=>{if("none"!==t){var[e,r]=function(t){var[e,i]=t.split("(");return[e.trim(),i.trim().replace(")","")]}(t),s=ti.transformTypes[e];void 0!==s&&this.transforms.push(new s(this.document,r,i))}})}static fromElement(t,e){var i=e.getStyle("transform",!1,!0),[r,s=r]=e.getStyle("transform-origin",!1,!0).split();return i.hasValue()?new ti(t,i.getString(),[r,s]):null}apply(t){for(var{transforms:e}=this,i=e.length,r=0;r<i;r++)e[r].apply(t)}unapply(t){for(var{transforms:e}=this,i=e.length,r=i-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,i=e.length,r=0;r<i;r++)e[r].applyToPoint(t)}}ti.transformTypes={translate:$,rotate:Y,scale:Z,matrix:J,skewX:tt,skewY:te};class tr{constructor(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=i,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],!e||1!==e.nodeType)return;Array.from(e.attributes).forEach(e=>{var i=f(e.nodeName);this.attributes[i]=new F(t,i,e.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()&&this.getAttribute("style").getString().split(";").map(t=>t.trim()).forEach(e=>{if(e){var[i,r]=e.split(":").map(t=>t.trim());this.styles[i]=new F(t,i,r)}});var{definitions:r}=t,s=this.getAttribute("id");s.hasValue()&&!r[s.getString()]&&(r[s.getString()]=this),Array.from(e.childNodes).forEach(e=>{if(1===e.nodeType)this.addChild(e);else if(i&&(3===e.nodeType||4===e.nodeType)){var r=t.createTextNode(e);r.getText().length>0&&this.addChild(r)}})}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.attributes[t];if(!i&&e){var r=new F(this.document,t,"");return this.attributes[t]=r,r}return i||F.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return F.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=this.styles[t];if(r)return r;var s=this.getAttribute(t);if(null!=s&&s.hasValue())return this.styles[t]=s,s;if(!i){var{parent:a}=this;if(a){var n=a.getStyle(t);if(null!=n&&n.hasValue())return n}}if(e){var h=new F(this.document,t,"");return this.styles[t]=h,h}return r||F.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var i=this.getStyle("filter").getDefinition();i&&(this.applyEffects(t),i.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=ti.fromElement(this.document,this);e&&e.apply(t);var i=this.getStyle("clip-path",!1,!0);if(i.hasValue()){var r=i.getDefinition();r&&r.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach(e=>{e.render(t)})}addChild(t){var e=t instanceof tr?t:this.document.createElement(t);e.parent=this,tr.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:i}=this;if("function"==typeof i.matches)return i.matches(t);var r=null==(e=i.getAttribute)?void 0:e.call(i,"class");return!!r&&""!==r&&r.split(" ").some(e=>".".concat(e)===t)}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var i in t)if(!i.startsWith("@")&&this.matchesSelector(i)){var r=t[i],s=e[i];if(r)for(var a in r){var n=this.stylesSpecificity[a];void 0===n&&(n="000"),s>=n&&(this.styles[a]=r[a],this.stylesSpecificity[a]=s)}}}removeStyles(t,e){return e.reduce((e,i)=>{var r=t.getStyle(i);if(!r.hasValue())return e;var s=r.getString();return r.setValue(""),[...e,[i,s]]},[])}restoreStyles(t,e){e.forEach(e=>{var[i,r]=e;t.getStyle(i,!0).setValue(r)})}isFirstChild(){var t;return(null==(t=this.parent)?void 0:t.children.indexOf(this))===0}}tr.ignoreChildTypes=["title"];class ts extends tr{constructor(t,e,i){super(t,e,i)}}function ta(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}class tn{constructor(t,e,i,r,s,a){var n=a?"string"==typeof a?tn.parse(a):a:{};this.fontFamily=s||n.fontFamily,this.fontSize=r||n.fontSize,this.fontStyle=t||n.fontStyle,this.fontWeight=i||n.fontWeight,this.fontVariant=e||n.fontVariant}static parse(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0,i="",r="",s="",a="",n="",h=g(t).trim().split(" "),o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return h.forEach(t=>{switch(!0){case!o.fontStyle&&tn.styles.includes(t):"inherit"!==t&&(i=t),o.fontStyle=!0;break;case!o.fontVariant&&tn.variants.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&tn.weights.includes(t):"inherit"!==t&&(s=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([a]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(n+=t)}}),new tn(i,r,s,a,n,e)}toString(){var t;return[function(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:if(/^oblique\s+(-|)\d+deg$/.test(e))return e;return""}}(this.fontStyle),this.fontVariant,function(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:if(/^[\d.]+$/.test(e))return e;return""}}(this.fontWeight),this.fontSize,(t=this.fontFamily,void 0===l?t:t.trim().split(",").map(ta).join(","))].join(" ").trim()}}tn.styles="normal|italic|oblique|inherit",tn.variants="normal|small-caps|inherit",tn.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class th{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:NaN,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:NaN,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:NaN;this.x1=t,this.y1=e,this.x2=i,this.y2=r,this.addPoint(t,e),this.addPoint(i,r)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:i,x2:r,y2:s}=t;this.addPoint(e,i),this.addPoint(r,s)}}sumCubic(t,e,i,r,s){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*i+3*(1-t)*Math.pow(t,2)*r+Math.pow(t,3)*s}bezierCurveAdd(t,e,i,r,s){var a=6*e-12*i+6*r,n=-3*e+9*i-9*r+3*s,h=3*i-3*e;if(0===n){if(0===a)return;var o=-h/a;0<o&&o<1&&(t?this.addX(this.sumCubic(o,e,i,r,s)):this.addY(this.sumCubic(o,e,i,r,s)));return}var l=Math.pow(a,2)-4*h*n;if(!(l<0)){var u=(-a+Math.sqrt(l))/(2*n);0<u&&u<1&&(t?this.addX(this.sumCubic(u,e,i,r,s)):this.addY(this.sumCubic(u,e,i,r,s)));var g=(-a-Math.sqrt(l))/(2*n);0<g&&g<1&&(t?this.addX(this.sumCubic(g,e,i,r,s)):this.addY(this.sumCubic(g,e,i,r,s)))}}addBezierCurve(t,e,i,r,s,a,n,h){this.addPoint(t,e),this.addPoint(n,h),this.bezierCurveAdd(!0,t,i,s,n),this.bezierCurveAdd(!1,e,r,a,h)}addQuadraticCurve(t,e,i,r,s,a){var n=t+2/3*(i-t),h=e+2/3*(r-e);this.addBezierCurve(t,e,n,n+1/3*(s-t),h,h+1/3*(a-e),s,a)}isPointInBox(t,e){var{x1:i,y1:r,x2:s,y2:a}=this;return i<=t&&t<=s&&r<=e&&e<=a}}class to extends h.LQ{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new _(0,0),this.control=new _(0,0),this.current=new _(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",i=new _(this.command[t],this.command[e]);return this.makeAbsolute(i)}getAsControlPoint(t,e){var i=this.getPoint(t,e);return this.control=i,i}getAsCurrentPoint(t,e){var i=this.getPoint(t,e);return this.current=i,i}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==h.LQ.CURVE_TO&&t!==h.LQ.SMOOTH_CURVE_TO&&t!==h.LQ.QUAD_TO&&t!==h.LQ.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:i},control:{x:r,y:s}}=this;return new _(2*e-r,2*i-s)}makeAbsolute(t){if(this.command.relative){var{x:e,y:i}=this.current;t.x+=e,t.y+=i}return t}addMarker(t,e,i){var{points:r,angles:s}=this;i&&s.length>0&&!s[s.length-1]&&(s[s.length-1]=r[r.length-1].angleTo(i)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,i=0;i<e;i++)if(!t[i]){for(var r=i+1;r<e;r++)if(t[r]){t[i]=t[r];break}}return t}}class tl extends tr{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var i=e.getStyle("opacity",!1,!0);i.hasValue(!0)&&(t*=i.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var i=this.getStyle("fill"),r=this.getStyle("fill-opacity"),s=this.getStyle("stroke"),a=this.getStyle("stroke-opacity");if(i.isUrlDefinition()){var n=i.getFillStyleDefinition(this,r);n&&(t.fillStyle=n)}else if(i.hasValue()){"currentColor"===i.getString()&&i.setValue(this.getStyle("color").getColor());var h=i.getColor();"inherit"!==h&&(t.fillStyle="none"===h?"rgba(0,0,0,0)":h)}if(r.hasValue()){var o=new F(this.document,"fill",t.fillStyle).addOpacity(r).getColor();t.fillStyle=o}if(s.isUrlDefinition()){var l=s.getFillStyleDefinition(this,a);l&&(t.strokeStyle=l)}else if(s.hasValue()){"currentColor"===s.getString()&&s.setValue(this.getStyle("color").getColor());var u=s.getString();"inherit"!==u&&(t.strokeStyle="none"===u?"rgba(0,0,0,0)":u)}if(a.hasValue()){var g=new F(this.document,"stroke",t.strokeStyle).addOpacity(a).getString();t.strokeStyle=g}var c=this.getStyle("stroke-width");c.hasValue()&&(t.lineWidth=c.getPixels()||E);var d=this.getStyle("stroke-linecap"),y=this.getStyle("stroke-linejoin"),f=this.getStyle("stroke-miterlimit"),v=this.getStyle("stroke-dasharray"),m=this.getStyle("stroke-dashoffset");if(d.hasValue()&&(t.lineCap=d.getString()),y.hasValue()&&(t.lineJoin=y.getString()),f.hasValue()&&(t.miterLimit=f.getNumber()),v.hasValue()&&"none"!==v.getString()){var x=p(v.getString());void 0!==t.setLineDash?t.setLineDash(x):void 0!==t.webkitLineDash?t.webkitLineDash=x:void 0!==t.mozDash&&(1!==x.length||0!==x[0])&&(t.mozDash=x);var b=m.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=b:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=b:void 0!==t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var S=this.getStyle("font"),w=this.getStyle("font-style"),P=this.getStyle("font-variant"),A=this.getStyle("font-weight"),C=this.getStyle("font-size"),T=this.getStyle("font-family"),V=new tn(w.getString(),P.getString(),A.getString(),C.hasValue()?"".concat(C.getPixels(!0),"px"):"",T.getString(),tn.parse(S.getString(),t.font));w.setValue(V.fontStyle),P.setValue(V.fontVariant),A.setValue(V.fontWeight),C.setValue(V.fontSize),T.setValue(V.fontFamily),t.font=V.toString(),C.isPixels()&&(this.document.emSize=C.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class tu extends tl{constructor(t,e,i){super(t,e,i),this.type="path",this.pathParser=null,this.pathParser=new to(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,i=new th;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case to.MOVE_TO:this.pathM(t,i);break;case to.LINE_TO:this.pathL(t,i);break;case to.HORIZ_LINE_TO:this.pathH(t,i);break;case to.VERT_LINE_TO:this.pathV(t,i);break;case to.CURVE_TO:this.pathC(t,i);break;case to.SMOOTH_CURVE_TO:this.pathS(t,i);break;case to.QUAD_TO:this.pathQ(t,i);break;case to.SMOOTH_QUAD_TO:this.pathT(t,i);break;case to.ARC:this.pathA(t,i);break;case to.CLOSE_PATH:this.pathZ(t,i)}return i}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),i=t.getMarkerAngles();return e.map((t,e)=>[t,i[e]])}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var i=this.getMarkers();if(i){var r=i.length-1,s=this.getStyle("marker-start"),a=this.getStyle("marker-mid"),n=this.getStyle("marker-end");if(s.isUrlDefinition()){var h=s.getDefinition(),[o,l]=i[0];h.render(t,o,l)}if(a.isUrlDefinition())for(var u=a.getDefinition(),g=1;g<r;g++){var[c,d]=i[g];u.render(t,c,d)}if(n.isUrlDefinition()){var p=n.getDefinition(),[y,f]=i[r];p.render(t,y,f)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:i}=this,{point:r}=tu.pathM(i),{x:s,y:a}=r;i.addMarker(r),e.addPoint(s,a),t&&t.moveTo(s,a)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:i}=this,{current:r,point:s}=tu.pathL(i),{x:a,y:n}=s;i.addMarker(s,r),e.addPoint(a,n),t&&t.lineTo(a,n)}static pathH(t){var{current:e,command:i}=t,r=new _((i.relative?e.x:0)+i.x,e.y);return t.current=r,{current:e,point:r}}pathH(t,e){var{pathParser:i}=this,{current:r,point:s}=tu.pathH(i),{x:a,y:n}=s;i.addMarker(s,r),e.addPoint(a,n),t&&t.lineTo(a,n)}static pathV(t){var{current:e,command:i}=t,r=new _(e.x,(i.relative?e.y:0)+i.y);return t.current=r,{current:e,point:r}}pathV(t,e){var{pathParser:i}=this,{current:r,point:s}=tu.pathV(i),{x:a,y:n}=s;i.addMarker(s,r),e.addPoint(a,n),t&&t.lineTo(a,n)}static pathC(t){var{current:e}=t,i=t.getPoint("x1","y1");return{current:e,point:i,controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:i}=this,{current:r,point:s,controlPoint:a,currentPoint:n}=tu.pathC(i);i.addMarker(n,a,s),e.addBezierCurve(r.x,r.y,s.x,s.y,a.x,a.y,n.x,n.y),t&&t.bezierCurveTo(s.x,s.y,a.x,a.y,n.x,n.y)}static pathS(t){var{current:e}=t,i=t.getReflectedControlPoint();return{current:e,point:i,controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:i}=this,{current:r,point:s,controlPoint:a,currentPoint:n}=tu.pathS(i);i.addMarker(n,a,s),e.addBezierCurve(r.x,r.y,s.x,s.y,a.x,a.y,n.x,n.y),t&&t.bezierCurveTo(s.x,s.y,a.x,a.y,n.x,n.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:i}=this,{current:r,controlPoint:s,currentPoint:a}=tu.pathQ(i);i.addMarker(a,s,s),e.addQuadraticCurve(r.x,r.y,s.x,s.y,a.x,a.y),t&&t.quadraticCurveTo(s.x,s.y,a.x,a.y)}static pathT(t){var{current:e}=t,i=t.getReflectedControlPoint();return t.control=i,{current:e,controlPoint:i,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:i}=this,{current:r,controlPoint:s,currentPoint:a}=tu.pathT(i);i.addMarker(a,s,s),e.addQuadraticCurve(r.x,r.y,s.x,s.y,a.x,a.y),t&&t.quadraticCurveTo(s.x,s.y,a.x,a.y)}static pathA(t){var{current:e,command:i}=t,{rX:r,rY:s,xRot:a,lArcFlag:n,sweepFlag:h}=i,o=Math.PI/180*a,l=t.getAsCurrentPoint(),u=new _(Math.cos(o)*(e.x-l.x)/2+Math.sin(o)*(e.y-l.y)/2,-Math.sin(o)*(e.x-l.x)/2+Math.cos(o)*(e.y-l.y)/2),g=Math.pow(u.x,2)/Math.pow(r,2)+Math.pow(u.y,2)/Math.pow(s,2);g>1&&(r*=Math.sqrt(g),s*=Math.sqrt(g));var c=(n===h?-1:1)*Math.sqrt((Math.pow(r,2)*Math.pow(s,2)-Math.pow(r,2)*Math.pow(u.y,2)-Math.pow(s,2)*Math.pow(u.x,2))/(Math.pow(r,2)*Math.pow(u.y,2)+Math.pow(s,2)*Math.pow(u.x,2)));isNaN(c)&&(c=0);var d=new _(c*r*u.y/s,-(c*s)*u.x/r),p=new _((e.x+l.x)/2+Math.cos(o)*d.x-Math.sin(o)*d.y,(e.y+l.y)/2+Math.sin(o)*d.x+Math.cos(o)*d.y),y=O([1,0],[(u.x-d.x)/r,(u.y-d.y)/s]),f=[(u.x-d.x)/r,(u.y-d.y)/s],v=[(-u.x-d.x)/r,(-u.y-d.y)/s],m=O(f,v);return -1>=k(f,v)&&(m=Math.PI),k(f,v)>=1&&(m=0),{currentPoint:l,rX:r,rY:s,sweepFlag:h,xAxisRotation:o,centp:p,a1:y,ad:m}}pathA(t,e){var{pathParser:i}=this,{currentPoint:r,rX:s,rY:a,sweepFlag:n,xAxisRotation:h,centp:o,a1:l,ad:u}=tu.pathA(i),g=1-n?1:-1,c=l+u/2*g,d=new _(o.x+s*Math.cos(c),o.y+a*Math.sin(c));if(i.addMarkerAngle(d,c-g*Math.PI/2),i.addMarkerAngle(r,c-g*Math.PI),e.addPoint(r.x,r.y),t&&!isNaN(l)&&!isNaN(u)){var p=s>a?1:s/a,y=s>a?a/s:1;t.translate(o.x,o.y),t.rotate(h),t.scale(p,y),t.arc(0,0,s>a?s:a,l,l+u,!!(1-n)),t.scale(1/p,1/y),t.rotate(-h),t.translate(-o.x,-o.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){tu.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class tg extends tu{constructor(t,e,i){super(t,e,i),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class tc extends tl{constructor(t,e,i){super(t,e,new.target===tc||i),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var i=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();i&&(t.textBaseline=i)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=1/0,this.maxX=-1/0}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((i,r)=>{var s=this.getChildBoundingBox(t,this,this,r);e?e.addBoundingBox(s):e=s}),e}getFontSize(){var{document:t,parent:e}=this,i=tn.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(i)}getTElementBoundingBox(t){var e=this.getFontSize();return new th(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,i){var r=e[i],s=null;if(t.isArabic){var a=e.length,n=e[i-1],h=e[i+1],o="isolated";if((0===i||" "===n)&&i<a-1&&" "!==h&&(o="terminal"),i>0&&" "!==n&&i<a-1&&" "!==h&&(o="medial"),i>0&&" "!==n&&(i===a-1||" "===h)&&(o="initial"),void 0!==t.glyphs[r]){var l=t.glyphs[r];s=l instanceof tg?l:l[o]}}else s=t.glyphs[r];return s||(s=t.missingGlyph),s}getText(){return""}getTextFromNode(t){var e=t||this.node,i=Array.from(e.parentNode.childNodes),r=i.indexOf(e),s=i.length-1,a=g(e.textContent||"");return 0===r&&(a=c(a)),r===s&&(a=d(a)),a}renderChildren(t){if("text"!==this.type)return void this.renderTElementChildren(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((e,i)=>{this.renderChild(t,this,this,i)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}renderTElementChildren(t){var{document:e,parent:i}=this,r=this.getText(),s=i.getStyle("font-family").getDefinition();if(s){for(var{unitsPerEm:a}=s.fontFace,n=tn.parse(e.ctx.font),h=i.getStyle("font-size").getNumber(n.fontSize),o=i.getStyle("font-style").getString(n.fontStyle),l=h/a,u=s.isRTL?r.split("").reverse().join(""):r,g=p(i.getAttribute("dx").getString()),c=u.length,d=0;d<c;d++){var y=this.getGlyph(s,u,d);t.translate(this.x,this.y),t.scale(l,-l);var f=t.lineWidth;t.lineWidth=t.lineWidth*a/h,"italic"===o&&t.transform(1,0,.4,1,0,0),y.render(t),"italic"===o&&t.transform(1,0,-.4,1,0,0),t.lineWidth=f,t.scale(1/l,-1/l),t.translate(-this.x,-this.y),this.x+=h*(y.horizAdvX||s.horizAdvX)/a,void 0===g[d]||isNaN(g[d])||(this.x+=g[d])}return}var{x:v,y:m}=this;t.fillStyle&&t.fillText(r,v,m),t.strokeStyle&&t.strokeText(r,v,m)}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),i=0;i="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var r=this.textChunkStart;r<this.leafTexts.length;r++)this.leafTexts[r].x+=i;this.minX=1/0,this.maxX=-1/0,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,i)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,i)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,i,r){var s=i.children[r];s.children.length>0?s.children.forEach((i,r)=>{e.adjustChildCoordinatesRecursiveCore(t,e,s,r)}):this.adjustChildCoordinates(t,e,i,r)}adjustChildCoordinates(t,e,i,r){var s=i.children[r];if("function"!=typeof s.measureText)return s;t.save(),s.setContext(t,!0);var a=s.getAttribute("x"),n=s.getAttribute("y"),h=s.getAttribute("dx"),o=s.getAttribute("dy"),l=s.getStyle("font-family").getDefinition(),u=!!l&&l.isRTL;0===r&&(a.hasValue()||a.setValue(s.getInheritedAttribute("x")),n.hasValue()||n.setValue(s.getInheritedAttribute("y")),h.hasValue()||h.setValue(s.getInheritedAttribute("dx")),o.hasValue()||o.setValue(s.getInheritedAttribute("dy")));var g=s.measureText(t);return u&&(e.x-=g),a.hasValue()?(e.applyAnchoring(),s.x=a.getPixels("x"),h.hasValue()&&(s.x+=h.getPixels("x"))):(h.hasValue()&&(e.x+=h.getPixels("x")),s.x=e.x),e.x=s.x,u||(e.x+=g),n.hasValue()?(s.y=n.getPixels("y"),o.hasValue()&&(s.y+=o.getPixels("y"))):(o.hasValue()&&(e.y+=o.getPixels("y")),s.y=e.y),e.y=s.y,e.leafTexts.push(s),e.minX=Math.min(e.minX,s.x,s.x+g),e.maxX=Math.max(e.maxX,s.x,s.x+g),s.clearContext(t),t.restore(),s}getChildBoundingBox(t,e,i,r){var s=i.children[r];if("function"!=typeof s.getBoundingBox)return null;var a=s.getBoundingBox(t);return a?(s.children.forEach((i,r)=>{var n=e.getChildBoundingBox(t,e,s,r);a.addBoundingBox(n)}),a):null}renderChild(t,e,i,r){var s=i.children[r];s.render(t),s.children.forEach((i,r)=>{e.renderChild(t,e,s,r)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var i=this.getText(),r=this.measureTargetText(t,i);return this.measureCache=r,r}measureTargetText(t,e){if(!e.length)return 0;var{parent:i}=this,r=i.getStyle("font-family").getDefinition();if(r){for(var s=this.getFontSize(),a=r.isRTL?e.split("").reverse().join(""):e,n=p(i.getAttribute("dx").getString()),h=a.length,o=0,l=0;l<h;l++)o+=(this.getGlyph(r,a,l).horizAdvX||r.horizAdvX)*s/r.fontFace.unitsPerEm,void 0===n[l]||isNaN(n[l])||(o+=n[l]);return o}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:u}=t.measureText(e);return this.clearContext(t),t.restore(),u}getInheritedAttribute(t){for(var e=this;e instanceof tc&&e.isFirstChild();){var i=e.parent.getAttribute(t);if(i.hasValue(!0))return i.getValue("0");e=e.parent}return null}}class td extends tc{constructor(t,e,i){super(t,e,new.target===td||i),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class tp extends td{constructor(){super(...arguments),this.type="textNode"}}class ty extends tl{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:i}=this,{screen:r,window:s}=i,a=t.canvas;if(r.setDefaults(t),a.style&&void 0!==t.font&&s&&void 0!==s.getComputedStyle){t.font=s.getComputedStyle(a).getPropertyValue("font");var n=new F(i,"fontSize",tn.parse(t.font).fontSize);n.hasValue()&&(i.rootEmSize=n.getPixels("y"),i.emSize=i.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:h,height:o}=r.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var l=this.getAttribute("refX"),u=this.getAttribute("refY"),g=this.getAttribute("viewBox"),c=g.hasValue()?p(g.getString()):null,d=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),y=0,f=0,v=0,m=0;c&&(y=c[0],f=c[1]),this.root||(h=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y"),"marker"===this.type&&(v=y,m=f,y=0,f=0)),r.viewPort.setCurrent(h,o),this.node&&(!this.parent||(null==(e=this.node.parentNode)?void 0:e.nodeName)==="foreignObject")&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),c&&(h=c[2],o=c[3]),i.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:r.viewPort.width,desiredWidth:h,height:r.viewPort.height,desiredHeight:o,minX:y,minY:f,refX:l.getValue(),refY:u.getValue(),clip:d,clipX:v,clipY:m}),c&&(r.viewPort.removeCurrent(),r.viewPort.setCurrent(h,o))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=this.getAttribute("width",!0),s=this.getAttribute("height",!0),a=this.getAttribute("viewBox"),n=this.getAttribute("style"),h=r.getNumber(0),o=s.getNumber(0);if(i)if("string"==typeof i)this.getAttribute("preserveAspectRatio",!0).setValue(i);else{var l=this.getAttribute("preserveAspectRatio");l.hasValue()&&l.setValue(l.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(r.setValue(t),s.setValue(e),a.hasValue()||a.setValue("0 0 ".concat(h||t," ").concat(o||e)),n.hasValue()){var u=this.getStyle("width"),g=this.getStyle("height");u.hasValue()&&u.setValue("".concat(t,"px")),g.hasValue()&&g.setValue("".concat(e,"px"))}}}class tf extends tu{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),i=this.getAttribute("y").getPixels("y"),r=this.getStyle("width",!1,!0).getPixels("x"),s=this.getStyle("height",!1,!0).getPixels("y"),a=this.getAttribute("rx"),n=this.getAttribute("ry"),h=a.getPixels("x"),o=n.getPixels("y");if(a.hasValue()&&!n.hasValue()&&(o=h),n.hasValue()&&!a.hasValue()&&(h=o),h=Math.min(h,r/2),o=Math.min(o,s/2),t){var l=(Math.sqrt(2)-1)/3*4;t.beginPath(),s>0&&r>0&&(t.moveTo(e+h,i),t.lineTo(e+r-h,i),t.bezierCurveTo(e+r-h+l*h,i,e+r,i+o-l*o,e+r,i+o),t.lineTo(e+r,i+s-o),t.bezierCurveTo(e+r,i+s-o+l*o,e+r-h+l*h,i+s,e+r-h,i+s),t.lineTo(e+h,i+s),t.bezierCurveTo(e+h-l*h,i+s,e,i+s-o+l*o,e,i+s-o),t.lineTo(e,i+o),t.bezierCurveTo(e,i+o-l*o,e+h-l*h,i,e+h,i),t.closePath())}return new th(e,i,e+r,i+s)}getMarkers(){return null}}class tv extends tu{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),i=this.getAttribute("cy").getPixels("y"),r=this.getAttribute("r").getPixels();return t&&r>0&&(t.beginPath(),t.arc(e,i,r,0,2*Math.PI,!1),t.closePath()),new th(e-r,i-r,e+r,i+r)}getMarkers(){return null}}class tm extends tu{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,i=this.getAttribute("rx").getPixels("x"),r=this.getAttribute("ry").getPixels("y"),s=this.getAttribute("cx").getPixels("x"),a=this.getAttribute("cy").getPixels("y");return t&&i>0&&r>0&&(t.beginPath(),t.moveTo(s+i,a),t.bezierCurveTo(s+i,a+e*r,s+e*i,a+r,s,a+r),t.bezierCurveTo(s-e*i,a+r,s-i,a+e*r,s-i,a),t.bezierCurveTo(s-i,a-e*r,s-e*i,a-r,s,a-r),t.bezierCurveTo(s+e*i,a-r,s+i,a-e*r,s+i,a),t.closePath()),new th(s-i,a-r,s+i,a+r)}getMarkers(){return null}}class tx extends tu{constructor(){super(...arguments),this.type="line"}getPoints(){return[new _(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new _(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:i},{x:r,y:s}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,i),t.lineTo(r,s)),new th(e,i,r,s)}getMarkers(){var[t,e]=this.getPoints(),i=t.angleTo(e);return[[t,i],[e,i]]}}class tb extends tu{constructor(t,e,i){super(t,e,i),this.type="polyline",this.points=[],this.points=_.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:i,y:r}]=e,s=new th(i,r);return t&&(t.beginPath(),t.moveTo(i,r)),e.forEach(e=>{var{x:i,y:r}=e;s.addPoint(i,r),t&&t.lineTo(i,r)}),s}getMarkers(){var{points:t}=this,e=t.length-1,i=[];return t.forEach((r,s)=>{s!==e&&i.push([r,r.angleTo(t[s+1])])}),i.length>0&&i.push([t[t.length-1],i[i.length-1][1]]),i}}class tS extends tb{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:i,y:r}]=this.points;return t&&(t.lineTo(i,r),t.closePath()),e}}class tw extends tr{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,i){var r=this.getStyle("width").getPixels("x",!0),s=this.getStyle("height").getPixels("y",!0),a=new ty(this.document,null);a.attributes.viewBox=new F(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.width=new F(this.document,"width","".concat(r,"px")),a.attributes.height=new F(this.document,"height","".concat(s,"px")),a.attributes.transform=new F(this.document,"transform",this.getAttribute("patternTransform").getValue()),a.children=this.children;var n=this.document.createCanvas(r,s),h=n.getContext("2d"),o=this.getAttribute("x"),l=this.getAttribute("y");o.hasValue()&&l.hasValue()&&h.translate(o.getPixels("x",!0),l.getPixels("y",!0)),i.hasValue()?this.styles["fill-opacity"]=i:Reflect.deleteProperty(this.styles,"fill-opacity");for(var u=-1;u<=1;u++)for(var g=-1;g<=1;g++)h.save(),a.attributes.x=new F(this.document,"x",u*n.width),a.attributes.y=new F(this.document,"y",g*n.height),a.render(h),h.restore();return t.createPattern(n,"repeat")}}class tP extends tr{constructor(){super(...arguments),this.type="marker"}render(t,e,i){if(e){var{x:r,y:s}=e,a=this.getAttribute("orient").getString("auto"),n=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(r,s),"auto"===a&&t.rotate(i),"strokeWidth"===n&&t.scale(t.lineWidth,t.lineWidth),t.save();var h=new ty(this.document,null);h.type=this.type,h.attributes.viewBox=new F(this.document,"viewBox",this.getAttribute("viewBox").getValue()),h.attributes.refX=new F(this.document,"refX",this.getAttribute("refX").getValue()),h.attributes.refY=new F(this.document,"refY",this.getAttribute("refY").getValue()),h.attributes.width=new F(this.document,"width",this.getAttribute("markerWidth").getValue()),h.attributes.height=new F(this.document,"height",this.getAttribute("markerHeight").getValue()),h.attributes.overflow=new F(this.document,"overflow",this.getAttribute("overflow").getValue()),h.attributes.fill=new F(this.document,"fill",this.getAttribute("fill").getColor("black")),h.attributes.stroke=new F(this.document,"stroke",this.getAttribute("stroke").getValue("none")),h.children=this.children,h.render(t),t.restore(),"strokeWidth"===n&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===a&&t.rotate(-i),t.translate(-r,-s)}}}class tA extends tr{constructor(){super(...arguments),this.type="defs"}render(){}}class tC extends tl{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new th;return this.children.forEach(i=>{e.addBoundingBox(i.getBoundingBox(t))}),e}}class tT extends tr{constructor(t,e,i){super(t,e,i),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:r,children:s}=this;s.forEach(t=>{"stop"===t.type&&r.push(t)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,i){var r=this;this.getHrefAttribute().hasValue()&&(r=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(r));var{stops:s}=r,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(i,s[s.length-1].color);if(s.forEach(t=>{a.addColorStop(t.offset,this.addParentOpacity(i,t.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:n}=this,{MAX_VIRTUAL_PIXELS:h,viewPort:o}=n.screen,[l]=o.viewPorts,u=new tf(n,null);u.attributes.x=new F(n,"x",-h/3),u.attributes.y=new F(n,"y",-h/3),u.attributes.width=new F(n,"width",h),u.attributes.height=new F(n,"height",h);var g=new tC(n,null);g.attributes.transform=new F(n,"transform",this.getAttribute("gradientTransform").getValue()),g.children=[u];var c=new ty(n,null);c.attributes.x=new F(n,"x",0),c.attributes.y=new F(n,"y",0),c.attributes.width=new F(n,"width",l.width),c.attributes.height=new F(n,"height",l.height),c.children=[g];var d=n.createCanvas(l.width,l.height),p=d.getContext("2d");return p.fillStyle=a,c.render(p),p.createPattern(d,"no-repeat")}return a}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new F(this.document,"color",e).addOpacity(t).getColor():e}}class tV extends tT{constructor(t,e,i){super(t,e,i),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var i="objectBoundingBox"===this.getGradientUnits(),r=i?e.getBoundingBox(t):null;if(i&&!r)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var s=i?r.x+r.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),a=i?r.y+r.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),n=i?r.x+r.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),h=i?r.y+r.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return s===n&&a===h?null:t.createLinearGradient(s,a,n,h)}}class tE extends tT{constructor(t,e,i){super(t,e,i),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var i="objectBoundingBox"===this.getGradientUnits(),r=e.getBoundingBox(t);if(i&&!r)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var s=i?r.x+r.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),a=i?r.y+r.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),n=s,h=a;this.getAttribute("fx").hasValue()&&(n=i?r.x+r.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(h=i?r.y+r.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var o=i?(r.width+r.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),l=this.getAttribute("fr").getPixels();return t.createRadialGradient(n,h,l,s,a,o)}}class tM extends tr{constructor(t,e,i){super(t,e,i),this.type="stop";var r=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),s=this.getStyle("stop-opacity"),a=this.getStyle("stop-color",!0);""===a.getString()&&a.setValue("#000"),s.hasValue()&&(a=a.addOpacity(s)),this.offset=r,this.color=a.getColor()}}class tk extends tr{constructor(t,e,i){super(t,e,i),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new F(t,"values",null);var r=this.getAttribute("values");r.hasValue()&&this.values.setValue(r.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:i,to:r}=this.getProgress(),s=i.getNumber()+(r.getNumber()-i.getNumber())*e;return"%"===t&&(s*=100),"".concat(s).concat(t)}update(t){var{parent:e}=this,i=this.getProperty();if(this.initialValue||(this.initialValue=i.getString(),this.initialUnits=i.getUnits()),this.duration>this.maxDuration){var r=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==r||this.frozen){if("remove"===r&&!this.removed)return this.removed=!0,i.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=i.getString();return!1}this.duration+=t;var s=!1;if(this.begin<this.duration){var a=this.calcValue(),n=this.getAttribute("type");if(n.hasValue()){var h=n.getString();a="".concat(h,"(").concat(a,")")}i.setValue(a),s=!0}return s}getProgress(){var{document:t,values:e}=this,i={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var r=i.progress*(e.getValue().length-1),s=Math.floor(r),a=Math.ceil(r);i.from=new F(t,"from",parseFloat(e.getValue()[s])),i.to=new F(t,"to",parseFloat(e.getValue()[a])),i.progress=(r-s)/(a-s)}else i.from=this.from,i.to=this.to;return i}}class tO extends tk{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:i}=this.getProgress(),r=new n(e.getColor()),s=new n(i.getColor());if(r.ok&&s.ok){var a=r.r+(s.r-r.r)*t,h=r.g+(s.g-r.g)*t,o=r.b+(s.b-r.b)*t;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(h),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class tN extends tk{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:i}=this.getProgress(),r=p(e.getString()),s=p(i.getString());return r.map((e,i)=>e+(s[i]-e)*t).join(" ")}}class tD extends tr{constructor(t,e,i){super(t,e,i),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:r}=t,{children:s}=this;for(var a of s)switch(a.type){case"font-face":this.fontFace=a;var n=a.getStyle("font-family");n.hasValue()&&(r[n.getString()]=this);break;case"missing-glyph":this.missingGlyph=a;break;case"glyph":a.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[a.unicode]&&(this.glyphs[a.unicode]=Object.create(null)),this.glyphs[a.unicode][a.arabicForm]=a):this.glyphs[a.unicode]=a}}render(){}}class tB extends tr{constructor(t,e,i){super(t,e,i),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class tz extends tu{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class tL extends tc{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class tR extends tc{constructor(t,e,i){super(t,e,i),this.type="a";var{childNodes:r}=e,s=r[0],a=r.length>0&&Array.from(r).every(t=>3===t.nodeType);this.hasText=a,this.text=a?this.getTextFromNode(s):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:i,y:r}=this,{mouse:s}=e.screen,a=new F(e,"fontSize",tn.parse(e.ctx.font).fontSize);s.isWorking()&&s.checkBoundingBox(this,new th(i,r-a.getPixels("y"),i+this.measureText(t),r))}else if(this.children.length>0){var n=new tC(this.document,null);n.children=this.children,n.parent=this,n.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function tI(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function tF(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?tI(Object(i),!0).forEach(function(e){(0,s.A)(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):tI(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}class tj extends tc{constructor(t,e,i){super(t,e,i),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var r=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(r)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(e=>{var{type:i,points:r}=e;switch(i){case to.LINE_TO:t&&t.lineTo(r[0],r[1]);break;case to.MOVE_TO:t&&t.moveTo(r[0],r[1]);break;case to.CURVE_TO:t&&t.bezierCurveTo(r[0],r[1],r[2],r[3],r[4],r[5]);break;case to.QUAD_TO:t&&t.quadraticCurveTo(r[0],r[1],r[2],r[3]);break;case to.ARC:var[s,a,n,h,o,l,u,g]=r,c=n>h?1:n/h,d=n>h?h/n:1;t&&(t.translate(s,a),t.rotate(u),t.scale(c,d),t.arc(0,0,n>h?n:h,o,o+l,!!(1-g)),t.scale(1/c,1/d),t.rotate(-u),t.translate(-s,-a));break;case to.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),i=this.getFontSize(),{glyphInfo:r}=this,s=t.fillStyle;"underline"===e&&t.beginPath(),r.forEach((r,s)=>{var{p0:a,p1:n,rotation:h,text:o}=r;t.save(),t.translate(a.x,a.y),t.rotate(h),t.fillStyle&&t.fillText(o,0,0),t.strokeStyle&&t.strokeText(o,0,0),t.restore(),"underline"===e&&(0===s&&t.moveTo(a.x,a.y+i/8),t.lineTo(n.x,n.y+i/5))}),"underline"===e&&(t.lineWidth=i/20,t.strokeStyle=s,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,i,r,s,a,n,h,o){var l=a,u=this.measureText(t,h);" "===h&&"justify"===e&&i<r&&(u+=(r-i)/s),o>-1&&(l+=this.getLetterSpacingAt(o));var g=this.textHeight/20,c=this.getEquidistantPointOnPath(l,g,0),d=this.getEquidistantPointOnPath(l+u,g,0),p={p0:c,p1:d},y=c&&d?Math.atan2(d.y-c.y,d.x-c.x):0;if(n){var f=Math.cos(Math.PI/2+y)*n,v=Math.cos(-y)*n;p.p0=tF(tF({},c),{},{x:c.x+f,y:c.y+v}),p.p1=tF(tF({},d),{},{x:d.x+f,y:d.y+v})}return{offset:l+=u,segment:p,rotation:y}}measureText(t,e){var{measuresCache:i}=this,r=e||this.getText();if(i.has(r))return i.get(r);var s=this.measureTargetText(t,r);return i.set(r,s),s}setTextData(t){if(!this.glyphInfo){var e=this.getText(),i=e.split(""),r=e.split(" ").length-1,s=this.parent.getAttribute("dx").split().map(t=>t.getPixels("x")),a=this.parent.getAttribute("dy").getPixels("y"),n=this.parent.getStyle("text-anchor").getString("start"),h=this.getStyle("letter-spacing"),o=this.parent.getStyle("letter-spacing"),l=0;h.hasValue()&&"inherit"!==h.getValue()?h.hasValue()&&"initial"!==h.getValue()&&"unset"!==h.getValue()&&(l=h.getPixels()):l=o.getPixels();var u=[],g=e.length;this.letterSpacingCache=u;for(var c=0;c<g;c++)u.push(void 0!==s[c]?s[c]:l);var d=u.reduce((t,e,i)=>0===i?0:t+e||0,0),p=this.measureText(t),y=Math.max(p+d,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var f=this.getPathLength(),v=this.getStyle("startOffset").getNumber(0)*f,m=0;("middle"===n||"center"===n)&&(m=-y/2),("end"===n||"right"===n)&&(m=-y),m+=v,i.forEach((e,s)=>{var{offset:h,segment:o,rotation:l}=this.findSegmentToFitChar(t,n,y,f,r,m,a,e,s);m=h,o.p0&&o.p1&&this.glyphInfo.push({text:i[s],p0:o.p0,p1:o.p1,rotation:l})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:i}=t;for(i.reset();!i.isEnd();){var{current:r}=i,s=r?r.x:0,a=r?r.y:0,n=i.next(),h=n.type,o=[];switch(n.type){case to.MOVE_TO:this.pathM(i,o);break;case to.LINE_TO:h=this.pathL(i,o);break;case to.HORIZ_LINE_TO:h=this.pathH(i,o);break;case to.VERT_LINE_TO:h=this.pathV(i,o);break;case to.CURVE_TO:this.pathC(i,o);break;case to.SMOOTH_CURVE_TO:h=this.pathS(i,o);break;case to.QUAD_TO:this.pathQ(i,o);break;case to.SMOOTH_QUAD_TO:h=this.pathT(i,o);break;case to.ARC:o=this.pathA(i);break;case to.CLOSE_PATH:tu.pathZ(i)}n.type!==to.CLOSE_PATH?e.push({type:h,points:o,start:{x:s,y:a},pathLength:this.calcLength(s,a,h,o)}):e.push({type:to.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:i,y:r}=tu.pathM(t).point;e.push(i,r)}pathL(t,e){var{x:i,y:r}=tu.pathL(t).point;return e.push(i,r),to.LINE_TO}pathH(t,e){var{x:i,y:r}=tu.pathH(t).point;return e.push(i,r),to.LINE_TO}pathV(t,e){var{x:i,y:r}=tu.pathV(t).point;return e.push(i,r),to.LINE_TO}pathC(t,e){var{point:i,controlPoint:r,currentPoint:s}=tu.pathC(t);e.push(i.x,i.y,r.x,r.y,s.x,s.y)}pathS(t,e){var{point:i,controlPoint:r,currentPoint:s}=tu.pathS(t);return e.push(i.x,i.y,r.x,r.y,s.x,s.y),to.CURVE_TO}pathQ(t,e){var{controlPoint:i,currentPoint:r}=tu.pathQ(t);e.push(i.x,i.y,r.x,r.y)}pathT(t,e){var{controlPoint:i,currentPoint:r}=tu.pathT(t);return e.push(i.x,i.y,r.x,r.y),to.QUAD_TO}pathA(t){var{rX:e,rY:i,sweepFlag:r,xAxisRotation:s,centp:a,a1:n,ad:h}=tu.pathA(t);return 0===r&&h>0&&(h-=2*Math.PI),1===r&&h<0&&(h+=2*Math.PI),[a.x,a.y,e,i,n,h,s,r]}calcLength(t,e,i,r){var s=0,a=null,n=null,h=0;switch(i){case to.LINE_TO:return this.getLineLength(t,e,r[0],r[1]);case to.CURVE_TO:for(h=.01,s=0,a=this.getPointOnCubicBezier(0,t,e,r[0],r[1],r[2],r[3],r[4],r[5]);h<=1;h+=.01)n=this.getPointOnCubicBezier(h,t,e,r[0],r[1],r[2],r[3],r[4],r[5]),s+=this.getLineLength(a.x,a.y,n.x,n.y),a=n;return s;case to.QUAD_TO:for(h=.01,s=0,a=this.getPointOnQuadraticBezier(0,t,e,r[0],r[1],r[2],r[3]);h<=1;h+=.01)n=this.getPointOnQuadraticBezier(h,t,e,r[0],r[1],r[2],r[3]),s+=this.getLineLength(a.x,a.y,n.x,n.y),a=n;return s;case to.ARC:s=0;var o=r[4],l=r[5],u=r[4]+l,g=Math.PI/180;if(Math.abs(o-u)<g&&(g=Math.abs(o-u)),a=this.getPointOnEllipticalArc(r[0],r[1],r[2],r[3],o,0),l<0)for(h=o-g;h>u;h-=g)n=this.getPointOnEllipticalArc(r[0],r[1],r[2],r[3],h,0),s+=this.getLineLength(a.x,a.y,n.x,n.y),a=n;else for(h=o+g;h<u;h+=g)n=this.getPointOnEllipticalArc(r[0],r[1],r[2],r[3],h,0),s+=this.getLineLength(a.x,a.y,n.x,n.y),a=n;return n=this.getPointOnEllipticalArc(r[0],r[1],r[2],r[3],u,0),s+=this.getLineLength(a.x,a.y,n.x,n.y)}return 0}getPointOnLine(t,e,i,r,s){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,n=arguments.length>6&&void 0!==arguments[6]?arguments[6]:i,h=(s-i)/(r-e+E),o=Math.sqrt(t*t/(1+h*h));r<e&&(o*=-1);var l=h*o,u=null;if(r===e)u={x:a,y:n+l};else if((n-i)/(a-e+E)===h)u={x:a+o,y:n+l};else{var g=0,c=0,d=this.getLineLength(e,i,r,s);if(d<E)return null;var p=(a-e)*(r-e)+(n-i)*(s-i);p/=d*d,g=e+p*(r-e),c=i+p*(s-i);var y=this.getLineLength(a,n,g,c),f=Math.sqrt(t*t-y*y);o=Math.sqrt(f*f/(1+h*h)),r<e&&(o*=-1),l=h*o,u={x:g+o,y:c+l}}return u}getPointOnPath(t){var e=this.getPathLength(),i=0,r=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:s}=this;for(var a of s){if(a&&(a.pathLength<5e-5||i+a.pathLength+5e-5<t)){i+=a.pathLength;continue}var n=t-i,h=0;switch(a.type){case to.LINE_TO:r=this.getPointOnLine(n,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case to.ARC:var o=a.points[4],l=a.points[5],u=a.points[4]+l;if(h=o+n/a.pathLength*l,l<0&&h<u||l>=0&&h>u)break;r=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],h,a.points[6]);break;case to.CURVE_TO:(h=n/a.pathLength)>1&&(h=1),r=this.getPointOnCubicBezier(h,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case to.QUAD_TO:(h=n/a.pathLength)>1&&(h=1),r=this.getPointOnQuadraticBezier(h,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3])}if(r)return r;break}return null}getLineLength(t,e,i,r){return Math.sqrt((i-t)*(i-t)+(r-e)*(r-e))}getPathLength(){return -1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,i,r,s,a,n,h,o){return{x:h*N(t)+a*D(t)+r*B(t)+e*z(t),y:o*N(t)+n*D(t)+s*B(t)+i*z(t)}}getPointOnQuadraticBezier(t,e,i,r,s,a,n){return{x:t*t*a+r*R(t)+e*I(t),y:t*t*n+s*R(t)+i*I(t)}}getPointOnEllipticalArc(t,e,i,r,s,a){var n=Math.cos(a),h=Math.sin(a),o={x:i*Math.cos(s),y:r*Math.sin(s)};return{x:t+(o.x*n-o.y*h),y:e+(o.x*h+o.y*n)}}buildEquidistantCache(t,e){var i=this.getPathLength(),r=e||.25,s=t||i/100;if(!this.equidistantCache||this.equidistantCache.step!==s||this.equidistantCache.precision!==r){this.equidistantCache={step:s,precision:r,points:[]};for(var a=0,n=0;n<=i;n+=r){var h=this.getPointOnPath(n),o=this.getPointOnPath(n+r);h&&o&&(a+=this.getLineLength(h.x,h.y,o.x,o.y))>=s&&(this.equidistantCache.points.push({x:h.x,y:h.y,distance:n}),a-=s)}}}getEquidistantPointOnPath(t,e,i){if(this.buildEquidistantCache(e,i),t<0||t-this.getPathLength()>5e-5)return null;var r=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[r]||null}}var t_=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class tX extends tl{constructor(t,e,i){super(t,e,i),this.type="image",this.loaded=!1;var r=this.getHrefAttribute().getString();if(!r)return;var s=r.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(r);t.images.push(this),s?this.loadSvg(r):this.loadImage(r),this.isSvg=s}loadImage(t){var e=this;return(0,r.A)(function*(){try{var i=yield e.document.createImage(t);e.image=i}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0})()}loadSvg(t){var e=this;return(0,r.A)(function*(){var i=t_.exec(t);if(i){var r=i[5];"base64"===i[4]?e.image=atob(r):e.image=decodeURIComponent(r)}else try{var s=yield e.document.fetch(t);e.image=yield s.text()}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0})()}renderChildren(t){var{document:e,image:i,loaded:r}=this,s=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),n=this.getStyle("width").getPixels("x"),h=this.getStyle("height").getPixels("y");if(r&&i&&n&&h){if(t.save(),t.translate(s,a),this.isSvg){var o=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:n,scaleHeight:h});o.document.documentElement.parent=this,o.render()}else{var l=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:n,desiredWidth:l.width,height:h,desiredHeight:l.height}),this.loaded&&(void 0===l.complete||l.complete)&&t.drawImage(l,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y");return new th(t,e,t+this.getStyle("width").getPixels("x"),e+this.getStyle("height").getPixels("y"))}}class tU extends tl{constructor(){super(...arguments),this.type="symbol"}render(t){}}class tW{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var i=this;return(0,r.A)(function*(){try{var{document:r}=i,s=(yield r.canvg.parser.load(e)).getElementsByTagName("font");Array.from(s).forEach(e=>{var i=r.createElement(e);r.definitions[t]=i})}catch(t){console.error('Error while loading font "'.concat(e,'":'),t)}i.loaded=!0})()}}class tH extends tr{constructor(t,e,i){super(t,e,i),this.type="style",g(Array.from(e.childNodes).map(t=>t.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(e=>{var i=e.trim();if(i){var r=i.split("{"),s=r[0].split(","),a=r[1].split(";");s.forEach(e=>{var i=e.trim();if(i){var r=t.styles[i]||{};if(a.forEach(e=>{var i=e.indexOf(":"),s=e.substr(0,i).trim(),a=e.substr(i+1,e.length-i).trim();s&&a&&(r[s]=new F(t,s,a))}),t.styles[i]=r,t.stylesSpecificity[i]=V(i),"@font-face"===i){var s=r["font-family"].getString().replace(/"|'/g,"");r.src.getString().split(",").forEach(e=>{if(e.indexOf('format("svg")')>0){var i=v(e);i&&new tW(t).load(s,i)}})}}})}})}}tH.parseExternalUrl=v;class tq extends tl{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),i=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),i.hasValue()&&t.translate(0,i.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:i}=this;if(i){var r=i;if("symbol"===i.type&&((r=new ty(e,null)).attributes.viewBox=new F(e,"viewBox",i.getAttribute("viewBox").getString()),r.attributes.preserveAspectRatio=new F(e,"preserveAspectRatio",i.getAttribute("preserveAspectRatio").getString()),r.attributes.overflow=new F(e,"overflow",i.getAttribute("overflow").getString()),r.children=i.children,i.styles.opacity=new F(e,"opacity",this.calculateOpacity())),"svg"===r.type){var s=this.getStyle("width",!1,!0),a=this.getStyle("height",!1,!0);s.hasValue()&&(r.attributes.width=new F(e,"width",s.getString())),a.hasValue()&&(r.attributes.height=new F(e,"height",a.getString()))}var n=r.parent;r.parent=this,r.render(t),r.parent=n}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return ti.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function tG(t,e,i,r){return e+Math.cos(t)*i+Math.sin(t)*r}class tQ extends tr{constructor(t,e,i){super(t,e,i),this.type="feColorMatrix";var r=p(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var s=r[0];r=[.213+.787*s,.715-.715*s,.072-.072*s,0,0,.213-.213*s,.715+.285*s,.072-.072*s,0,0,.213-.213*s,.715-.715*s,.072+.928*s,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var a=r[0]*Math.PI/180;r=[tG(a,.213,.787,-.213),tG(a,.715,-.715,-.715),tG(a,.072,-.072,.928),0,0,tG(a,.213,-.213,.143),tG(a,.715,.285,.14),tG(a,.072,-.072,-.283),0,0,tG(a,.213,-.213,-.787),tG(a,.715,-.715,.715),tG(a,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=r,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,i,r,s){for(var{includeOpacity:a,matrix:n}=this,h=t.getImageData(0,0,r,s),o=0;o<s;o++)for(var l=0;l<r;l++){var u,g,c,d,p,y,f,v,m,x,b,S,w,P,A,C,T,V,E,M,k=(u=h.data,u[o*r*4+4*l+0]),O=(g=h.data,g[o*r*4+4*l+1]),N=(c=h.data,c[o*r*4+4*l+2]),D=(d=h.data,d[o*r*4+4*l+3]),B=n[0]*k+n[1]*O+n[2]*N+n[3]*D+ +n[4],z=n[5]*k+n[6]*O+n[7]*N+n[8]*D+ +n[9],L=n[10]*k+n[11]*O+n[12]*N+n[13]*D+ +n[14],R=n[15]*k+n[16]*O+n[17]*N+n[18]*D+ +n[19];a&&(B=0,z=0,L=0,R*=D/255),p=h.data,y=l,f=o,v=B,p[f*r*4+4*y+0]=v,m=h.data,x=l,b=o,S=z,m[b*r*4+4*x+1]=S,w=h.data,P=l,A=o,C=L,w[A*r*4+4*P+2]=C,T=h.data,V=l,E=o,M=R,T[E*r*4+4*V+3]=M}t.clearRect(0,0,r,s),t.putImageData(h,0,0)}}class t$ extends tr{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:i}=this,r=this.getAttribute("x").getPixels("x"),s=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),n=this.getStyle("height").getPixels("y");if(!a&&!n){var h=new th;this.children.forEach(e=>{h.addBoundingBox(e.getBoundingBox(t))}),r=Math.floor(h.x1),s=Math.floor(h.y1),a=Math.floor(h.width),n=Math.floor(h.height)}var o=this.removeStyles(e,t$.ignoreStyles),l=i.createCanvas(r+a,s+n),u=l.getContext("2d");i.screen.setDefaults(u),this.renderChildren(u),new tQ(i,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(u,0,0,r+a,s+n);var g=i.createCanvas(r+a,s+n),c=g.getContext("2d");i.screen.setDefaults(c),e.render(c),c.globalCompositeOperation="destination-in",c.fillStyle=u.createPattern(l,"no-repeat"),c.fillRect(0,0,r+a,s+n),t.fillStyle=c.createPattern(g,"no-repeat"),t.fillRect(0,0,r+a,s+n),this.restoreStyles(e,o)}render(t){}}t$.ignoreStyles=["mask","transform","clip-path"];var tY=()=>{};class tZ extends tr{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,i=Reflect.getPrototypeOf(t),{beginPath:r,closePath:s}=t;i&&(i.beginPath=tY,i.closePath=tY),Reflect.apply(r,t,[]),this.children.forEach(r=>{if(void 0!==r.path){var a=void 0!==r.elementTransform?r.elementTransform():null;a||(a=ti.fromElement(e,r)),a&&a.apply(t),r.path(t),i&&(i.closePath=s),a&&a.unapply(t)}}),Reflect.apply(s,t,[]),t.clip(),i&&(i.beginPath=r,i.closePath=s)}render(t){}}class tJ extends tr{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:i,children:r}=this,s=e.getBoundingBox(t);if(s){var a=0,n=0;r.forEach(t=>{var e=t.extraFilterDistance||0;a=Math.max(a,e),n=Math.max(n,e)});var h=Math.floor(s.width),o=Math.floor(s.height),l=h+2*a,u=o+2*n;if(!(l<1)&&!(u<1)){var g=Math.floor(s.x),c=Math.floor(s.y),d=this.removeStyles(e,tJ.ignoreStyles),p=i.createCanvas(l,u),y=p.getContext("2d");i.screen.setDefaults(y),y.translate(-g+a,-c+n),e.render(y),r.forEach(t=>{"function"==typeof t.apply&&t.apply(y,0,0,l,u)}),t.drawImage(p,0,0,l,u,g-a,c-n,l,u),this.restoreStyles(e,d)}}}render(t){}}tJ.ignoreStyles=["filter","transform","clip-path"];class tK extends tr{constructor(t,e,i){super(t,e,i),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,i,r,s){}}class t0 extends tr{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,i,r,s){}}class t1 extends tr{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,i,r,s){}}class t2 extends tr{constructor(t,e,i){super(t,e,i),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,i,r,s){var{document:a,blurRadius:n}=this,h=a.window?a.window.document.body:null,l=t.canvas;l.id=a.getUniqueId(),h&&(l.style.display="none",h.appendChild(l)),(0,o.dD)(l,e,i,r,s,n),h&&h.removeChild(l)}}class t3 extends tr{constructor(){super(...arguments),this.type="title"}}class t5 extends tr{constructor(){super(...arguments),this.type="desc"}}function t4(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function t7(){return(t7=(0,r.A)(function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=document.createElement("img");return e&&(i.crossOrigin="Anonymous"),new Promise((e,r)=>{i.onload=()=>{e(i)},i.onerror=(t,e,i,s,a)=>{r(a)},i.src=t})})).apply(this,arguments)}class t8{constructor(t){var{rootEmSize:e=12,emSize:i=12,createCanvas:r=t8.createCanvas,createImage:s=t8.createImage,anonymousCrossOrigin:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=i,this.createCanvas=r,this.createImage=this.bindCreateImage(s,a),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(i,r)=>t(i,"boolean"==typeof r?r:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(t=>t.loaded)}isFontsLoaded(){return this.fonts.every(t=>t.loaded)}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),i=t8.elementTypes[e];return void 0!==i?new i(this,t):new ts(this,t)}createTextNode(t){return new tp(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?t4(Object(i),!0).forEach(function(e){(0,s.A)(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):t4(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}({document:this},t))}}function t9(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function t6(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?t9(Object(i),!0).forEach(function(e){(0,s.A)(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):t9(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}t8.createCanvas=function(t,e){var i=document.createElement("canvas");return i.width=t,i.height=e,i},t8.createImage=function(t){return t7.apply(this,arguments)},t8.elementTypes={svg:ty,rect:tf,circle:tv,ellipse:tm,line:tx,polyline:tb,polygon:tS,path:tu,pattern:tw,marker:tP,defs:tA,linearGradient:tV,radialGradient:tE,stop:tM,animate:tk,animateColor:tO,animateTransform:tN,font:tD,"font-face":tB,"missing-glyph":tz,glyph:tg,text:tc,tspan:td,tref:tL,a:tR,textPath:tj,image:tX,g:tC,symbol:tU,style:tH,use:tq,mask:t$,clipPath:tZ,filter:tJ,feDropShadow:tK,feMorphology:t0,feComposite:t1,feColorMatrix:tQ,feGaussianBlur:t2,title:t3,desc:t5};class et{constructor(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new Q(i),this.screen=new H(t,i),this.options=i;var r=new t8(this,i),s=r.createDocumentElement(e);this.document=r,this.documentElement=s}static from(t,e){var i=arguments;return(0,r.A)(function*(){var r=i.length>2&&void 0!==i[2]?i[2]:{},s=new Q(r);return new et(t,(yield s.parse(e)),r)})()}static fromString(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new et(t,new Q(i).parseFromString(e),i)}fork(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return et.from(t,e,t6(t6({},this.options),i))}forkString(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return et.fromString(t,e,t6(t6({},this.options),i))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return(0,r.A)(function*(){var i=t.length>0&&void 0!==t[0]?t[0]:{};e.start(t6({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},i)),yield e.ready(),e.stop()})()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:i,options:r}=this;i.start(e,t6(t6({enableRedraw:!0},r),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,i)}}}}]);