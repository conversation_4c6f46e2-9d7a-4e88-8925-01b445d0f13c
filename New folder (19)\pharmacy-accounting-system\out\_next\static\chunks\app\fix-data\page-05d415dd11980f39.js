(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1173],{67411:(e,s,c)=>{"use strict";c.r(s),c.d(s,{default:()=>m});var t=c(95155),l=c(12115),a=c(61932),n=c(10988),r=c(48313),i=c(85339),o=c(53904),d=c(40646);function m(){let[e,s]=(0,l.useState)(!1),[c,m]=(0,l.useState)(null),[x,h]=(0,l.useState)([]),u=e=>{h(s=>[...s,"".concat(new Date().toLocaleTimeString(),": ").concat(e)])},g=async()=>{s(!0),m(null),h([]),u("\uD83D\uDD27 بدء عملية إصلاح البيانات...");try{let e=console.log;console.log=function(){for(var s=arguments.length,c=Array(s),t=0;t<s;t++)c[t]=arguments[t];u(c.join(" ")),e(...c)};let s=(0,n.fixLocalStorageInvoiceItems)();console.log=e,m(s),s.success?u("✅ تم إصلاح ".concat(s.fixedCount," عنصر من أصل ").concat(s.totalCount)):u("❌ فشل في إصلاح البيانات: ".concat(s.error))}catch(e){u("❌ خطأ غير متوقع: ".concat(e)),m({success:!1,error:e})}finally{s(!1)}};return(0,t.jsx)(a.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,t.jsx)("div",{className:"bg-orange-50 p-3 rounded-lg",children:(0,t.jsx)(r.A,{className:"h-6 w-6 text-orange-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"إصلاح بيانات الفواتير"}),(0,t.jsx)("p",{className:"text-gray-600",children:"إصلاح أسماء الأدوية المفقودة في الفواتير المحفوظة"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,t.jsxs)("button",{onClick:()=>{u("\uD83D\uDD0D فحص البيانات الحالية...");try{let e=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),s=JSON.parse(localStorage.getItem("medicines")||"[]"),c=JSON.parse(localStorage.getItem("medicine_batches")||"[]");u("\uD83D\uDCE6 عدد عناصر الفواتير: ".concat(e.length)),u("\uD83D\uDC8A عدد الأدوية: ".concat(s.length)),u("\uD83D\uDCCB عدد الدفعات: ".concat(c.length));let t=0,l=0;e.forEach((e,s)=>{var c,a;(null==(a=e.medicine_batches)||null==(c=a.medicines)?void 0:c.name)&&"غير محدد"!==e.medicine_batches.medicines.name?(t++,u("✅ العنصر ".concat(s+1,": ").concat(e.medicine_batches.medicines.name))):(l++,u("❌ العنصر ".concat(s+1,": اسم الدواء مفقود (").concat(e.medicine_name||"غير محدد",")")))}),u("\uD83D\uDCCA الملخص: ".concat(t," عنصر بأسماء صحيحة، ").concat(l," عنصر بأسماء مفقودة"))}catch(e){u("❌ خطأ في فحص البيانات: ".concat(e))}},className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2 justify-center",children:[(0,t.jsx)(i.A,{className:"h-5 w-5"}),"فحص البيانات الحالية"]}),(0,t.jsxs)("button",{onClick:g,disabled:e,className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 justify-center",children:[e?(0,t.jsx)(o.A,{className:"h-5 w-5 animate-spin"}):(0,t.jsx)(d.A,{className:"h-5 w-5"}),e?"جاري الإصلاح...":"إصلاح البيانات"]})]}),c&&(0,t.jsxs)("div",{className:"p-4 rounded-lg mb-6 ".concat(c.success?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"),children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[c.success?(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-600"}):(0,t.jsx)(i.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsx)("span",{className:"font-medium ".concat(c.success?"text-green-800":"text-red-800"),children:c.success?"تم إصلاح ".concat(c.fixedCount," عنصر من أصل ").concat(c.totalCount," بنجاح!"):"فشل في إصلاح البيانات"})]}),c.success&&c.notFoundCount>0&&(0,t.jsxs)("div",{className:"mt-2 text-sm text-yellow-700",children:["تحذير: لم يتم العثور على أسماء الأدوية لـ ",c.notFoundCount," عنصر"]})]}),x.length>0&&(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"سجل العمليات:"}),(0,t.jsx)("div",{className:"bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto",children:x.map((e,s)=>(0,t.jsx)("div",{className:"mb-1",children:e},s))})]})]}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(i.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"ملاحظات مهمة:"}),(0,t.jsxs)("ul",{className:"text-yellow-700 space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"• هذه الأداة تصلح أسماء الأدوية المفقودة في الفواتير المحفوظة محلياً"}),(0,t.jsx)("li",{children:"• يُنصح بعمل نسخة احتياطية من البيانات قبل تشغيل الإصلاح"}),(0,t.jsx)("li",{children:"• العملية آمنة ولا تحذف أي بيانات موجودة"}),(0,t.jsx)("li",{children:"• بعد الإصلاح، ستظهر أسماء الأدوية بشكل صحيح في الطباعة"})]})]})]})})]})})}},69511:(e,s,c)=>{Promise.resolve().then(c.bind(c,67411))},85339:(e,s,c)=>{"use strict";c.d(s,{A:()=>t});let t=(0,c(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{e.O(0,[6874,6543,5647,8080,1932,988,8441,5964,7358],()=>e(e.s=69511)),_N_E=e.O()}]);