@import "tailwindcss";

/* استيراد خطوط جوجل العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

:root {
    --background: #ffffff;
    --foreground: #171717;
    --primary: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary: #10b981;
    --accent: #f59e0b;
    --danger: #ef4444;
    --warning: #f59e0b;
    --success: #10b981;
    --info: #3b82f6;
    --light: #f8fafc;
    --dark: #1e293b;
    --border-radius: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* تحسين الخطوط */
body {
    font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* Custom Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
    }
}

/* Animation Classes */
.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInDown {
    animation: fadeInDown 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-scaleIn {
    animation: scaleIn 0.4s ease-out;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-shimmer {
    animation: shimmer 2s infinite;
    background: linear-gradient(to right, #eff6ff 4%, #dbeafe 25%, #eff6ff 36%);
    background-size: 1000px 100%;
}

.animate-blob {
    animation: blob 7s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

.animation-delay-150 {
    animation-delay: 150ms;
}

.animation-delay-300 {
    animation-delay: 300ms;
}

.animation-delay-500 {
    animation-delay: 500ms;
}

.animation-delay-700 {
    animation-delay: 700ms;
}

.animation-delay-1000 {
    animation-delay: 1s;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}


/* Background Patterns */
.bg-grid-pattern {
    background-image:
        linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
}

.bg-dots-pattern {
    background-image: radial-gradient(circle, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 15px 15px;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* Glassmorphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

.glass-primary {
    background: rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 8px 32px 0 rgba(59, 130, 246, 0.1);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 10px;
    border: 2px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

::-webkit-scrollbar-corner {
    background: #f1f5f9;
}

/* Enhanced Hover Effects */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.hover-scale {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    transform: translateY(-2px);
}

.hover-slide {
    position: relative;
    overflow: hidden;
}

.hover-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.hover-slide:hover::before {
    left: 100%;
}

/* Enhanced Text Colors */
.text-primary {
    color: var(--primary) !important;
}

.text-secondary {
    color: var(--secondary) !important;
}

.text-accent {
    color: var(--accent) !important;
}

.text-dark {
    color: #1a1a1a !important;
}

.text-darker {
    color: #0f0f0f !important;
}

.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}


/* Form Input Enhancements */

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="number"],
textarea,
select {
    color: #1a1a1a !important;
    font-weight: 500 !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    color: #0f0f0f !important;
    font-weight: 600 !important;
}


/* Placeholder Text */

input::placeholder,
textarea::placeholder {
    color: #6b7280 !important;
    font-weight: 400 !important;
}


/* Label Text */

label {
    color: #1f2937 !important;
    font-weight: 600 !important;
}


/* Table Text */

table th {
    color: #1f2937 !important;
    font-weight: 700 !important;
}

table td {
    color: #1a1a1a !important;
    font-weight: 500 !important;
}


/* Button Text */

button {
    font-weight: 600 !important;
}


/* Mobile Responsive Enhancements */

@media (max-width: 768px) {
    /* Mobile Typography */
    h1 {
        font-size: 1.875rem !important;
        line-height: 2.25rem !important;
    }
    h2 {
        font-size: 1.5rem !important;
        line-height: 2rem !important;
    }
    h3 {
        font-size: 1.25rem !important;
        line-height: 1.75rem !important;
    }
    /* Mobile Form Inputs */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    input[type="number"],
    textarea,
    select {
        font-size: 16px !important;
        /* Prevents zoom on iOS */
        padding: 12px !important;
        min-height: 44px !important;
        /* Touch target size */
    }
    /* Mobile Buttons */
    button {
        min-height: 44px !important;
        padding: 12px 16px !important;
        font-size: 16px !important;
    }
    /* Mobile Tables */
    table {
        font-size: 14px !important;
    }
    /* Mobile Cards */
    .card {
        padding: 16px !important;
        margin: 8px !important;
    }
    /* Mobile Modals */
    .modal {
        margin: 16px !important;
        max-height: calc(100vh - 32px) !important;
    }
}

@media (max-width: 640px) {
    /* Small Mobile Adjustments */
    .container {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }
    /* Smaller text for very small screens */
    table {
        font-size: 12px !important;
    }
    /* Stack elements vertically */
    .flex-mobile-stack {
        flex-direction: column !important;
        gap: 8px !important;
    }
}


/* Touch-friendly enhancements */

@media (hover: none) and (pointer: coarse) {
    /* Touch device specific styles */
    button:hover {
        transform: none !important;
    }
    .hover-lift:hover {
        transform: none !important;
    }
    /* Larger touch targets */
    a,
    button,
    input,
    select,
    textarea {
        min-height: 44px !important;
    }
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
     :root {
        --background: #0a0a0a;
        --foreground: #ededed;
    }
}

body {
    background: var(--background);
    color: var(--foreground);
    font-family: 'Cairo', 'Tajawal', Arial, Helvetica, sans-serif;
    overflow-x: hidden;
    /* منع التمرير الأفقي */
}

/* Enhanced Form Input Styles */
.input-modern {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 12px 16px;
    font-size: 14px;
    background: white;
    position: relative;
}

.input-modern:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.input-modern:hover {
    border-color: #cbd5e1;
}

.input-floating {
    position: relative;
}

.input-floating label {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.3s ease;
    pointer-events: none;
    color: #64748b;
    font-size: 14px;
}

.input-floating input:focus + label,
.input-floating input:not(:placeholder-shown) + label {
    top: -8px;
    font-size: 12px;
    color: var(--primary);
    background: white;
    padding: 0 8px;
}

/* Enhanced Button Styles */
.btn-modern {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 14px;
}

.btn-modern.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-modern.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
}

.btn-modern.btn-secondary {
    background: linear-gradient(135deg, var(--secondary) 0%, #059669 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-modern.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-ghost {
    background: transparent;
    color: var(--primary);
    border: none;
}

.btn-ghost:hover {
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

/* Enhanced Card Styles */
.card-enhanced {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
}

.card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.card-glass {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Enhanced Loading Styles */
.loading-enhanced {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-pulse 1.5s infinite;
}

.loading-dots-enhanced {
    display: inline-flex;
    gap: 4px;
}

.loading-dots-enhanced div {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary);
    animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots-enhanced div:nth-child(1) { animation-delay: -0.32s; }
.loading-dots-enhanced div:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
    0%, 80%, 100% {
        transform: scale(0);
    } 40% {
        transform: scale(1);
    }
}


/* Mobile-specific improvements */

@media (max-width: 768px) {
    /* Better mobile table handling */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
    /* Mobile-friendly cards */
    .mobile-card {
        margin: 8px !important;
        padding: 16px !important;
        border-radius: 12px !important;
    }
    /* Mobile content spacing */
    .mobile-content {
        padding: 12px !important;
    }
    /* Stack buttons vertically on mobile */
    .button-group-mobile {
        flex-direction: column !important;
        gap: 8px !important;
    }
    /* Mobile modal adjustments */
    .mobile-modal {
        margin: 8px !important;
        max-height: calc(100vh - 16px) !important;
    }
}


/* Very small screens */

@media (max-width: 480px) {
    .mobile-content {
        padding: 8px !important;
    }
    /* Smaller text for very small screens */
    h1 {
        font-size: 1.5rem !important;
    }
    h2 {
        font-size: 1.25rem !important;
    }
}

/* Enhanced Responsive Grid System */
.grid-responsive-1 { @apply grid-cols-1; }
.grid-responsive-2 { @apply grid-cols-1 md:grid-cols-2; }
.grid-responsive-3 { @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3; }
.grid-responsive-4 { @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-4; }

/* Enhanced Responsive Spacing */
.space-responsive { @apply space-y-4 md:space-y-6 lg:space-y-8; }
.gap-responsive { @apply gap-4 md:gap-6 lg:gap-8; }
.p-responsive { @apply p-4 md:p-6 lg:p-8; }
.m-responsive { @apply m-4 md:m-6 lg:m-8; }

/* Enhanced Responsive Text */
.text-responsive-sm { @apply text-sm md:text-base lg:text-lg; }
.text-responsive-base { @apply text-base md:text-lg lg:text-xl; }
.text-responsive-lg { @apply text-lg md:text-xl lg:text-2xl; }
.text-responsive-xl { @apply text-xl md:text-2xl lg:text-3xl; }

/* Enhanced Animation Delays */
.animation-delay-100 { animation-delay: 100ms; }
.animation-delay-150 { animation-delay: 150ms; }
.animation-delay-200 { animation-delay: 200ms; }
.animation-delay-300 { animation-delay: 300ms; }
.animation-delay-500 { animation-delay: 500ms; }

/* Enhanced Background Patterns */
.bg-dots-pattern {
    background-image: radial-gradient(circle, #e2e8f0 1px, transparent 1px);
    background-size: 20px 20px;
}

.bg-grid-pattern {
    background-image: linear-gradient(rgba(255,255,255,.1) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(255,255,255,.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Enhanced Border Gradients */
.border-gradient {
    border: 1px solid;
    border-image: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4) 1;
}