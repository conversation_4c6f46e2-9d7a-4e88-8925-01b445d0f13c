<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .button:hover {
            background: #2563eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام الصيدلية</h1>
        
        <div>
            <button class="button" onclick="clearLocalStorage()">مسح localStorage</button>
            <button class="button" onclick="initializeData()">تهيئة البيانات</button>
            <button class="button" onclick="testCreateInvoice()">إنشاء فاتورة تجريبية</button>
            <button class="button" onclick="checkData()">فحص البيانات</button>
            <button class="button" onclick="clearLog()">مسح السجل</button>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function clearLocalStorage() {
            const keys = ['sales_invoices', 'sales_invoice_items', 'medicines', 'medicine_batches', 'customers'];
            keys.forEach(key => localStorage.removeItem(key));
            log('تم مسح جميع البيانات من localStorage', 'success');
        }

        function initializeData() {
            log('بدء تهيئة البيانات...');
            
            // Create sample medicines
            const medicines = [
                {
                    id: 'med_1',
                    name: 'باراسيتامول 500 مجم',
                    category: 'مسكنات',
                    manufacturer: 'شركة الأدوية العراقية',
                    strength: '500mg',
                    form: 'أقراص',
                    created_at: new Date().toISOString()
                },
                {
                    id: 'med_2',
                    name: 'أموكسيسيلين 250 مجم',
                    category: 'مضادات حيوية',
                    manufacturer: 'شركة بغداد للأدوية',
                    strength: '250mg',
                    form: 'كبسولات',
                    created_at: new Date().toISOString()
                }
            ];

            const batches = [
                {
                    id: 'batch_1',
                    medicine_id: 'med_1',
                    batch_code: 'PAR001',
                    expiry_date: '2025-12-31',
                    quantity: 100,
                    cost_price: 500,
                    selling_price: 750,
                    received_date: '2024-01-01',
                    created_at: new Date().toISOString()
                },
                {
                    id: 'batch_2',
                    medicine_id: 'med_2',
                    batch_code: 'AMX001',
                    expiry_date: '2025-06-30',
                    quantity: 50,
                    cost_price: 1000,
                    selling_price: 1500,
                    received_date: '2024-01-01',
                    created_at: new Date().toISOString()
                }
            ];

            const customers = [
                {
                    id: 'cust_1',
                    name: 'أحمد محمد علي',
                    phone: '07701234567',
                    address: 'بغداد - الكرادة',
                    created_at: new Date().toISOString()
                }
            ];

            localStorage.setItem('medicines', JSON.stringify(medicines));
            localStorage.setItem('medicine_batches', JSON.stringify(batches));
            localStorage.setItem('customers', JSON.stringify(customers));
            localStorage.setItem('sales_invoices', JSON.stringify([]));
            localStorage.setItem('sales_invoice_items', JSON.stringify([]));

            log(`تم إنشاء ${medicines.length} دواء`, 'success');
            log(`تم إنشاء ${batches.length} دفعة`, 'success');
            log(`تم إنشاء ${customers.length} عميل`, 'success');
        }

        function testCreateInvoice() {
            log('بدء إنشاء فاتورة تجريبية...');
            
            const invoiceData = {
                id: `invoice_${Date.now()}`,
                invoice_number: `TEST-${Date.now()}`,
                customer_name: 'عميل تجريبي',
                total_amount: 1500,
                discount_amount: 0,
                final_amount: 1500,
                payment_method: 'cash',
                payment_status: 'paid',
                notes: 'فاتورة اختبار',
                created_at: new Date().toISOString()
            };

            const invoiceItems = [
                {
                    id: `item_${Date.now()}`,
                    invoice_id: invoiceData.id,
                    medicine_batch_id: 'batch_1',
                    quantity: 2,
                    unit_price: 750,
                    total_price: 1500,
                    is_gift: false,
                    medicine_name: 'باراسيتامول 500 مجم',
                    created_at: new Date().toISOString()
                }
            ];

            // Save to localStorage
            const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            
            existingInvoices.push(invoiceData);
            existingItems.push(...invoiceItems);
            
            localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices));
            localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems));

            log(`تم إنشاء الفاتورة: ${invoiceData.invoice_number}`, 'success');
            log(`تم إضافة ${invoiceItems.length} عنصر للفاتورة`, 'success');
        }

        function checkData() {
            log('فحص البيانات المحفوظة...');
            
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            const invoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const items = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');

            log(`الأدوية: ${medicines.length}`, 'info');
            log(`الدفعات: ${batches.length}`, 'info');
            log(`العملاء: ${customers.length}`, 'info');
            log(`الفواتير: ${invoices.length}`, 'info');
            log(`عناصر الفواتير: ${items.length}`, 'info');

            if (invoices.length > 0) {
                log('تفاصيل آخر فاتورة:', 'info');
                const lastInvoice = invoices[invoices.length - 1];
                log(`رقم الفاتورة: ${lastInvoice.invoice_number}`, 'info');
                log(`اسم العميل: ${lastInvoice.customer_name}`, 'info');
                log(`المبلغ النهائي: ${lastInvoice.final_amount}`, 'info');
                
                const invoiceItems = items.filter(item => item.invoice_id === lastInvoice.id);
                log(`عدد العناصر: ${invoiceItems.length}`, 'info');
                
                invoiceItems.forEach((item, index) => {
                    log(`العنصر ${index + 1}: ${item.medicine_name || 'غير محدد'} - الكمية: ${item.quantity}`, 'info');
                });
            }
        }

        // Initialize on page load
        log('تم تحميل صفحة الاختبار', 'success');
    </script>
</body>
</html>
