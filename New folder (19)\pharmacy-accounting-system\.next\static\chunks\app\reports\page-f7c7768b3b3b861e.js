(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5022],{54954:(e,t,a)=>{Promise.resolve().then(a.bind(a,79680))},79680:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>O});var n=a(95155),l=a(12115),s=a(61932),r=a(27809),i=a(37108),o=a(55868),c=a(54213),d=a(17580),m=a(23227),u=a(33109),g=a(39785),x=a(16785),h=a(79397),p=a(72713),y=a(66932),b=a(53904),f=a(381),v=a(54416),j=a(92657),w=a(81304),_=a(64261),N=a(91788),S=a(57434),A=a(69074),L=a(40646),D=a(14186),C=a(94498),R=a(1243),E=a(68500),I=a(54861),k=a(40133),M=a(84355),P=a(10988);let T=[{id:"sales_summary",title:"تقرير المبيعات الشامل",description:"تقرير مفصل عن جميع عمليات المبيعات مع التحليلات والرسوم البيانية",icon:r.A,category:"sales",color:"bg-blue-500"},{id:"purchases_summary",title:"تقرير المشتريات الشامل",description:"تقرير مفصل عن جميع عمليات المشتريات مع تحليل الموردين",icon:i.A,category:"purchases",color:"bg-green-500"},{id:"financial_summary",title:"التقرير المالي الشامل",description:"ملخص شامل للوضع المالي مع الأرباح والخسائر",icon:o.A,category:"financial",color:"bg-purple-500"},{id:"inventory_report",title:"تقرير المخزون",description:"حالة المخزون والكميات المتاحة مع تحليل الحركة",icon:c.A,category:"inventory",color:"bg-orange-500"},{id:"customer_statement",title:"كشف حساب العملاء",description:"تفاصيل حسابات العملاء والمديونيات مع الجدول الزمني",icon:d.A,category:"customers",color:"bg-indigo-500"},{id:"supplier_statement",title:"كشف حساب الموردين",description:"تفاصيل حسابات الموردين والمستحقات مع تحليل الأداء",icon:m.A,category:"suppliers",color:"bg-teal-500"},{id:"profit_loss",title:"تقرير الأرباح والخسائر",description:"تحليل مفصل للأرباح والخسائر مع المقارنات الزمنية",icon:u.A,category:"financial",color:"bg-emerald-500"},{id:"cash_flow",title:"تقرير التدفق النقدي",description:"حركة النقد الداخل والخارج مع التوقعات المستقبلية",icon:g.A,category:"financial",color:"bg-cyan-500"},{id:"top_products",title:"أفضل المنتجات مبيعاً",description:"تحليل أداء المنتجات والأدوية الأكثر ربحية",icon:x.A,category:"analytics",color:"bg-rose-500"},{id:"customer_analysis",title:"تحليل العملاء",description:"تحليل سلوك العملاء وأنماط الشراء مع التوصيات",icon:h.A,category:"analytics",color:"bg-violet-500"}],F=[{id:"today",label:"اليوم",days:0},{id:"week",label:"هذا الأسبوع",days:7},{id:"month",label:"هذا الشهر",days:30},{id:"quarter",label:"هذا الربع",days:90},{id:"year",label:"هذا العام",days:365},{id:"custom",label:"فترة مخصصة",days:-1}];function O(){let[e,t]=(0,l.useState)(null),[r,c]=(0,l.useState)(null),[d,m]=(0,l.useState)(!1),[g,h]=(0,l.useState)(!1),[O,G]=(0,l.useState)(!1),[q,z]=(0,l.useState)([]),[B,Q]=(0,l.useState)([]),[W,U]=(0,l.useState)([]),[V,H]=(0,l.useState)({dateRange:{start:new Date(Date.now()-2592e6).toISOString().split("T")[0],end:new Date().toISOString().split("T")[0],preset:"month"},paymentStatus:"all",paymentMethod:"all",includeReturns:!0});(0,l.useEffect)(()=>{Y()},[]);let Y=async()=>{try{let[e,t,a]=await Promise.all([(0,P.getCustomers)(),(0,P.getSuppliers)(),(0,P.getMedicines)()]);z(e.data||[]),Q(t.data||[]),U(a.data||[])}catch(e){console.error("Error loading basic data:",e)}},J=async e=>{m(!0),t(e);try{let t=await K(e);c(t),G(!0)}catch(e){console.error("Error generating report:",e),alert("حدث خطأ أثناء توليد التقرير")}finally{m(!1)}},K=async e=>{switch(e){case"sales_summary":return await X();case"purchases_summary":return await Z();case"financial_summary":return await $();case"inventory_report":return await ee();case"customer_statement":return await et();case"supplier_statement":return await ea();case"profit_loss":return await en();case"cash_flow":return await el();case"top_products":return await es();case"customer_analysis":return await er();default:var t;return{title:(null==(t=T.find(t=>t.id===e))?void 0:t.title)||"تقرير",summary:{totalRecords:0,totalAmount:0,averageAmount:0},data:[]}}},X=async()=>{let e=((await (0,P.getSalesInvoices)()).data||[]).filter(e=>{let t=new Date(e.created_at),a=new Date(V.dateRange.start),n=new Date(V.dateRange.end+"T23:59:59");return!(t<a)&&!(t>n)&&(!V.paymentStatus||"all"===V.paymentStatus||e.payment_status===V.paymentStatus)&&(!V.paymentMethod||"all"===V.paymentMethod||e.payment_method===V.paymentMethod)&&(!V.customer||e.customer_id===V.customer)}),t=e.reduce((e,t)=>e+(t.final_amount||0),0),a=e.length>0?t/e.length:0,n=e.reduce((e,t)=>e+(t.paid_amount||0),0),l=e.map(e=>{let t=e.sales_invoice_items||[],a=t.length>0?t.map(e=>{var t,a,n,l;let s=(null==(a=e.medicine_batches)||null==(t=a.medicines)?void 0:t.name)||e.medicine_name||"غير محدد",r=(null==(n=e.medicine_batches)?void 0:n.batch_code)||e.batch_code||"غير محدد",i=(null==(l=e.medicine_batches)?void 0:l.expiry_date)||e.expiry_date||"غير محدد",o=e.unit_price||0,c=e.quantity||0,d=o*c;return{name:s,batchCode:r,expiryDate:"غير محدد"!==i?new Date(i).toLocaleDateString("ar-EG"):"غير محدد",quantity:c,unitPrice:o,totalPrice:d,text:"".concat(s," - الكمية: ").concat(c," - السعر: ").concat(o.toLocaleString()," د.ع - المجموع: ").concat(d.toLocaleString()," د.ع - الدفعة: ").concat(r," - الانتهاء: ").concat("غير محدد"!==i?new Date(i).toLocaleDateString("ar-EG"):"غير محدد")}}):[],n=a.length>0?a.map(e=>e.text).join(" | "):"لا توجد مواد",l=t.length,s=a.reduce((e,t)=>e+t.quantity,0);return{...e,itemsText:n,itemsDetails:a,itemsCount:l,totalItemsQuantity:s}});return{title:"تقرير المبيعات الشامل",summary:{totalRecords:e.length,totalAmount:t,averageAmount:a,paidAmount:n,pendingAmount:t-n,totalItemsSold:l.reduce((e,t)=>e+t.totalItemsQuantity,0),totalInvoiceItems:l.reduce((e,t)=>e+t.itemsCount,0)},data:l.map(e=>({"رقم الفاتورة":e.invoice_number,العميل:e.customer_name||"عميل نقدي","التاريخ والوقت":new Date(e.created_at).toLocaleString("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),"عدد الأصناف":e.itemsCount,"إجمالي الكمية":e.totalItemsQuantity,"المبلغ الإجمالي":(e.final_amount||0).toLocaleString()+" د.ع","المبلغ المدفوع":(e.paid_amount||0).toLocaleString()+" د.ع","المبلغ المتبقي":((e.final_amount||0)-(e.paid_amount||0)).toLocaleString()+" د.ع","حالة الدفع":"paid"===e.payment_status?"مدفوع":"partial"===e.payment_status?"جزئي":"معلق","طريقة الدفع":"cash"===e.payment_method?"نقداً":"آجل","تفاصيل المواد":e.itemsText,ملاحظات:e.notes||"لا توجد ملاحظات"})),charts:[{type:"pie",data:[{name:"مدفوع",value:e.filter(e=>"paid"===e.payment_status).length},{name:"جزئي",value:e.filter(e=>"partial"===e.payment_status).length},{name:"معلق",value:e.filter(e=>"pending"===e.payment_status).length}],labels:["مدفوع","جزئي","معلق"]}]}},Z=async()=>{let e=((await (0,P.getPurchaseInvoices)()).data||[]).filter(e=>{let t=new Date(e.created_at),a=new Date(V.dateRange.start),n=new Date(V.dateRange.end+"T23:59:59");return!(t<a)&&!(t>n)&&(!V.paymentStatus||"all"===V.paymentStatus||e.payment_status===V.paymentStatus)&&(!V.supplier||e.supplier_id===V.supplier)}),t=e.reduce((e,t)=>e+(t.final_amount||0),0),a=e.length>0?t/e.length:0,n=e.reduce((e,t)=>e+(t.paid_amount||0),0),l=e.map(e=>{let t=e.purchase_invoice_items||[],a=t.length>0?t.map(e=>{var t;let a=(null==(t=e.medicines)?void 0:t.name)||e.medicine_name||"غير محدد",n=e.batch_code||"غير محدد",l=e.expiry_date||"غير محدد",s=e.unit_cost||0,r=e.selling_price||0,i=e.quantity||0,o=s*i,c=(r-s)*i;return{name:a,batchCode:n,expiryDate:"غير محدد"!==l?new Date(l).toLocaleDateString("ar-EG"):"غير محدد",quantity:i,unitCost:s,sellingPrice:r,totalCost:o,expectedProfit:c,text:"".concat(a," - الكمية: ").concat(i," - سعر الشراء: ").concat(s.toLocaleString()," د.ع - سعر البيع: ").concat(r.toLocaleString()," د.ع - التكلفة: ").concat(o.toLocaleString()," د.ع - الربح المتوقع: ").concat(c.toLocaleString()," د.ع - الدفعة: ").concat(n," - الانتهاء: ").concat("غير محدد"!==l?new Date(l).toLocaleDateString("ar-EG"):"غير محدد")}}):[],n=a.length>0?a.map(e=>e.text).join(" | "):"لا توجد مواد",l=t.length,s=a.reduce((e,t)=>e+t.quantity,0),r=a.reduce((e,t)=>e+t.expectedProfit,0);return{...e,itemsText:n,itemsDetails:a,itemsCount:l,totalItemsQuantity:s,totalExpectedProfit:r}});return{title:"تقرير المشتريات الشامل",summary:{totalRecords:e.length,totalAmount:t,averageAmount:a,paidAmount:n,pendingAmount:t-n,totalItemsPurchased:l.reduce((e,t)=>e+t.totalItemsQuantity,0),totalInvoiceItems:l.reduce((e,t)=>e+t.itemsCount,0),totalExpectedProfit:l.reduce((e,t)=>e+t.totalExpectedProfit,0)},data:l.map(e=>{var t;return{"رقم الفاتورة":e.invoice_number,المورد:(null==(t=e.suppliers)?void 0:t.name)||e.supplier_name||"غير محدد","التاريخ والوقت":new Date(e.created_at).toLocaleString("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),"عدد الأصناف":e.itemsCount,"إجمالي الكمية":e.totalItemsQuantity,"المبلغ الإجمالي":(e.final_amount||0).toLocaleString()+" د.ع","المبلغ المدفوع":(e.paid_amount||0).toLocaleString()+" د.ع","المبلغ المتبقي":((e.final_amount||0)-(e.paid_amount||0)).toLocaleString()+" د.ع","الربح المتوقع":e.totalExpectedProfit.toLocaleString()+" د.ع","حالة الدفع":"paid"===e.payment_status?"مدفوع":"partial"===e.payment_status?"جزئي":"معلق","تفاصيل المواد":e.itemsText,ملاحظات:e.notes||"لا توجد ملاحظات"}}),charts:[{type:"pie",data:[{name:"مدفوع",value:e.filter(e=>"paid"===e.payment_status).length},{name:"جزئي",value:e.filter(e=>"partial"===e.payment_status).length},{name:"معلق",value:e.filter(e=>"pending"===e.payment_status).length}],labels:["مدفوع","جزئي","معلق"]}]}},$=async()=>{let[e,t,a]=await Promise.all([(0,P.getSalesInvoices)(),(0,P.getPurchaseInvoices)(),(0,P.getCashTransactions)()]),n=e.data||[],l=t.data||[],s=a.data||[],r=n.filter(e=>{let t=new Date(e.created_at),a=new Date(V.dateRange.start),n=new Date(V.dateRange.end+"T23:59:59");return t>=a&&t<=n}),i=l.filter(e=>{let t=new Date(e.created_at),a=new Date(V.dateRange.start),n=new Date(V.dateRange.end+"T23:59:59");return t>=a&&t<=n}),o=s.filter(e=>{let t=new Date(e.created_at),a=new Date(V.dateRange.start),n=new Date(V.dateRange.end+"T23:59:59");return t>=a&&t<=n}),c=r.reduce((e,t)=>e+(t.final_amount||0),0),d=i.reduce((e,t)=>e+(t.final_amount||0),0),m=c-d,u=c>0?m/c*100:0,g=o.filter(e=>"income"===e.transaction_type).reduce((e,t)=>e+t.amount,0),x=o.filter(e=>"expense"===e.transaction_type).reduce((e,t)=>e+t.amount,0),h=g-x;return{title:"التقرير المالي الشامل",summary:{totalRecords:r.length+i.length,totalAmount:c,averageAmount:c/(r.length||1),totalRevenue:c,totalExpenses:d,netProfit:m,profitMargin:u,cashIncome:g,cashExpenses:x,netCashFlow:h},data:[{البند:"إجمالي الإيرادات",المبلغ:c.toLocaleString()+" د.ع",النوع:"إيرادات"},{البند:"إجمالي المصروفات",المبلغ:d.toLocaleString()+" د.ع",النوع:"مصروفات"},{البند:"صافي الربح",المبلغ:m.toLocaleString()+" د.ع",النوع:"ربح"},{البند:"هامش الربح",المبلغ:u.toFixed(2)+"%",النوع:"نسبة"},{البند:"النقد الداخل",المبلغ:g.toLocaleString()+" د.ع",النوع:"تدفق نقدي"},{البند:"النقد الخارج",المبلغ:x.toLocaleString()+" د.ع",النوع:"تدفق نقدي"},{البند:"صافي التدفق النقدي",المبلغ:h.toLocaleString()+" د.ع",النوع:"تدفق نقدي"}],charts:[{type:"pie",data:[{name:"الإيرادات",value:c},{name:"المصروفات",value:d}],labels:["الإيرادات","المصروفات"]}]}},ee=async()=>{let e=(await (0,P.getMedicines)()).data||[],t=e.map(e=>{let t=e.medicine_batches||[],a=t.reduce((e,t)=>e+(t.quantity||0),0),n=t.reduce((e,t)=>e+(t.quantity||0)*(t.selling_price||0),0),l=t.reduce((e,t)=>e+(t.quantity||0)*(t.unit_cost||0),0),s=new Date,r=t.filter(e=>e.expiry_date&&new Date(e.expiry_date)<s),i=t.filter(e=>{if(!e.expiry_date)return!1;let t=Math.ceil((new Date(e.expiry_date).getTime()-s.getTime())/864e5);return t>0&&t<=90}),o=t.map(e=>{let t=e.expiry_date?new Date(e.expiry_date):null,a=t?Math.ceil((t.getTime()-s.getTime())/864e5):null,n=t?a<0?"منتهي الصلاحية":a<=30?"ينتهي قريباً":a<=90?"تحذير":"صالح":"غير محدد";return{batchCode:e.batch_code||"غير محدد",quantity:e.quantity||0,unitCost:e.unit_cost||0,sellingPrice:e.selling_price||0,expiryDate:t?t.toLocaleDateString("ar-EG"):"غير محدد",daysUntilExpiry:a,status:n,value:(e.quantity||0)*(e.selling_price||0),cost:(e.quantity||0)*(e.unit_cost||0)}}),c=o.length>0?o.map(e=>"الدفعة: ".concat(e.batchCode," - الكمية: ").concat(e.quantity," - السعر: ").concat(e.sellingPrice.toLocaleString()," د.ع - الانتهاء: ").concat(e.expiryDate," - الحالة: ").concat(e.status)).join(" | "):"لا توجد دفعات";return{...e,totalQuantity:a,totalValue:n,totalCost:l,expectedProfit:n-l,batchesCount:t.length,expiredBatchesCount:r.length,nearExpiryBatchesCount:i.length,batchesDetails:o,batchesText:c,status:0===a?"نفد المخزون":a<=10?"مخزون منخفض":a<=50?"مخزون متوسط":"مخزون جيد"}}),a=e.length,n=t.reduce((e,t)=>e+t.totalQuantity,0),l=t.reduce((e,t)=>e+t.totalValue,0),s=t.reduce((e,t)=>e+t.totalCost,0),r=t.reduce((e,t)=>e+t.expectedProfit,0),i=t.filter(e=>0===e.totalQuantity).length,o=t.filter(e=>e.totalQuantity>0&&e.totalQuantity<=10).length;return{title:"تقرير المخزون الشامل",summary:{totalRecords:a,totalAmount:l,averageAmount:a>0?l/a:0,totalQuantity:n,totalCost:s,totalExpectedProfit:r,outOfStockCount:i,lowStockCount:o,expiredItemsCount:t.reduce((e,t)=>e+t.expiredBatchesCount,0),nearExpiryItemsCount:t.reduce((e,t)=>e+t.nearExpiryBatchesCount,0)},data:t.map(e=>({"اسم الدواء":e.name,"الشركة المصنعة":e.manufacturer||"غير محدد","الكمية المتاحة":e.totalQuantity,"عدد الدفعات":e.batchesCount,"القيمة الإجمالية":e.totalValue.toLocaleString()+" د.ع","التكلفة الإجمالية":e.totalCost.toLocaleString()+" د.ع","الربح المتوقع":e.expectedProfit.toLocaleString()+" د.ع","حالة المخزون":e.status,"دفعات منتهية":e.expiredBatchesCount,"دفعات قريبة الانتهاء":e.nearExpiryBatchesCount,"تفاصيل الدفعات":e.batchesText,الوصف:e.description||"لا يوجد وصف"})),charts:[{type:"pie",data:[{name:"مخزون جيد",value:t.filter(e=>"مخزون جيد"===e.status).length},{name:"مخزون متوسط",value:t.filter(e=>"مخزون متوسط"===e.status).length},{name:"مخزون منخفض",value:t.filter(e=>"مخزون منخفض"===e.status).length},{name:"نفد المخزون",value:t.filter(e=>"نفد المخزون"===e.status).length}],labels:["مخزون جيد","مخزون متوسط","مخزون منخفض","نفد المخزون"]}]}},et=async()=>{let[e,t]=await Promise.all([(0,P.getSalesInvoices)(),(0,P.getCustomers)()]),a=e.data||[],n=t.data||[],l=a.filter(e=>{let t=new Date(e.created_at),a=new Date(V.dateRange.start),n=new Date(V.dateRange.end+"T23:59:59");return t>=a&&t<=n});if(V.customer){let e=n.find(e=>e.id===V.customer),t=l.filter(e=>e.customer_id===V.customer);if(e&&t.length>0){let a=t.map(e=>{let t=e.sales_invoice_items||[],a=t.length>0?t.map(e=>{var t,a,n,l;let s=(null==(a=e.medicine_batches)||null==(t=a.medicines)?void 0:t.name)||e.medicine_name||"غير محدد",r=(null==(n=e.medicine_batches)?void 0:n.batch_code)||e.batch_code||"غير محدد",i=(null==(l=e.medicine_batches)?void 0:l.expiry_date)||e.expiry_date||"غير محدد",o=e.unit_price||0,c=e.quantity||0,d=e.total_price||o*c;return{name:s,batchCode:r,expiryDate:"غير محدد"!==i?new Date(i).toLocaleDateString("ar-EG"):"غير محدد",quantity:c,unitPrice:o,totalPrice:d,text:"".concat(s," - الكمية: ").concat(c," - السعر: ").concat(o.toLocaleString()," د.ع - المجموع: ").concat(d.toLocaleString()," د.ع - الدفعة: ").concat(r," - الانتهاء: ").concat("غير محدد"!==i?new Date(i).toLocaleDateString("ar-EG"):"غير محدد")}}):[],n=a.length>0?a.map(e=>e.text).join(" | "):"لا توجد مواد",l=t.length,s=a.reduce((e,t)=>e+t.quantity,0);return{"رقم الفاتورة":e.invoice_number,"التاريخ والوقت":new Date(e.created_at).toLocaleString("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),"عدد الأصناف":l,"إجمالي الكمية":s,"المبلغ الإجمالي":(e.final_amount||0).toLocaleString()+" د.ع","المبلغ المدفوع":(e.paid_amount||0).toLocaleString()+" د.ع","المبلغ المتبقي":((e.final_amount||0)-(e.paid_amount||0)).toLocaleString()+" د.ع","حالة الدفع":"paid"===e.payment_status?"مدفوع":"partial"===e.payment_status?"جزئي":"معلق","طريقة الدفع":"cash"===e.payment_method?"نقداً":"آجل","تفاصيل المواد المشتراة":n,ملاحظات:e.notes||"لا توجد ملاحظات"}}),n=t.reduce((e,t)=>e+(t.final_amount||0),0),l=t.reduce((e,t)=>e+(t.paid_amount||0),0),s=a.reduce((e,t)=>e+(t["إجمالي الكمية"]||0),0),r=a.reduce((e,t)=>e+(t["عدد الأصناف"]||0),0);return{title:"كشف حساب العميل: ".concat(e.name),summary:{totalRecords:t.length,totalAmount:n,averageAmount:t.length>0?n/t.length:0,paidAmount:l,pendingAmount:n-l,totalItemsPurchased:s,totalInvoiceItems:r,customerInfo:{name:e.name,phone:e.phone||"غير محدد",address:e.address||"غير محدد",email:e.email||"غير محدد"}},data:a}}}let s=n.map(e=>{let t=l.filter(t=>t.customer_id===e.id);return{customer:e,totalAmount:t.reduce((e,t)=>e+(t.final_amount||0),0),paidAmount:t.reduce((e,t)=>e+(t.paid_amount||0),0),salesCount:t.length}}).filter(e=>e.salesCount>0);return{title:"كشف حساب العملاء",summary:{totalRecords:s.length,totalAmount:s.reduce((e,t)=>e+t.totalAmount,0),averageAmount:s.length>0?s.reduce((e,t)=>e+t.totalAmount,0)/s.length:0},data:s.map(e=>({"اسم العميل":e.customer.name,الهاتف:e.customer.phone||"غير محدد",العنوان:e.customer.address||"غير محدد","عدد الفواتير":e.salesCount,"إجمالي المبلغ":e.totalAmount.toLocaleString()+" د.ع","المبلغ المدفوع":e.paidAmount.toLocaleString()+" د.ع","المبلغ المتبقي":(e.totalAmount-e.paidAmount).toLocaleString()+" د.ع"}))}},ea=async()=>{let[e,t]=await Promise.all([(0,P.getPurchaseInvoices)(),(0,P.getSuppliers)()]),a=e.data||[],n=t.data||[],l=a.filter(e=>{let t=new Date(e.created_at),a=new Date(V.dateRange.start),n=new Date(V.dateRange.end+"T23:59:59");return t>=a&&t<=n});if(V.supplier){let e=n.find(e=>e.id===V.supplier),t=l.filter(e=>e.supplier_id===V.supplier);if(e&&t.length>0){let a=t.map(e=>{let t=e.purchase_invoice_items||[],a=t.length>0?t.map(e=>{var t;let a=(null==(t=e.medicines)?void 0:t.name)||e.medicine_name||"غير محدد";return"".concat(a," (").concat(e.quantity," \xd7 ").concat((e.unit_cost||0).toLocaleString()," = ").concat((e.total_cost||0).toLocaleString()," د.ع)")}).join(" | "):"لا توجد مواد";return{"رقم الفاتورة":e.invoice_number,التاريخ:new Date(e.created_at).toLocaleDateString("ar-EG"),"المبلغ الإجمالي":(e.final_amount||0).toLocaleString()+" د.ع","المبلغ المدفوع":(e.paid_amount||0).toLocaleString()+" د.ع","المبلغ المتبقي":((e.final_amount||0)-(e.paid_amount||0)).toLocaleString()+" د.ع","حالة الدفع":"paid"===e.payment_status?"مدفوع":"partial"===e.payment_status?"جزئي":"معلق","المواد المشتراة":a}}),n=t.reduce((e,t)=>e+(t.final_amount||0),0),l=t.reduce((e,t)=>e+(t.paid_amount||0),0);return{title:"كشف حساب المورد: ".concat(e.name),summary:{totalRecords:t.length,totalAmount:n,averageAmount:t.length>0?n/t.length:0,paidAmount:l,pendingAmount:n-l},data:a}}}let s=n.map(e=>{let t=l.filter(t=>t.supplier_id===e.id);return{supplier:e,totalAmount:t.reduce((e,t)=>e+(t.final_amount||0),0),paidAmount:t.reduce((e,t)=>e+(t.paid_amount||0),0),purchasesCount:t.length}}).filter(e=>e.purchasesCount>0);return{title:"كشف حساب الموردين",summary:{totalRecords:s.length,totalAmount:s.reduce((e,t)=>e+t.totalAmount,0),averageAmount:s.length>0?s.reduce((e,t)=>e+t.totalAmount,0)/s.length:0},data:s.map(e=>({"اسم المورد":e.supplier.name,الهاتف:e.supplier.phone||"غير محدد",العنوان:e.supplier.address||"غير محدد","الشخص المسؤول":e.supplier.contact_person||"غير محدد","عدد الفواتير":e.purchasesCount,"إجمالي المبلغ":e.totalAmount.toLocaleString()+" د.ع","المبلغ المدفوع":e.paidAmount.toLocaleString()+" د.ع","المبلغ المتبقي":(e.totalAmount-e.paidAmount).toLocaleString()+" د.ع"}))}},en=async()=>{let[e,t]=await Promise.all([(0,P.getSalesInvoices)(),(0,P.getPurchaseInvoices)()]),a=e.data||[],n=t.data||[],l=a.reduce((e,t)=>e+(t.final_amount||0),0),s=n.reduce((e,t)=>e+(t.final_amount||0),0),r=l-s;return{title:"تقرير الأرباح والخسائر",summary:{totalRecords:a.length+n.length,totalAmount:l,averageAmount:r},data:[{البند:"الإيرادات",المبلغ:l.toLocaleString()+" د.ع"},{البند:"تكلفة البضاعة المباعة",المبلغ:s.toLocaleString()+" د.ع"},{البند:"إجمالي الربح",المبلغ:r.toLocaleString()+" د.ع"},{البند:"هامش الربح الإجمالي",المبلغ:l>0?(r/l*100).toFixed(2)+"%":"0%"}]}},el=async()=>{let e=((await (0,P.getCashTransactions)()).data||[]).filter(e=>{let t=new Date(e.created_at),a=new Date(V.dateRange.start),n=new Date(V.dateRange.end+"T23:59:59");return t>=a&&t<=n}),t=e.filter(e=>"income"===e.transaction_type).reduce((e,t)=>e+t.amount,0),a=e.filter(e=>"expense"===e.transaction_type).reduce((e,t)=>e+t.amount,0),n=t-a,l=e.map(e=>({التاريخ:new Date(e.created_at).toLocaleDateString("ar-EG"),النوع:"income"===e.transaction_type?"داخل":"خارج",المبلغ:e.amount.toLocaleString()+" د.ع",الوصف:e.description||"غير محدد",المرجع:e.reference_type||"غير محدد"}));return{title:"تقرير التدفق النقدي",summary:{totalRecords:e.length,totalAmount:t,averageAmount:n,income:t,expenses:a,netFlow:n},data:[{البند:"إجمالي الداخل",المبلغ:t.toLocaleString()+" د.ع",النوع:"إيجابي"},{البند:"إجمالي الخارج",المبلغ:a.toLocaleString()+" د.ع",النوع:"سلبي"},{البند:"صافي التدفق النقدي",المبلغ:n.toLocaleString()+" د.ع",النوع:n>=0?"إيجابي":"سلبي"},...l],charts:[{type:"pie",data:[{name:"الداخل",value:t},{name:"الخارج",value:a}],labels:["الداخل","الخارج"]}]}},es=async()=>{let e=(await (0,P.getMedicines)()).data||[];return{title:"أفضل المنتجات مبيعاً",summary:{totalRecords:e.length,totalAmount:0,averageAmount:0},data:e.slice(0,10).map((e,t)=>({الترتيب:t+1,"اسم الدواء":e.name,"الشركة المصنعة":e.manufacturer||"غير محدد","الكمية المباعة":Math.floor(100*Math.random())+1,"إجمالي المبيعات":(Math.floor(1e6*Math.random())+1e5).toLocaleString()+" د.ع"}))}},er=async()=>{let[e,t]=await Promise.all([(0,P.getSalesInvoices)(),(0,P.getCustomers)()]),a=e.data||[],n=t.data||[];return{title:"تحليل العملاء",summary:{totalRecords:n.length,totalAmount:a.reduce((e,t)=>e+(t.final_amount||0),0),averageAmount:0},data:n.slice(0,10).map(e=>{let t=a.filter(t=>t.customer_id===e.id);return{"اسم العميل":e.name,"عدد الزيارات":t.length,"متوسط قيمة الطلب":t.length>0?(t.reduce((e,t)=>e+(t.final_amount||0),0)/t.length).toLocaleString()+" د.ع":"0 د.ع","آخر زيارة":t.length>0?new Date(Math.max(...t.map(e=>new Date(e.created_at).getTime()))).toLocaleDateString("ar-EG"):"لا توجد زيارات"}})}},ei=()=>{if(!r)return;let{printReport:t}=a(53363),{usePrintSettings:n}=a(53363);t(r.data,e||"general",r.title,{companyName:"مكتب لارين العلمي",companyNameEn:"LAREN SCIENTIFIC BUREAU",companyAddress:"بغداد - شارع فلسطين",companyPhone:"+*********** 4567",companyEmail:"<EMAIL>",showLogo:!0,showHeader:!0,showFooter:!0,footerText:"شكراً لتعاملكم معنا",fontSize:"medium",paperSize:"A4",showBorders:!0,showColors:!1,includeBarcode:!1,includeQRCode:!1,showWatermark:!1,headerColor:"#1f2937",accentColor:"#3b82f6",textColor:"#374151",backgroundColor:"#ffffff"})},eo=async()=>{if(r)try{let e=await Promise.all([a.e(3524),a.e(8436)]).then(a.bind(a,3925)),t=e.utils.book_new(),n=[["نظام إدارة الصيدلية الاحترافي"],[""],["اسم التقرير:",r.title],["تاريخ الإنشاء:",new Date().toLocaleDateString("ar-EG")],["وقت الإنشاء:",new Date().toLocaleTimeString("ar-EG")],["الفترة الزمنية:","من ".concat(V.dateRange.start," إلى ").concat(V.dateRange.end)],[""],["الملخص التنفيذي"],["المؤشر","القيمة","الوحدة"],["إجمالي السجلات",r.summary.totalRecords,"سجل"],["إجمالي المبلغ",Math.round(r.summary.totalAmount),"دينار عراقي"],["متوسط المبلغ",Math.round(r.summary.averageAmount),"دينار عراقي"]];if(void 0!==r.summary.paidAmount&&n.push(["المبلغ المدفوع",Math.round(r.summary.paidAmount),"دينار عراقي"]),void 0!==r.summary.pendingAmount&&n.push(["المبلغ المعلق",Math.round(r.summary.pendingAmount),"دينار عراقي"]),void 0!==r.summary.totalRevenue&&n.push(["إجمالي الإيرادات",Math.round(r.summary.totalRevenue),"دينار عراقي"]),void 0!==r.summary.totalExpenses&&n.push(["إجمالي المصروفات",Math.round(r.summary.totalExpenses),"دينار عراقي"]),void 0!==r.summary.netProfit&&n.push(["صافي الربح",Math.round(r.summary.netProfit),"دينار عراقي"]),void 0!==r.summary.profitMargin&&n.push(["هامش الربح",Math.round(100*r.summary.profitMargin)/100,"%"]),n.push([""]),n.push(["الفلاتر المطبقة"]),n.push(["الفلتر","القيمة"]),n.push(["حالة الدفع","all"===V.paymentStatus?"جميع الحالات":"paid"===V.paymentStatus?"مدفوع":"partial"===V.paymentStatus?"جزئي":"معلق"]),n.push(["طريقة الدفع","all"===V.paymentMethod?"جميع الطرق":"cash"===V.paymentMethod?"نقداً":"آجل"]),V.customer){let e=q.find(e=>e.id===V.customer);n.push(["العميل المحدد",(null==e?void 0:e.name)||"غير معروف"])}if(V.supplier){let e=B.find(e=>e.id===V.supplier);n.push(["المورد المحدد",(null==e?void 0:e.name)||"غير معروف"])}let l=e.utils.aoa_to_sheet(n);if(e.utils.decode_range(l["!ref"]||"A1"),l["!cols"]=[{width:25},{width:20},{width:15}],e.utils.book_append_sheet(t,l,"الملخص التنفيذي"),r.data.length>0){let a=r.data.map(e=>{let t={};return Object.entries(e).forEach(e=>{let[a,n]=e,l=em(a);if(a.includes("تفاصيل المواد")||"المواد"===a||"المواد المشتراة"===a||"تفاصيل الدفعات"===a){let e=String(n||"").split("|");e.length>1?t[l]="عدد العناصر: ".concat(e.length,"\n\n").concat(e.map((e,t)=>"".concat(t+1,". ").concat(e.trim())).join("\n")):t[l]=String(n||"لا توجد مواد")}else t[l]=n}),t}),n=e.utils.json_to_sheet(a),l=Object.keys(a[0]||{});n["!cols"]=l.map(e=>e.includes("تفاصيل المواد")||"المواد"===e||"المواد المشتراة"===e||"تفاصيل الدفعات"===e?{width:60}:"ملاحظات"===e||"الوصف"===e?{width:30}:"التاريخ والوقت"===e?{width:20}:{width:15}),e.utils.book_append_sheet(t,n,"البيانات التفصيلية")}if(r.charts&&r.charts.length>0){let a=[["الإحصائيات والرسوم البيانية"],[""]];r.charts.forEach((e,t)=>{a.push(["الرسم البياني ".concat(t+1),"pie"===e.type?"دائري":"bar"===e.type?"عمودي":"خطي"]),a.push(["التصنيف","القيمة"]),e.data.forEach(e=>{a.push([e.name||"غير محدد",e.value||0])}),a.push([""])});let n=e.utils.aoa_to_sheet(a);n["!cols"]=[{width:20},{width:15}],e.utils.book_append_sheet(t,n,"الإحصائيات")}let s="".concat(r.title,"_").concat(new Date().toISOString().split("T")[0],".xlsx");e.writeFile(t,s)}catch(e){console.error("Error exporting Excel:",e),ec()}},ec=()=>{if(!r)return;let e=new Blob(["\uFEFF"+[[r.title],["تاريخ التقرير: ".concat(new Date().toLocaleDateString("ar-EG"))],["الفترة: ".concat(V.dateRange.start," إلى ").concat(V.dateRange.end)],[],["الملخص"],["إجمالي السجلات",r.summary.totalRecords.toLocaleString()],["إجمالي المبلغ",r.summary.totalAmount.toLocaleString()+" د.ع"],["متوسط المبلغ",r.summary.averageAmount.toLocaleString()+" د.ع"],[],...r.data.length>0?[Object.keys(r.data[0]),...r.data.map(e=>Object.values(e))]:[]].map(e=>Array.isArray(e)?e.join(","):e).join("\n")],{type:"text/csv;charset=utf-8;"}),t=document.createElement("a"),a=URL.createObjectURL(e);t.setAttribute("href",a),t.setAttribute("download","".concat(r.title,"_").concat(new Date().toISOString().split("T")[0],".csv")),t.style.visibility="hidden",document.body.appendChild(t),t.click(),document.body.removeChild(t)},ed=async()=>{if(r)try{let{jsPDF:e}=await Promise.all([a.e(3930),a.e(913)]).then(a.bind(a,26597));a(86087);let t=new e({orientation:"portrait",unit:"mm",format:"a4"});t.setFont("helvetica"),t.setFillColor(102,126,234),t.rect(0,0,210,35,"F"),t.setTextColor(255,255,255),t.setFontSize(18),t.text(r.title,105,15,{align:"center"}),t.setFontSize(12),t.text("نظام إدارة الصيدلية الاحترافي",105,25,{align:"center"}),t.setTextColor(0,0,0),t.setFillColor(248,250,252),t.rect(15,40,180,25,"F"),t.setDrawColor(226,232,240),t.rect(15,40,180,25,"S"),t.setFontSize(10),t.text("تاريخ التقرير: ".concat(new Date().toLocaleDateString("ar-EG")),20,50),t.text("الفترة: ".concat(V.dateRange.start," إلى ").concat(V.dateRange.end),20,58);let n=75;t.setFontSize(14),t.setTextColor(45,55,72),t.text("ملخص التقرير:",20,n),n+=10;let l=[{label:"إجمالي السجلات",value:r.summary.totalRecords.toLocaleString(),color:[59,130,246]},{label:"إجمالي المبلغ",value:Math.round(r.summary.totalAmount).toLocaleString()+" د.ع",color:[16,185,129]},{label:"متوسط المبلغ",value:Math.round(r.summary.averageAmount).toLocaleString()+" د.ع",color:[139,92,246]}];void 0!==r.summary.paidAmount&&l.push({label:"المبلغ المدفوع",value:Math.round(r.summary.paidAmount).toLocaleString()+" د.ع",color:[5,150,105]}),void 0!==r.summary.pendingAmount&&l.push({label:"المبلغ المعلق",value:Math.round(r.summary.pendingAmount).toLocaleString()+" د.ع",color:[220,38,38]}),void 0!==r.summary.totalItemsSold&&l.push({label:"إجمالي الكمية المباعة",value:r.summary.totalItemsSold.toLocaleString(),color:[99,102,241]}),void 0!==r.summary.totalItemsPurchased&&l.push({label:"إجمالي الكمية المشتراة",value:r.summary.totalItemsPurchased.toLocaleString(),color:[20,184,166]}),void 0!==r.summary.totalInvoiceItems&&l.push({label:"عدد الأصناف",value:r.summary.totalInvoiceItems.toLocaleString(),color:[6,182,212]}),void 0!==r.summary.totalExpectedProfit&&l.push({label:"الربح المتوقع",value:Math.round(r.summary.totalExpectedProfit).toLocaleString()+" د.ع",color:[251,191,36]});let s=20;if(l.forEach((e,a)=>{a>0&&a%2==0&&(n+=25,s=20),t.setFillColor(e.color[0],e.color[1],e.color[2]),t.rect(s,n,85,20,"F"),t.setTextColor(255,255,255),t.setFontSize(9),t.text(e.label,s+5,n+8),t.setFontSize(12),t.text(e.value,s+5,n+16),s+=95}),n+=35,r.data.length>0){t.setTextColor(0,0,0),t.setFontSize(12),t.text("تفاصيل التقرير:",20,n),n+=10;let e=Object.keys(r.data[0]).map(e=>em(e)),a=r.data.map(e=>Object.entries(e).map(e=>{let[t,a]=e;if(t.includes("تفاصيل المواد")||"المواد"===t||"المواد المشتراة"===t||"تفاصيل الدفعات"===t){let e=String(a||"لا توجد مواد"),t=e.split("|").length;return e.length>80?"".concat(t," عنصر - ").concat(e.substring(0,80),"..."):e}return String(a||"-")}));t.autoTable({head:[e],body:a,startY:n,styles:{fontSize:7,cellPadding:3,textColor:[45,55,72],fillColor:[255,255,255],lineColor:[226,232,240],lineWidth:.1},headStyles:{fillColor:[102,126,234],textColor:[255,255,255],fontStyle:"bold",fontSize:8},alternateRowStyles:{fillColor:[248,250,252]},margin:{top:10,right:15,bottom:20,left:15},tableWidth:"auto",columnStyles:{[e.indexOf("المواد")]:{cellWidth:40},[e.indexOf("المواد المشتراة")]:{cellWidth:40}},didDrawPage:function(e){let a=t.internal.pageSize.height;t.setFontSize(8),t.setTextColor(128,128,128),t.text("تم إنشاء هذا التقرير في: ".concat(new Date().toLocaleString("ar-EG")),105,a-10,{align:"center"}),t.text("صفحة ".concat(e.pageNumber),195,a-10,{align:"right"})}})}let i="".concat(r.title,"_").concat(new Date().toISOString().split("T")[0],".pdf");t.save(i)}catch(e){console.error("Error exporting PDF:",e),alert("حدث خطأ في تصدير PDF. سيتم فتح نافذة الطباعة بدلاً من ذلك."),ei()}},em=e=>({"رقم الفاتورة":"رقم الفاتورة",العميل:"العميل",المورد:"المورد",التاريخ:"التاريخ","المبلغ الإجمالي":"المبلغ الإجمالي","المبلغ المدفوع":"المبلغ المدفوع","المبلغ المتبقي":"المبلغ المتبقي","حالة الدفع":"حالة الدفع","طريقة الدفع":"طريقة الدفع",المواد:"المواد","المواد المشتراة":"المواد المشتراة","اسم العميل":"اسم العميل","اسم المورد":"اسم المورد",الهاتف:"الهاتف",العنوان:"العنوان","الشخص المسؤول":"الشخص المسؤول","عدد الفواتير":"عدد الفواتير",البند:"البند",المبلغ:"المبلغ",النوع:"النوع",الوصف:"الوصف",المرجع:"المرجع","اسم الدواء":"اسم الدواء","الشركة المصنعة":"الشركة المصنعة","الكمية المتاحة":"الكمية المتاحة","تاريخ الانتهاء":"تاريخ الانتهاء",الترتيب:"الترتيب","الكمية المباعة":"الكمية المباعة","إجمالي المبيعات":"إجمالي المبيعات","عدد الزيارات":"عدد الزيارات","متوسط قيمة الطلب":"متوسط قيمة الطلب","آخر زيارة":"آخر زيارة","التاريخ والوقت":"التاريخ والوقت","عدد الأصناف":"عدد الأصناف","إجمالي الكمية":"إجمالي الكمية","المبلغ المتبقي":"المبلغ المتبقي","الربح المتوقع":"الربح المتوقع","تفاصيل المواد":"تفاصيل المواد","تفاصيل المواد المشتراة":"تفاصيل المواد المشتراة",ملاحظات:"ملاحظات","عدد الدفعات":"عدد الدفعات","القيمة الإجمالية":"القيمة الإجمالية","التكلفة الإجمالية":"التكلفة الإجمالية","حالة المخزون":"حالة المخزون","دفعات منتهية":"دفعات منتهية","دفعات قريبة الانتهاء":"دفعات قريبة الانتهاء","تفاصيل الدفعات":"تفاصيل الدفعات",invoice_number:"رقم الفاتورة",customer_name:"اسم العميل",supplier_name:"اسم المورد",created_at:"التاريخ",final_amount:"المبلغ الإجمالي",paid_amount:"المبلغ المدفوع",payment_status:"حالة الدفع",payment_method:"طريقة الدفع",name:"الاسم",phone:"الهاتف",address:"العنوان",quantity:"الكمية",unit_price:"سعر الوحدة",total_price:"المجموع"})[e]||e,eu=e=>{if(null==e)return"-";if("number"==typeof e)return e.toLocaleString("ar-EG");if("string"==typeof e&&e.includes("T"))try{return new Date(e).toLocaleDateString("ar-EG",{year:"numeric",month:"long",day:"numeric"})}catch(t){return e}let t=String(e);return t.length>100?t.substring(0,100)+"...":t};return(0,n.jsx)(s.A,{children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center gap-3",children:[(0,n.jsx)(p.A,{className:"h-8 w-8 text-blue-600"}),"نظام التقارير الاحترافي"]}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:"تقارير شاملة مع إمكانيات طباعة وتصدير متقدمة"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("button",{onClick:()=>h(!g),className:"flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ".concat(g?"bg-blue-50 border-blue-200 text-blue-700":"bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100"),children:[(0,n.jsx)(y.A,{className:"h-4 w-4"}),"الفلاتر المتقدمة"]}),(0,n.jsxs)("button",{onClick:()=>window.location.reload(),className:"flex items-center gap-2 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,n.jsx)(b.A,{className:"h-4 w-4"}),"تحديث"]})]})]})}),g&&(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,n.jsx)(f.A,{className:"h-5 w-5"}),"الفلاتر المتقدمة"]}),(0,n.jsx)("button",{onClick:()=>h(!1),className:"text-gray-400 hover:text-gray-600",children:(0,n.jsx)(v.A,{className:"h-5 w-5"})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الفترة الزمنية"}),(0,n.jsx)("select",{value:V.dateRange.preset,onChange:e=>(e=>{let t=new Date,a=new Date;switch(e){case"today":a=new Date(t);break;case"week":a=new Date(t.getTime()-6048e5);break;case"month":a=new Date(t.getTime()-2592e6);break;case"quarter":a=new Date(t.getTime()-7776e6);break;case"year":a=new Date(t.getTime()-31536e6);break;default:return}H(n=>({...n,dateRange:{...n.dateRange,start:a.toISOString().split("T")[0],end:t.toISOString().split("T")[0],preset:e}}))})(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:F.map(e=>(0,n.jsx)("option",{value:e.id,children:e.label},e.id))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"حالة الدفع"}),(0,n.jsxs)("select",{value:V.paymentStatus||"all",onChange:e=>H(t=>({...t,paymentStatus:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,n.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,n.jsx)("option",{value:"paid",children:"مدفوع"}),(0,n.jsx)("option",{value:"partial",children:"جزئي"}),(0,n.jsx)("option",{value:"pending",children:"معلق"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"طريقة الدفع"}),(0,n.jsxs)("select",{value:V.paymentMethod||"all",onChange:e=>H(t=>({...t,paymentMethod:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,n.jsx)("option",{value:"all",children:"جميع الطرق"}),(0,n.jsx)("option",{value:"cash",children:"نقداً"}),(0,n.jsx)("option",{value:"credit",children:"آجل"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العميل"}),(0,n.jsxs)("select",{value:V.customer||"",onChange:e=>H(t=>({...t,customer:e.target.value||void 0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,n.jsx)("option",{value:"",children:"جميع العملاء"}),q.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"المورد"}),(0,n.jsxs)("select",{value:V.supplier||"",onChange:e=>H(t=>({...t,supplier:e.target.value||void 0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,n.jsx)("option",{value:"",children:"جميع الموردين"}),B.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"المرتجعات"}),(0,n.jsxs)("label",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"checkbox",checked:V.includeReturns||!1,onChange:e=>H(t=>({...t,includeReturns:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"mr-2 text-sm text-gray-700",children:"تضمين المرتجعات"})]})]})]}),"custom"===V.dateRange.preset&&(0,n.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"من تاريخ"}),(0,n.jsx)("input",{type:"date",value:V.dateRange.start,onChange:e=>H(t=>({...t,dateRange:{...t.dateRange,start:e.target.value}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"إلى تاريخ"}),(0,n.jsx)("input",{type:"date",value:V.dateRange.end,onChange:e=>H(t=>({...t,dateRange:{...t.dateRange,end:e.target.value}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:T.map(t=>{let a=t.icon;return(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all cursor-pointer",children:(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("div",{className:"p-3 rounded-lg ".concat(t.color," text-white"),children:(0,n.jsx)(a,{className:"h-6 w-6"})}),(0,n.jsxs)("button",{onClick:()=>J(t.id),disabled:d,className:"px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1",children:[d&&e===t.id?(0,n.jsx)(b.A,{className:"h-4 w-4 animate-spin"}):(0,n.jsx)(j.A,{className:"h-4 w-4"}),"عرض"]})]}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t.title}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:t.description})]})},t.id)})}),O&&r&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:r.title}),(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("button",{onClick:()=>ei(),className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:[(0,n.jsx)(w.A,{className:"h-4 w-4"}),"طباعة"]}),(0,n.jsxs)("button",{onClick:()=>eo(),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:[(0,n.jsx)(_.A,{className:"h-4 w-4"}),"تصدير Excel"]}),(0,n.jsxs)("button",{onClick:()=>ed(),className:"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:[(0,n.jsx)(N.A,{className:"h-4 w-4"}),"تصدير PDF"]}),(0,n.jsx)("button",{onClick:()=>G(!1),className:"text-gray-400 hover:text-gray-600",children:(0,n.jsx)(v.A,{className:"h-5 w-5"})})]})]}),(0,n.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,n.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"إجمالي السجلات"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:r.summary.totalRecords.toLocaleString()})]}),(0,n.jsx)(S.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,n.jsx)("div",{className:"bg-green-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-green-600",children:"إجمالي المبلغ"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-green-900",children:[Math.round(r.summary.totalAmount).toLocaleString()," د.ع"]})]}),(0,n.jsx)(o.A,{className:"h-8 w-8 text-green-600"})]})}),(0,n.jsx)("div",{className:"bg-purple-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"متوسط المبلغ"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-purple-900",children:[Math.round(r.summary.averageAmount).toLocaleString()," د.ع"]})]}),(0,n.jsx)(p.A,{className:"h-8 w-8 text-purple-600"})]})}),(0,n.jsx)("div",{className:"bg-orange-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"الفترة"}),(0,n.jsxs)("p",{className:"text-sm font-bold text-orange-900",children:[V.dateRange.start," إلى ",V.dateRange.end]})]}),(0,n.jsx)(A.A,{className:"h-8 w-8 text-orange-600"})]})}),void 0!==r.summary.paidAmount&&(0,n.jsx)("div",{className:"bg-emerald-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-emerald-600",children:"المبلغ المدفوع"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-emerald-900",children:[Math.round(r.summary.paidAmount).toLocaleString()," د.ع"]})]}),(0,n.jsx)(L.A,{className:"h-8 w-8 text-emerald-600"})]})}),void 0!==r.summary.pendingAmount&&(0,n.jsx)("div",{className:"bg-red-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-red-600",children:"المبلغ المعلق"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-red-900",children:[Math.round(r.summary.pendingAmount).toLocaleString()," د.ع"]})]}),(0,n.jsx)(D.A,{className:"h-8 w-8 text-red-600"})]})}),void 0!==r.summary.totalItemsSold&&(0,n.jsx)("div",{className:"bg-indigo-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-indigo-600",children:"إجمالي الكمية المباعة"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-indigo-900",children:r.summary.totalItemsSold.toLocaleString()})]}),(0,n.jsx)(i.A,{className:"h-8 w-8 text-indigo-600"})]})}),void 0!==r.summary.totalItemsPurchased&&(0,n.jsx)("div",{className:"bg-teal-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-teal-600",children:"إجمالي الكمية المشتراة"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-teal-900",children:r.summary.totalItemsPurchased.toLocaleString()})]}),(0,n.jsx)(i.A,{className:"h-8 w-8 text-teal-600"})]})}),void 0!==r.summary.totalInvoiceItems&&(0,n.jsx)("div",{className:"bg-cyan-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-cyan-600",children:"عدد الأصناف"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-cyan-900",children:r.summary.totalInvoiceItems.toLocaleString()})]}),(0,n.jsx)(C.A,{className:"h-8 w-8 text-cyan-600"})]})}),void 0!==r.summary.totalExpectedProfit&&(0,n.jsx)("div",{className:"bg-yellow-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-yellow-600",children:"الربح المتوقع"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-yellow-900",children:[Math.round(r.summary.totalExpectedProfit).toLocaleString()," د.ع"]})]}),(0,n.jsx)(u.A,{className:"h-8 w-8 text-yellow-600"})]})}),void 0!==r.summary.outOfStockCount&&(0,n.jsx)("div",{className:"bg-rose-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-rose-600",children:"نفد المخزون"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-rose-900",children:r.summary.outOfStockCount.toLocaleString()})]}),(0,n.jsx)(R.A,{className:"h-8 w-8 text-rose-600"})]})}),void 0!==r.summary.lowStockCount&&(0,n.jsx)("div",{className:"bg-amber-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-amber-600",children:"مخزون منخفض"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-amber-900",children:r.summary.lowStockCount.toLocaleString()})]}),(0,n.jsx)(E.A,{className:"h-8 w-8 text-amber-600"})]})}),void 0!==r.summary.expiredItemsCount&&(0,n.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"منتهية الصلاحية"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:r.summary.expiredItemsCount.toLocaleString()})]}),(0,n.jsx)(I.A,{className:"h-8 w-8 text-gray-600"})]})}),void 0!==r.summary.nearExpiryItemsCount&&(0,n.jsx)("div",{className:"bg-orange-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"قريبة الانتهاء"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-orange-900",children:r.summary.nearExpiryItemsCount.toLocaleString()})]}),(0,n.jsx)(k.A,{className:"h-8 w-8 text-orange-600"})]})}),void 0!==r.summary.netProfit&&(0,n.jsx)("div",{className:"bg-cyan-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-cyan-600",children:"صافي الربح"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-cyan-900",children:[Math.round(r.summary.netProfit).toLocaleString()," د.ع"]})]}),(0,n.jsx)(u.A,{className:"h-8 w-8 text-cyan-600"})]})}),void 0!==r.summary.profitMargin&&(0,n.jsx)("div",{className:"bg-indigo-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-indigo-600",children:"هامش الربح"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-indigo-900",children:[Math.round(100*r.summary.profitMargin)/100,"%"]})]}),(0,n.jsx)(x.A,{className:"h-8 w-8 text-indigo-600"})]})})]}),r.data.length>0&&(0,n.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 overflow-hidden",children:[(0,n.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"تفاصيل التقرير"}),(0,n.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["عدد السجلات: ",r.data.length]})]}),(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,n.jsx)("thead",{className:"bg-gray-50",children:(0,n.jsx)("tr",{children:Object.keys(r.data[0]||{}).map(e=>(0,n.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:em(e)},e))})}),(0,n.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:r.data.map((e,t)=>(0,n.jsx)("tr",{className:"hover:bg-gray-50",children:Object.entries(e).map((e,t)=>{let[a,l]=e;return(0,n.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900 ".concat(a.includes("تفاصيل المواد")||"المواد"===a||"المواد المشتراة"===a||"تفاصيل الدفعات"===a?"max-w-md":"whitespace-nowrap"),children:a.includes("تفاصيل المواد")||"المواد"===a||"المواد المشتراة"===a||"تفاصيل الدفعات"===a?(0,n.jsx)("div",{className:"text-xs text-gray-600 break-words",children:(0,n.jsxs)("details",{className:"cursor-pointer",children:[(0,n.jsxs)("summary",{className:"font-medium text-blue-600 hover:text-blue-800",children:["عرض التفاصيل (",String(l||"").split("|").length," عنصر)"]}),(0,n.jsx)("div",{className:"mt-2 p-2 bg-gray-50 rounded text-xs",children:String(l||"").split("|").map((e,t)=>(0,n.jsx)("div",{className:"mb-1 p-1 bg-white rounded border-r-2 border-blue-200",children:e.trim()},t))})]})}):"حالة الدفع"===a?(0,n.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("مدفوع"===l?"bg-green-100 text-green-800":"جزئي"===l?"bg-yellow-100 text-yellow-800":"معلق"===l?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:eu(l)}):"حالة المخزون"===a?(0,n.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("مخزون جيد"===l?"bg-green-100 text-green-800":"مخزون متوسط"===l?"bg-yellow-100 text-yellow-800":"مخزون منخفض"===l?"bg-orange-100 text-orange-800":"نفد المخزون"===l?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:eu(l)}):"طريقة الدفع"===a?(0,n.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("نقداً"===l?"bg-blue-100 text-blue-800":"آجل"===l?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:eu(l)}):eu(l)},t)})},t))})]})})]}),r.charts&&r.charts.length>0&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"الرسوم البيانية"}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:r.charts.map((e,t)=>(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,n.jsx)("h4",{className:"text-md font-medium text-gray-700 mb-2",children:"bar"===e.type?"رسم بياني عمودي":"pie"===e.type?"رسم بياني دائري":"رسم بياني خطي"}),(0,n.jsxs)("div",{className:"h-64 flex items-center justify-center text-gray-500",children:[(0,n.jsx)(M.A,{className:"h-16 w-16"}),(0,n.jsx)("span",{className:"mr-2",children:"الرسم البياني سيتم عرضه هنا"})]})]},t))})]})]})]})})]})})}}},e=>{e.O(0,[6874,6543,5647,41,8080,1932,988,3363,8441,5964,7358],()=>e(e.s=54954)),_N_E=e.O()}]);