'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import {
  createSalesInvoice,
  getSalesInvoices,
  completeSalesTransaction,
  getMedicines,
  getSalesInvoiceForPrint,
  initializeSystemData
} from '@/lib/database'

export default function DebugPage() {
  const [logs, setLogs] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `[${timestamp}] ${message}`
    setLogs(prev => [...prev, logMessage])
    console.log(logMessage)
  }

  const clearLogs = () => {
    setLogs([])
  }

  // Test 1: Check Supabase connection
  const testSupabaseConnection = async () => {
    addLog('🔄 اختبار اتصال Supabase...')
    
    try {
      const { data, error } = await supabase
        .from('medicines')
        .select('id, name')
        .limit(1)
      
      if (error) {
        addLog(`❌ خطأ في Supabase: ${error.message}`)
        addLog(`📋 تفاصيل الخطأ: ${JSON.stringify(error)}`)
        return false
      }
      
      addLog(`✅ Supabase متصل بنجاح`)
      addLog(`📊 عدد الأدوية المسترجعة: ${data?.length || 0}`)
      if (data && data.length > 0) {
        addLog(`📋 أول دواء: ${JSON.stringify(data[0])}`)
      }
      return true
    } catch (error: any) {
      addLog(`❌ خطأ في الاتصال: ${error.message}`)
      return false
    }
  }

  // Test 2: Check localStorage data
  const checkLocalStorageData = () => {
    addLog('🔄 فحص بيانات localStorage...')
    
    const keys = [
      'medicines',
      'medicine_batches', 
      'sales_invoices',
      'sales_invoice_items',
      'customers'
    ]
    
    keys.forEach(key => {
      try {
        const data = localStorage.getItem(key)
        if (data) {
          const parsed = JSON.parse(data)
          addLog(`📊 ${key}: ${parsed.length} عنصر`)
          
          if (key === 'medicines' && parsed.length > 0) {
            addLog(`📋 أول دواء في localStorage: ${JSON.stringify(parsed[0])}`)
          }
          
          if (key === 'sales_invoices' && parsed.length > 0) {
            addLog(`📋 أول فاتورة في localStorage: ${JSON.stringify(parsed[0])}`)
          }
        } else {
          addLog(`📊 ${key}: لا توجد بيانات`)
        }
      } catch (error: any) {
        addLog(`❌ خطأ في قراءة ${key}: ${error.message}`)
      }
    })
  }

  // Test 3: Test creating invoice
  const testCreateInvoice = async () => {
    addLog('🔄 اختبار إنشاء فاتورة...')

    try {
      const testInvoiceData = {
        invoice_number: `DEBUG-${Date.now()}`,
        customer_name: 'عميل تجريبي',
        total_amount: 100,
        discount_amount: 0,
        final_amount: 100,
        payment_method: 'cash',
        payment_status: 'paid',
        notes: 'فاتورة اختبار تشخيص'
      }

      addLog(`📋 بيانات الفاتورة: ${JSON.stringify(testInvoiceData)}`)

      const result = await createSalesInvoice(testInvoiceData)
      addLog(`📋 نتيجة إنشاء الفاتورة: ${JSON.stringify(result)}`)

      if (result.success) {
        addLog(`✅ تم إنشاء الفاتورة بنجاح. ID: ${result.data?.id}`)

        // Check if it was saved to localStorage
        const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
        addLog(`📊 عدد الفواتير في localStorage بعد الإنشاء: ${localInvoices.length}`)

        return result.data
      } else {
        addLog(`❌ فشل في إنشاء الفاتورة: ${result.error?.message}`)
        return null
      }
    } catch (error: any) {
      addLog(`❌ خطأ في إنشاء الفاتورة: ${error.message}`)
      addLog(`📋 تفاصيل الخطأ: ${JSON.stringify(error)}`)
      return null
    }
  }

  // Test 6: Test complete transaction
  const testCompleteTransaction = async () => {
    addLog('🔄 اختبار معاملة كاملة...')

    try {
      // Load medicines first
      const medicinesResult = await getMedicines()
      if (!medicinesResult.success || !medicinesResult.data || medicinesResult.data.length === 0) {
        addLog('❌ لا توجد أدوية متاحة للاختبار')
        return null
      }

      const firstMedicine = medicinesResult.data[0]
      const firstBatch = firstMedicine.batches?.[0] || firstMedicine.medicine_batches?.[0]

      if (!firstBatch) {
        addLog('❌ لا توجد دفعات متاحة للاختبار')
        return null
      }

      addLog(`📦 استخدام الدواء: ${firstMedicine.name}`)
      addLog(`📦 استخدام الدفعة: ${firstBatch.batch_code}`)

      const testInvoiceData = {
        invoice_number: `COMPLETE-${Date.now()}`,
        customer_name: 'عميل اختبار كامل',
        total_amount: 1500,
        discount_amount: 0,
        final_amount: 1500,
        payment_method: 'cash',
        payment_status: 'paid',
        notes: 'اختبار معاملة كاملة'
      }

      const testItems = [{
        medicine_batch_id: firstBatch.id,
        quantity: 2,
        unit_price: 750,
        total_price: 1500,
        is_gift: false,
        medicine_name: firstMedicine.name,
        medicineName: firstMedicine.name
      }]

      addLog(`📋 بيانات المعاملة: ${JSON.stringify(testInvoiceData)}`)
      addLog(`📦 عناصر المعاملة: ${JSON.stringify(testItems)}`)

      const result = await completeSalesTransaction(testInvoiceData, testItems)
      addLog(`📋 نتيجة المعاملة الكاملة: ${JSON.stringify(result)}`)

      if (result.success) {
        addLog(`✅ تمت المعاملة الكاملة بنجاح`)

        // Check localStorage after complete transaction
        const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
        const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
        addLog(`📊 عدد الفواتير في localStorage: ${localInvoices.length}`)
        addLog(`📊 عدد العناصر في localStorage: ${localItems.length}`)

        return result.data
      } else {
        addLog(`❌ فشلت المعاملة الكاملة: ${result.error?.message}`)
        return null
      }
    } catch (error: any) {
      addLog(`❌ خطأ في المعاملة الكاملة: ${error.message}`)
      addLog(`📋 تفاصيل الخطأ: ${JSON.stringify(error)}`)
      return null
    }
  }

  // Test 4: Test getting invoices
  const testGetInvoices = async () => {
    addLog('🔄 اختبار استرجاع الفواتير...')
    
    try {
      const result = await getSalesInvoices()
      addLog(`📋 نتيجة استرجاع الفواتير: ${JSON.stringify(result)}`)
      
      if (result.success) {
        addLog(`✅ تم استرجاع ${result.data?.length || 0} فاتورة`)
        
        if (result.data && result.data.length > 0) {
          const firstInvoice = result.data[0]
          addLog(`📋 أول فاتورة: ${JSON.stringify(firstInvoice)}`)
          
          // Check medicine names in items
          const items = firstInvoice.sales_invoice_items || []
          if (items.length > 0) {
            const firstItem = items[0]
            addLog(`📋 أول عنصر: ${JSON.stringify(firstItem)}`)
            
            const medicineName = firstItem.medicine_batches?.medicines?.name || 
                               firstItem.medicine_name || 
                               firstItem.medicineName
            addLog(`🔍 اسم الدواء في أول عنصر: ${medicineName || 'غير متوفر'}`)
          }
        }
        return result.data
      } else {
        addLog(`❌ فشل في استرجاع الفواتير: ${result.error?.message}`)
        return null
      }
    } catch (error: any) {
      addLog(`❌ خطأ في استرجاع الفواتير: ${error.message}`)
      addLog(`📋 تفاصيل الخطأ: ${JSON.stringify(error)}`)
      return null
    }
  }

  // Test 5: Test medicines loading
  const testLoadMedicines = async () => {
    addLog('🔄 اختبار تحميل الأدوية...')
    
    try {
      const result = await getMedicines()
      addLog(`📋 نتيجة تحميل الأدوية: ${JSON.stringify(result)}`)
      
      if (result.success) {
        addLog(`✅ تم تحميل ${result.data?.length || 0} دواء`)
        
        if (result.data && result.data.length > 0) {
          const firstMedicine = result.data[0]
          addLog(`📋 أول دواء: ${JSON.stringify(firstMedicine)}`)
          
          // Check batches
          if (firstMedicine.batches && firstMedicine.batches.length > 0) {
            addLog(`📦 عدد الدفعات للدواء الأول: ${firstMedicine.batches.length}`)
            addLog(`📦 أول دفعة: ${JSON.stringify(firstMedicine.batches[0])}`)
          }
        }
        return result.data
      } else {
        addLog(`❌ فشل في تحميل الأدوية: ${result.error?.message}`)
        return null
      }
    } catch (error: any) {
      addLog(`❌ خطأ في تحميل الأدوية: ${error.message}`)
      addLog(`📋 تفاصيل الخطأ: ${JSON.stringify(error)}`)
      return null
    }
  }

  // Test 7: Test print data
  const testPrintData = async () => {
    addLog('🔄 اختبار بيانات الطباعة...')

    try {
      // Get invoices first
      const invoicesResult = await getSalesInvoices()
      if (!invoicesResult.success || !invoicesResult.data || invoicesResult.data.length === 0) {
        addLog('❌ لا توجد فواتير للاختبار')
        return null
      }

      const firstInvoice = invoicesResult.data[0]
      addLog(`📋 اختبار طباعة الفاتورة: ${firstInvoice.invoice_number}`)

      // Test getSalesInvoiceForPrint
      const printResult = await getSalesInvoiceForPrint(firstInvoice.id)
      addLog(`📋 نتيجة استرجاع بيانات الطباعة: ${JSON.stringify(printResult)}`)

      if (printResult.success && printResult.data) {
        const invoice = printResult.data
        addLog(`📋 بيانات الفاتورة للطباعة: ${JSON.stringify(invoice)}`)

        // Check medicine names in items
        const items = invoice.sales_invoice_items || []
        addLog(`📦 عدد العناصر: ${items.length}`)

        items.forEach((item: any, index: number) => {
          const medicineName = item.medicine_batches?.medicines?.name ||
                             item.medicine_name ||
                             item.medicineName ||
                             'غير متوفر'
          addLog(`📦 العنصر ${index + 1}: ${medicineName}`)
          addLog(`📦 تفاصيل العنصر ${index + 1}: ${JSON.stringify(item)}`)
        })

        return invoice
      } else {
        addLog(`❌ فشل في استرجاع بيانات الطباعة: ${printResult.error?.message}`)
        return null
      }
    } catch (error: any) {
      addLog(`❌ خطأ في اختبار الطباعة: ${error.message}`)
      addLog(`📋 تفاصيل الخطأ: ${JSON.stringify(error)}`)
      return null
    }
  }

  // Test 0: Initialize system data
  const testInitializeData = async () => {
    addLog('🔄 اختبار تهيئة البيانات...')

    try {
      const result = await initializeSystemData()
      addLog(`📋 نتيجة التهيئة: ${JSON.stringify(result)}`)

      if (result.success) {
        addLog(`✅ تمت تهيئة البيانات بنجاح`)

        // Check what was created
        const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
        const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')
        const customers = JSON.parse(localStorage.getItem('customers') || '[]')

        addLog(`📊 الأدوية: ${medicines.length}`)
        addLog(`📊 الدفعات: ${batches.length}`)
        addLog(`📊 العملاء: ${customers.length}`)

        return result.data
      } else {
        addLog(`❌ فشلت تهيئة البيانات: ${result.error?.message}`)
        return null
      }
    } catch (error: any) {
      addLog(`❌ خطأ في تهيئة البيانات: ${error.message}`)
      return null
    }
  }

  // Run all tests
  const runAllTests = async () => {
    setLoading(true)
    clearLogs()

    addLog('🚀 بدء التشخيص الشامل...')

    // Test 0: Initialize Data
    await testInitializeData()

    // Test 1: Supabase Connection
    await testSupabaseConnection()

    // Test 2: LocalStorage Data
    checkLocalStorageData()

    // Test 3: Load Medicines
    await testLoadMedicines()

    // Test 4: Create Invoice
    await testCreateInvoice()

    // Test 5: Get Invoices
    await testGetInvoices()

    // Test 6: Complete Transaction
    await testCompleteTransaction()

    // Test 7: Print Data
    await testPrintData()

    addLog('🎉 انتهى التشخيص الشامل!')

    setLoading(false)
  }

  return (
    <div className="container mx-auto p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-center">🔍 صفحة التشخيص</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Controls */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold mb-4">أدوات التشخيص</h2>
            
            <div className="space-y-3">
              <button
                onClick={runAllTests}
                disabled={loading}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                {loading ? 'جاري التشخيص...' : '🚀 تشخيص شامل'}
              </button>

              <button
                onClick={testInitializeData}
                disabled={loading}
                className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                🔧 تهيئة البيانات
              </button>
              
              <button
                onClick={testSupabaseConnection}
                disabled={loading}
                className="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                🔗 اختبار Supabase
              </button>
              
              <button
                onClick={checkLocalStorageData}
                disabled={loading}
                className="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                💾 فحص localStorage
              </button>
              
              <button
                onClick={testLoadMedicines}
                disabled={loading}
                className="w-full bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                💊 اختبار الأدوية
              </button>

              <button
                onClick={testCompleteTransaction}
                disabled={loading}
                className="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                🔄 اختبار معاملة كاملة
              </button>

              <button
                onClick={testPrintData}
                disabled={loading}
                className="w-full bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                🖨️ اختبار بيانات الطباعة
              </button>

              <button
                onClick={() => {
                  const keys = ['sales_invoices', 'sales_invoice_items', 'medicines', 'medicine_batches', 'customers']
                  keys.forEach(key => localStorage.removeItem(key))
                  addLog('🗑️ تم مسح جميع البيانات من localStorage')
                }}
                className="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg"
              >
                🗑️ مسح localStorage
              </button>

              <button
                onClick={clearLogs}
                className="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
              >
                🗑️ مسح السجل
              </button>
            </div>
            
            <div className="mt-6">
              <a 
                href="/sales" 
                className="block w-full text-center bg-gray-700 hover:bg-gray-800 text-white px-4 py-2 rounded-lg"
              >
                ← العودة إلى المبيعات
              </a>
            </div>
          </div>
          
          {/* Logs */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold mb-4">سجل التشخيص</h2>
            
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
              {logs.length === 0 ? (
                <p className="text-gray-500">لم يتم تشغيل أي اختبار بعد...</p>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
