(()=>{var a={};a.id=573,a.ids=[573],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},12640:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23303:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>D});var d=c(60687),e=c(43210),f=c(21979),g=c(25541),h=c(12640),i=c(71444),j=c(96474),k=c(35583),l=c(23928),m=c(10022),n=c(99270),o=c(78122),p=c(28561),q=c(19080),r=c(13861),s=c(80462),t=c(62688);let u=(0,t.A)("banknote",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M6 12h.01M18 12h.01",key:"113zkx"}]]),v=(0,t.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var w=c(40228),x=c(41312),y=c(5336),z=c(79410),A=c(84997),B=c(97711),C=c(31836);function D(){let[a,b]=(0,e.useState)([]),[c,t]=(0,e.useState)([]),[D,E]=(0,e.useState)(0),[F,G]=(0,e.useState)([]),[H,I]=(0,e.useState)([]),[J,K]=(0,e.useState)([]),[L,M]=(0,e.useState)([]),[N,O]=(0,e.useState)([]),[P,Q]=(0,e.useState)(""),[R,S]=(0,e.useState)("all"),[T,U]=(0,e.useState)(""),[V,W]=(0,e.useState)(!1),[X,Y]=(0,e.useState)(!1),[Z,$]=(0,e.useState)("invoices"),[_,aa]=(0,e.useState)(!1),[ab,ac]=(0,e.useState)(null),{settings:ad}=(0,B.usePrintSettings)(),[ae,af]=(0,e.useState)({transaction_type:"expense",category:"",amount:0,description:"",payment_method:"cash",notes:""}),ag=async()=>{console.log("Loading cash box data..."),Y(!0);try{await (0,C.initializeSystemData)();let[a,c,d,e]=await Promise.all([(0,C.getCashTransactions)(),(0,C.getCashBalance)(),(0,C.getCustomerDebts)(),(0,C.getSupplierDebts)()]);console.log("Cash box data results:",{transactions:a,balance:c,customerDebts:d,supplierDebts:e}),a.success&&(console.log("Setting transactions:",a.data?.length||0,"items"),b(a.data||[])),c.success&&(console.log("Setting cash balance:",c.data||0),E(c.data||0)),d.success&&G(d.data||[]),e.success&&I(e.data||[]),await ah()}catch(a){console.error("Error loading cash box data:",a)}finally{Y(!1)}},ah=async()=>{try{let a=JSON.parse(localStorage.getItem("sales_invoices")||"[]");K(a);let b=JSON.parse(localStorage.getItem("purchase_invoices")||"[]");M(b),console.log(`Loaded ${a.length} sales invoices and ${b.length} purchase invoices`)}catch(a){console.error("Error loading invoices:",a)}},ai=async()=>{if(!ae.category||!ae.description||ae.amount<=0)return void alert("يرجى ملء جميع الحقول المطلوبة");Y(!0);try{console.log("Adding cash transaction:",ae);let a=await (0,C.addCashTransaction)(ae);console.log("Add transaction result:",a),a.success?(alert("تم إضافة المعاملة بنجاح!"),W(!1),af({transaction_type:"expense",category:"",amount:0,description:"",payment_method:"cash",notes:""}),console.log("Reloading data after successful transaction..."),await ag()):(console.error("Failed to add transaction:",a),alert("حدث خطأ أثناء إضافة المعاملة"))}catch(a){console.error("Error adding transaction:",a),alert("حدث خطأ أثناء إضافة المعاملة")}finally{Y(!1)}},aj=async(a,b,c)=>{if(confirm(`هل تريد تسديد هذا الدين بمبلغ ${c.toLocaleString()} د.ع؟`)){Y(!0);try{(await (0,C.updatePaymentStatus)(a,b,"paid",c)).success?(alert("تم تسديد الدين بنجاح!"),await ag()):alert("حدث خطأ أثناء تسديد الدين")}catch(a){console.error("Error paying debt:",a),alert("حدث خطأ أثناء تسديد الدين")}finally{Y(!1)}}},ak=async a=>{try{let b="customer_name"in a?"sales":"purchases",c=await al(a,b);(0,B.printInvoice)(c,b,ad)}catch(c){console.error("Error printing invoice:",c);let b="customer_name"in a?"sales":"purchases";(0,B.printInvoice)(a,b,ad)}},al=async(a,b)=>{try{let c=JSON.parse(localStorage.getItem("sales"===b?"sales_invoice_items":"purchase_invoice_items")||"[]"),d=JSON.parse(localStorage.getItem("medicines")||"[]"),e=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),f=c.filter(b=>b.invoice_id===a.id).map(a=>{let b=e.find(b=>b.id===a.medicine_batch_id),c=d.find(a=>a.id===b?.medicine_id),f=c?.name||a.medicine_name||a.medicineName||"غير محدد";return{...a,medicine_name:f,medicineName:f,medicine_batches:{batch_code:b?.batch_code||"",expiry_date:b?.expiry_date||"",medicines:{name:f,category:c?.category||"",manufacturer:c?.manufacturer||"",strength:c?.strength||"",form:c?.form||""}}}});return{...a,["sales"===b?"sales_invoice_items":"purchase_invoice_items"]:f}}catch(b){return console.error("Error enhancing invoice:",b),a}},am=a=>"income"===a?"text-green-600":"text-red-600",an=()=>a.filter(a=>"income"===a.transaction_type).reduce((a,b)=>a+b.amount,0),ao=()=>a.filter(a=>"expense"===a.transaction_type).reduce((a,b)=>a+b.amount,0),ap=["رواتب","إيجار","كهرباء","ماء","هاتف وإنترنت","صيانة","مواد تنظيف","قرطاسية","مواصلات","ضرائب ورسوم","تأمين","دعاية وإعلان","أخرى"],aq=["مبيعات","خدمات","استشارات","أخرى"];return(0,d.jsxs)(f.A,{children:[(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة الصندوق"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"متابعة الواردات والمصروفات والمديونيات"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:()=>{c.length>0?aa(!0):alert("لا توجد معاملات للطباعة")},disabled:0===c.length,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),"معاينة وطباعة"]}),(0,d.jsxs)("button",{onClick:()=>{c.length>0?(0,B.printReport)(c,"cashbox","تقرير الصندوق",ad):alert("لا توجد معاملات للطباعة")},disabled:0===c.length,className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),"طباعة مباشرة"]}),(0,d.jsxs)("button",{onClick:()=>W(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),"إضافة معاملة"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"رصيد الصندوق"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-blue-600",children:[D.toLocaleString()," د.ع"]})]}),(0,d.jsx)(k.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الواردات"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[an().toLocaleString()," د.ع"]})]}),(0,d.jsx)(g.A,{className:"h-8 w-8 text-green-600"})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي المصروفات"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:[ao().toLocaleString()," د.ع"]})]}),(0,d.jsx)(h.A,{className:"h-8 w-8 text-red-600"})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"صافي الربح"}),(0,d.jsxs)("p",{className:`text-2xl font-bold ${an()-ao()>=0?"text-green-600":"text-red-600"}`,children:[(an()-ao()).toLocaleString()," د.ع"]})]}),(0,d.jsx)(l.A,{className:"h-8 w-8 text-gray-600"})]})})]}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsxs)("nav",{className:"flex space-x-8 px-6",children:[(0,d.jsx)("button",{onClick:()=>$("invoices"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"invoices"===Z?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"جميع الفواتير"]})}),(0,d.jsx)("button",{onClick:()=>$("transactions"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"transactions"===Z?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"المعاملات المالية"}),(0,d.jsx)("button",{onClick:()=>$("debts"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"debts"===Z?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"المديونيات"})]})})}),"invoices"===Z&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البحث"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",value:P,onChange:a=>Q(a.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"البحث في الفواتير..."})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"نوع الفاتورة"}),(0,d.jsxs)("select",{value:R,onChange:a=>S(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الفواتير"}),(0,d.jsx)("option",{value:"sales",children:"فواتير المبيعات"}),(0,d.jsx)("option",{value:"purchases",children:"فواتير المشتريات"})]})]}),(0,d.jsx)("div",{className:"flex items-end",children:(0,d.jsxs)("button",{onClick:()=>ah(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),"تحديث"]})})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"فواتير المبيعات"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-green-600",children:J.length})]}),(0,d.jsx)(p.A,{className:"h-8 w-8 text-green-600"})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"فواتير المشتريات"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:L.length})]}),(0,d.jsx)(q.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الفواتير"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:N.length})]}),(0,d.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"قائمة الفواتير"})}),(0,d.jsxs)("div",{className:"overflow-x-auto",children:[(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم الفاتورة"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النوع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"العميل/المورد"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ النهائي"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"حالة الدفع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:a.invoice_number}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,d.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"sales"===a.type?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:"sales"===a.type?"مبيعات":"مشتريات"})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.customer_name||a.supplier_name}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[a.final_amount?.toLocaleString()," د.ع"]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,d.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"paid"===a.payment_status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"paid"===a.payment_status?"مدفوع":"معلق"})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString("ar-EG")}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>{ac(a),aa(!0)},className:"text-blue-600 hover:text-blue-900",title:"عرض التفاصيل",children:(0,d.jsx)(r.A,{className:"h-4 w-4"})}),(0,d.jsx)("button",{onClick:()=>ak(a),className:"text-green-600 hover:text-green-900",title:"طباعة",children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})]})})]},a.id))})]}),0===N.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(m.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"لا توجد فواتير"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"لم يتم العثور على فواتير تطابق معايير البحث."})]})]})]})]}),"transactions"===Z&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البحث"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",value:P,onChange:a=>Q(a.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"البحث في المعاملات..."})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"نوع المعاملة"}),(0,d.jsxs)("select",{value:R,onChange:a=>S(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع المعاملات"}),(0,d.jsx)("option",{value:"income",children:"واردات"}),(0,d.jsx)("option",{value:"expense",children:"مصروفات"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الفئة"}),(0,d.jsxs)("select",{value:T,onChange:a=>U(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"",children:"جميع الفئات"}),[...new Set([...ap,...aq])].map((a,b)=>(0,d.jsx)("option",{value:a,children:a},`filter_category_${b}_${a}`))]})]}),(0,d.jsx)("div",{className:"flex items-end",children:(0,d.jsxs)("button",{onClick:()=>{Q(""),S("all"),U("")},className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2",children:[(0,d.jsx)(s.A,{className:"h-4 w-4"}),"إعادة تعيين"]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===c.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(k.A,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد معاملات"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"لم يتم تسجيل أي معاملات مالية بعد"}),(0,d.jsx)("button",{onClick:()=>W(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"إضافة أول معاملة"})]}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النوع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الفئة"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الوصف"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"طريقة الدفع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:["income"===a.transaction_type?(0,d.jsx)(g.A,{className:"h-4 w-4 text-green-500"}):(0,d.jsx)(h.A,{className:"h-4 w-4 text-red-500"}),(0,d.jsx)("span",{className:`mr-2 text-sm font-medium ${am(a.transaction_type)}`,children:"income"===a.transaction_type?"وارد":"مصروف"})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:"text-sm text-gray-900",children:a.category})}),(0,d.jsxs)("td",{className:"px-6 py-4",children:[(0,d.jsx)("span",{className:"text-sm text-gray-900",children:a.description}),a.notes&&(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a.notes})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,d.jsxs)("span",{className:`text-sm font-medium ${am(a.transaction_type)}`,children:["income"===a.transaction_type?"+":"-",a.amount.toLocaleString()," د.ع"]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:["cash"===a.payment_method?(0,d.jsx)(u,{className:"h-4 w-4 text-green-500 mr-1"}):(0,d.jsx)(v,{className:"h-4 w-4 text-blue-500 mr-1"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:"cash"===a.payment_method?"نقداً":"بنكي"})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(w.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:new Date(a.created_at).toLocaleDateString("ar-EG")})]})})]},a.id))})]})})})]}),"debts"===Z&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"ديون العملاء"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["إجمالي: ",F.reduce((a,b)=>a+b.final_amount,0).toLocaleString()," د.ع"]})]})]}),0===F.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(y.A,{className:"h-12 w-12 text-green-300 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500",children:"لا توجد ديون على العملاء"})]}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"رقم الفاتورة"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"العميل"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المبلغ"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"التاريخ"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"إجراءات"})]})}),(0,d.jsx)("tbody",{className:"divide-y divide-gray-200",children:F.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:a.invoice_number}),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:a.customers?.name||a.customer_name}),(0,d.jsxs)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium text-red-600",children:[a.final_amount.toLocaleString()," د.ع"]}),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString("ar-EG")}),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,d.jsx)("button",{onClick:()=>aj("sales",a.id,a.final_amount),className:"bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700",children:"تسديد"})})]},a.id))})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"ديون الموردين"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(z.A,{className:"h-5 w-5 text-orange-600"}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["إجمالي: ",H.reduce((a,b)=>a+b.final_amount,0).toLocaleString()," د.ع"]})]})]}),0===H.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(y.A,{className:"h-12 w-12 text-green-300 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500",children:"لا توجد ديون للموردين"})]}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"رقم الفاتورة"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المورد"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المبلغ"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"التاريخ"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"إجراءات"})]})}),(0,d.jsx)("tbody",{className:"divide-y divide-gray-200",children:H.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:a.invoice_number}),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:a.suppliers?.name}),(0,d.jsxs)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium text-orange-600",children:[a.final_amount.toLocaleString()," د.ع"]}),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString("ar-EG")}),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,d.jsx)("button",{onClick:()=>aj("purchase",a.id,a.final_amount),className:"bg-orange-600 text-white px-3 py-1 rounded text-xs hover:bg-orange-700",children:"دفع"})})]},a.id))})]})})]})]}),V&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"إضافة معاملة جديدة"}),(0,d.jsx)("button",{onClick:()=>W(!1),className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"نوع المعاملة"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)("button",{onClick:()=>af({...ae,transaction_type:"income"}),className:`p-3 border-2 rounded-lg text-center transition-colors ${"income"===ae.transaction_type?"border-green-500 bg-green-50 text-green-700":"border-gray-300 text-gray-600 hover:border-green-300"}`,children:[(0,d.jsx)(g.A,{className:"h-5 w-5 mx-auto mb-1"}),(0,d.jsx)("div",{className:"text-sm font-medium",children:"وارد"})]}),(0,d.jsxs)("button",{onClick:()=>af({...ae,transaction_type:"expense"}),className:`p-3 border-2 rounded-lg text-center transition-colors ${"expense"===ae.transaction_type?"border-red-500 bg-red-50 text-red-700":"border-gray-300 text-gray-600 hover:border-red-300"}`,children:[(0,d.jsx)(h.A,{className:"h-5 w-5 mx-auto mb-1"}),(0,d.jsx)("div",{className:"text-sm font-medium",children:"مصروف"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الفئة *"}),(0,d.jsxs)("select",{value:ae.category,onChange:a=>af({...ae,category:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"",children:"اختر الفئة"}),("expense"===ae.transaction_type?ap:aq).map((a,b)=>(0,d.jsx)("option",{value:a,children:a},`new_transaction_category_${b}_${a}`))]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"المبلغ *"}),(0,d.jsx)("input",{type:"number",value:ae.amount,onChange:a=>af({...ae,amount:Number(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0",min:"0",step:"0.01"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الوصف *"}),(0,d.jsx)("input",{type:"text",value:ae.description,onChange:a=>af({...ae,description:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"وصف المعاملة"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"طريقة الدفع"}),(0,d.jsxs)("select",{value:ae.payment_method,onChange:a=>af({...ae,payment_method:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"cash",children:"نقداً"}),(0,d.jsx)("option",{value:"bank",children:"بنكي"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات"}),(0,d.jsx)("textarea",{value:ae.notes,onChange:a=>af({...ae,notes:a.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ملاحظات إضافية..."})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,d.jsx)("button",{onClick:()=>W(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"إلغاء"}),(0,d.jsx)("button",{onClick:ai,disabled:X||!ae.category||!ae.description||ae.amount<=0,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed",children:X?"جاري الإضافة...":"إضافة المعاملة"})]})]})})]}),_&&!ab&&c.length>0&&(0,d.jsx)(A.Ay,{title:"تقرير الصندوق",data:c,type:"report",settings:ad,onClose:()=>aa(!1),children:(0,d.jsx)(A.W1,{reportData:c,reportType:"cashbox",title:"تقرير الصندوق",settings:ad})}),_&&ab&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["معاينة الفاتورة - ",ab.invoice_number]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("button",{onClick:()=>ak(ab),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),"طباعة"]}),(0,d.jsx)("button",{onClick:()=>{aa(!1),ac(null)},className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]})]}),(0,d.jsx)("div",{className:"border border-gray-200 rounded-lg p-6 bg-gray-50",children:(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"صيدلية الشفاء"}),(0,d.jsxs)("p",{className:"text-gray-600",children:["فاتورة ","customer_name"in ab?"مبيعات":"مشتريات"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"معلومات الفاتورة"}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"رقم الفاتورة:"})," ",ab.invoice_number]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"التاريخ:"})," ",new Date(ab.created_at).toLocaleDateString("ar-EG")]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"طريقة الدفع:"})," ","cash"===ab.payment_method?"نقداً":"آجل"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"حالة الدفع:"})," ","paid"===ab.payment_status?"مدفوع":"معلق"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"customer_name"in ab?"معلومات العميل":"معلومات المورد"}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"الاسم:"})," ","customer_name"in ab?ab.customer_name:ab.supplier_name]})]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"تفاصيل الفاتورة"}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full border border-gray-300",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"الدواء"}),(0,d.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"الكمية"}),(0,d.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"السعر"}),(0,d.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"المجموع"})]})}),(0,d.jsx)("tbody",{children:(0,d.jsx)("tr",{children:(0,d.jsx)("td",{className:"border border-gray-300 px-4 py-2",colSpan:4,children:(0,d.jsx)("div",{className:"text-center text-gray-500 py-4",children:"لا توجد تفاصيل متاحة للعرض"})})})})]})})]}),(0,d.jsx)("div",{className:"border-t pt-4",children:(0,d.jsx)("div",{className:"flex justify-end",children:(0,d.jsxs)("div",{className:"w-64",children:[(0,d.jsxs)("div",{className:"flex justify-between py-2",children:[(0,d.jsx)("span",{children:"المجموع الفرعي:"}),(0,d.jsxs)("span",{children:[ab.total_amount?.toLocaleString()," د.ع"]})]}),(0,d.jsxs)("div",{className:"flex justify-between py-2",children:[(0,d.jsx)("span",{children:"الخصم:"}),(0,d.jsxs)("span",{children:[ab.discount_amount?.toLocaleString()," د.ع"]})]}),(0,d.jsxs)("div",{className:"flex justify-between py-2 border-t font-bold",children:[(0,d.jsx)("span",{children:"المجموع النهائي:"}),(0,d.jsxs)("span",{children:[ab.final_amount?.toLocaleString()," د.ع"]})]})]})})}),ab.notes&&(0,d.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"ملاحظات:"}),(0,d.jsx)("p",{className:"text-gray-700",children:ab.notes})]})]})})]})})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},52223:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\cashbox\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\cashbox\\page.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},57267:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["cashbox",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,52223)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\cashbox\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\cashbox\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/cashbox/page",pathname:"/cashbox",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/cashbox/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67015:(a,b,c)=>{Promise.resolve().then(c.bind(c,52223))},74075:a=>{"use strict";a.exports=require("zlib")},79410:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},80462:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97279:(a,b,c)=>{Promise.resolve().then(c.bind(c,23303))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,463,314,979,31,711,997],()=>b(b.s=57267));module.exports=c})();