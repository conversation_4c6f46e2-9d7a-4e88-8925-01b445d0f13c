exports.id=31,exports.ids=[31],exports.modules={16391:(a,b,c)=>{"use strict";c.d(b,{N:()=>d});let d=(0,c(60463).UU)("https://tazhdabhidycvsvjxqcb.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRhemhkYWJoaWR5Y3Zzdmp4cWNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMzU0NzYsImV4cCI6MjA2ODgxMTQ3Nn0.0nxvya6ZMm-22SgiqCrxxjfJyNK-Zs4vkAPAaPlAQEE")},31836:(a,b,c)=>{"use strict";c.r(b),c.d(b,{addCashTransaction:()=>J,addCustomer:()=>u,addInventoryMovement:()=>s,addMedicine:()=>e,addMedicineBatch:()=>j,addPurchaseInvoiceItems:()=>r,addReturnItems:()=>E,addSalesInvoiceItems:()=>m,addSupplier:()=>w,completePurchaseTransaction:()=>I,completeSalesTransaction:()=>x,createPurchaseInvoice:()=>q,createPurchaseReturn:()=>D,createSalesInvoice:()=>l,createSalesReturn:()=>C,fixLocalStorageInvoiceItems:()=>o,getCashBalance:()=>L,getCashTransactions:()=>K,getCustomerDebts:()=>M,getCustomerStatement:()=>R,getCustomers:()=>t,getInventoryReport:()=>U,getMedicineMovementReport:()=>T,getMedicineNameFromBatch:()=>p,getMedicines:()=>f,getPurchaseInvoiceForPrint:()=>A,getPurchaseInvoices:()=>B,getPurchasesReport:()=>Q,getReturnById:()=>H,getReturnForPrint:()=>W,getReturns:()=>G,getSalesInvoiceForPrint:()=>z,getSalesInvoices:()=>y,getSalesReport:()=>P,getSupplierDebts:()=>N,getSupplierStatement:()=>S,getSuppliers:()=>v,initializeSystemData:()=>i,processReturn:()=>F,updateBatchQuantity:()=>k,updatePaymentStatus:()=>O,updateReturn:()=>X,updateReturnStatus:()=>V});var d=c(16391);let e=async a=>{try{let{data:b,error:c}=await d.N.from("medicines").insert([{name:a.name,category:a.category,manufacturer:a.manufacturer||"",active_ingredient:a.active_ingredient||"",strength:a.strength||"",form:a.form,unit_price:a.unit_price,selling_price:a.selling_price}]).select().single();if(c)throw c;return{success:!0,data:b}}catch(a){return console.error("Error adding medicine:",a),{success:!1,error:a}}},f=async()=>{try{let{data:a,error:b}=await d.N.from("medicines").select(`
        *,
        medicine_batches (
          id,
          batch_code,
          expiry_date,
          quantity,
          cost_price,
          selling_price,
          supplier_id,
          received_date
        )
      `).order("name");if(b)return console.warn("Supabase error fetching medicines, using localStorage:",b),g();return{success:!0,data:a}}catch(a){return console.error("Error fetching medicines:",a),g()}},g=()=>{try{let a=JSON.parse(localStorage.getItem("medicines")||"[]"),b=JSON.parse(localStorage.getItem("medicine_batches")||"[]");if(0===a.length)return console.log("\uD83D\uDD04 لا توجد أدوية في localStorage، إنشاء بيانات تجريبية..."),h();let c=a.map(a=>({...a,medicine_batches:b.filter(b=>b.medicine_id===a.id),batches:b.filter(b=>b.medicine_id===a.id)}));return console.log(`✅ تم تحميل ${c.length} دواء من localStorage`),{success:!0,data:c}}catch(a){return console.error("Error loading medicines from localStorage:",a),{success:!1,error:a}}},h=()=>{try{let a=[{id:"med_1",name:"باراسيتامول 500 مجم",category:"مسكنات",manufacturer:"شركة الأدوية العراقية",strength:"500mg",form:"أقراص",created_at:new Date().toISOString()},{id:"med_2",name:"أموكسيسيلين 250 مجم",category:"مضادات حيوية",manufacturer:"شركة بغداد للأدوية",strength:"250mg",form:"كبسولات",created_at:new Date().toISOString()},{id:"med_3",name:"أسبرين 100 مجم",category:"مسكنات",manufacturer:"شركة النهرين",strength:"100mg",form:"أقراص",created_at:new Date().toISOString()},{id:"med_4",name:"إيبوبروفين 400 مجم",category:"مسكنات",manufacturer:"شركة الرافدين",strength:"400mg",form:"أقراص",created_at:new Date().toISOString()},{id:"med_5",name:"أوميبرازول 20 مجم",category:"أدوية المعدة",manufacturer:"شركة دجلة",strength:"20mg",form:"كبسولات",created_at:new Date().toISOString()}],b=[{id:"batch_1",medicine_id:"med_1",batch_code:"PAR001",expiry_date:"2025-12-31",quantity:100,cost_price:500,selling_price:750,received_date:"2024-01-01",created_at:new Date().toISOString()},{id:"batch_2",medicine_id:"med_2",batch_code:"AMX001",expiry_date:"2025-06-30",quantity:50,cost_price:1e3,selling_price:1500,received_date:"2024-01-01",created_at:new Date().toISOString()},{id:"batch_3",medicine_id:"med_3",batch_code:"ASP001",expiry_date:"2026-03-31",quantity:200,cost_price:300,selling_price:500,received_date:"2024-01-01",created_at:new Date().toISOString()},{id:"batch_4",medicine_id:"med_4",batch_code:"IBU001",expiry_date:"2025-09-30",quantity:75,cost_price:800,selling_price:1200,received_date:"2024-01-01",created_at:new Date().toISOString()},{id:"batch_5",medicine_id:"med_5",batch_code:"OME001",expiry_date:"2025-11-30",quantity:30,cost_price:1500,selling_price:2e3,received_date:"2024-01-01",created_at:new Date().toISOString()}],c=[{id:"cust_1",name:"أحمد محمد علي",phone:"07701234567",address:"بغداد - الكرادة",created_at:new Date().toISOString()},{id:"cust_2",name:"فاطمة حسن",phone:"07809876543",address:"بغداد - الجادرية",created_at:new Date().toISOString()}];localStorage.setItem("medicines",JSON.stringify(a)),localStorage.setItem("medicine_batches",JSON.stringify(b)),localStorage.setItem("customers",JSON.stringify(c)),localStorage.setItem("sales_invoices",JSON.stringify([])),localStorage.setItem("sales_invoice_items",JSON.stringify([]));let d=a.map(a=>({...a,medicine_batches:b.filter(b=>b.medicine_id===a.id),batches:b.filter(b=>b.medicine_id===a.id)}));return console.log(`✅ تم إنشاء ${d.length} دواء تجريبي`),console.log(`✅ تم إنشاء ${b.length} دفعة تجريبية`),console.log(`✅ تم إنشاء ${c.length} عميل تجريبي`),{success:!0,data:d}}catch(a){return console.error("Error creating sample medicines:",a),{success:!1,error:a}}},i=async()=>{try{console.log("\uD83D\uDD04 تهيئة بيانات النظام...");let a=JSON.parse(localStorage.getItem("medicines")||"[]"),b=JSON.parse(localStorage.getItem("medicine_batches")||"[]");if(0===a.length||0===b.length)return console.log("\uD83D\uDCE6 إنشاء البيانات الأساسية..."),h();return console.log(`✅ البيانات الأساسية موجودة: ${a.length} دواء، ${b.length} دفعة`),{success:!0,data:a}}catch(a){return console.error("Error initializing system data:",a),{success:!1,error:a}}},j=async a=>{try{let{data:b,error:c}=await d.N.from("medicine_batches").insert([a]).select().single();if(c)throw c;return{success:!0,data:b}}catch(a){return console.error("Error adding medicine batch:",a),{success:!1,error:a}}},k=async(a,b)=>{try{let{data:c,error:e}=await d.N.from("medicine_batches").update({quantity:b}).eq("id",a).select().single();if(e){console.warn("Supabase error updating batch quantity, using localStorage:",e);let c=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),d=c.findIndex(b=>b.id===a);if(-1!==d)return c[d].quantity=b,localStorage.setItem("medicine_batches",JSON.stringify(c)),{success:!0,data:c[d]};return{success:!1,error:"Batch not found in localStorage"}}return{success:!0,data:c}}catch(c){console.error("Error updating batch quantity:",c);try{let c=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),d=c.findIndex(b=>b.id===a);if(-1!==d)return c[d].quantity=b,localStorage.setItem("medicine_batches",JSON.stringify(c)),{success:!0,data:c[d]};return{success:!1,error:"Batch not found"}}catch(a){return console.error("LocalStorage fallback failed for batch update:",a),{success:!1,error:a}}}},l=async a=>{try{let{data:b,error:c}=await d.N.from("sales_invoices").insert([a]).select().single();if(c){console.warn("Supabase error, using localStorage:",c);let b={id:`invoice_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,created_at:new Date().toISOString()},d=JSON.parse(localStorage.getItem("sales_invoices")||"[]");return d.push(b),localStorage.setItem("sales_invoices",JSON.stringify(d)),{success:!0,data:b}}return{success:!0,data:b}}catch(b){console.error("Error creating sales invoice:",b);try{let b={id:`invoice_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,created_at:new Date().toISOString()},c=JSON.parse(localStorage.getItem("sales_invoices")||"[]");return c.push(b),localStorage.setItem("sales_invoices",JSON.stringify(c)),{success:!0,data:b}}catch(a){return console.error("LocalStorage fallback failed:",a),{success:!1,error:a}}}},m=async a=>{try{let{data:b,error:c}=await d.N.from("sales_invoice_items").insert(a).select();if(c){console.warn("Supabase error for invoice items, using localStorage:",c),console.log("\uD83D\uDCE6 العناصر الواردة للحفظ:",a);let b=a.map(a=>{let b=a.medicine_name||a.medicineName;return b&&"غير محدد"!==b?(console.log(`✅ استخدام اسم الدواء الموجود: ${b}`),{id:`item_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,medicine_name:b,medicineName:b,medicine_batches:{batch_code:"",expiry_date:"",medicines:{name:b,category:"",manufacturer:"",strength:"",form:""}},created_at:new Date().toISOString()}):(console.log(`⚠️ لا يوجد اسم دواء، سيتم البحث عنه...`),a)}),d=b.filter(a=>!a.medicine_name||"غير محدد"===a.medicine_name),e=b;if(d.length>0){console.log(`🔍 تحسين ${d.length} عنصر يحتاج أسماء أدوية`);let a=await n(d);e=b.map(b=>(!b.medicine_name||"غير محدد"===b.medicine_name)&&a.find(a=>a.medicine_batch_id===b.medicine_batch_id)||b)}let f=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]");return f.push(...e),localStorage.setItem("sales_invoice_items",JSON.stringify(f)),console.log("✅ تم حفظ العناصر في localStorage:",e),{success:!0,data:e}}return{success:!0,data:b}}catch(b){console.error("Error adding sales invoice items:",b);try{console.log("\uD83D\uDD04 Final fallback - حفظ العناصر مع الأسماء الموجودة");let b=a.map(a=>{let b=a.medicine_name||a.medicineName||"غير محدد";return{id:`item_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,medicine_name:b,medicineName:b,medicine_batches:{batch_code:"",expiry_date:"",medicines:{name:b,category:"",manufacturer:"",strength:"",form:""}},created_at:new Date().toISOString()}}),c=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]");return c.push(...b),localStorage.setItem("sales_invoice_items",JSON.stringify(c)),console.log("✅ Final fallback - تم حفظ العناصر:",b),{success:!0,data:b}}catch(a){return console.error("LocalStorage fallback failed for items:",a),{success:!1,error:a}}}},n=async a=>{try{let b=JSON.parse(localStorage.getItem("medicines")||"[]"),c=JSON.parse(localStorage.getItem("medicine_batches")||"[]");return a.map(a=>{let d=a.medicine_name||a.medicineName;if(!d||"غير محدد"===d){let e=c.find(b=>b.id===a.medicine_batch_id),f=b.find(a=>a.id===e?.medicine_id);d=f?.name||"غير محدد"}let e=c.find(b=>b.id===a.medicine_batch_id),f=b.find(a=>a.id===e?.medicine_id);return{id:`item_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,medicine_name:d,medicineName:d,medicine_batches:{batch_code:e?.batch_code||"",expiry_date:e?.expiry_date||"",medicines:{name:d,category:f?.category||"",manufacturer:f?.manufacturer||"",strength:f?.strength||"",form:f?.form||""}},created_at:new Date().toISOString()}})}catch(b){return console.error("خطأ في تحسين العناصر بأسماء الأدوية:",b),a.map(a=>({id:`item_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,medicine_name:"غير محدد",created_at:new Date().toISOString()}))}},o=()=>{try{console.log("\uD83D\uDD27 بدء إصلاح بيانات الفواتير في localStorage...");let a=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),b=JSON.parse(localStorage.getItem("medicines")||"[]"),c=JSON.parse(localStorage.getItem("medicine_batches")||"[]");console.log(`📦 عدد عناصر الفواتير: ${a.length}`),console.log(`💊 عدد الأدوية: ${b.length}`),console.log(`📋 عدد الدفعات: ${c.length}`);let d=0,e=0,f=a.map(a=>{if(a.medicine_batches?.medicines?.name&&"غير محدد"!==a.medicine_batches.medicines.name)return a;let f=c.find(b=>b.id===a.medicine_batch_id),g=b.find(a=>a.id===f?.medicine_id);if(g?.name)return d++,console.log(`✅ إصلاح العنصر: ${g.name} (Batch: ${f?.batch_code})`),{...a,medicine_name:g.name,medicineName:g.name,medicine_batches:{batch_code:f?.batch_code||a.batch_code||"",expiry_date:f?.expiry_date||a.expiry_date||"",medicines:{name:g.name,category:g.category||"",manufacturer:g.manufacturer||"",strength:g.strength||"",form:g.form||""}}};{e++,console.log(`⚠️ لم يتم العثور على الدواء للعنصر: ${a.medicine_batch_id}`);let b=a.medicine_name||a.medicineName||"غير محدد";return{...a,medicine_name:b,medicineName:b,medicine_batches:{batch_code:f?.batch_code||a.batch_code||"",expiry_date:f?.expiry_date||a.expiry_date||"",medicines:{name:b,category:"",manufacturer:"",strength:"",form:""}}}}});return localStorage.setItem("sales_invoice_items",JSON.stringify(f)),console.log(`✅ تم إصلاح ${d} عنصر من أصل ${a.length}`),console.log(`⚠️ لم يتم العثور على ${e} عنصر`),{success:!0,fixedCount:d,notFoundCount:e,totalCount:a.length}}catch(a){return console.error("❌ خطأ في إصلاح بيانات localStorage:",a),{success:!1,error:a}}},p=a=>{try{let b=JSON.parse(localStorage.getItem("medicines")||"[]"),c=JSON.parse(localStorage.getItem("medicine_batches")||"[]").find(b=>b.id===a),d=b.find(a=>a.id===c?.medicine_id);return d?.name||"غير محدد"}catch(a){return console.error("خطأ في الحصول على اسم الدواء:",a),"غير محدد"}},q=async a=>{try{let{data:b,error:c}=await d.N.from("purchase_invoices").insert([a]).select().single();if(c){console.warn("Supabase error for purchase invoice, using localStorage:",c);let b={id:`purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,created_at:new Date().toISOString()},d=JSON.parse(localStorage.getItem("purchase_invoices")||"[]");return d.push(b),localStorage.setItem("purchase_invoices",JSON.stringify(d)),{success:!0,data:b}}return{success:!0,data:b}}catch(b){console.error("Error creating purchase invoice:",b);try{let b={id:`purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,created_at:new Date().toISOString()},c=JSON.parse(localStorage.getItem("purchase_invoices")||"[]");return c.push(b),localStorage.setItem("purchase_invoices",JSON.stringify(c)),{success:!0,data:b}}catch(a){return console.error("LocalStorage fallback failed for purchase invoice:",a),{success:!1,error:a}}}},r=async a=>{try{let{data:b,error:c}=await d.N.from("purchase_invoice_items").insert(a).select();if(c){console.warn("Supabase error for purchase invoice items, using localStorage:",c);let b=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),d=a.map(a=>({id:`purchase_item_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,medicine_name:a.medicine_name||"غير محدد",medicineName:a.medicine_name||"غير محدد",created_at:new Date().toISOString()}));return b.push(...d),localStorage.setItem("purchase_invoice_items",JSON.stringify(b)),{success:!0,data:d}}return{success:!0,data:b}}catch(b){console.error("Error adding purchase invoice items:",b);try{let b=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),c=a.map(a=>({id:`purchase_item_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,medicine_name:a.medicine_name||"غير محدد",medicineName:a.medicine_name||"غير محدد",created_at:new Date().toISOString()}));return b.push(...c),localStorage.setItem("purchase_invoice_items",JSON.stringify(b)),{success:!0,data:c}}catch(a){return console.error("LocalStorage fallback failed for purchase items:",a),{success:!1,error:a}}}},s=async a=>{try{let{data:b,error:c}=await d.N.from("inventory_movements").insert([a]).select().single();if(c){console.warn("Supabase error for inventory movement, using localStorage:",c);let b={id:`movement_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,created_at:new Date().toISOString()},d=JSON.parse(localStorage.getItem("inventory_movements")||"[]");return d.push(b),localStorage.setItem("inventory_movements",JSON.stringify(d)),{success:!0,data:b}}return{success:!0,data:b}}catch(b){console.error("Error adding inventory movement:",b);try{let b={id:`movement_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...a,created_at:new Date().toISOString()},c=JSON.parse(localStorage.getItem("inventory_movements")||"[]");return c.push(b),localStorage.setItem("inventory_movements",JSON.stringify(c)),{success:!0,data:b}}catch(a){return console.error("LocalStorage fallback failed for inventory movement:",a),{success:!1,error:a}}}},t=async()=>{try{let{data:a,error:b}=await d.N.from("customers").select("*").order("name");if(b)throw b;return{success:!0,data:a}}catch(a){return console.error("Error fetching customers:",a),{success:!1,error:a}}},u=async a=>{try{let{data:b,error:c}=await d.N.from("customers").insert([a]).select().single();if(c)throw c;return{success:!0,data:b}}catch(a){return console.error("Error adding customer:",a),{success:!1,error:a}}},v=async()=>{try{let{data:a,error:b}=await d.N.from("suppliers").select("*").order("name");if(b)throw b;return{success:!0,data:a}}catch(a){return console.error("Error fetching suppliers:",a),{success:!1,error:a}}},w=async a=>{try{let{data:b,error:c}=await d.N.from("suppliers").insert([a]).select().single();if(c)throw c;return{success:!0,data:b}}catch(a){return console.error("Error adding supplier:",a),{success:!1,error:a}}},x=async(a,b)=>{try{console.log("\uD83D\uDD04 بدء معاملة المبيعات الكاملة..."),console.log("\uD83D\uDCC4 بيانات الفاتورة:",a),console.log("\uD83D\uDCE6 العناصر:",b),console.log("\uD83D\uDCDD إنشاء الفاتورة...");let c=await l(a);if(console.log("\uD83D\uDCDD نتيجة إنشاء الفاتورة:",c),!c.success)throw console.error("❌ فشل في إنشاء الفاتورة:",c.error),Error(`فشل في إنشاء الفاتورة: ${c.error?.message||"خطأ غير معروف"}`);let e=c.data.id;console.log("✅ تم إنشاء الفاتورة بنجاح، ID:",e),console.log("\uD83D\uDCE6 معالجة عناصر الفاتورة...");let f=[];for(let a of b){console.log("\uD83D\uDCE6 معالجة العنصر:",a);let b=a.medicine_batch_id||a.batchId;if(f.push({invoice_id:e,medicine_batch_id:b,quantity:a.quantity,unit_price:a.unit_price||a.unitPrice,total_price:a.total_price||a.totalPrice,is_gift:a.is_gift||a.isGift||!1,medicine_name:a.medicine_name||a.medicineName||"غير محدد"}),!(a.is_gift||a.isGift))try{let c=await d.N.from("medicine_batches").select("quantity").eq("id",b).single();if(c.data){let d=Math.max(0,c.data.quantity-a.quantity);await k(b,d),console.log(`✅ تم تحديث كمية الدفعة ${b} إلى ${d}`)}}catch(a){console.warn("تحذير: فشل في تحديث كمية الدفعة:",a)}try{await s({medicine_batch_id:b,movement_type:"out",quantity:a.quantity,reference_type:"sale",reference_id:e,notes:a.is_gift||a.isGift?"هدية":void 0}),console.log(`✅ تم إضافة حركة المخزون للدفعة ${b}`)}catch(a){console.warn("تحذير: فشل في إضافة حركة المخزون:",a)}}console.log("\uD83D\uDCDD إضافة عناصر الفاتورة...");let g=await m(f);if(g.success?console.log("✅ تم إضافة جميع عناصر الفاتورة بنجاح"):console.warn("تحذير: فشل في إضافة عناصر الفاتورة:",g.error),"cash"===a.payment_method&&"paid"===a.payment_status)try{await J({transaction_type:"income",category:"مبيعات",amount:a.final_amount,description:`فاتورة مبيعات رقم ${a.invoice_number}`,reference_type:"sale",reference_id:e,payment_method:"cash",notes:a.notes}),console.log("✅ تم إضافة معاملة الصندوق")}catch(a){console.warn("تحذير: فشل في إضافة معاملة الصندوق:",a)}return console.log("\uD83C\uDF89 تمت معاملة المبيعات بنجاح!"),{success:!0,data:{invoiceId:e}}}catch(a){return console.error("❌ خطأ في إتمام معاملة المبيعات:",a),{success:!1,error:a}}},y=async()=>{try{let{data:a,error:b}=await d.N.from("sales_invoices").select(`
        *,
        customers (name, phone, address),
        sales_invoice_items (
          *,
          medicine_batches (
            batch_code,
            expiry_date,
            medicines (name, category, manufacturer, strength, form)
          )
        )
      `).order("created_at",{ascending:!1});if(b){console.warn("Supabase error for sales invoices, using localStorage fallback:",b);let a=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),c=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),d=JSON.parse(localStorage.getItem("medicines")||"[]"),e=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),f=a.map(a=>{let b=c.filter(b=>b.invoice_id===a.id).map(a=>{if(a.medicine_batches?.medicines?.name)return a;let b=e.find(b=>b.id===a.medicine_batch_id),c=d.find(a=>a.id===b?.medicine_id);return{...a,medicine_name:c?.name||a.medicine_name||"غير محدد",medicine_batches:{batch_code:b?.batch_code||"",expiry_date:b?.expiry_date||"",medicines:{name:c?.name||a.medicine_name||"غير محدد",category:c?.category||"",manufacturer:c?.manufacturer||"",strength:c?.strength||"",form:c?.form||""}}}});return{...a,sales_invoice_items:b}});return{success:!0,data:f}}return{success:!0,data:a}}catch(a){console.error("Error fetching sales invoices:",a);try{let a=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),b=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),c=JSON.parse(localStorage.getItem("medicines")||"[]"),d=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),e=a.map(a=>{let e=b.filter(b=>b.invoice_id===a.id).map(a=>{if(a.medicine_batches?.medicines?.name)return a;let b=d.find(b=>b.id===a.medicine_batch_id),e=c.find(a=>a.id===b?.medicine_id);return{...a,medicine_name:e?.name||a.medicine_name||"غير محدد",medicine_batches:{batch_code:b?.batch_code||"",expiry_date:b?.expiry_date||"",medicines:{name:e?.name||a.medicine_name||"غير محدد",category:e?.category||"",manufacturer:e?.manufacturer||"",strength:e?.strength||"",form:e?.form||""}}}});return{...a,sales_invoice_items:e}});return{success:!0,data:e}}catch(b){return console.error("LocalStorage fallback failed:",b),{success:!1,error:a}}}},z=async a=>{try{let{data:b,error:c}=await d.N.from("sales_invoices").select(`
        *,
        customers (name, phone, address),
        sales_invoice_items (
          *,
          medicine_batches (
            batch_code,
            expiry_date,
            medicines (name, category, manufacturer, strength, form)
          )
        )
      `).eq("id",a).single();if(c){console.warn("Supabase error for single invoice, using localStorage fallback:",c);let b=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),d=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),e=JSON.parse(localStorage.getItem("medicines")||"[]"),f=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),g=b.find(b=>b.id===a);if(g){let b=d.filter(b=>b.invoice_id===a);console.log("\uD83D\uDD27 بدء تحسين عناصر الفاتورة للطباعة..."),console.log("\uD83D\uDCE6 عدد العناصر:",b.length),console.log("\uD83D\uDC8A عدد الأدوية المتاحة:",e.length),console.log("\uD83D\uDCCB عدد الدفعات المتاحة:",f.length);let c=b.map((a,b)=>{console.log(`
--- العنصر ${b+1} ---`),console.log("البيانات الأصلية:",a);let c=f.find(b=>b.id===a.medicine_batch_id);console.log("الدفعة الموجودة:",c);let d=e.find(a=>a.id===c?.medicine_id);console.log("الدواء الموجود:",d);let g=d?.name||"غير محدد";console.log("اسم الدواء المحسوب:",g);let h={...a,medicine_name:g,medicineName:g,medicine_batches:{id:c?.id,batch_code:c?.batch_code||a.batch_code||"",expiry_date:c?.expiry_date||a.expiry_date||"",medicine_id:c?.medicine_id,medicines:{id:d?.id,name:g,category:d?.category||"",manufacturer:d?.manufacturer||"",strength:d?.strength||"",form:d?.form||""}}};return console.log("العنصر المحسن:",h),h});return console.log("✅ تم تحسين جميع العناصر"),console.log("النتيجة النهائية:",c),{success:!0,data:{...g,sales_invoice_items:c}}}}return{success:!0,data:b}}catch(a){return console.error("Error fetching sales invoice for print:",a),{success:!1,error:a}}},A=async a=>{try{let{data:b,error:c}=await d.N.from("purchase_invoices").select(`
        *,
        suppliers (name, contact_person, phone, address),
        purchase_invoice_items (
          *,
          medicines (name, category, manufacturer, strength, form)
        )
      `).eq("id",a).single();if(c){console.warn("Supabase error for single purchase invoice, using localStorage fallback:",c);let b=JSON.parse(localStorage.getItem("purchase_invoices")||"[]"),d=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),e=b.find(b=>b.id===a);if(e){let b=d.filter(b=>b.invoice_id===a);console.log("\uD83D\uDD27 بدء تحسين عناصر فاتورة المشتريات للطباعة..."),console.log("\uD83D\uDCE6 عدد العناصر:",b.length);let c=b.map((a,b)=>{console.log(`
--- العنصر ${b+1} ---`),console.log("البيانات الأصلية:",a);let c=a.medicine_name||a.medicineName||"غير محدد";console.log("اسم الدواء:",c);let d={...a,medicine_name:c,medicineName:c,medicines:{name:c,category:a.category||"",manufacturer:a.manufacturer||"",strength:a.strength||"",form:a.form||""}};return console.log("العنصر المحسن:",d),d});return console.log("✅ تم تحسين جميع عناصر المشتريات"),{success:!0,data:{...e,purchase_invoice_items:c}}}}return{success:!0,data:b}}catch(a){return console.error("Error fetching purchase invoice for print:",a),{success:!1,error:a}}},B=async()=>{try{let{data:a,error:b}=await d.N.from("purchase_invoices").select(`
        *,
        suppliers (name, contact_person, phone, address),
        purchase_invoice_items (
          *,
          medicines (name, category, manufacturer, strength, form)
        )
      `).order("created_at",{ascending:!1});if(b){console.warn("Supabase error for purchase invoices, using localStorage fallback:",b);let a=JSON.parse(localStorage.getItem("purchase_invoices")||"[]"),c=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),d=a.map(a=>({...a,purchase_invoice_items:c.filter(b=>b.invoice_id===a.id)}));return{success:!0,data:d}}return{success:!0,data:a}}catch(a){console.error("Error fetching purchase invoices:",a);try{let a=JSON.parse(localStorage.getItem("purchase_invoices")||"[]"),b=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),c=a.map(a=>({...a,purchase_invoice_items:b.filter(b=>b.invoice_id===a.id)}));return{success:!0,data:c}}catch(b){return console.error("LocalStorage fallback failed:",b),{success:!1,error:a}}}},C=async a=>{try{let{data:b,error:c}=await d.N.from("sales_returns").insert([a]).select().single();if(c)throw c;return{success:!0,data:b}}catch(b){console.warn("Supabase sales return failed, using localStorage fallback:",b);try{let b={...a,id:`sr_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,created_at:new Date().toISOString()},c=JSON.parse(localStorage.getItem("sales_returns")||"[]");return c.push(b),localStorage.setItem("sales_returns",JSON.stringify(c)),console.log("Sales return saved to localStorage:",b),console.log("Total sales returns in localStorage:",c.length),{success:!0,data:b}}catch(a){return console.error("Error creating sales return (fallback):",a),{success:!1,error:a}}}},D=async a=>{try{let{data:b,error:c}=await d.N.from("purchase_returns").insert([a]).select().single();if(c)throw c;return{success:!0,data:b}}catch(b){console.warn("Supabase purchase return failed, using localStorage fallback:",b);try{let b={...a,id:`pr_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,created_at:new Date().toISOString()},c=JSON.parse(localStorage.getItem("purchase_returns")||"[]");return c.push(b),localStorage.setItem("purchase_returns",JSON.stringify(c)),console.log("Purchase return saved to localStorage:",b),console.log("Total purchase returns in localStorage:",c.length),{success:!0,data:b}}catch(a){return console.error("Error creating purchase return (fallback):",a),{success:!1,error:a}}}},E=async a=>{try{let b=a[0]?.return_type==="sales"?"sales_return_items":"purchase_return_items",{data:c,error:e}=await d.N.from(b).insert(a.map(a=>({return_id:a.return_id,medicine_batch_id:a.medicine_batch_id,medicine_id:a.medicine_id,quantity:a.quantity,unit_price:a.unit_price,total_price:a.total_price}))).select();if(e)throw e;return{success:!0,data:c}}catch(b){console.warn("Supabase return items failed, using localStorage fallback:",b);try{let b=a[0]?.return_type==="sales"?"sales_return_items":"purchase_return_items",c=a.map(a=>({...a,id:`ri_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,created_at:new Date().toISOString()})),d=JSON.parse(localStorage.getItem(b)||"[]");return d.push(...c),localStorage.setItem(b,JSON.stringify(d)),{success:!0,data:c}}catch(a){return console.error("Error adding return items (fallback):",a),{success:!1,error:a}}}},F=async(a,b,c)=>{try{let e="sales"===a?await C(b):await D(b);if(!e.success)throw Error("Failed to create return");let f=e.data.id,g=c.map(b=>({return_id:f,return_type:a,medicine_batch_id:b.batchId,medicine_id:b.medicineId,quantity:b.quantity,unit_price:b.unitPrice,total_price:b.totalPrice}));await E(g);try{if("sales"===a){for(let a of c)if(a.batchId)try{let{data:c}=await d.N.from("medicine_batches").select("quantity").eq("id",a.batchId).single();c&&await k(a.batchId,c.quantity+a.quantity),await s({medicine_batch_id:a.batchId,movement_type:"in",quantity:a.quantity,reference_type:"return",reference_id:f,notes:`مرتجع مبيعات - ${b.reason}`})}catch(b){console.warn("Failed to update inventory for item:",a.batchId,b)}}if("purchase"===a){for(let a of c)if(a.batchId)try{let{data:c}=await d.N.from("medicine_batches").select("quantity").eq("id",a.batchId).single();if(c){let b=Math.max(0,c.quantity-a.quantity);await k(a.batchId,b)}await s({medicine_batch_id:a.batchId,movement_type:"out",quantity:a.quantity,reference_type:"return",reference_id:f,notes:`مرتجع مشتريات - ${b.reason}`})}catch(b){console.warn("Failed to update inventory for item:",a.batchId,b)}}}catch(a){console.warn("Inventory update failed, but return was created successfully:",a)}return{success:!0,data:{returnId:f}}}catch(a){return console.error("Error processing return:",a),{success:!1,error:a}}},G=async()=>{try{let a=JSON.parse(localStorage.getItem("sales_returns")||"[]"),b=JSON.parse(localStorage.getItem("purchase_returns")||"[]"),c=JSON.parse(localStorage.getItem("customers")||"[]"),d=JSON.parse(localStorage.getItem("suppliers")||"[]");console.log("Loading returns from localStorage:",{salesReturns:a.length,purchaseReturns:b.length,customers:c.length,suppliers:d.length});let e=a.map(a=>{let b=c.find(b=>b.id===a.customer_id);return console.log(`Enriching sales return ${a.id}:`,{original_items:a.return_items,items_count:a.return_items?.length||0}),{...a,return_type:"sales",customers:b?{name:b.name,phone:b.phone,address:b.address}:null,customer_name:b?.name||a.customer_name||"عميل غير محدد",return_items:a.return_items||[]}}),f=b.map(a=>{let b=d.find(b=>b.id===a.supplier_id);return console.log(`Enriching purchase return ${a.id}:`,{original_items:a.return_items,items_count:a.return_items?.length||0}),{...a,return_type:"purchase",suppliers:b?{name:b.name,phone:b.phone,address:b.address}:null,supplier_name:b?.name||a.supplier_name||"مورد غير محدد",return_items:a.return_items||[]}});if(e.length>0||f.length>0){let a=[...e,...f].sort((a,b)=>new Date(b.created_at).getTime()-new Date(a.created_at).getTime());return console.log("Returning enriched returns from localStorage:",a.slice(0,2)),{success:!0,data:a}}}catch(a){console.warn("Error reading from localStorage:",a)}try{console.log("Trying Supabase for returns...");let[a,b]=await Promise.all([d.N.from("sales_returns").select(`
          *,
          customers (name, phone),
          sales_return_items (
            *,
            medicine_batches (
              batch_code,
              medicines (name)
            )
          )
        `).order("created_at",{ascending:!1}),d.N.from("purchase_returns").select(`
          *,
          suppliers (name, contact_person),
          purchase_return_items (
            *,
            medicines (name)
          )
        `).order("created_at",{ascending:!1})]),c=[...(a.data||[]).map(a=>({...a,return_type:"sales",customer_name:a.customers?.name||"عميل غير محدد"})),...(b.data||[]).map(a=>({...a,return_type:"purchase",supplier_name:a.suppliers?.name||"مورد غير محدد"}))].sort((a,b)=>new Date(b.created_at).getTime()-new Date(a.created_at).getTime());return console.log("Returning returns from Supabase:",c.slice(0,2)),{success:!0,data:c}}catch(a){return console.warn("Supabase returns failed, returning empty array:",a),{success:!0,data:[]}}},H=async a=>{try{let b=JSON.parse(localStorage.getItem("sales_returns")||"[]"),c=JSON.parse(localStorage.getItem("purchase_returns")||"[]"),e=JSON.parse(localStorage.getItem("customers")||"[]"),f=JSON.parse(localStorage.getItem("suppliers")||"[]"),g=b.find(b=>b.id===a),h="sales";if(g||(g=c.find(b=>b.id===a),h="purchase"),g){if("sales"===h){let a=e.find(a=>a.id===g.customer_id);g={...g,return_type:"sales",customers:a?{name:a.name,phone:a.phone,address:a.address}:null,customer_name:a?.name||g.customer_name||"عميل غير محدد"}}else{let a=f.find(a=>a.id===g.supplier_id);g={...g,return_type:"purchase",suppliers:a?{name:a.name,phone:a.phone,address:a.address}:null,supplier_name:a?.name||g.supplier_name||"مورد غير محدد"}}return console.log("Found enriched return in localStorage:",g),{success:!0,data:g}}if(!d.N)return console.warn("Supabase not available, return not found"),{success:!1,error:"Return not found"};let{data:i,error:j}=await d.N.from("sales_returns").select(`
        *,
        customers (name, phone, address),
        sales_return_items (
          *,
          medicine_batches (
            *,
            medicines (name)
          )
        )
      `).eq("id",a).single();if(i&&!j){let a={...i,return_type:"sales",return_items:i.sales_return_items||[]};return console.log("Found sales return in Supabase:",a),{success:!0,data:a}}let{data:k,error:l}=await d.N.from("purchase_returns").select(`
        *,
        suppliers (name, phone, address),
        purchase_return_items (
          *,
          medicines (name)
        )
      `).eq("id",a).single();if(k&&!l){let a={...k,return_type:"purchase",return_items:k.purchase_return_items||[]};return console.log("Found purchase return in Supabase:",a),{success:!0,data:a}}return console.warn("Return not found:",a),{success:!1,error:"Return not found"}}catch(a){return console.error("Error getting return by ID:",a),{success:!1,error:a}}},I=async(a,b)=>{try{let c=await q(a);if(!c.success)throw Error("Failed to create purchase invoice");let d=c.data.id;for(let c of b){let b=c.medicineId;if(!b){let a=await e({name:c.medicineName,category:c.category||"أخرى",manufacturer:c.manufacturer||"",active_ingredient:c.activeIngredient||"",strength:c.strength||"",form:c.form||"tablet",unit_price:c.unitCost,selling_price:c.sellingPrice||1.5*c.unitCost});if(a.success)b=a.data.id;else{console.error("Failed to create medicine:",a.error);continue}}await r([{invoice_id:d,medicine_id:b,batch_code:c.batchCode,quantity:c.quantity,unit_cost:c.unitCost,total_cost:c.totalCost,expiry_date:c.expiryDate,medicine_name:c.medicineName||"غير محدد"}]);let f=await j({medicine_id:b,batch_code:c.batchCode,expiry_date:c.expiryDate,quantity:c.quantity,cost_price:c.unitCost,selling_price:c.sellingPrice||1.5*c.unitCost,supplier_id:a.supplier_id});f.success&&await s({medicine_batch_id:f.data.id,movement_type:"in",quantity:c.quantity,reference_type:"purchase",reference_id:d})}return"cash"===a.payment_method&&"paid"===a.payment_status&&await J({transaction_type:"expense",category:"مشتريات",amount:a.final_amount,description:`فاتورة مشتريات رقم ${a.invoice_number}`,reference_type:"purchase",reference_id:d,payment_method:"cash",notes:a.notes}),{success:!0,data:{invoiceId:d}}}catch(a){return console.error("Error completing purchase transaction:",a),{success:!1,error:a}}},J=async a=>{try{let{data:b,error:c}=await d.N.from("cash_transactions").insert([a]).select().single();if(c)throw c;return{success:!0,data:b}}catch(b){console.warn("Supabase cash transaction failed, using localStorage fallback:",b);try{let b={...a,id:`ct_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,created_at:new Date().toISOString()},c=JSON.parse(localStorage.getItem("cash_transactions")||"[]");return c.push(b),localStorage.setItem("cash_transactions",JSON.stringify(c)),console.log("Cash transaction saved to localStorage:",b),console.log("Total cash transactions in localStorage:",c.length),{success:!0,data:b}}catch(a){return console.error("Error adding cash transaction (fallback):",a),{success:!1,error:a}}}},K=async a=>{try{let b=JSON.parse(localStorage.getItem("cash_transactions")||"[]");if(b.length>0){console.log("Loading cash transactions from localStorage:",b.length);let c=b;return a?.start_date&&(c=c.filter(b=>b.created_at>=a.start_date)),a?.end_date&&(c=c.filter(b=>b.created_at<=a.end_date)),a?.transaction_type&&(c=c.filter(b=>b.transaction_type===a.transaction_type)),a?.category&&(c=c.filter(b=>b.category===a.category)),c.sort((a,b)=>new Date(b.created_at).getTime()-new Date(a.created_at).getTime()),{success:!0,data:c}}}catch(a){console.warn("Error reading cash transactions from localStorage:",a)}try{console.log("Trying Supabase for cash transactions...");let b=d.N.from("cash_transactions").select("*").order("created_at",{ascending:!1});a?.start_date&&(b=b.gte("created_at",a.start_date)),a?.end_date&&(b=b.lte("created_at",a.end_date)),a?.transaction_type&&(b=b.eq("transaction_type",a.transaction_type)),a?.category&&(b=b.eq("category",a.category));let{data:c,error:e}=await b;if(e)throw e;return{success:!0,data:c}}catch(a){return console.warn("Supabase cash transactions failed, returning empty array:",a),{success:!0,data:[]}}},L=async()=>{try{let a=JSON.parse(localStorage.getItem("cash_transactions")||"[]");if(a.length>0){console.log("Calculating cash balance from localStorage:",a.length,"transactions");let b=a.reduce((a,b)=>"income"===b.transaction_type?a+b.amount:a-b.amount,0);return{success:!0,data:b}}}catch(a){console.warn("Error calculating balance from localStorage:",a)}try{console.log("Trying Supabase for cash balance...");let{data:a,error:b}=await d.N.from("cash_transactions").select("transaction_type, amount");if(b)throw b;let c=a.reduce((a,b)=>"income"===b.transaction_type?a+b.amount:a-b.amount,0);return{success:!0,data:c}}catch(a){return console.warn("Supabase cash balance failed, returning 0:",a),{success:!0,data:0}}},M=async()=>{try{let a=JSON.parse(localStorage.getItem("sales_invoices")||"[]");if(a.length>0){console.log("Loading customer debts from localStorage:",a.length,"invoices");let b=a.filter(a=>"pending"===a.payment_status||"partial"===a.payment_status);return console.log("Found customer debts:",b.length),{success:!0,data:b}}{console.log("No sales invoices found, creating sample customer debts");let a=[{id:"debt_1",invoice_number:"INV-001",customer_id:"cust_1",customer_name:"أحمد محمد علي",final_amount:15e4,payment_status:"pending",created_at:new Date(Date.now()-1728e5).toISOString(),customers:{name:"أحمد محمد علي",phone:"07901111111"}},{id:"debt_2",invoice_number:"INV-003",customer_id:"cust_2",customer_name:"فاطمة حسن محمد",final_amount:85e3,payment_status:"partial",created_at:new Date(Date.now()-432e6).toISOString(),customers:{name:"فاطمة حسن محمد",phone:"07802222222"}}];return{success:!0,data:a}}}catch(a){console.warn("Error reading customer debts from localStorage:",a)}try{console.log("Trying Supabase for customer debts...");let{data:a,error:b}=await d.N.from("sales_invoices").select(`
        id,
        invoice_number,
        customer_id,
        customer_name,
        final_amount,
        payment_status,
        created_at,
        customers (name, phone)
      `).eq("payment_status","pending").order("created_at",{ascending:!1});if(b)throw b;return{success:!0,data:a}}catch(a){return console.warn("Supabase customer debts failed, returning empty array:",a),{success:!0,data:[]}}},N=async()=>{try{let a=JSON.parse(localStorage.getItem("purchase_invoices")||"[]");if(a.length>0){console.log("Loading supplier debts from localStorage:",a.length,"invoices");let b=a.filter(a=>"pending"===a.payment_status||"partial"===a.payment_status);return console.log("Found supplier debts:",b.length),{success:!0,data:b}}{console.log("No purchase invoices found, creating sample supplier debts");let a=[{id:"debt_3",invoice_number:"PUR-001",supplier_id:"sup_1",final_amount:25e5,payment_status:"pending",created_at:new Date(Date.now()-2592e5).toISOString(),suppliers:{name:"شركة الأدوية العراقية",contact_person:"أحمد محمد",phone:"07901234567"}},{id:"debt_4",invoice_number:"PUR-004",supplier_id:"sup_2",final_amount:18e5,payment_status:"partial",created_at:new Date(Date.now()-6048e5).toISOString(),suppliers:{name:"شركة بغداد للأدوية",contact_person:"فاطمة علي",phone:"07801234567"}}];return{success:!0,data:a}}}catch(a){console.warn("Error reading supplier debts from localStorage:",a)}try{console.log("Trying Supabase for supplier debts...");let{data:a,error:b}=await d.N.from("purchase_invoices").select(`
        id,
        invoice_number,
        supplier_id,
        final_amount,
        payment_status,
        created_at,
        suppliers (name, contact_person, phone)
      `).eq("payment_status","pending").order("created_at",{ascending:!1});if(b)throw b;return{success:!0,data:a}}catch(a){return console.warn("Supabase supplier debts failed, returning empty array:",a),{success:!0,data:[]}}},O=async(a,b,c,e)=>{try{let{data:f,error:g}=await d.N.from("sales"===a?"sales_invoices":"purchase_invoices").update({payment_status:c,...e&&{paid_amount:e}}).eq("id",b).select().single();if(g)throw g;return"paid"===c&&e&&await J({transaction_type:"sales"===a?"income":"expense",category:"sales"===a?"مبيعات":"مشتريات",amount:e,description:`دفع فاتورة ${"sales"===a?"مبيعات":"مشتريات"} رقم ${f.invoice_number}`,reference_type:a,reference_id:b,payment_method:"cash"}),{success:!0,data:f}}catch(d){console.warn("Supabase payment update failed, using localStorage fallback:",d);try{let d="sales"===a?"sales_invoices":"purchase_invoices",f=JSON.parse(localStorage.getItem(d)||"[]"),g=f.findIndex(a=>a.id===b);if(-1!==g)return f[g].payment_status=c,e&&(f[g].paid_amount=e),localStorage.setItem(d,JSON.stringify(f)),"paid"===c&&e&&await J({transaction_type:"sales"===a?"income":"expense",category:"sales"===a?"مبيعات":"مشتريات",amount:e,description:`دفع فاتورة ${"sales"===a?"مبيعات":"مشتريات"} رقم ${f[g].invoice_number}`,reference_type:a,reference_id:b,payment_method:"cash"}),console.log("Payment status updated in localStorage:",f[g]),{success:!0,data:f[g]};throw Error("Invoice not found in localStorage")}catch(a){return console.error("Error updating payment status (fallback):",a),{success:!1,error:a}}}},P=async a=>{try{let b=d.N.from("sales_invoices").select(`
        *,
        customers (name, phone),
        sales_invoice_items (
          *,
          medicine_batches (
            batch_code,
            medicines (name, category)
          )
        )
      `).order("created_at",{ascending:!1});a.start_date&&(b=b.gte("created_at",a.start_date)),a.end_date&&(b=b.lte("created_at",a.end_date)),a.customer_id&&(b=b.eq("customer_id",a.customer_id));let{data:c,error:e}=await b;if(e)throw e;let f=c;return a.medicine_id&&(f=c.filter(b=>b.sales_invoice_items.some(b=>b.medicine_batches?.medicines?.id===a.medicine_id))),{success:!0,data:f}}catch(a){return console.error("Error fetching sales report:",a),{success:!1,error:a}}},Q=async a=>{try{let b=d.N.from("purchase_invoices").select(`
        *,
        suppliers (name, contact_person, phone),
        purchase_invoice_items (
          *,
          medicines (name, category)
        )
      `).order("created_at",{ascending:!1});a.start_date&&(b=b.gte("created_at",a.start_date)),a.end_date&&(b=b.lte("created_at",a.end_date)),a.supplier_id&&(b=b.eq("supplier_id",a.supplier_id));let{data:c,error:e}=await b;if(e)throw e;let f=c;return a.medicine_id&&(f=c.filter(b=>b.purchase_invoice_items.some(b=>b.medicines?.id===a.medicine_id))),{success:!0,data:f}}catch(a){return console.error("Error fetching purchases report:",a),{success:!1,error:a}}},R=async(a,b)=>{try{let c=d.N.from("sales_invoices").select(`
        id,
        invoice_number,
        total_amount,
        discount_amount,
        final_amount,
        payment_status,
        payment_method,
        created_at,
        notes
      `).eq("customer_id",a).order("created_at",{ascending:!1}),e=d.N.from("sales_returns").select(`
        id,
        return_number,
        total_amount,
        reason,
        status,
        created_at
      `).eq("customer_id",a).order("created_at",{ascending:!1});b.start_date&&(c=c.gte("created_at",b.start_date),e=e.gte("created_at",b.start_date)),b.end_date&&(c=c.lte("created_at",b.end_date),e=e.lte("created_at",b.end_date));let[f,g]=await Promise.all([c,e]);if(f.error)throw f.error;if(g.error)throw g.error;return{success:!0,data:{sales:f.data,returns:g.data}}}catch(a){return console.error("Error fetching customer statement:",a),{success:!1,error:a}}},S=async(a,b)=>{try{let c=d.N.from("purchase_invoices").select(`
        id,
        invoice_number,
        total_amount,
        discount_amount,
        final_amount,
        payment_status,
        payment_method,
        created_at,
        notes
      `).eq("supplier_id",a).order("created_at",{ascending:!1}),e=d.N.from("purchase_returns").select(`
        id,
        return_number,
        total_amount,
        reason,
        status,
        created_at
      `).eq("supplier_id",a).order("created_at",{ascending:!1});b.start_date&&(c=c.gte("created_at",b.start_date),e=e.gte("created_at",b.start_date)),b.end_date&&(c=c.lte("created_at",b.end_date),e=e.lte("created_at",b.end_date));let[f,g]=await Promise.all([c,e]);if(f.error)throw f.error;if(g.error)throw g.error;return{success:!0,data:{purchases:f.data,returns:g.data}}}catch(a){return console.error("Error fetching supplier statement:",a),{success:!1,error:a}}},T=async(a,b)=>{try{let c=d.N.from("inventory_movements").select(`
        *,
        medicine_batches (
          batch_code,
          expiry_date,
          medicines (name, category)
        )
      `).order("created_at",{ascending:!1});b.start_date&&(c=c.gte("created_at",b.start_date)),b.end_date&&(c=c.lte("created_at",b.end_date));let{data:e,error:f}=await c;if(f)throw f;let g=e.filter(b=>b.medicine_batches?.medicines?.id===a);return{success:!0,data:g}}catch(a){return console.error("Error fetching medicine movement report:",a),{success:!1,error:a}}},U=async a=>{try{let b=d.N.from("medicine_batches").select(`
        *,
        medicines (name, category, manufacturer)
      `).order("expiry_date",{ascending:!0}),{data:c,error:e}=await b;if(e)throw e;let f=c;if(a.category&&(f=f.filter(b=>b.medicines?.category===a.category)),a.low_stock&&(f=f.filter(a=>a.quantity<10)),a.expired){let a=new Date().toISOString().split("T")[0];f=f.filter(b=>b.expiry_date<a)}if(a.expiring_soon){let a=new Date;a.setDate(a.getDate()+30);let b=a.toISOString().split("T")[0],c=new Date().toISOString().split("T")[0];f=f.filter(a=>a.expiry_date>=c&&a.expiry_date<=b)}return{success:!0,data:f}}catch(a){return console.error("Error fetching inventory report:",a),{success:!1,error:a}}},V=async(a,b,c)=>{let d={status:b};return c&&(d.rejection_reason=c),X(a,d)},W=async a=>{try{console.log("\uD83D\uDD0D البحث عن المرتجع للطباعة:",a);let b=JSON.parse(localStorage.getItem("sales_returns")||"[]"),c=JSON.parse(localStorage.getItem("purchase_returns")||"[]"),e=JSON.parse(localStorage.getItem("sales_return_items")||"[]"),f=JSON.parse(localStorage.getItem("purchase_return_items")||"[]"),g=b.find(b=>b.id===a);if(g){let b=e.filter(b=>b.return_id===a),c=JSON.parse(localStorage.getItem("medicines")||"[]"),d=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),f=b.map(a=>{let b=a.medicine_name||a.medicineName||"غير محدد",e=a.batch_code||a.batchCode||"",f=a.expiry_date||a.expiryDate||"";if("غير محدد"===b&&a.medicine_batch_id){let g=d.find(b=>b.id===a.medicine_batch_id);if(g){e=g.batch_number||e,f=g.expiry_date||f;let a=c.find(a=>a.id===g.medicine_id);a&&(b=a.name||b)}}if("غير محدد"===b&&a.medicine_id){let d=c.find(b=>b.id===a.medicine_id);d&&(b=d.name||b)}return{...a,medicine_name:b,batch_code:e,expiry_date:f,unit_price:a.unit_price||a.unitPrice||0,total_price:a.total_price||a.totalPrice||0}}),h={...g,type:"sales",return_type:"sales",return_invoice_items:f};return console.log("✅ تم العثور على مرتجع مبيعات:",h),{success:!0,data:h}}if(g=c.find(b=>b.id===a)){let b=f.filter(b=>b.return_id===a),c=JSON.parse(localStorage.getItem("medicines")||"[]"),d=b.map(a=>{let b=a.medicine_name||a.medicineName||"غير محدد";if("غير محدد"===b&&a.medicine_id){let d=c.find(b=>b.id===a.medicine_id);d&&(b=d.name||b)}return{...a,medicine_name:b,batch_code:a.batch_code||a.batchCode||"",expiry_date:a.expiry_date||a.expiryDate||"",unit_cost:a.unit_cost||a.unitCost||0,total_cost:a.total_cost||a.totalCost||0}}),e={...g,type:"purchase",return_type:"purchase",return_invoice_items:d};return console.log("✅ تم العثور على مرتجع مشتريات:",e),{success:!0,data:e}}console.log("⚠️ لم يتم العثور على المرتجع في localStorage، محاولة Supabase...");let{data:h,error:i}=await d.N.from("sales_returns").select(`
        *,
        customers (name, phone, address),
        sales_return_items (
          *,
          medicine_batches (
            batch_code,
            expiry_date,
            medicines (name, category, manufacturer, strength, form)
          )
        )
      `).eq("id",a).single();if(h&&!i){let a={...h,type:"sales",return_type:"sales",return_invoice_items:(h.sales_return_items||[]).map(a=>({...a,medicine_name:a.medicine_batches?.medicines?.name||a.medicine_name||"غير محدد",unit_price:a.unit_price||0,total_price:a.total_price||0}))};return console.log("✅ تم العثور على مرتجع مبيعات في Supabase:",a),{success:!0,data:a}}let{data:j,error:k}=await d.N.from("purchase_returns").select(`
        *,
        suppliers (name, contact_person, phone, address),
        purchase_return_items (
          *,
          medicines (name, category, manufacturer, strength, form)
        )
      `).eq("id",a).single();if(j&&!k){let a={...j,type:"purchase",return_type:"purchase",return_invoice_items:(j.purchase_return_items||[]).map(a=>({...a,medicine_name:a.medicines?.name||a.medicine_name||"غير محدد",unit_cost:a.unit_cost||0,total_cost:a.total_cost||0}))};return console.log("✅ تم العثور على مرتجع مشتريات في Supabase:",a),{success:!0,data:a}}return console.log("❌ لم يتم العثور على المرتجع"),{success:!1,error:"Return not found"}}catch(a){return console.error("Error fetching return for print:",a),{success:!1,error:a}}},X=async(a,b)=>{try{let c=JSON.parse(localStorage.getItem("sales_returns")||"[]"),e=JSON.parse(localStorage.getItem("purchase_returns")||"[]"),f=c.findIndex(b=>b.id===a);if(-1!==f){c[f]={...c[f],...b,updated_at:new Date().toISOString()},localStorage.setItem("sales_returns",JSON.stringify(c));try{let{error:c}=await d.N.from("sales_returns").update(b).eq("id",a);c&&console.warn("Failed to update return in Supabase:",c)}catch(a){console.warn("Supabase update failed, continuing with localStorage:",a)}return{success:!0,data:c[f]}}let g=e.findIndex(b=>b.id===a);if(-1!==g){e[g]={...e[g],...b,updated_at:new Date().toISOString()},localStorage.setItem("purchase_returns",JSON.stringify(e));try{let{error:c}=await d.N.from("purchase_returns").update(b).eq("id",a);c&&console.warn("Failed to update return in Supabase:",c)}catch(a){console.warn("Supabase update failed, continuing with localStorage:",a)}return{success:!0,data:e[g]}}return{success:!1,error:"Return not found"}}catch(a){return console.error("Error updating return:",a),{success:!1,error:a}}}},39727:()=>{},47990:()=>{}};