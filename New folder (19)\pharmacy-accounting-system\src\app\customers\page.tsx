'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  User,
  Phone,
  MapPin,
  Mail,
  FileText,
  X,
  Download,
  Eye,
  Printer,
  Calendar,
  DollarSign
} from 'lucide-react'
import { exportCustomersData } from '@/lib/exportUtils'

interface Customer {
  id: string
  name: string
  phone?: string
  email?: string
  address?: string
  notes?: string
  created_at: string
}

interface SalesInvoice {
  id: string
  invoice_number: string
  customer_name: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: string
  payment_status: string
  notes?: string
  created_at: string
}

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([
    {
      id: '1',
      name: 'أحمد محمد علي',
      phone: '07901111111',
      email: '<EMAIL>',
      address: 'بغداد - الكرادة',
      notes: 'عميل مميز',
      created_at: '2024-01-15'
    },
    {
      id: '2',
      name: 'فاطمة حسن محمد',
      phone: '07802222222',
      address: 'بغداد - الجادرية',
      created_at: '2024-02-10'
    },
    {
      id: '3',
      name: 'محمد علي حسن',
      phone: '07703333333',
      email: '<EMAIL>',
      address: 'بغداد - الأعظمية',
      created_at: '2024-03-05'
    },
    {
      id: '4',
      name: 'سارة أحمد محمود',
      phone: '07604444444',
      address: 'بغداد - الكاظمية',
      notes: 'يفضل التواصل مساءً',
      created_at: '2024-03-20'
    },
    {
      id: '5',
      name: 'علي محمود حسن',
      phone: '***********',
      address: 'بغداد - المنصور',
      created_at: '2024-04-01'
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [showAccountStatement, setShowAccountStatement] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [customerInvoices, setCustomerInvoices] = useState<SalesInvoice[]>([])
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    notes: ''
  })

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone?.includes(searchTerm) ||
    customer.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddCustomer = () => {
    setEditingCustomer(null)
    setFormData({ name: '', phone: '', email: '', address: '', notes: '' })
    setShowModal(true)
  }

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer)
    setFormData({
      name: customer.name,
      phone: customer.phone || '',
      email: customer.email || '',
      address: customer.address || '',
      notes: customer.notes || ''
    })
    setShowModal(true)
  }

  const handleDeleteCustomer = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      setCustomers(customers.filter(customer => customer.id !== id))
      alert('تم حذف العميل بنجاح!')
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      alert('يرجى إدخال اسم العميل')
      return
    }

    if (editingCustomer) {
      // تحديث العميل
      setCustomers(customers.map(customer =>
        customer.id === editingCustomer.id
          ? { ...customer, ...formData }
          : customer
      ))
      alert('تم تحديث بيانات العميل بنجاح!')
    } else {
      // إضافة عميل جديد
      const newCustomer: Customer = {
        id: Date.now().toString(),
        ...formData,
        created_at: new Date().toISOString().split('T')[0]
      }
      setCustomers([...customers, newCustomer])
      alert('تم إضافة العميل بنجاح!')
    }

    setShowModal(false)
    setEditingCustomer(null)
    setFormData({ name: '', phone: '', email: '', address: '', notes: '' })
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingCustomer(null)
    setFormData({ name: '', phone: '', email: '', address: '', notes: '' })
  }

  const handleViewAccountStatement = (customer: Customer) => {
    setSelectedCustomer(customer)
    loadCustomerInvoices(customer.name)
    setShowAccountStatement(true)
  }

  const loadCustomerInvoices = (customerName: string) => {
    try {
      // Load sales invoices from localStorage
      const salesData = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      const customerInvoicesData = salesData.filter((invoice: SalesInvoice) =>
        invoice.customer_name === customerName
      )
      setCustomerInvoices(customerInvoicesData)
    } catch (error) {
      console.error('Error loading customer invoices:', error)
      setCustomerInvoices([])
    }
  }

  const calculateCustomerTotalDebt = (customerName: string) => {
    const invoices = customerInvoices.filter(invoice =>
      invoice.customer_name === customerName && invoice.payment_status === 'pending'
    )
    return invoices.reduce((total, invoice) => total + invoice.final_amount, 0)
  }

  const calculateCustomerTotalPaid = (customerName: string) => {
    const invoices = customerInvoices.filter(invoice =>
      invoice.customer_name === customerName && invoice.payment_status === 'paid'
    )
    return invoices.reduce((total, invoice) => total + invoice.final_amount, 0)
  }

  const handleExportCustomers = () => {
    try {
      exportCustomersData(customers)
      alert('تم تصدير بيانات العملاء بنجاح!')
    } catch (error) {
      console.error('Error exporting customers:', error)
      alert('حدث خطأ أثناء تصدير البيانات')
    }
  }

  const printAccountStatement = () => {
    if (!selectedCustomer) return

    const printContent = `
      <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1>صيدلية الشفاء</h1>
          <h2>كشف حساب العميل</h2>
        </div>

        <div style="margin-bottom: 20px;">
          <h3>معلومات العميل:</h3>
          <p><strong>الاسم:</strong> ${selectedCustomer.name}</p>
          <p><strong>الهاتف:</strong> ${selectedCustomer.phone || 'غير محدد'}</p>
          <p><strong>العنوان:</strong> ${selectedCustomer.address || 'غير محدد'}</p>
          <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-EG')}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 8px;">رقم الفاتورة</th>
              <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>
              <th style="border: 1px solid #ddd; padding: 8px;">المبلغ</th>
              <th style="border: 1px solid #ddd; padding: 8px;">حالة الدفع</th>
            </tr>
          </thead>
          <tbody>
            ${customerInvoices.map(invoice => `
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${invoice.invoice_number}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${new Date(invoice.created_at).toLocaleDateString('ar-EG')}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${invoice.final_amount.toLocaleString()} د.ع</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div style="margin-top: 20px;">
          <p><strong>إجمالي المبالغ المدفوعة:</strong> ${calculateCustomerTotalPaid(selectedCustomer.name).toLocaleString()} د.ع</p>
          <p><strong>إجمالي المبالغ المعلقة:</strong> ${calculateCustomerTotalDebt(selectedCustomer.name).toLocaleString()} د.ع</p>
          <p><strong>إجمالي المعاملات:</strong> ${(calculateCustomerTotalPaid(selectedCustomer.name) + calculateCustomerTotalDebt(selectedCustomer.name)).toLocaleString()} د.ع</p>
        </div>
      </div>
    `

    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(printContent)
      printWindow.document.close()
      printWindow.print()
    }
  }

  const handleExportCustomers = async () => {
    const success = await exportCustomersData(customers)
    if (success) {
      alert('تم تصدير بيانات العملاء بنجاح!')
    } else {
      alert('حدث خطأ أثناء التصدير')
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة العملاء</h1>
            <p className="text-gray-600 mt-1">إدارة بيانات العملاء ومعلومات الاتصال</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleExportCustomers}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              تصدير للإكسل
            </button>
            <button
              onClick={handleAddCustomer}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة عميل جديد
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="البحث عن عميل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Customers List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              قائمة العملاء ({filteredCustomers.length})
            </h2>
          </div>
          
          {filteredCustomers.length === 0 ? (
            <div className="text-center py-12">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عملاء</h3>
              <p className="text-gray-500">لم يتم العثور على عملاء تطابق معايير البحث</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredCustomers.map((customer) => (
                <div key={customer.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <User className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{customer.name}</h3>
                        <div className="flex flex-wrap gap-4 mt-2 text-sm text-gray-600">
                          {customer.phone && (
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              <span>{customer.phone}</span>
                            </div>
                          )}
                          {customer.email && (
                            <div className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              <span>{customer.email}</span>
                            </div>
                          )}
                          {customer.address && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              <span>{customer.address}</span>
                            </div>
                          )}
                        </div>
                        {customer.notes && (
                          <div className="flex items-center gap-1 mt-1 text-sm text-gray-500">
                            <FileText className="h-3 w-3" />
                            <span>{customer.notes}</span>
                          </div>
                        )}
                        <p className="text-xs text-gray-400 mt-2">
                          تاريخ الإضافة: {customer.created_at}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleViewAccountStatement(customer)}
                        className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg"
                        title="كشف الحساب"
                      >
                        <FileText className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEditCustomer(customer)}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                        title="تعديل"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCustomer(customer.id)}
                        className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg"
                        title="حذف"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Add/Edit Customer Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  {editingCustomer ? 'تعديل العميل' : 'إضافة عميل جديد'}
                </h2>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم العميل *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="اسم العميل"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم الهاتف
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="07901234567"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    العنوان
                  </label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="العنوان"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="ملاحظات إضافية"
                    rows={3}
                  />
                </div>
                
                <div className="flex justify-end gap-3 mt-6">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {editingCustomer ? 'تحديث' : 'إضافة'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Account Statement Modal */}
        {showAccountStatement && selectedCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  كشف حساب العميل - {selectedCustomer.name}
                </h2>
                <div className="flex items-center gap-2">
                  <button
                    onClick={printAccountStatement}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
                  >
                    <Printer className="h-4 w-4" />
                    طباعة
                  </button>
                  <button
                    onClick={() => {
                      setShowAccountStatement(false)
                      setSelectedCustomer(null)
                      setCustomerInvoices([])
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Customer Info */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">معلومات العميل</h3>
                    <p><span className="font-medium">الاسم:</span> {selectedCustomer.name}</p>
                    <p><span className="font-medium">الهاتف:</span> {selectedCustomer.phone || 'غير محدد'}</p>
                    <p><span className="font-medium">العنوان:</span> {selectedCustomer.address || 'غير محدد'}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">ملخص الحساب</h3>
                    <p><span className="font-medium">إجمالي المبالغ المدفوعة:</span> <span className="text-green-600">{calculateCustomerTotalPaid(selectedCustomer.name).toLocaleString()} د.ع</span></p>
                    <p><span className="font-medium">إجمالي المبالغ المعلقة:</span> <span className="text-red-600">{calculateCustomerTotalDebt(selectedCustomer.name).toLocaleString()} د.ع</span></p>
                    <p><span className="font-medium">إجمالي المعاملات:</span> <span className="text-blue-600">{(calculateCustomerTotalPaid(selectedCustomer.name) + calculateCustomerTotalDebt(selectedCustomer.name)).toLocaleString()} د.ع</span></p>
                  </div>
                </div>
              </div>

              {/* Invoices Table */}
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">فواتير العميل ({customerInvoices.length})</h3>
                </div>
                <div className="overflow-x-auto">
                  {customerInvoices.length === 0 ? (
                    <div className="text-center py-12">
                      <FileText className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد فواتير</h3>
                      <p className="mt-1 text-sm text-gray-500">لم يتم العثور على فواتير لهذا العميل.</p>
                    </div>
                  ) : (
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            رقم الفاتورة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            التاريخ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المبلغ الإجمالي
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الخصم
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المبلغ النهائي
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            طريقة الدفع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            حالة الدفع
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {customerInvoices.map((invoice) => (
                          <tr key={invoice.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {invoice.invoice_number}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(invoice.created_at).toLocaleDateString('ar-EG')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {invoice.total_amount.toLocaleString()} د.ع
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {invoice.discount_amount.toLocaleString()} د.ع
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {invoice.final_amount.toLocaleString()} د.ع
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex items-center">
                                {invoice.payment_method === 'cash' ? (
                                  <DollarSign className="h-4 w-4 text-green-500 mr-1" />
                                ) : (
                                  <Calendar className="h-4 w-4 text-blue-500 mr-1" />
                                )}
                                {invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                invoice.payment_status === 'paid'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  )
}
