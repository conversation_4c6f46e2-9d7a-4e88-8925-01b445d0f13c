(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1533],{20451:(e,i,n)=>{Promise.resolve().then(n.bind(n,49587))},49587:(e,i,n)=>{"use strict";n.r(i),n.d(i,{default:()=>o});var c=n(95155),t=n(12115),l=n(61932),a=n(30446),d=n(54213),s=n(47924);function o(){let[e,i]=(0,t.useState)(null),[n,o]=(0,t.useState)(""),[r,m]=(0,t.useState)([]),[g,h]=(0,t.useState)([]),u=()=>{try{let e=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),n=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),c=JSON.parse(localStorage.getItem("medicines")||"[]"),t=JSON.parse(localStorage.getItem("medicine_batches")||"[]");console.log("\uD83D\uDCCA تحليل البيانات:"),console.log("الفواتير:",e),console.log("عناصر الفواتير:",n),console.log("الأدوية:",c),console.log("الدفعات:",t);let l={invoicesCount:e.length,itemsCount:n.length,medicinesCount:c.length,batchesCount:t.length,sampleInvoice:e[0]||null,sampleItem:n[0]||null,sampleMedicine:c[0]||null,sampleBatch:t[0]||null,itemsWithNames:n.filter(e=>e.medicine_name&&"غير محدد"!==e.medicine_name).length,itemsWithBatchStructure:n.filter(e=>{var i,n;return null==(n=e.medicine_batches)||null==(i=n.medicines)?void 0:i.name}).length,batchMedicineMapping:t.map(e=>{let i=c.find(i=>i.id===e.medicine_id);return{batchId:e.id,batchCode:e.batch_code,medicineId:e.medicine_id,medicineName:(null==i?void 0:i.name)||"غير موجود"}}).slice(0,5),itemMedicineMapping:n.map(e=>{var i,n;let l=t.find(i=>i.id===e.medicine_batch_id),a=c.find(e=>e.id===(null==l?void 0:l.medicine_id));return{itemId:e.id,batchId:e.medicine_batch_id,currentName:e.medicine_name||e.medicineName,batchStructureName:null==(n=e.medicine_batches)||null==(i=n.medicines)?void 0:i.name,actualMedicineName:null==a?void 0:a.name,batchCode:null==l?void 0:l.batch_code}}).slice(0,10)};i(l),m(e.slice(0,10)),h(c.slice(0,10))}catch(e){console.error("خطأ في تحليل البيانات:",e),i({error:e.toString()})}};return(0,c.jsx)(l.A,{children:(0,c.jsx)("div",{className:"space-y-6",children:(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,c.jsx)("div",{className:"bg-red-50 p-3 rounded-lg",children:(0,c.jsx)(a.A,{className:"h-6 w-6 text-red-600"})}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"تشخيص مشكلة البيانات"}),(0,c.jsx)("p",{className:"text-gray-600",children:"فحص مفصل لبيانات localStorage وهيكل الفواتير"})]})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,c.jsxs)("button",{onClick:u,className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2 justify-center",children:[(0,c.jsx)(d.A,{className:"h-5 w-5"}),"تحليل البيانات العام"]}),(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)("input",{type:"text",value:n,onChange:e=>o(e.target.value),placeholder:"معرف الفاتورة",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg"}),(0,c.jsx)("button",{onClick:()=>{if(n)try{let e=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),c=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),t=JSON.parse(localStorage.getItem("medicines")||"[]"),l=JSON.parse(localStorage.getItem("medicine_batches")||"[]");console.log("\uD83D\uDD0D تحليل فاتورة محددة:",n),console.log("جميع الفواتير:",e.map(e=>({id:e.id,number:e.invoice_number}))),console.log("جميع العناصر:",c.map(e=>({id:e.id,invoice_id:e.invoice_id})));let a=e.find(e=>e.id===n),d=c.filter(e=>e.invoice_id===n);if(console.log("الفاتورة الموجودة:",a),console.log("العناصر المطابقة:",d),console.log("عدد العناصر المطابقة:",d.length),0===d.length){console.log("⚠️ لم يتم العثور على عناصر، جرب استراتيجيات أخرى...");let e=c.filter(e=>e.invoice_number===(null==a?void 0:a.invoice_number));console.log("العناصر بالرقم:",e);let i=c.filter(e=>e.invoice_id&&e.invoice_id.includes(n.slice(-5)));console.log("العناصر بالمطابقة الجزئية:",i),console.log("جميع العناصر للفحص اليدوي:",c)}let s={searchedId:n,invoice:a,itemsFound:d.length,allInvoiceIds:e.map(e=>e.id),allItemInvoiceIds:[...new Set(c.map(e=>e.invoice_id))],items:d.map(e=>{var i,n;let c=l.find(i=>i.id===e.medicine_batch_id),a=t.find(e=>e.id===(null==c?void 0:c.medicine_id));return{...e,debug:{batchFound:!!c,medicineFound:!!a,batchData:c,medicineData:a,currentMedicineName:e.medicine_name||e.medicineName,batchStructureName:null==(n=e.medicine_batches)||null==(i=n.medicines)?void 0:i.name,calculatedName:(null==a?void 0:a.name)||"غير موجود"}}})};i({specificInvoice:s}),console.log("تحليل مفصل:",s)}catch(e){console.error("خطأ في تحليل الفاتورة:",e)}},className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700",children:(0,c.jsx)(s.A,{className:"h-5 w-5"})})]}),(0,c.jsxs)("button",{onClick:()=>{try{let e=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),i=JSON.parse(localStorage.getItem("medicines")||"[]"),n=JSON.parse(localStorage.getItem("medicine_batches")||"[]");console.log("\uD83D\uDD27 إصلاح هيكل البيانات..."),console.log("\uD83D\uDCE6 عناصر الفواتير:",e.length),console.log("\uD83D\uDC8A الأدوية:",i.length),console.log("\uD83D\uDCCB الدفعات:",n.length);let c=new Set,t=e.map(e=>{let t=n.find(i=>i.id===e.medicine_batch_id);if(t){let n=i.find(e=>e.id===t.medicine_id);return(null==n?void 0:n.name)?(console.log("✅ إصلاح: ".concat(n.name," للعنصر ").concat(e.id)),{...e,medicine_name:n.name,medicineName:n.name,medicine_batches:{id:t.id,batch_code:t.batch_code||"",expiry_date:t.expiry_date||"",medicine_id:t.medicine_id,medicines:{id:n.id,name:n.name,category:n.category||"",manufacturer:n.manufacturer||"",strength:n.strength||"",form:n.form||""}}}):(console.log("❌ لم يتم العثور على الدواء للدفعة ".concat(t.id)),e)}{c.add(e.medicine_batch_id),console.log("⚠️ دفعة مفقودة: ".concat(e.medicine_batch_id));let t=null;if(e.medicine_name&&"غير محدد"!==e.medicine_name&&(t=i.find(i=>i.name===e.medicine_name)),!t&&i.length>0&&(t=i[0],console.log("\uD83D\uDD04 استخدام دواء افتراضي: ".concat(t.name))),!t)return console.log("❌ لا توجد أدوية متاحة لإنشاء دفعة للعنصر ".concat(e.id)),e;{let i={id:e.medicine_batch_id,medicine_id:t.id,batch_code:"BATCH-".concat(Date.now()),quantity:1e3,expiry_date:new Date(Date.now()+31536e6).toISOString().split("T")[0],created_at:new Date().toISOString()};return n.push(i),console.log("✅ تم إنشاء دفعة جديدة: ".concat(i.batch_code," للدواء ").concat(t.name)),{...e,medicine_name:t.name,medicineName:t.name,medicine_batches:{id:i.id,batch_code:i.batch_code,expiry_date:i.expiry_date,medicine_id:i.medicine_id,medicines:{id:t.id,name:t.name,category:t.category||"",manufacturer:t.manufacturer||"",strength:t.strength||"",form:t.form||""}}}}}});localStorage.setItem("medicine_batches",JSON.stringify(n)),localStorage.setItem("sales_invoice_items",JSON.stringify(t)),console.log("✅ تم حفظ البيانات المصلحة"),console.log("\uD83D\uDCCB تم إنشاء ".concat(c.size," دفعة جديدة")),u()}catch(e){console.error("خطأ في إصلاح البيانات:",e)}},className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 flex items-center gap-2 justify-center",children:[(0,c.jsx)(a.A,{className:"h-5 w-5"}),"إصلاح فوري"]})]}),r.length>0&&(0,c.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-4",children:[(0,c.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"الفواتير المتاحة (أول 10):"}),(0,c.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2",children:r.map(e=>(0,c.jsxs)("button",{onClick:()=>o(e.id),className:"bg-white p-2 rounded border text-sm hover:bg-blue-100 text-left",children:[(0,c.jsx)("div",{className:"font-medium",children:e.invoice_number}),(0,c.jsxs)("div",{className:"text-xs text-gray-500",children:[e.id.slice(0,8),"..."]})]},e.id))})]}),g.length>0&&(0,c.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 mb-4",children:[(0,c.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"الأدوية المتاحة (أول 10):"}),(0,c.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:g.map(e=>(0,c.jsxs)("div",{className:"bg-white p-2 rounded border text-sm",children:[(0,c.jsx)("div",{className:"font-medium",children:e.name}),(0,c.jsx)("div",{className:"text-xs text-gray-500",children:e.category}),(0,c.jsxs)("div",{className:"text-xs text-gray-400",children:[e.id.slice(0,8),"..."]})]},e.id))})]}),e&&(0,c.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,c.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"نتائج التشخيص:"}),(0,c.jsx)("div",{className:"bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto",children:(0,c.jsx)("pre",{children:JSON.stringify(e,null,2)})})]})]})})})}},54213:(e,i,n)=>{"use strict";n.d(i,{A:()=>c});let c=(0,n(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])}},e=>{e.O(0,[6874,6543,8080,1932,8441,5964,7358],()=>e(e.s=20451)),_N_E=e.O()}]);