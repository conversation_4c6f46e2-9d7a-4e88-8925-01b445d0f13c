// Utility functions for exporting data to Excel and other formats

export interface ExportData {
  headers: string[]
  rows: (string | number)[][]
  filename: string
  sheetName?: string
}

export const exportToCSV = (data: ExportData) => {
  const csvContent = [
    data.headers.join(','),
    ...data.rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n')

  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  
  link.setAttribute('href', url)
  link.setAttribute('download', `${data.filename}.csv`)
  link.style.visibility = 'hidden'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export const exportToExcel = async (data: ExportData) => {
  try {
    // Create a simple Excel-compatible format using HTML table
    const htmlTable = `
      <table>
        <thead>
          <tr>
            ${data.headers.map(header => `<th>${header}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
          ${data.rows.map(row => 
            `<tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>`
          ).join('')}
        </tbody>
      </table>
    `

    const blob = new Blob([htmlTable], { 
      type: 'application/vnd.ms-excel;charset=utf-8;' 
    })
    
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    link.setAttribute('href', url)
    link.setAttribute('download', `${data.filename}.xls`)
    link.style.visibility = 'hidden'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    return true
  } catch (error) {
    console.error('Error exporting to Excel:', error)
    return false
  }
}

// Export functions for different data types
export const exportSalesData = (salesData: any[]) => {
  const data: ExportData = {
    headers: [
      'رقم الفاتورة',
      'التاريخ',
      'العميل',
      'المجموع الفرعي',
      'الخصم',
      'المجموع النهائي',
      'طريقة الدفع',
      'ملاحظات'
    ],
    rows: salesData.map(sale => [
      sale.invoiceNumber || '',
      sale.date || '',
      sale.customerName || '',
      sale.subtotal || 0,
      sale.discount || 0,
      sale.finalAmount || 0,
      sale.paymentMethod || '',
      sale.notes || ''
    ]),
    filename: `مبيعات_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'المبيعات'
  }
  
  return exportToExcel(data)
}

export const exportPurchasesData = (purchasesData: any[]) => {
  const data: ExportData = {
    headers: [
      'رقم الفاتورة',
      'التاريخ',
      'المورد',
      'المجموع الفرعي',
      'الخصم',
      'المجموع النهائي',
      'طريقة الدفع',
      'ملاحظات'
    ],
    rows: purchasesData.map(purchase => [
      purchase.invoiceNumber || '',
      purchase.date || '',
      purchase.supplierName || '',
      purchase.subtotal || 0,
      purchase.discount || 0,
      purchase.finalAmount || 0,
      purchase.paymentMethod || '',
      purchase.notes || ''
    ]),
    filename: `مشتريات_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'المشتريات'
  }
  
  return exportToExcel(data)
}

export const exportInventoryData = (inventoryData: any[]) => {
  const data: ExportData = {
    headers: [
      'اسم الدواء',
      'الفئة',
      'الشركة المصنعة',
      'التركيز',
      'الشكل',
      'كود الوجبة',
      'الكمية',
      'تاريخ الانتهاء',
      'سعر التكلفة',
      'سعر البيع'
    ],
    rows: inventoryData.map(item => [
      item.medicineName || '',
      item.category || '',
      item.manufacturer || '',
      item.strength || '',
      item.form || '',
      item.batchCode || '',
      item.quantity || 0,
      item.expiryDate || '',
      item.costPrice || 0,
      item.sellingPrice || 0
    ]),
    filename: `مخزون_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'المخزون'
  }
  
  return exportToExcel(data)
}

export const exportCustomersData = (customersData: any[]) => {
  const data: ExportData = {
    headers: [
      'اسم العميل',
      'رقم الهاتف',
      'البريد الإلكتروني',
      'العنوان',
      'ملاحظات',
      'تاريخ الإضافة'
    ],
    rows: customersData.map(customer => [
      customer.name || '',
      customer.phone || '',
      customer.email || '',
      customer.address || '',
      customer.notes || '',
      customer.created_at || ''
    ]),
    filename: `عملاء_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'العملاء'
  }
  
  return exportToExcel(data)
}

export const exportSuppliersData = (suppliersData: any[]) => {
  const data: ExportData = {
    headers: [
      'اسم المورد',
      'جهة الاتصال',
      'رقم الهاتف',
      'البريد الإلكتروني',
      'العنوان',
      'ملاحظات',
      'تاريخ الإضافة'
    ],
    rows: suppliersData.map(supplier => [
      supplier.name || '',
      supplier.contact_person || '',
      supplier.phone || '',
      supplier.email || '',
      supplier.address || '',
      supplier.notes || '',
      supplier.created_at || ''
    ]),
    filename: `موردين_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'الموردين'
  }
  
  return exportToExcel(data)
}

export const exportReturnsData = (returnsData: any[]) => {
  const data: ExportData = {
    headers: [
      'رقم المرتجع',
      'النوع',
      'الفاتورة الأصلية',
      'العميل/المورد',
      'المبلغ',
      'السبب',
      'الحالة',
      'التاريخ'
    ],
    rows: returnsData.map(returnItem => [
      returnItem.returnNumber || '',
      returnItem.type === 'sales' ? 'مرتجع مبيعات' : 'مرتجع مشتريات',
      returnItem.originalInvoiceNumber || '',
      returnItem.customerOrSupplier || '',
      returnItem.totalAmount || 0,
      returnItem.reason || '',
      returnItem.status === 'pending' ? 'قيد المراجعة' : 
      returnItem.status === 'approved' ? 'مقبول' : 'مرفوض',
      returnItem.created_at || ''
    ]),
    filename: `مرتجعات_${new Date().toISOString().split('T')[0]}`,
    sheetName: 'المرتجعات'
  }
  
  return exportToExcel(data)
}

// Print utilities
export const printElement = (elementId: string) => {
  const printContent = document.getElementById(elementId)
  if (!printContent) {
    console.error('Element not found for printing')
    return false
  }

  const originalContent = document.body.innerHTML
  document.body.innerHTML = printContent.innerHTML
  
  window.print()
  
  document.body.innerHTML = originalContent
  window.location.reload() // Reload to restore event listeners
  
  return true
}

export const generatePrintableReport = (title: string, data: any[], headers: string[]) => {
  const printWindow = window.open('', '_blank')
  if (!printWindow) return false

  const htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .company-info { margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>صيدلية الشفاء</h1>
        <div class="company-info">
          <p>بغداد - الكرادة - شارع الرئيسي</p>
          <p>هاتف: 07901234567 | البريد الإلكتروني: <EMAIL></p>
        </div>
        <h2>${title}</h2>
        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
      </div>
      
      <table>
        <thead>
          <tr>
            ${headers.map(header => `<th>${header}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
          ${data.map(row => 
            `<tr>${Object.values(row).map(cell => `<td>${cell}</td>`).join('')}</tr>`
          ).join('')}
        </tbody>
      </table>
      
      <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الصيدلية</p>
      </div>
      
      <script>
        window.onload = function() {
          window.print();
          window.onafterprint = function() {
            window.close();
          }
        }
      </script>
    </body>
    </html>
  `

  printWindow.document.write(htmlContent)
  printWindow.document.close()
  
  return true
}
