(()=>{var a={};a.id=117,a.ids=[117],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},16459:(a,b,c)=>{Promise.resolve().then(c.bind(c,72680))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43227:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\sales\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\sales\\page.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56278:(a,b,c)=>{"use strict";c.d(b,{F:()=>e});var d=c(43210);function e(){let[a,b]=(0,d.useState)(!1),[c,e]=(0,d.useState)("");return{mounted:a,currentDate:c,generateInvoiceNumber:()=>`INV-${Date.now()}`,getCurrentDateISO:()=>new Date().toISOString().split("T")[0],formatNumber:b=>a?b.toLocaleString():b.toString()}}},60367:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["sales",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,43227)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\sales\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\sales\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/sales/page",pathname:"/sales",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/sales/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72680:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>w});var d=c(60687),e=c(43210),f=c(21979),g=c(40228),h=c(99270),i=c(28561),j=c(11860),k=c(62688);let l=(0,k.A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var m=c(58869);let n=(0,k.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var o=c(8819),p=c(71444),q=c(31158),r=c(84997),s=c(97711),t=c(99926),u=c(31836),v=c(56278);function w(){let[a,b]=(0,e.useState)([]),[c,k]=(0,e.useState)(""),[w,x]=(0,e.useState)(null),[y,z]=(0,e.useState)(0),[A,B]=(0,e.useState)(""),[C,D]=(0,e.useState)(!1),[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(null),[I,J]=(0,e.useState)([]),[K,L]=(0,e.useState)([]),[M,N]=(0,e.useState)(!1),[O,P]=(0,e.useState)("cash"),[Q,R]=(0,e.useState)(""),[S,T]=(0,e.useState)(""),{settings:U}=(0,s.usePrintSettings)(),{mounted:V,currentDate:W,generateInvoiceNumber:X,getCurrentDateISO:Y,formatNumber:Z}=(0,v.F)(),$=async()=>{try{await (0,u.initializeSystemData)();let a=await (0,u.getMedicines)();if(a.success&&a.data){let b=[];a.data.forEach(a=>{a.medicine_batches&&Array.isArray(a.medicine_batches)&&a.medicine_batches.forEach(c=>{c.quantity>0&&b.push({id:a.id,batchId:c.id,name:a.name,batchCode:c.batch_code,price:c.selling_price,quantity:c.quantity,expiry:c.expiry_date,category:a.category,manufacturer:a.manufacturer})})}),J(b)}else console.error("Failed to load medicines:",a.error),J([{id:"1",batchId:"b1",name:"باراسيتامول 500mg",batchCode:"B001",price:750,quantity:150,expiry:"2024-12-15"},{id:"2",batchId:"b2",name:"أموكسيسيلين 250mg",batchCode:"B003",price:1800,quantity:200,expiry:"2025-03-10"}])}catch(a){console.error("Error loading medicines:",a),J([{id:"1",batchId:"b1",name:"باراسيتامول 500mg",batchCode:"B001",price:750,quantity:150,expiry:"2024-12-15"},{id:"2",batchId:"b2",name:"أموكسيسيلين 250mg",batchCode:"B003",price:1800,quantity:200,expiry:"2025-03-10"}])}},_=I.filter(a=>a.name.toLowerCase().includes(A.toLowerCase())),aa=(a,c)=>{let d=Math.max(0,c||0);b(b=>b.map(b=>b.batchId===a?d+b.giftQuantity>b.availableQuantity&&d>0?(alert("الكمية المطلوبة تتجاوز المتوفر في المخزون"),b):{...b,quantity:d,totalPrice:d*b.unitPrice}:b))},ab=(a,c)=>{c<0||b(b=>b.map(b=>b.batchId===a?b.quantity+c>b.availableQuantity?(alert("الكمية المطلوبة تتجاوز المتوفر في المخزون"),b):{...b,giftQuantity:c}:b))},ac=()=>a.reduce((a,b)=>a+b.totalPrice,0),ad=()=>ac()-y,ae=async()=>{if(console.log("\uD83D\uDD04 بدء عملية حفظ الفاتورة..."),console.log("\uD83D\uDCE6 عدد العناصر:",a.length),console.log("\uD83D\uDCCB العناصر:",a),0===a.length)return void alert("يرجى إضافة عناصر للفاتورة");N(!0);try{let d=X(),e={invoice_number:d,customer_id:w?.id,customer_name:w?.name||c,total_amount:ac(),discount_amount:y,final_amount:ad(),payment_method:O,payment_status:"cash"===O?"paid":"pending",notes:Q,private_notes:S},f=[];for(let b of a){let a=I.find(a=>a.batchId===b.batchId),c=a?.name||"غير محدد";console.log(`🔍 البحث عن الدواء للدفعة ${b.batchId}:`),console.log(`📋 الدواء الموجود:`,a),console.log(`💊 اسم الدواء: ${c}`),b.quantity>0&&f.push({medicine_batch_id:b.batchId,quantity:b.quantity,unit_price:b.unitPrice,total_price:b.totalPrice,is_gift:!1,medicine_name:c,medicineName:c}),b.giftQuantity>0&&f.push({medicine_batch_id:b.batchId,quantity:b.giftQuantity,unit_price:0,total_price:0,is_gift:!0,medicine_name:c,medicineName:c})}console.log("\uD83D\uDCBE حفظ البيانات في قاعدة البيانات..."),console.log("\uD83D\uDCC4 بيانات الفاتورة:",e),console.log("\uD83D\uDCE6 عناصر الفاتورة:",f);let g=await (0,u.completeSalesTransaction)(e,f);if(console.log("✅ نتيجة الحفظ:",g),g.success){let a=f.map(a=>{let b=I.find(b=>b.batchId===a.medicine_batch_id),c=b?.name||a.medicine_name||a.medicineName||"غير محدد";return console.log(`🖨️ تحضير عنصر للطباعة:`),console.log(`📋 الدواء الموجود:`,b),console.log(`💊 اسم الدواء للطباعة: ${c}`),{id:a.id||`item_${Date.now()}_${Math.random()}`,batchId:a.medicine_batch_id||a.batchId,name:c,quantity:a.quantity,unitPrice:a.unit_price||a.unitPrice,totalPrice:a.total_price||a.totalPrice,isGift:a.is_gift||a.isGift||!1,medicine_name:c,medicineName:c,medicine_batches:{batch_code:b?.batchCode||"",expiry_date:b?.expiry||"",medicines:{name:c,category:b?.category||"",manufacturer:b?.manufacturer||"",strength:b?.strength||"",form:b?.form||""}}}}),g={...e,invoiceNumber:d,date:Y(),customerName:w?.name||c,customerPhone:w?.phone,customerAddress:w?.address,items:a,subtotal:ac(),discount:y,finalAmount:ad(),sales_invoice_items:a};H(g),alert("تم حفظ الفاتورة بنجاح!"),setTimeout(()=>{(0,s.printInvoice)(g,"sales",U)},500),await $(),b([]),x(null),k(""),z(0),P("cash"),R(""),T("")}else{console.error("❌ فشل في حفظ الفاتورة:",g.error);let a=g.error?.message||"خطأ غير معروف";alert(`حدث خطأ أثناء حفظ الفاتورة:
${a}

يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.`)}}catch(a){console.error("\uD83D\uDCA5 خطأ غير متوقع في حفظ الفاتورة:",a),alert(`حدث خطأ غير متوقع أثناء حفظ الفاتورة:
${a}

يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.`)}finally{N(!1)}},af=async()=>{if(0===a.length)return void alert("لا توجد بيانات للتصدير");let b=G||{invoiceNumber:X(),date:Y(),customerName:w?.name||c,subtotal:ac(),discount:y,finalAmount:ad()};await (0,t.P3)([b])?alert("تم تصدير الفاتورة بنجاح!"):alert("حدث خطأ أثناء التصدير")};return(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"المبيعات"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"إنشاء فاتورة مبيعات جديدة"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:V?W:""})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"البحث عن الأدوية"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",placeholder:"ابحث عن دواء...",value:A,onChange:a=>B(a.target.value),className:"w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),A&&(0,d.jsxs)("div",{className:"mt-4 max-h-60 overflow-y-auto",children:[_.map(c=>(0,d.jsxs)("div",{onClick:()=>(c=>{if(c.quantity<=0)return void alert("هذا الدواء غير متوفر في المخزون");let d=a.find(a=>a.batchId===c.batchId);if(d){if(d.quantity+d.giftQuantity+1>c.quantity)return void alert("الكمية المطلوبة تتجاوز المتوفر في المخزون");b(a=>a.map(a=>a.batchId===c.batchId?{...a,quantity:a.quantity+1,totalPrice:(a.quantity+1)*a.unitPrice}:a))}else b([...a,{id:c.id,batchId:c.batchId,medicineName:c.name,batchCode:c.batchCode,quantity:1,giftQuantity:0,unitPrice:c.price,totalPrice:c.price,expiryDate:c.expiry,availableQuantity:c.quantity}]);B("")})(c),className:"flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:c.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["وجبة: ",c.batchCode," • متوفر: ",c.quantity," • انتهاء: ",c.expiry]})]}),(0,d.jsxs)("div",{className:"text-left",children:[(0,d.jsxs)("p",{className:"font-medium text-gray-900",children:[c.price," د.ع"]}),(0,d.jsx)("button",{className:"text-blue-600 text-sm hover:text-blue-800",children:"إضافة"})]})]},`${c.id}-${c.batchCode}`)),0===_.length&&(0,d.jsx)("p",{className:"text-gray-500 text-center py-4",children:"لا توجد نتائج"})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"عناصر الفاتورة"}),0===a.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(i.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500",children:"لا توجد عناصر في الفاتورة"})]}):(0,d.jsx)("div",{className:"space-y-3",children:a.map(a=>(0,d.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:a.medicineName}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["وجبة: ",a.batchCode," • انتهاء: ",a.expiryDate," • متوفر: ",a.availableQuantity]})]}),(0,d.jsx)("button",{onClick:()=>{var c;return c=a.batchId,void b(a=>a.filter(a=>a.batchId!==c))},className:"p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200",children:(0,d.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"البيع"}),(0,d.jsxs)("span",{className:"text-sm text-blue-600",children:[a.unitPrice," د.ع/قطعة"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>aa(a.batchId,a.quantity-1),className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200",children:"-"}),(0,d.jsx)("input",{type:"number",value:a.quantity||"",onChange:b=>{let c=b.target.value;if(""===c)aa(a.batchId,0);else{let b=parseInt(c);isNaN(b)||aa(a.batchId,b)}},onBlur:b=>{(""===b.target.value||0===parseInt(b.target.value))&&aa(a.batchId,1)},className:"w-16 text-center font-medium border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500",min:"0",max:a.availableQuantity,placeholder:"1"},`quantity-${a.batchId}`),(0,d.jsx)("button",{onClick:()=>aa(a.batchId,a.quantity+1),className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200",children:"+"}),(0,d.jsxs)("span",{className:"mr-2 text-sm font-medium text-blue-800",children:["= ",a.totalPrice," د.ع"]})]})]}),(0,d.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-green-800",children:"هدية"}),(0,d.jsx)("span",{className:"text-sm text-green-600",children:"مجاناً"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>ab(a.batchId,a.giftQuantity-1),className:"w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 hover:bg-green-200",children:"-"}),(0,d.jsx)("input",{type:"number",value:a.giftQuantity||"",onChange:b=>{let c=b.target.value;if(""===c)ab(a.batchId,0);else{let b=parseInt(c);isNaN(b)||ab(a.batchId,b)}},className:"w-16 text-center font-medium border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-green-500",min:"0",max:a.availableQuantity,placeholder:"0"}),(0,d.jsx)("button",{onClick:()=>ab(a.batchId,a.giftQuantity+1),className:"w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 hover:bg-green-200",children:"+"}),(0,d.jsx)(l,{className:"h-4 w-4 text-green-600 mr-2"})]})]})]}),(0,d.jsx)("div",{className:"mt-3 text-right",children:(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["إجمالي الكمية: ",a.quantity+a.giftQuantity," • المتبقي: ",a.availableQuantity-(a.quantity+a.giftQuantity)]})})]},a.batchId))})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"معلومات العميل"}),(0,d.jsx)("div",{className:"space-y-4",children:w?(0,d.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:w.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:w.phone}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:w.address})]}),(0,d.jsx)("button",{onClick:()=>{x(null),k("")},className:"text-red-600 hover:text-red-800",children:(0,d.jsx)(j.A,{className:"h-4 w-4"})})]})}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم العميل"}),(0,d.jsx)("input",{type:"text",value:c,onChange:a=>k(a.target.value),placeholder:"اسم العميل (اختياري)",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("button",{onClick:()=>D(!0),className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"اختيار من قائمة العملاء"]})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"إجمالي الفاتورة"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"المجموع الفرعي:"}),(0,d.jsxs)("span",{className:"font-medium",children:[Z(ac())," د.ع"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(n,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("input",{type:"number",value:y,onChange:a=>z(Number(a.target.value)),placeholder:"مبلغ الخصم",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-gray-600",children:"د.ع"})]}),(0,d.jsx)("div",{className:"border-t pt-3",children:(0,d.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,d.jsx)("span",{children:"المجموع النهائي:"}),(0,d.jsxs)("span",{className:"text-blue-600",children:[Z(ad())," د.ع"]})]})}),(0,d.jsxs)("div",{className:"border-t pt-3 mt-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"حالة الفاتورة:"}),(0,d.jsx)("span",{className:`font-medium px-2 py-1 rounded-full text-xs ${a.length>0?"bg-green-100 text-green-700":"bg-orange-100 text-orange-700"}`,children:a.length>0?"✓ جاهزة للحفظ":"⚠ تحتاج عناصر"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"عدد العناصر:"}),(0,d.jsx)("span",{className:"font-medium text-blue-600",children:a.length})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"طريقة الدفع"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)("button",{onClick:()=>P("cash"),className:`p-3 border-2 rounded-lg text-center transition-colors ${"cash"===O?"border-green-500 bg-green-50 text-green-700":"border-gray-300 text-gray-600 hover:border-green-300"}`,children:[(0,d.jsx)("div",{className:"font-medium",children:"نقداً"}),(0,d.jsx)("div",{className:"text-sm",children:"دفع فوري"})]}),(0,d.jsxs)("button",{onClick:()=>P("credit"),className:`p-3 border-2 rounded-lg text-center transition-colors ${"credit"===O?"border-orange-500 bg-orange-50 text-orange-700":"border-gray-300 text-gray-600 hover:border-orange-300"}`,children:[(0,d.jsx)("div",{className:"font-medium",children:"آجل"}),(0,d.jsx)("div",{className:"text-sm",children:"دفع لاحق"})]})]}),"credit"===O&&(0,d.jsx)("div",{className:"mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,d.jsx)("p",{className:"text-orange-800 text-sm",children:"⚠️ سيتم إضافة هذا المبلغ لحساب العميل كدين"})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"الملاحظات"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات عامة (تظهر في الطباعة)"}),(0,d.jsx)("textarea",{value:Q,onChange:a=>R(a.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ملاحظات للعميل..."})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات خاصة (للنظام فقط)"}),(0,d.jsx)("textarea",{value:S,onChange:a=>T(a.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50",placeholder:"ملاحظات داخلية..."})]})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("button",{onClick:ae,disabled:0===a.length||M,className:`w-full px-4 py-3 rounded-lg flex items-center justify-center gap-2 font-semibold transition-all duration-200 min-h-[48px] ${0===a.length||M?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700 hover:shadow-lg transform hover:scale-105"}`,children:[(0,d.jsx)(o.A,{className:"h-5 w-5"}),M?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"جاري الحفظ..."]}):"حفظ الفاتورة"]}),0===a.length&&(0,d.jsx)("p",{className:"text-center text-sm text-gray-500 bg-gray-50 p-2 rounded-lg",children:"أضف عناصر للفاتورة لتفعيل زر الحفظ"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)("button",{onClick:()=>{!G&&a.length>0&&ae(),F(!0)},disabled:0===a.length,className:"bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),"معاينة وطباعة"]}),(0,d.jsxs)("button",{onClick:()=>{G?(0,s.printInvoice)(G,"sales",U):alert("لا توجد فاتورة للطباعة")},disabled:!G,className:"bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),"طباعة مباشرة"]})]}),(0,d.jsxs)("button",{onClick:af,disabled:0===a.length,className:"w-full bg-orange-600 text-white px-4 py-3 rounded-lg hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),"تصدير للإكسل"]})]})]})]}),C&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-96 overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"اختيار عميل"}),(0,d.jsx)("button",{onClick:()=>D(!1),className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(j.A,{className:"h-5 w-5"})})]}),(0,d.jsx)("div",{className:"space-y-2",children:K.map(a=>(0,d.jsxs)("div",{onClick:()=>{x(a),k(a.name),D(!1)},className:"p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer",children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.phone}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.address})]},a.id))}),(0,d.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,d.jsx)("button",{onClick:()=>D(!1),className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200",children:"إلغاء"})})]})}),E&&G&&(0,d.jsx)(r.Ay,{title:"فاتورة مبيعات",data:G,type:"invoice",settings:U,onClose:()=>F(!1),children:(0,d.jsx)(r.dt,{invoice:G,type:"sales",settings:U})})]})})}},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79507:(a,b,c)=>{Promise.resolve().then(c.bind(c,43227))},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},99926:(a,b,c)=>{"use strict";c.d(b,{KS:()=>f,P3:()=>e});let d=async a=>{try{let b=`
      <table>
        <thead>
          <tr>
            ${a.headers.map(a=>`<th>${a}</th>`).join("")}
          </tr>
        </thead>
        <tbody>
          ${a.rows.map(a=>`<tr>${a.map(a=>`<td>${a}</td>`).join("")}</tr>`).join("")}
        </tbody>
      </table>
    `,c=new Blob([b],{type:"application/vnd.ms-excel;charset=utf-8;"}),d=document.createElement("a"),e=URL.createObjectURL(c);return d.setAttribute("href",e),d.setAttribute("download",`${a.filename}.xls`),d.style.visibility="hidden",document.body.appendChild(d),d.click(),document.body.removeChild(d),!0}catch(a){return console.error("Error exporting to Excel:",a),!1}},e=a=>d({headers:["رقم الفاتورة","التاريخ","العميل","المجموع الفرعي","الخصم","المجموع النهائي","طريقة الدفع","ملاحظات"],rows:a.map(a=>[a.invoiceNumber||"",a.date||"",a.customerName||"",a.subtotal||0,a.discount||0,a.finalAmount||0,a.paymentMethod||"",a.notes||""]),filename:`مبيعات_${new Date().toISOString().split("T")[0]}`,sheetName:"المبيعات"}),f=a=>d({headers:["اسم العميل","رقم الهاتف","البريد الإلكتروني","العنوان","ملاحظات","تاريخ الإضافة"],rows:a.map(a=>[a.name||"",a.phone||"",a.email||"",a.address||"",a.notes||"",a.created_at||""]),filename:`عملاء_${new Date().toISOString().split("T")[0]}`,sheetName:"العملاء"})}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,463,314,979,31,711,997],()=>b(b.s=60367));module.exports=c})();