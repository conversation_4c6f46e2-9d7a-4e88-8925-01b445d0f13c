import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface Medicine {
  id: string
  name: string
  category: string
  manufacturer: string
  active_ingredient: string
  strength: string
  form: string // tablet, capsule, syrup, etc.
  unit_price: number
  selling_price: number
  created_at: string
  updated_at: string
}

export interface MedicineBatch {
  id: string
  medicine_id: string
  batch_code: string
  expiry_date: string
  quantity: number
  cost_price: number
  selling_price: number
  supplier_id: string
  received_date: string
  created_at: string
  updated_at: string
}

export interface Customer {
  id: string
  name: string
  phone?: string
  email?: string
  address?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface Supplier {
  id: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface SalesInvoice {
  id: string
  invoice_number: string
  customer_id?: string
  customer_name?: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface SalesInvoiceItem {
  id: string
  invoice_id: string
  medicine_batch_id: string
  quantity: number
  unit_price: number
  total_price: number
  is_gift: boolean
  created_at: string
}

export interface PurchaseInvoice {
  id: string
  invoice_number: string
  supplier_id: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface PurchaseInvoiceItem {
  id: string
  invoice_id: string
  medicine_id: string
  batch_code: string
  quantity: number
  unit_cost: number
  total_cost: number
  expiry_date: string
  created_at: string
}

export interface SalesReturn {
  id: string
  original_invoice_id: string
  return_number: string
  total_amount: number
  reason: string
  notes?: string
  created_at: string
}

export interface PurchaseReturn {
  id: string
  original_invoice_id: string
  return_number: string
  total_amount: number
  reason: string
  notes?: string
  created_at: string
}

export interface InventoryMovement {
  id: string
  medicine_batch_id: string
  movement_type: 'in' | 'out' | 'adjustment'
  quantity: number
  reference_type: 'sale' | 'purchase' | 'return' | 'adjustment'
  reference_id?: string
  notes?: string
  created_at: string
}
