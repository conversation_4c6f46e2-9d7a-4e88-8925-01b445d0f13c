'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Search,
  Filter,
  Eye,
  Printer,
  Download,
  Calendar,
  Building,
  DollarSign,
  FileText,
  RefreshCw,
  Edit,
  Trash2,
  X
} from 'lucide-react'
import PrintTemplate, { InvoicePrint } from '@/components/PrintTemplate'
import { usePrintSettings, printInvoice } from '@/hooks/usePrintSettings'
import { formatCurrency, formatDate } from '@/utils/formatters'

interface PurchaseInvoice {
  id: string
  invoice_number: string
  supplier_id?: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: 'cash' | 'credit'
  payment_status: 'paid' | 'pending'
  notes?: string
  created_at: string
  suppliers?: {
    name: string
    phone?: string
    address?: string
  }
  purchase_invoice_items?: any[]
}

export default function PurchasesHistoryPage() {
  const [purchaseInvoices, setPurchaseInvoices] = useState<PurchaseInvoice[]>([])
  const [filteredInvoices, setFilteredInvoices] = useState<PurchaseInvoice[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedInvoice, setSelectedInvoice] = useState<PurchaseInvoice | null>(null)
  const [showInvoiceModal, setShowInvoiceModal] = useState(false)
  const [showPrintPreview, setShowPrintPreview] = useState(false)
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    paymentStatus: '',
    paymentMethod: '',
    supplier: ''
  })
  const { settings: printSettings } = usePrintSettings()

  useEffect(() => {
    loadPurchaseInvoices()
  }, [])

  useEffect(() => {
    filterInvoices()
  }, [purchaseInvoices, searchTerm, filters])

  const loadPurchaseInvoices = async () => {
    setLoading(true)
    try {
      // Try to load from localStorage first
      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')

      if (localInvoices.length > 0) {
        setPurchaseInvoices(localInvoices)
      } else {
        // Fallback to mock data if no local data
        const mockData: PurchaseInvoice[] = [
          {
            id: '1',
            invoice_number: 'PUR-001',
            total_amount: 250000,
            discount_amount: 10000,
            final_amount: 240000,
            payment_method: 'cash',
            payment_status: 'paid',
            created_at: '2024-01-15T09:30:00Z',
            suppliers: {
              name: 'شركة الأدوية المتحدة',
              phone: '07701234567'
            }
          },
          {
            id: '2',
            invoice_number: 'PUR-002',
            total_amount: 180000,
            discount_amount: 5000,
            final_amount: 175000,
            payment_method: 'credit',
            payment_status: 'pending',
            created_at: '2024-01-15T14:20:00Z',
            suppliers: {
              name: 'مختبرات الشرق الأوسط',
              phone: '07709876543'
            }
          }
        ]
        setPurchaseInvoices(mockData)
      }
    } catch (error) {
      console.error('Error loading purchase invoices:', error)
      alert('حدث خطأ أثناء تحميل فواتير المشتريات')
    } finally {
      setLoading(false)
    }
  }

  const filterInvoices = () => {
    let filtered = purchaseInvoices

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(invoice =>
        invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.suppliers?.name?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Date filters
    if (filters.startDate) {
      filtered = filtered.filter(invoice => 
        new Date(invoice.created_at) >= new Date(filters.startDate)
      )
    }
    if (filters.endDate) {
      filtered = filtered.filter(invoice => 
        new Date(invoice.created_at) <= new Date(filters.endDate)
      )
    }

    // Status filters
    if (filters.paymentStatus) {
      filtered = filtered.filter(invoice => invoice.payment_status === filters.paymentStatus)
    }
    if (filters.paymentMethod) {
      filtered = filtered.filter(invoice => invoice.payment_method === filters.paymentMethod)
    }

    setFilteredInvoices(filtered)
  }

  const handleViewInvoice = (invoice: PurchaseInvoice) => {
    setSelectedInvoice(invoice)
    setShowInvoiceModal(true)
  }

  const handlePrintInvoice = (invoice: PurchaseInvoice) => {
    setSelectedInvoice(invoice)
    setShowPrintPreview(true)
  }

  const handleDirectPrint = (invoice: PurchaseInvoice) => {
    printInvoice(invoice, 'purchase', printSettings)
  }

  const calculateTotalPurchases = () => {
    return filteredInvoices.reduce((total, invoice) => total + invoice.final_amount, 0)
  }

  const calculatePaidAmount = () => {
    return filteredInvoices
      .filter(invoice => invoice.payment_status === 'paid')
      .reduce((total, invoice) => total + invoice.final_amount, 0)
  }

  const calculatePendingAmount = () => {
    return filteredInvoices
      .filter(invoice => invoice.payment_status === 'pending')
      .reduce((total, invoice) => total + invoice.final_amount, 0)
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">سجل المشتريات</h1>
            <p className="text-gray-600 mt-1">عرض وإدارة جميع فواتير المشتريات</p>
          </div>
          <button
            onClick={loadPurchaseInvoices}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            تحديث
          </button>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الفواتير</p>
                <p className="text-2xl font-bold text-gray-900">{filteredInvoices.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المشتريات</p>
                <p className="text-2xl font-bold text-orange-600">{formatCurrency(calculateTotalPurchases())}</p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المبلغ المدفوع</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(calculatePaidAmount())}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المبلغ المعلق</p>
                <p className="text-2xl font-bold text-red-600">{formatCurrency(calculatePendingAmount())}</p>
              </div>
              <DollarSign className="h-8 w-8 text-red-600" />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">البحث والفلترة</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">البحث</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="رقم الفاتورة أو اسم المورد..."
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
              <input
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
              <input
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">حالة الدفع</label>
              <select
                value={filters.paymentStatus}
                onChange={(e) => setFilters({ ...filters, paymentStatus: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">الكل</option>
                <option value="paid">مدفوع</option>
                <option value="pending">معلق</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">طريقة الدفع</label>
              <select
                value={filters.paymentMethod}
                onChange={(e) => setFilters({ ...filters, paymentMethod: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">الكل</option>
                <option value="cash">نقداً</option>
                <option value="credit">آجل</option>
              </select>
            </div>
          </div>
        </div>

        {/* Invoices Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">فواتير المشتريات</h3>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="mr-3 text-gray-600">جاري التحميل...</span>
            </div>
          ) : filteredInvoices.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">لا توجد فواتير مشتريات</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الفاتورة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المورد</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ النهائي</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">طريقة الدفع</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">حالة الدفع</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredInvoices.map((invoice) => (
                    <tr key={invoice.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                        {invoice.invoice_number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.suppliers?.name || 'غير محدد'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-orange-600">
                        {formatCurrency(invoice.final_amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          invoice.payment_status === 'paid' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(invoice.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleViewInvoice(invoice)}
                            className="text-blue-600 hover:text-blue-900"
                            title="عرض التفاصيل"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handlePrintInvoice(invoice)}
                            className="text-green-600 hover:text-green-900"
                            title="معاينة وطباعة"
                          >
                            <Printer className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDirectPrint(invoice)}
                            className="text-purple-600 hover:text-purple-900"
                            title="طباعة مباشرة"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Print Preview Modal */}
        {showPrintPreview && selectedInvoice && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-xl font-semibold text-gray-900">معاينة فاتورة المشتريات</h2>
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => printInvoice(selectedInvoice, 'purchase', printSettings)}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <Printer className="h-4 w-4" />
                    طباعة
                  </button>
                  <button
                    onClick={() => setShowPrintPreview(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>
              <div className="p-4 overflow-auto max-h-[calc(90vh-120px)]">
                <div
                  dangerouslySetInnerHTML={{
                    __html: require('@/utils/larenPrintTemplate').generateLarenInvoiceHTML(selectedInvoice, 'purchase', printSettings)
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  )
}
