(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[41],{16785:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},23227:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},33109:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},54213:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},57434:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},64261:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},66932:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68500:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},69074:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84355:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]])},86087:function(t,e,n){"undefined"!=typeof globalThis||void 0!==this||("undefined"!=typeof window?window:"undefined"!=typeof self?self:n.g),t.exports=function(){"use strict";var t={28:function(t,e){var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlRowInput=void 0,e.defaultStyles=function(t){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/t,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}},e.getTheme=function(t){return({striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}})[t]},e.HtmlRowInput=function(t){function e(e){var n=t.call(this)||this;return n._element=e,n}return o(e,t),e}(Array)},150:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e,n,o,r){o=o||{};var i=r.internal.scaleFactor,l=r.internal.getFontSize()/i,a=l*(r.getLineHeightFactor?r.getLineHeightFactor():1.15),s="",h=1;if(("middle"===o.valign||"bottom"===o.valign||"center"===o.halign||"right"===o.halign)&&(h=(s="string"==typeof t?t.split(/\r\n|\r|\n/g):t).length||1),n+=.8500000000000001*l,"middle"===o.valign?n-=h/2*a:"bottom"===o.valign&&(n-=h*a),"center"===o.halign||"right"===o.halign){var u=l;if("center"===o.halign&&(u*=.5),s&&h>=1){for(var d=0;d<s.length;d++)r.text(s[d],e-r.getStringUnitWidth(s[d])*u,n),n+=a;return r}e-=r.getStringUnitWidth(t)*u}return"justify"===o.halign?r.text(t,e,n,{maxWidth:o.maxWidth||100,align:"justify"}):r.text(t,e,n),r}},152:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.calculateWidths=function(t,e){n=t,l=e,a=n.scaleFactor(),s=l.settings.horizontalPageBreak,h=(0,o.getPageAvailableWidth)(n,l),l.allRows().forEach(function(t){for(var e=0,r=l.columns;e<r.length;e++){var i=r[e],u=t.cells[i.index];if(u){var d=l.hooks.didParseCell;l.callCellHooks(n,d,u,t,i,null);var c=u.padding("horizontal");u.contentWidth=(0,o.getStringWidth)(u.text,u.styles,n)+c;var f=(0,o.getStringWidth)(u.text.join(" ").split(/[^\S\u00A0]+/),u.styles,n);if(u.minReadableWidth=f+u.padding("horizontal"),"number"==typeof u.styles.cellWidth)u.minWidth=u.styles.cellWidth,u.wrappedWidth=u.styles.cellWidth;else if("wrap"===u.styles.cellWidth||!0===s)u.contentWidth>h?(u.minWidth=h,u.wrappedWidth=h):(u.minWidth=u.contentWidth,u.wrappedWidth=u.contentWidth);else{var p=10/a;u.minWidth=u.styles.minCellWidth||p,u.wrappedWidth=u.contentWidth,u.minWidth>u.wrappedWidth&&(u.wrappedWidth=u.minWidth)}}}}),l.allRows().forEach(function(t){for(var e=0,n=l.columns;e<n.length;e++){var o=n[e],r=t.cells[o.index];if(r&&1===r.colSpan)o.wrappedWidth=Math.max(o.wrappedWidth,r.wrappedWidth),o.minWidth=Math.max(o.minWidth,r.minWidth),o.minReadableWidth=Math.max(o.minReadableWidth,r.minReadableWidth);else{var i=l.styles.columnStyles[o.dataKey]||l.styles.columnStyles[o.index]||{},a=i.cellWidth||i.minCellWidth;a&&"number"==typeof a&&(o.minWidth=a,o.wrappedWidth=a)}r&&(r.colSpan>1&&!o.minWidth&&(o.minWidth=r.minWidth),r.colSpan>1&&!o.wrappedWidth&&(o.wrappedWidth=r.minWidth))}});var n,l,a,s,h,u=[],d=0;e.columns.forEach(function(t){var n=t.getMaxCustomCellWidth(e);n?t.width=n:(t.width=t.wrappedWidth,u.push(t)),d+=t.width});var c=e.getWidth(t.pageSize().width)-d;c&&(c=r(u,c,function(t){return Math.max(t.minReadableWidth,t.minWidth)})),c&&(c=r(u,c,function(t){return t.minWidth})),c=Math.abs(c),!e.settings.horizontalPageBreak&&c>.1/t.scaleFactor()&&(c=c<1?c:Math.round(c),console.warn("Of the table content, ".concat(c," units width could not fit page"))),function(t){for(var e=t.allRows(),n=0;n<e.length;n++)for(var o=e[n],r=null,i=0,l=0,a=0;a<t.columns.length;a++){var s=t.columns[a];if((l-=1)>1&&t.columns[a+1])i+=s.width,delete o.cells[s.index];else if(r){var h=r;delete o.cells[s.index],r=null,h.width=s.width+i}else{var h=o.cells[s.index];if(!h)continue;if(l=h.colSpan,i=0,h.colSpan>1){r=h,i+=s.width;continue}h.width=s.width+i}}}(e),function(t,e){for(var n={count:0,height:0},o=0,r=t.allRows();o<r.length;o++){for(var l=r[o],a=0,s=t.columns;a<s.length;a++){var h=s[a],u=l.cells[h.index];if(u){e.applyStyles(u.styles,!0);var d=u.width-u.padding("horizontal");if("linebreak"===u.styles.overflow)u.text=e.splitTextToSize(u.text,d+1/e.scaleFactor(),{fontSize:u.styles.fontSize});else if("ellipsize"===u.styles.overflow)u.text=i(u.text,d,u.styles,e,"...");else if("hidden"===u.styles.overflow)u.text=i(u.text,d,u.styles,e,"");else if("function"==typeof u.styles.overflow){var c=u.styles.overflow(u.text,d);"string"==typeof c?u.text=[c]:u.text=c}u.contentHeight=u.getContentHeight(e.scaleFactor(),e.getLineHeightFactor());var f=u.contentHeight/u.rowSpan;u.rowSpan>1&&n.count*n.height<f*u.rowSpan?n={height:f,count:u.rowSpan}:n&&n.count>0&&n.height>f&&(f=n.height),f>l.height&&(l.height=f)}}n.count--}}(e,t),function(t){for(var e={},n=1,o=t.allRows(),r=0;r<o.length;r++)for(var i=o[r],l=0,a=t.columns;l<a.length;l++){var s=a[l],h=e[s.index];if(n>1)n--,delete i.cells[s.index];else if(h)h.cell.height+=i.height,n=h.cell.colSpan,delete i.cells[s.index],h.left--,h.left<=1&&delete e[s.index];else{var u=i.cells[s.index];if(!u)continue;if(u.height=i.height,u.rowSpan>1){var d=o.length-r,c=u.rowSpan>d?d:u.rowSpan;e[s.index]={cell:u,left:c,row:i}}}}}(e)},e.resizeColumns=r,e.ellipsize=i;var o=n(799);function r(t,e,n){for(var o=e,i=t.reduce(function(t,e){return t+e.wrappedWidth},0),l=0;l<t.length;l++){var a=t[l],s=o*(a.wrappedWidth/i),h=a.width+s,u=n(a),d=h<u?u:h;e-=d-a.width,a.width=d}if(e=Math.round(1e10*e)/1e10){var c=t.filter(function(t){return!(e<0)||t.width>n(t)});c.length&&(e=r(c,e,n))}return e}function i(t,e,n,r,i){return t.map(function(t){var l=t,a=e,s=n,h=r,u=i,d=1e4*h.scaleFactor();if((a=Math.ceil(a*d)/d)>=(0,o.getStringWidth)(l,s,h))return l;for(;a<(0,o.getStringWidth)(l+u,s,h)&&!(l.length<=1);)l=l.substring(0,l.length-1);return l.trim()+u})}},176:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.assign=function(t,e,n,o,r){if(null==t)throw TypeError("Cannot convert undefined or null to object");for(var i=Object(t),l=1;l<arguments.length;l++){var a=arguments[l];if(null!=a)for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(i[s]=a[s])}return i}},344:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.validateInput=function(t,e,n){for(var o=0,r=[t,e,n];o<r.length;o++){var i=r[o];i&&"object"!=typeof i&&console.error("The options parameter should be of type object, is: "+typeof i),i.startY&&"number"!=typeof i.startY&&(console.error("Invalid value for startY option",i.startY),delete i.startY)}}},371:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseInput=function(t,e){var n,s,h,u,d,c,f,p,g,y,v,m,b,w,x,S,P,C,W,D,k,H,F,j,A,M,_,T,z,O=new r.DocHandler(t),R=O.getDocumentOptions(),L=O.getGlobalOptions();(0,l.validateInput)(L,R,e);var I=(0,a.assign)({},L,R,e);"undefined"!=typeof window&&(z=window);var B=function(t,e,n){for(var o={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},r=function(r){if("columnStyles"===r){var i=t[r],l=e[r],s=n[r];o.columnStyles=(0,a.assign)({},i,l,s)}else{var h=[t,e,n].map(function(t){return t[r]||{}});o[r]=(0,a.assign)({},h[0],h[1],h[2])}},i=0,l=Object.keys(o);i<l.length;i++)r(l[i]);return o}(L,R,e),E=function(t,e,n){for(var o={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},r=0,i=[t,e,n];r<i.length;r++){var l=i[r];l.didParseCell&&o.didParseCell.push(l.didParseCell),l.willDrawCell&&o.willDrawCell.push(l.willDrawCell),l.didDrawCell&&o.didDrawCell.push(l.didDrawCell),l.willDrawPage&&o.willDrawPage.push(l.willDrawPage),l.didDrawPage&&o.didDrawPage.push(l.didDrawPage)}return o}(L,R,e),N=(n=O,s=I,F=(0,o.parseSpacing)(s.margin,40/n.scaleFactor()),j=null!=(h=n,u=s.startY,d=h.getLastAutoTable(),c=h.scaleFactor(),f=h.pageNumber(),p=!1,d&&d.startPageNumber&&(p=d.startPageNumber+d.pageNumber-1===f),g="number"==typeof u?u:(null==u||!1===u)&&p&&(null==d?void 0:d.finalY)!=null?d.finalY+20/c:null)?g:F.top,k=!0===s.showFoot?"everyPage":!1===s.showFoot?"never":null!=(y=s.showFoot)?y:"everyPage",H=!0===s.showHead?"everyPage":!1===s.showHead?"never":null!=(v=s.showHead)?v:"everyPage",A=null!=(m=s.useCss)&&m,M=s.theme||(A?"plain":"striped"),_=!!s.horizontalPageBreak,T=null!=(b=s.horizontalPageBreakRepeat)?b:null,{includeHiddenHtml:null!=(w=s.includeHiddenHtml)&&w,useCss:A,theme:M,startY:j,margin:F,pageBreak:null!=(x=s.pageBreak)?x:"auto",rowPageBreak:null!=(S=s.rowPageBreak)?S:"auto",tableWidth:null!=(P=s.tableWidth)?P:"auto",showHead:H,showFoot:k,tableLineWidth:null!=(C=s.tableLineWidth)?C:0,tableLineColor:null!=(W=s.tableLineColor)?W:200,horizontalPageBreak:_,horizontalPageBreakRepeat:T,horizontalPageBreakBehaviour:null!=(D=s.horizontalPageBreakBehaviour)?D:"afterAllRows"}),Y=function(t,e,n){var o,r,l,a,s,h=e.head||[],u=e.body||[],d=e.foot||[];if(e.html){var c=e.includeHiddenHtml;if(n){var f=(0,i.parseHtml)(t,e.html,n,c,e.useCss)||{};h=f.head||h,u=f.body||h,d=f.foot||h}else console.error("Cannot parse html in non browser environment")}return{columns:e.columns||(o=h,r=u,l=d,a=o[0]||r[0]||l[0]||[],s=[],Object.keys(a).filter(function(t){return"_element"!==t}).forEach(function(t){var e,n=1;"object"!=typeof(e=Array.isArray(a)?a[parseInt(t)]:a[t])||Array.isArray(e)||(n=(null==e?void 0:e.colSpan)||1);for(var o=0;o<n;o++){var r={dataKey:Array.isArray(a)?s.length:t+(o>0?"_".concat(o):"")};s.push(r)}}),s),head:h,body:u,foot:d}}(O,I,z);return{id:e.tableId,content:Y,hooks:E,styles:B,settings:N}};var o=n(799),r=n(643),i=n(660),l=n(344),a=n(176)},376:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.createTable=function(t,e){var n=new r.DocHandler(t),o=function(t,e){var n=t.content,o=n.columns.map(function(t,e){var n,o;return o="object"==typeof t&&null!=(n=t.dataKey)?n:e,new i.Column(o,t,e)});if(0===n.head.length){var r=h(o,"head");r&&n.head.push(r)}if(0===n.foot.length){var r=h(o,"foot");r&&n.foot.push(r)}var l=t.settings.theme,a=t.styles;return{columns:o,head:s("head",n.head,o,a,l,e),body:s("body",n.body,o,a,l,e),foot:s("foot",n.foot,o,a,l,e)}}(e,n.scaleFactor()),l=new i.Table(e,o);return(0,a.calculateWidths)(n,l),n.applyStyles(n.userStyles),l};var o=n(28),r=n(643),i=n(524),l=n(176),a=n(152);function s(t,e,n,r,a,s){var h={};return e.map(function(e,u){for(var d=0,c={},f=0,p=0,g=0;g<n.length;g++){var y=n[g];if(null==h[y.index]||0===h[y.index].left)if(0===p){var v=void 0;v=Array.isArray(e)?e[y.index-f-d]:e[y.dataKey];var m={};"object"!=typeof v||Array.isArray(v)||(m=(null==v?void 0:v.styles)||{});var b=function(t,e,n,r,i,a,s){var h,u=(0,o.getTheme)(r);"head"===t?h=i.headStyles:"body"===t?h=i.bodyStyles:"foot"===t&&(h=i.footStyles);var d=(0,l.assign)({},u.table,u[t],i.styles,h),c=i.columnStyles[e.dataKey]||i.columnStyles[e.index]||{},f="body"===t&&n%2==0?(0,l.assign)({},u.alternateRow,i.alternateRowStyles):{},p=(0,o.defaultStyles)(a),g=(0,l.assign)({},p,d,f,"body"===t?c:{});return(0,l.assign)(g,s)}(t,y,u,a,r,s,m),w=new i.Cell(v,b,t);c[y.dataKey]=w,c[y.index]=w,p=w.colSpan-1,h[y.index]={left:w.rowSpan-1,times:p}}else p--,f++;else h[y.index].left--,p=h[y.index].times,d++}return new i.Row(e,u,t,c)})}function h(t,e){var n={};return t.forEach(function(t){if(null!=t.raw){var o=function(t,e){if("head"===t){if("object"==typeof e)return e.header||null;else if("string"==typeof e||"number"==typeof e)return e}else if("foot"===t&&"object"==typeof e)return e.footer;return null}(e,t.raw);null!=o&&(n[t.dataKey]=o)}}),Object.keys(n).length>0?n:null}},460:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseCss=function(t,e,n,i,l){var a,s,h,u,d,c,f,p,g,y={},v=96/72,m=r(e,function(t){return l.getComputedStyle(t).backgroundColor});null!=m&&(y.fillColor=m);var b=r(e,function(t){return l.getComputedStyle(t).color});null!=b&&(y.textColor=b);var w=(a=i,s=n,h=[a.paddingTop,a.paddingRight,a.paddingBottom,a.paddingLeft],u=96/(72/s),d=(parseInt(a.lineHeight)-parseInt(a.fontSize))/s/2,c=h.map(function(t){return parseInt(t||"0")/u}),d>(f=(0,o.parseSpacing)(c,0)).top&&(f.top=d),d>f.bottom&&(f.bottom=d),f);w&&(y.cellPadding=w);var x="borderTopColor",S=v*n,P=i.borderTopWidth;if(i.borderBottomWidth===P&&i.borderRightWidth===P&&i.borderLeftWidth===P){var C=(parseFloat(P)||0)/S;C&&(y.lineWidth=C)}else y.lineWidth={top:(parseFloat(i.borderTopWidth)||0)/S,right:(parseFloat(i.borderRightWidth)||0)/S,bottom:(parseFloat(i.borderBottomWidth)||0)/S,left:(parseFloat(i.borderLeftWidth)||0)/S},!y.lineWidth.top&&(y.lineWidth.right?x="borderRightColor":y.lineWidth.bottom?x="borderBottomColor":y.lineWidth.left&&(x="borderLeftColor"));var W=r(e,function(t){return l.getComputedStyle(t)[x]});null!=W&&(y.lineColor=W);var D=["left","right","center","justify"];-1!==D.indexOf(i.textAlign)&&(y.halign=i.textAlign),-1!==(D=["middle","bottom","top"]).indexOf(i.verticalAlign)&&(y.valign=i.verticalAlign);var k=parseInt(i.fontSize||"");isNaN(k)||(y.fontSize=k/v);var H=(g="",("bold"===(p=i).fontWeight||"bolder"===p.fontWeight||parseInt(p.fontWeight)>=700)&&(g="bold"),("italic"===p.fontStyle||"oblique"===p.fontStyle)&&(g+="italic"),g);H&&(y.fontStyle=H);var F=(i.fontFamily||"").toLowerCase();return -1!==t.indexOf(F)&&(y.font=F),y};var o=n(799);function r(t,e){var n=function t(e,n){var o=n(e);return"rgba(0, 0, 0, 0)"!==o&&"transparent"!==o&&"initial"!==o&&"inherit"!==o?o:null==e.parentElement?null:t(e.parentElement,n)}(t,e);if(!n)return null;var o=n.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!o||!Array.isArray(o))return null;var r=[parseInt(o[1]),parseInt(o[2]),parseInt(o[3])];return 0===parseInt(o[4])||isNaN(r[0])||isNaN(r[1])||isNaN(r[2])?null:r}},524:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.Column=e.Cell=e.Row=e.Table=void 0;var o=n(799),r=n(28),i=n(601);e.Table=function(){function t(t,e){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return t.prototype.getHeadHeight=function(t){return this.head.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.getFootHeight=function(t){return this.foot.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},t.prototype.callCellHooks=function(t,e,n,o,r,l){for(var a=0;a<e.length;a++){var s=!1===(0,e[a])(new i.CellHookData(t,this,n,o,r,l));if(n.text=Array.isArray(n.text)?n.text:[n.text],s)return!1}return!0},t.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var n=0,o=this.hooks.didDrawPage;n<o.length;n++)(0,o[n])(new i.HookData(t,this,e))},t.prototype.callWillDrawPageHooks=function(t,e){for(var n=0,o=this.hooks.willDrawPage;n<o.length;n++)(0,o[n])(new i.HookData(t,this,e))},t.prototype.getWidth=function(t){if("number"==typeof this.settings.tableWidth)return this.settings.tableWidth;if("wrap"===this.settings.tableWidth)return this.columns.reduce(function(t,e){return t+e.wrappedWidth},0);var e=this.settings.margin;return t-e.left-e.right},t}(),e.Row=function(){function t(t,e,n,o,i){void 0===i&&(i=!1),this.height=0,this.raw=t,t instanceof r.HtmlRowInput&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=n,this.cells=o,this.spansMultiplePages=i}return t.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce(function(t,n){var o;return Math.max(t,(null==(o=e.cells[n.index])?void 0:o.height)||0)},0)},t.prototype.hasRowSpan=function(t){var e=this;return t.filter(function(t){var n=e.cells[t.index];return!!n&&n.rowSpan>1}).length>0},t.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},t.prototype.getMinimumRowHeight=function(t,e){var n=this;return t.reduce(function(t,o){var r=n.cells[o.index];if(!r)return 0;var i=e.getLineHeight(r.styles.fontSize),l=r.padding("vertical")+i;return l>t?l:t},0)},t}(),e.Cell=function(){function t(t,e,n){this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=n,this.raw=t;var o,r=t;null==t||"object"!=typeof t||Array.isArray(t)?(this.rowSpan=1,this.colSpan=1):(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,r=null!=(o=t.content)?o:t,t._element&&(this.raw=t._element));var i=null!=r?""+r:"";this.text=i.split(/\r\n|\r|\n/g)}return t.prototype.getTextPos=function(){if("top"===this.styles.valign)t=this.y+this.padding("top");else if("bottom"===this.styles.valign)t=this.y+this.height-this.padding("bottom");else{var t,e,n=this.height-this.padding("vertical");t=this.y+n/2+this.padding("top")}if("right"===this.styles.halign)e=this.x+this.width-this.padding("right");else if("center"===this.styles.halign){var o=this.width-this.padding("horizontal");e=this.x+o/2+this.padding("left")}else e=this.x+this.padding("left");return{x:e,y:t}},t.prototype.getContentHeight=function(t,e){return void 0===e&&(e=1.15),Math.max((Array.isArray(this.text)?this.text.length:1)*(this.styles.fontSize/t*e)+this.padding("vertical"),this.styles.minCellHeight)},t.prototype.padding=function(t){var e=(0,o.parseSpacing)(this.styles.cellPadding,0);return"vertical"===t?e.top+e.bottom:"horizontal"===t?e.left+e.right:e[t]},t}(),e.Column=function(){function t(t,e,n){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=n}return t.prototype.getMaxCustomCellWidth=function(t){for(var e=0,n=0,o=t.allRows();n<o.length;n++){var r=o[n].cells[this.index];r&&"number"==typeof r.styles.cellWidth&&(e=Math.max(e,r.styles.cellWidth))}return e},t}()},601:function(t,e){var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.CellHookData=e.HookData=void 0;var r=function(t,e,n){this.table=e,this.pageNumber=e.pageNumber,this.settings=e.settings,this.cursor=n,this.doc=t.getDocument()};e.HookData=r,e.CellHookData=function(t){function e(e,n,o,r,i,l){var a=t.call(this,e,n,l)||this;return a.cell=o,a.row=r,a.column=i,a.section=r.section,a}return o(e,t),e}(r)},626:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.calculateAllColumnsCanFitInPage=function(t,e){for(var n=[],r=0;r<e.columns.length;r++){var i=function(t,e,n){void 0===n&&(n={});var r,i=(0,o.getPageAvailableWidth)(t,e),l=new Map,a=[],s=[],h=[];Array.isArray(e.settings.horizontalPageBreakRepeat)?h=e.settings.horizontalPageBreakRepeat:("string"==typeof e.settings.horizontalPageBreakRepeat||"number"==typeof e.settings.horizontalPageBreakRepeat)&&(h=[e.settings.horizontalPageBreakRepeat]),h.forEach(function(t){var n=e.columns.find(function(e){return e.dataKey===t||e.index===t});n&&!l.has(n.index)&&(l.set(n.index,!0),a.push(n.index),s.push(e.columns[n.index]),i-=n.wrappedWidth)});for(var u=!0,d=null!=(r=null==n?void 0:n.start)?r:0;d<e.columns.length;){if(l.has(d)){d++;continue}var c=e.columns[d].wrappedWidth;if(u||i>=c)u=!1,a.push(d),s.push(e.columns[d]),i-=c;else break;d++}return{colIndexes:a,columns:s,lastIndex:d-1}}(t,e,{start:r});i.columns.length&&(n.push(i),r=i.lastIndex)}return n};var o=n(799)},639:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.applyPlugin=function(t){t.API.autoTable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[0],o=(0,l.parseInput)(this,n),r=(0,a.createTable)(this,o);return(0,s.drawTable)(this,r),this},t.API.lastAutoTable=!1,t.API.autoTableText=function(t,e,n,r){(0,o.default)(t,e,n,r,this)},t.API.autoTableSetDefaults=function(t){return r.DocHandler.setDefaults(t,this),this},t.autoTableSetDefaults=function(t,e){r.DocHandler.setDefaults(t,e)},t.API.autoTableHtmlToJson=function(t,e){if(void 0===e&&(e=!1),"undefined"==typeof window)return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var n,o=new r.DocHandler(this),l=(0,i.parseHtml)(o,t,window,e,!1),a=l.head,s=l.body;return{columns:(null==(n=a[0])?void 0:n.map(function(t){return t.content}))||[],rows:s,data:s}}};var o=n(150),r=n(643),i=n(660),l=n(371),a=n(376),s=n(789)},643:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.DocHandler=void 0;var n={};e.DocHandler=function(){function t(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return t.setDefaults=function(t,e){void 0===e&&(e=null),e?e.__autoTableDocumentDefaults=t:n=t},t.unifyColor=function(t){return Array.isArray(t)?t:"number"==typeof t?[t,t,t]:"string"==typeof t?[t]:null},t.prototype.applyStyles=function(e,n){void 0===n&&(n=!1),e.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(e.fontStyle);var o,r,i,l=this.jsPDFDocument.internal.getFont(),a=l.fontStyle,s=l.fontName;if(e.font&&(s=e.font),e.fontStyle){a=e.fontStyle;var h=this.getFontList()[s];h&&-1===h.indexOf(a)&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(h[0]),a=h[0])}if(this.jsPDFDocument.setFont(s,a),e.fontSize&&this.jsPDFDocument.setFontSize(e.fontSize),!n){var u=t.unifyColor(e.fillColor);u&&(o=this.jsPDFDocument).setFillColor.apply(o,u),(u=t.unifyColor(e.textColor))&&(r=this.jsPDFDocument).setTextColor.apply(r,u),(u=t.unifyColor(e.lineColor))&&(i=this.jsPDFDocument).setDrawColor.apply(i,u),"number"==typeof e.lineWidth&&this.jsPDFDocument.setLineWidth(e.lineWidth)}},t.prototype.splitTextToSize=function(t,e,n){return this.jsPDFDocument.splitTextToSize(t,e,n)},t.prototype.rect=function(t,e,n,o,r){return this.jsPDFDocument.rect(t,e,n,o,r)},t.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},t.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},t.prototype.getDocument=function(){return this.jsPDFDocument},t.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},t.prototype.addPage=function(){return this.jsPDFDocument.addPage()},t.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},t.prototype.getGlobalOptions=function(){return n||{}},t.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},t.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return null==t.width&&(t={width:t.getWidth(),height:t.getHeight()}),t},t.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},t.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},t.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},t.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},t}()},660:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseHtml=function(t,e,n,i,l){void 0===i&&(i=!1),void 0===l&&(l=!1);var a,s,h="string"==typeof e?n.document.querySelector(e):e,u=Object.keys(t.getFontList()),d=t.scaleFactor(),c=[],f=[],p=[];if(!h)return console.error("Html table could not be found with input: ",e),{head:c,body:f,foot:p};for(var g=0;g<h.rows.length;g++){var y=h.rows[g],v=null==(s=null==(a=null==y?void 0:y.parentElement)?void 0:a.tagName)?void 0:s.toLowerCase(),m=function(t,e,n,i,l,a){for(var s=new o.HtmlRowInput(i),h=0;h<i.cells.length;h++){var u=i.cells[h],d=n.getComputedStyle(u);if(l||"none"!==d.display){var c=void 0;a&&(c=(0,r.parseCss)(t,u,e,d,n)),s.push({rowSpan:u.rowSpan,colSpan:u.colSpan,styles:c,_element:u,content:function(t){var e=t.cloneNode(!0);return e.innerHTML=e.innerHTML.replace(/\n/g,"").replace(/ +/g," "),e.innerHTML=e.innerHTML.split(/<br.*?>/).map(function(t){return t.trim()}).join("\n"),e.innerText||e.textContent||""}(u)})}}var f=n.getComputedStyle(i);if(s.length>0&&(l||"none"!==f.display))return s}(u,d,n,y,i,l);m&&("thead"===v?c.push(m):"tfoot"===v?p.push(m):f.push(m))}return{head:c,body:f,foot:p}};var o=n(28),r=n(460)},789:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.drawTable=function(t,e){var n=e.settings,o=n.startY,l=n.margin,p={x:l.left,y:o},v=e.getHeadHeight(e.columns)+e.getFootHeight(e.columns),m=o+l.bottom+v;"avoid"===n.pageBreak&&(m+=e.body.reduce(function(t,e){return t+e.height},0));var b=new i.DocHandler(t);("always"===n.pageBreak||null!=n.startY&&m>b.pageSize().height)&&(y(b),p.y=l.top),e.callWillDrawPageHooks(b,p);var w=(0,a.assign)({},p);e.startPageNumber=b.pageNumber(),n.horizontalPageBreak?function(t,e,n,o){var r=(0,s.calculateAllColumnsCanFitInPage)(t,e);if("afterAllRows"===e.settings.horizontalPageBreakBehaviour)r.forEach(function(r,i){var l,a,s,u,f;t.applyStyles(t.userStyles),i>0?g(t,e,n,o,r.columns,!0):h(t,e,o,r.columns),l=t,a=e,s=n,u=o,f=r.columns,l.applyStyles(l.userStyles),a.body.forEach(function(t,e){var n=e===a.body.length-1;c(l,a,t,n,s,u,f)}),d(t,e,o,r.columns)});else for(var i=-1,l=r[0];i<e.body.length-1;)!function(){var a=i;if(l){t.applyStyles(t.userStyles);var s=l.columns;i>=0?g(t,e,n,o,s,!0):h(t,e,o,s),a=u(t,e,i+1,o,s),d(t,e,o,s)}var c=a-i;r.slice(1).forEach(function(r){t.applyStyles(t.userStyles),g(t,e,n,o,r.columns,!0),u(t,e,i+1,o,r.columns,c),d(t,e,o,r.columns)}),i=a}()}(b,e,w,p):(b.applyStyles(b.userStyles),("firstPage"===n.showHead||"everyPage"===n.showHead)&&e.head.forEach(function(t){return f(b,e,t,p,e.columns)}),b.applyStyles(b.userStyles),e.body.forEach(function(t,n){var o=n===e.body.length-1;c(b,e,t,o,w,p,e.columns)}),b.applyStyles(b.userStyles),("lastPage"===n.showFoot||"everyPage"===n.showFoot)&&e.foot.forEach(function(t){return f(b,e,t,p,e.columns)})),(0,r.addTableBorder)(b,e,w,p),e.callEndPageHooks(b,p),e.finalY=p.y,t.lastAutoTable=e,b.applyStyles(b.userStyles)},e.addPage=g;var o=n(150),r=n(799),i=n(643),l=n(524),a=n(176),s=n(626);function h(t,e,n,o){var r=e.settings;t.applyStyles(t.userStyles),("firstPage"===r.showHead||"everyPage"===r.showHead)&&e.head.forEach(function(r){return f(t,e,r,n,o)})}function u(t,e,n,o,r,i){t.applyStyles(t.userStyles);var l=Math.min(n+(i=null!=i?i:e.body.length),e.body.length),a=-1;return e.body.slice(n,l).forEach(function(i,l){var s=n+l===e.body.length-1,h=p(t,e,s,o);i.canEntireRowFit(h,r)&&(f(t,e,i,o,r),a=n+l)}),a}function d(t,e,n,o){var r=e.settings;t.applyStyles(t.userStyles),("lastPage"===r.showFoot||"everyPage"===r.showFoot)&&e.foot.forEach(function(r){return f(t,e,r,n,o)})}function c(t,e,n,o,r,i,s){var h=p(t,e,o,i);if(n.canEntireRowFit(h,s))f(t,e,n,i,s);else if(function(t,e,n,o){var r=t.pageSize().height,i=o.settings.margin,l=r-(i.top+i.bottom);"body"===e.section&&(l-=o.getHeadHeight(o.columns)+o.getFootHeight(o.columns));var a=e.getMinimumRowHeight(o.columns,t);if(a>l)return console.error("Will not be able to print row ".concat(e.index," correctly since it's minimum height is larger than page height")),!0;if(!(a<n))return!1;var s=e.hasRowSpan(o.columns);return e.getMaxCellHeight(o.columns)>l?(s&&console.error("The content of row ".concat(e.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!s&&"avoid"!==o.settings.rowPageBreak}(t,n,h,e)){var u=function(t,e,n,o){var r={};t.spansMultiplePages=!0,t.height=0;for(var i=0,s=0,h=n.columns;s<h.length;s++){var u=h[s],d=t.cells[u.index];if(d){Array.isArray(d.text)||(d.text=[d.text]);var c=new l.Cell(d.raw,d.styles,d.section);(c=(0,a.assign)(c,d)).text=[];var f=function(t,e,n){var o=n.getLineHeight(t.styles.fontSize);return Math.max(0,Math.floor((e-t.padding("vertical"))/o))}(d,e,o);d.text.length>f&&(c.text=d.text.splice(f,d.text.length));var p=o.scaleFactor(),g=o.getLineHeightFactor();d.contentHeight=d.getContentHeight(p,g),d.contentHeight>=e&&(d.contentHeight=e,c.styles.minCellHeight-=e),d.contentHeight>t.height&&(t.height=d.contentHeight),c.contentHeight=c.getContentHeight(p,g),c.contentHeight>i&&(i=c.contentHeight),r[u.index]=c}}var y=new l.Row(t.raw,-1,t.section,r,!0);y.height=i;for(var v=0,m=n.columns;v<m.length;v++){var u=m[v],c=y.cells[u.index];c&&(c.height=y.height);var d=t.cells[u.index];d&&(d.height=t.height)}return y}(n,h,e,t);f(t,e,n,i,s),g(t,e,r,i,s),c(t,e,u,o,r,i,s)}else g(t,e,r,i,s),c(t,e,n,o,r,i,s)}function f(t,e,n,i,l){i.x=e.settings.margin.left;for(var a=0;a<l.length;a++){var s=l[a],h=n.cells[s.index];if(!h||(t.applyStyles(h.styles),h.x=i.x,h.y=i.y,!1===e.callCellHooks(t,e.hooks.willDrawCell,h,n,s,i))){i.x+=s.width;continue}!function(t,e,n){var o=e.styles;if(t.getDocument().setFillColor(t.getDocument().getFillColor()),"number"==typeof o.lineWidth){var i=(0,r.getFillStyle)(o.lineWidth,o.fillColor);i&&t.rect(e.x,n.y,e.width,e.height,i)}else"object"==typeof o.lineWidth&&(o.fillColor&&t.rect(e.x,n.y,e.width,e.height,"F"),function(t,e,n,o){var r,i,l,a;function s(e,n,o,r,i){t.getDocument().setLineWidth(e),t.getDocument().line(n,o,r,i,"S")}o.top&&(r=n.x,i=n.y,l=n.x+e.width,a=n.y,o.right&&(l+=.5*o.right),o.left&&(r-=.5*o.left),s(o.top,r,i,l,a)),o.bottom&&(r=n.x,i=n.y+e.height,l=n.x+e.width,a=n.y+e.height,o.right&&(l+=.5*o.right),o.left&&(r-=.5*o.left),s(o.bottom,r,i,l,a)),o.left&&(r=n.x,i=n.y,l=n.x,a=n.y+e.height,o.top&&(i-=.5*o.top),o.bottom&&(a+=.5*o.bottom),s(o.left,r,i,l,a)),o.right&&(r=n.x+e.width,i=n.y,l=n.x+e.width,a=n.y+e.height,o.top&&(i-=.5*o.top),o.bottom&&(a+=.5*o.bottom),s(o.right,r,i,l,a))}(t,e,n,o.lineWidth))}(t,h,i);var u=h.getTextPos();(0,o.default)(h.text,u.x,u.y,{halign:h.styles.halign,valign:h.styles.valign,maxWidth:Math.ceil(h.width-h.padding("left")-h.padding("right"))},t.getDocument()),e.callCellHooks(t,e.hooks.didDrawCell,h,n,s,i),i.x+=s.width}i.y+=n.height}function p(t,e,n,o){var r=e.settings.margin.bottom,i=e.settings.showFoot;return("everyPage"===i||"lastPage"===i&&n)&&(r+=e.getFootHeight(e.columns)),t.pageSize().height-o.y-r}function g(t,e,n,o,i,l){void 0===i&&(i=[]),void 0===l&&(l=!1),t.applyStyles(t.userStyles),"everyPage"!==e.settings.showFoot||l||e.foot.forEach(function(n){return f(t,e,n,o,i)}),e.callEndPageHooks(t,o);var a=e.settings.margin;(0,r.addTableBorder)(t,e,n,o),y(t),e.pageNumber++,o.x=a.left,o.y=a.top,n.y=a.top,e.callWillDrawPageHooks(t,o),"everyPage"===e.settings.showHead&&(e.head.forEach(function(n){return f(t,e,n,o,i)}),t.applyStyles(t.userStyles))}function y(t){var e=t.pageNumber();return t.setPage(e+1),t.pageNumber()===e&&(t.addPage(),!0)}},799:function(t,e){function n(t,e){var n=t>0,o=e||0===e;return n&&o?"DF":n?"S":o?"F":null}function o(t,e){var n,o,r,i;if(Array.isArray(t=t||e))if(t.length>=4)return{top:t[0],right:t[1],bottom:t[2],left:t[3]};else{if(3===t.length)return{top:t[0],right:t[1],bottom:t[2],left:t[1]};if(2===t.length)return{top:t[0],right:t[1],bottom:t[0],left:t[1]};t=1===t.length?t[0]:e}return"object"==typeof t?("number"==typeof t.vertical&&(t.top=t.vertical,t.bottom=t.vertical),"number"==typeof t.horizontal&&(t.right=t.horizontal,t.left=t.horizontal),{left:null!=(n=t.left)?n:e,top:null!=(o=t.top)?o:e,right:null!=(r=t.right)?r:e,bottom:null!=(i=t.bottom)?i:e}):("number"!=typeof t&&(t=e),{top:t,right:t,bottom:t,left:t})}Object.defineProperty(e,"__esModule",{value:!0}),e.getStringWidth=function(t,e,n){return n.applyStyles(e,!0),(Array.isArray(t)?t:[t]).map(function(t){return n.getTextWidth(t)}).reduce(function(t,e){return Math.max(t,e)},0)},e.addTableBorder=function(t,e,o,r){var i=e.settings.tableLineWidth,l=e.settings.tableLineColor;t.applyStyles({lineWidth:i,lineColor:l});var a=n(i,!1);a&&t.rect(o.x,o.y,e.getWidth(t.pageSize().width),r.y-o.y,a)},e.getFillStyle=n,e.parseSpacing=o,e.getPageAvailableWidth=function(t,e){var n=o(e.settings.margin,0);return t.pageSize().width-(n.left+n.right)}}},e={};function n(o){var r=e[o];if(void 0!==r)return r.exports;var i=e[o]={exports:{}};return t[o].call(i.exports,i,i.exports,n),i.exports}var o={};return!function(){Object.defineProperty(o,"__esModule",{value:!0}),o.Table=o.Row=o.HookData=o.Column=o.CellHookData=o.Cell=o.applyPlugin=void 0,o.autoTable=h,o.__createTable=function(t,e){var n=(0,i.parseInput)(t,e);return(0,a.createTable)(t,n)},o.__drawTable=function(t,e){(0,s.drawTable)(t,e)};var t,e=n(639);Object.defineProperty(o,"applyPlugin",{enumerable:!0,get:function(){return e.applyPlugin}});var r=n(601);Object.defineProperty(o,"CellHookData",{enumerable:!0,get:function(){return r.CellHookData}}),Object.defineProperty(o,"HookData",{enumerable:!0,get:function(){return r.HookData}});var i=n(371),l=n(524);Object.defineProperty(o,"Cell",{enumerable:!0,get:function(){return l.Cell}}),Object.defineProperty(o,"Column",{enumerable:!0,get:function(){return l.Column}}),Object.defineProperty(o,"Row",{enumerable:!0,get:function(){return l.Row}}),Object.defineProperty(o,"Table",{enumerable:!0,get:function(){return l.Table}});var a=n(376),s=n(789);function h(t,e){var n=(0,i.parseInput)(t,e),o=(0,a.createTable)(t,n);(0,s.drawTable)(t,o)}try{if("undefined"!=typeof window&&window){var u=window,d=u.jsPDF||(null==(t=u.jspdf)?void 0:t.jsPDF);d&&(0,e.applyPlugin)(d)}}catch(t){console.error("Could not apply autoTable plugin",t)}o.default=h}(),o}()},91788:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94498:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});let o=(0,n(19946).A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]])}}]);