"use strict";exports.id=514,exports.ids=[514],exports.modules={73514:(a,b,c)=>{c.r(b),c.d(b,{default:()=>ac});let{entries:d,setPrototypeOf:e,isFrozen:f,getPrototypeOf:g,getOwnPropertyDescriptor:h}=Object,{freeze:i,seal:j,create:k}=Object,{apply:l,construct:m}="undefined"!=typeof Reflect&&Reflect;i||(i=function(a){return a}),j||(j=function(a){return a}),l||(l=function(a,b,c){return a.apply(b,c)}),m||(m=function(a,b){return new a(...b)});let n=B(Array.prototype.forEach),o=B(Array.prototype.lastIndexOf),p=B(Array.prototype.pop),q=B(Array.prototype.push),r=B(Array.prototype.splice),s=B(String.prototype.toLowerCase),t=B(String.prototype.toString),u=B(String.prototype.match),v=B(String.prototype.replace),w=B(String.prototype.indexOf),x=B(String.prototype.trim),y=B(Object.prototype.hasOwnProperty),z=B(RegExp.prototype.test),A=(Z=TypeError,function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return m(Z,b)});function B(a){return function(b){b instanceof RegExp&&(b.lastIndex=0);for(var c=arguments.length,d=Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];return l(a,b,d)}}function C(a,b){let c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;e&&e(a,null);let d=b.length;for(;d--;){let e=b[d];if("string"==typeof e){let a=c(e);a!==e&&(f(b)||(b[d]=a),e=a)}a[e]=!0}return a}function D(a){let b=k(null);for(let[c,e]of d(a))y(a,c)&&(Array.isArray(e)?b[c]=function(a){for(let b=0;b<a.length;b++)y(a,b)||(a[b]=null);return a}(e):e&&"object"==typeof e&&e.constructor===Object?b[c]=D(e):b[c]=e);return b}function E(a,b){for(;null!==a;){let c=h(a,b);if(c){if(c.get)return B(c.get);if("function"==typeof c.value)return B(c.value)}a=g(a)}return function(){return null}}let F=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),G=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),H=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),I=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),J=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),K=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),L=i(["#text"]),M=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),N=i(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),O=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),P=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Q=j(/\{\{[\w\W]*|[\w\W]*\}\}/gm),R=j(/<%[\w\W]*|[\w\W]*%>/gm),S=j(/\$\{[\w\W]*/gm),T=j(/^data-[\-\w.\u00B7-\uFFFF]+$/),U=j(/^aria-[\-\w]+$/),V=j(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),W=j(/^(?:\w+script|data):/i),X=j(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Y=j(/^html$/i);var Z,$=Object.freeze({__proto__:null,ARIA_ATTR:U,ATTR_WHITESPACE:X,CUSTOM_ELEMENT:j(/^[a-z][.\w]*(-[.\w]+)+$/i),DATA_ATTR:T,DOCTYPE_NAME:Y,ERB_EXPR:R,IS_ALLOWED_URI:V,IS_SCRIPT_OR_DATA:W,MUSTACHE_EXPR:Q,TMPLIT_EXPR:S});let _={element:1,text:3,progressingInstruction:7,comment:8,document:9},aa=function(a,b){if("object"!=typeof a||"function"!=typeof a.createPolicy)return null;let c=null,d="data-tt-policy-suffix";b&&b.hasAttribute(d)&&(c=b.getAttribute(d));let e="dompurify"+(c?"#"+c:"");try{return a.createPolicy(e,{createHTML:a=>a,createScriptURL:a=>a})}catch(a){return console.warn("TrustedTypes policy "+e+" could not be created."),null}},ab=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};var ac=function a(){let b,c=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window,e=b=>a(b);if(e.version="3.2.6",e.removed=[],!c||!c.document||c.document.nodeType!==_.document||!c.Element)return e.isSupported=!1,e;let{document:f}=c,g=f,h=g.currentScript,{DocumentFragment:j,HTMLTemplateElement:l,Node:m,Element:B,NodeFilter:Q,NamedNodeMap:R=c.NamedNodeMap||c.MozNamedAttrMap,HTMLFormElement:S,DOMParser:T,trustedTypes:U}=c,W=B.prototype,X=E(W,"cloneNode"),Z=E(W,"remove"),ac=E(W,"nextSibling"),ad=E(W,"childNodes"),ae=E(W,"parentNode");if("function"==typeof l){let a=f.createElement("template");a.content&&a.content.ownerDocument&&(f=a.content.ownerDocument)}let af="",{implementation:ag,createNodeIterator:ah,createDocumentFragment:ai,getElementsByTagName:aj}=f,{importNode:ak}=g,al=ab();e.isSupported="function"==typeof d&&"function"==typeof ae&&ag&&void 0!==ag.createHTMLDocument;let{MUSTACHE_EXPR:am,ERB_EXPR:an,TMPLIT_EXPR:ao,DATA_ATTR:ap,ARIA_ATTR:aq,IS_SCRIPT_OR_DATA:ar,ATTR_WHITESPACE:as,CUSTOM_ELEMENT:at}=$,{IS_ALLOWED_URI:au}=$,av=null,aw=C({},[...F,...G,...H,...J,...L]),ax=null,ay=C({},[...M,...N,...O,...P]),az=Object.seal(k(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),aA=null,aB=null,aC=!0,aD=!0,aE=!1,aF=!0,aG=!1,aH=!0,aI=!1,aJ=!1,aK=!1,aL=!1,aM=!1,aN=!1,aO=!0,aP=!1,aQ=!0,aR=!1,aS={},aT=null,aU=C({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),aV=null,aW=C({},["audio","video","img","source","image","track"]),aX=null,aY=C({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),aZ="http://www.w3.org/1998/Math/MathML",a$="http://www.w3.org/2000/svg",a_="http://www.w3.org/1999/xhtml",a0=a_,a1=!1,a2=null,a3=C({},[aZ,a$,a_],t),a4=C({},["mi","mo","mn","ms","mtext"]),a5=C({},["annotation-xml"]),a6=C({},["title","style","font","a","script"]),a7=null,a8=["application/xhtml+xml","text/html"],a9=null,ba=null,bb=f.createElement("form"),bc=function(a){return a instanceof RegExp||a instanceof Function},bd=function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ba||ba!==a){if(a&&"object"==typeof a||(a={}),a=D(a),a9="application/xhtml+xml"===(a7=-1===a8.indexOf(a.PARSER_MEDIA_TYPE)?"text/html":a.PARSER_MEDIA_TYPE)?t:s,av=y(a,"ALLOWED_TAGS")?C({},a.ALLOWED_TAGS,a9):aw,ax=y(a,"ALLOWED_ATTR")?C({},a.ALLOWED_ATTR,a9):ay,a2=y(a,"ALLOWED_NAMESPACES")?C({},a.ALLOWED_NAMESPACES,t):a3,aX=y(a,"ADD_URI_SAFE_ATTR")?C(D(aY),a.ADD_URI_SAFE_ATTR,a9):aY,aV=y(a,"ADD_DATA_URI_TAGS")?C(D(aW),a.ADD_DATA_URI_TAGS,a9):aW,aT=y(a,"FORBID_CONTENTS")?C({},a.FORBID_CONTENTS,a9):aU,aA=y(a,"FORBID_TAGS")?C({},a.FORBID_TAGS,a9):D({}),aB=y(a,"FORBID_ATTR")?C({},a.FORBID_ATTR,a9):D({}),aS=!!y(a,"USE_PROFILES")&&a.USE_PROFILES,aC=!1!==a.ALLOW_ARIA_ATTR,aD=!1!==a.ALLOW_DATA_ATTR,aE=a.ALLOW_UNKNOWN_PROTOCOLS||!1,aF=!1!==a.ALLOW_SELF_CLOSE_IN_ATTR,aG=a.SAFE_FOR_TEMPLATES||!1,aH=!1!==a.SAFE_FOR_XML,aI=a.WHOLE_DOCUMENT||!1,aL=a.RETURN_DOM||!1,aM=a.RETURN_DOM_FRAGMENT||!1,aN=a.RETURN_TRUSTED_TYPE||!1,aK=a.FORCE_BODY||!1,aO=!1!==a.SANITIZE_DOM,aP=a.SANITIZE_NAMED_PROPS||!1,aQ=!1!==a.KEEP_CONTENT,aR=a.IN_PLACE||!1,au=a.ALLOWED_URI_REGEXP||V,a0=a.NAMESPACE||a_,a4=a.MATHML_TEXT_INTEGRATION_POINTS||a4,a5=a.HTML_INTEGRATION_POINTS||a5,az=a.CUSTOM_ELEMENT_HANDLING||{},a.CUSTOM_ELEMENT_HANDLING&&bc(a.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(az.tagNameCheck=a.CUSTOM_ELEMENT_HANDLING.tagNameCheck),a.CUSTOM_ELEMENT_HANDLING&&bc(a.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(az.attributeNameCheck=a.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),a.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof a.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(az.allowCustomizedBuiltInElements=a.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),aG&&(aD=!1),aM&&(aL=!0),aS&&(av=C({},L),ax=[],!0===aS.html&&(C(av,F),C(ax,M)),!0===aS.svg&&(C(av,G),C(ax,N),C(ax,P)),!0===aS.svgFilters&&(C(av,H),C(ax,N),C(ax,P)),!0===aS.mathMl&&(C(av,J),C(ax,O),C(ax,P))),a.ADD_TAGS&&(av===aw&&(av=D(av)),C(av,a.ADD_TAGS,a9)),a.ADD_ATTR&&(ax===ay&&(ax=D(ax)),C(ax,a.ADD_ATTR,a9)),a.ADD_URI_SAFE_ATTR&&C(aX,a.ADD_URI_SAFE_ATTR,a9),a.FORBID_CONTENTS&&(aT===aU&&(aT=D(aT)),C(aT,a.FORBID_CONTENTS,a9)),aQ&&(av["#text"]=!0),aI&&C(av,["html","head","body"]),av.table&&(C(av,["tbody"]),delete aA.tbody),a.TRUSTED_TYPES_POLICY){if("function"!=typeof a.TRUSTED_TYPES_POLICY.createHTML)throw A('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof a.TRUSTED_TYPES_POLICY.createScriptURL)throw A('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');af=(b=a.TRUSTED_TYPES_POLICY).createHTML("")}else void 0===b&&(b=aa(U,h)),null!==b&&"string"==typeof af&&(af=b.createHTML(""));i&&i(a),ba=a}},be=C({},[...G,...H,...I]),bf=C({},[...J,...K]),bg=function(a){let b=ae(a);b&&b.tagName||(b={namespaceURI:a0,tagName:"template"});let c=s(a.tagName),d=s(b.tagName);return!!a2[a.namespaceURI]&&(a.namespaceURI===a$?b.namespaceURI===a_?"svg"===c:b.namespaceURI===aZ?"svg"===c&&("annotation-xml"===d||a4[d]):!!be[c]:a.namespaceURI===aZ?b.namespaceURI===a_?"math"===c:b.namespaceURI===a$?"math"===c&&a5[d]:!!bf[c]:a.namespaceURI===a_?(b.namespaceURI!==a$||!!a5[d])&&(b.namespaceURI!==aZ||!!a4[d])&&!bf[c]&&(a6[c]||!be[c]):"application/xhtml+xml"===a7&&!!a2[a.namespaceURI])},bh=function(a){q(e.removed,{element:a});try{ae(a).removeChild(a)}catch(b){Z(a)}},bi=function(a,b){try{q(e.removed,{attribute:b.getAttributeNode(a),from:b})}catch(a){q(e.removed,{attribute:null,from:b})}if(b.removeAttribute(a),"is"===a)if(aL||aM)try{bh(b)}catch(a){}else try{b.setAttribute(a,"")}catch(a){}},bj=function(a){let c=null,d=null;if(aK)a="<remove></remove>"+a;else{let b=u(a,/^[\r\n\t ]+/);d=b&&b[0]}"application/xhtml+xml"===a7&&a0===a_&&(a='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+a+"</body></html>");let e=b?b.createHTML(a):a;if(a0===a_)try{c=new T().parseFromString(e,a7)}catch(a){}if(!c||!c.documentElement){c=ag.createDocument(a0,"template",null);try{c.documentElement.innerHTML=a1?af:e}catch(a){}}let g=c.body||c.documentElement;return(a&&d&&g.insertBefore(f.createTextNode(d),g.childNodes[0]||null),a0===a_)?aj.call(c,aI?"html":"body")[0]:aI?c.documentElement:g},bk=function(a){return ah.call(a.ownerDocument||a,a,Q.SHOW_ELEMENT|Q.SHOW_COMMENT|Q.SHOW_TEXT|Q.SHOW_PROCESSING_INSTRUCTION|Q.SHOW_CDATA_SECTION,null)},bl=function(a){return a instanceof S&&("string"!=typeof a.nodeName||"string"!=typeof a.textContent||"function"!=typeof a.removeChild||!(a.attributes instanceof R)||"function"!=typeof a.removeAttribute||"function"!=typeof a.setAttribute||"string"!=typeof a.namespaceURI||"function"!=typeof a.insertBefore||"function"!=typeof a.hasChildNodes)},bm=function(a){return"function"==typeof m&&a instanceof m};function bn(a,b,c){n(a,a=>{a.call(e,b,c,ba)})}let bo=function(a){let b=null;if(bn(al.beforeSanitizeElements,a,null),bl(a))return bh(a),!0;let c=a9(a.nodeName);if(bn(al.uponSanitizeElement,a,{tagName:c,allowedTags:av}),aH&&a.hasChildNodes()&&!bm(a.firstElementChild)&&z(/<[/\w!]/g,a.innerHTML)&&z(/<[/\w!]/g,a.textContent)||a.nodeType===_.progressingInstruction||aH&&a.nodeType===_.comment&&z(/<[/\w]/g,a.data))return bh(a),!0;if(!av[c]||aA[c]){if(!aA[c]&&bq(c)&&(az.tagNameCheck instanceof RegExp&&z(az.tagNameCheck,c)||az.tagNameCheck instanceof Function&&az.tagNameCheck(c)))return!1;if(aQ&&!aT[c]){let b=ae(a)||a.parentNode,c=ad(a)||a.childNodes;if(c&&b){let d=c.length;for(let e=d-1;e>=0;--e){let d=X(c[e],!0);d.__removalCount=(a.__removalCount||0)+1,b.insertBefore(d,ac(a))}}}return bh(a),!0}return a instanceof B&&!bg(a)||("noscript"===c||"noembed"===c||"noframes"===c)&&z(/<\/no(script|embed|frames)/i,a.innerHTML)?(bh(a),!0):(aG&&a.nodeType===_.text&&(b=a.textContent,n([am,an,ao],a=>{b=v(b,a," ")}),a.textContent!==b&&(q(e.removed,{element:a.cloneNode()}),a.textContent=b)),bn(al.afterSanitizeElements,a,null),!1)},bp=function(a,b,c){if(aO&&("id"===b||"name"===b)&&(c in f||c in bb))return!1;if(aD&&!aB[b]&&z(ap,b));else if(aC&&z(aq,b));else if(!ax[b]||aB[b]){if(!(bq(a)&&(az.tagNameCheck instanceof RegExp&&z(az.tagNameCheck,a)||az.tagNameCheck instanceof Function&&az.tagNameCheck(a))&&(az.attributeNameCheck instanceof RegExp&&z(az.attributeNameCheck,b)||az.attributeNameCheck instanceof Function&&az.attributeNameCheck(b))||"is"===b&&az.allowCustomizedBuiltInElements&&(az.tagNameCheck instanceof RegExp&&z(az.tagNameCheck,c)||az.tagNameCheck instanceof Function&&az.tagNameCheck(c))))return!1}else if(aX[b]);else if(z(au,v(c,as,"")));else if(("src"===b||"xlink:href"===b||"href"===b)&&"script"!==a&&0===w(c,"data:")&&aV[a]);else if(aE&&!z(ar,v(c,as,"")));else if(c)return!1;return!0},bq=function(a){return"annotation-xml"!==a&&u(a,at)},br=function(a){bn(al.beforeSanitizeAttributes,a,null);let{attributes:c}=a;if(!c||bl(a))return;let d={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ax,forceKeepAttr:void 0},f=c.length;for(;f--;){let{name:g,namespaceURI:h,value:i}=c[f],j=a9(g),k="value"===g?i:x(i);if(d.attrName=j,d.attrValue=k,d.keepAttr=!0,d.forceKeepAttr=void 0,bn(al.uponSanitizeAttribute,a,d),k=d.attrValue,aP&&("id"===j||"name"===j)&&(bi(g,a),k="user-content-"+k),aH&&z(/((--!?|])>)|<\/(style|title)/i,k)){bi(g,a);continue}if(d.forceKeepAttr)continue;if(!d.keepAttr||!aF&&z(/\/>/i,k)){bi(g,a);continue}aG&&n([am,an,ao],a=>{k=v(k,a," ")});let l=a9(a.nodeName);if(!bp(l,j,k)){bi(g,a);continue}if(b&&"object"==typeof U&&"function"==typeof U.getAttributeType)if(h);else switch(U.getAttributeType(l,j)){case"TrustedHTML":k=b.createHTML(k);break;case"TrustedScriptURL":k=b.createScriptURL(k)}if(k!==i)try{h?a.setAttributeNS(h,g,k):a.setAttribute(g,k),bl(a)?bh(a):p(e.removed)}catch(b){bi(g,a)}}bn(al.afterSanitizeAttributes,a,null)},bs=function a(b){let c=null,d=bk(b);for(bn(al.beforeSanitizeShadowDOM,b,null);c=d.nextNode();)bn(al.uponSanitizeShadowNode,c,null),bo(c),br(c),c.content instanceof j&&a(c.content);bn(al.afterSanitizeShadowDOM,b,null)};return e.sanitize=function(a){let c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=null,f=null,h=null,i=null;if((a1=!a)&&(a="\x3c!--\x3e"),"string"!=typeof a&&!bm(a))if("function"==typeof a.toString){if("string"!=typeof(a=a.toString()))throw A("dirty is not a string, aborting")}else throw A("toString is not a function");if(!e.isSupported)return a;if(aJ||bd(c),e.removed=[],"string"==typeof a&&(aR=!1),aR){if(a.nodeName){let b=a9(a.nodeName);if(!av[b]||aA[b])throw A("root node is forbidden and cannot be sanitized in-place")}}else if(a instanceof m)(f=(d=bj("\x3c!----\x3e")).ownerDocument.importNode(a,!0)).nodeType===_.element&&"BODY"===f.nodeName||"HTML"===f.nodeName?d=f:d.appendChild(f);else{if(!aL&&!aG&&!aI&&-1===a.indexOf("<"))return b&&aN?b.createHTML(a):a;if(!(d=bj(a)))return aL?null:aN?af:""}d&&aK&&bh(d.firstChild);let k=bk(aR?a:d);for(;h=k.nextNode();)bo(h),br(h),h.content instanceof j&&bs(h.content);if(aR)return a;if(aL){if(aM)for(i=ai.call(d.ownerDocument);d.firstChild;)i.appendChild(d.firstChild);else i=d;return(ax.shadowroot||ax.shadowrootmode)&&(i=ak.call(g,i,!0)),i}let l=aI?d.outerHTML:d.innerHTML;return aI&&av["!doctype"]&&d.ownerDocument&&d.ownerDocument.doctype&&d.ownerDocument.doctype.name&&z(Y,d.ownerDocument.doctype.name)&&(l="<!DOCTYPE "+d.ownerDocument.doctype.name+">\n"+l),aG&&n([am,an,ao],a=>{l=v(l,a," ")}),b&&aN?b.createHTML(l):l},e.setConfig=function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};bd(a),aJ=!0},e.clearConfig=function(){ba=null,aJ=!1},e.isValidAttribute=function(a,b,c){return ba||bd({}),bp(a9(a),a9(b),c)},e.addHook=function(a,b){"function"==typeof b&&q(al[a],b)},e.removeHook=function(a,b){if(void 0!==b){let c=o(al[a],b);return -1===c?void 0:r(al[a],c,1)[0]}return p(al[a])},e.removeHooks=function(a){al[a]=[]},e.removeAllHooks=function(){al=ab()},e}()}};