(()=>{var a={};a.id=115,a.ids=[115],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9295:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(60687),e=c(43210),f=c(21979),g=c(48210),h=c(96474),i=c(8819);function j(){let[a,b]=(0,e.useState)([]),[j,k]=(0,e.useState)(null),[l,m]=(0,e.useState)(1),[n,o]=(0,e.useState)(0),[p,q]=(0,e.useState)([]),[r,s]=(0,e.useState)([]),t=a=>{let b=new Date().toLocaleTimeString();s(c=>[...c,`[${b}] ${a}`]),console.log(a)},u=async()=>{if(0===p.length)return void t("❌ لا توجد عناصر للاختبار");t("\uD83D\uDD04 بدء اختبار عملية الحفظ...");try{let a={invoice_number:`TEST-${Date.now()}`,customer_id:null,customer_name:"عميل تجريبي",total_amount:p.reduce((a,b)=>a+b.totalPrice,0),discount_amount:0,final_amount:p.reduce((a,b)=>a+b.totalPrice,0),payment_method:"cash",payment_status:"paid",notes:"فاتورة تجريبية للاختبار",private_notes:""},b=p.map(a=>({medicine_batch_id:a.batchId,quantity:a.quantity,unit_price:a.unitPrice,total_price:a.totalPrice,is_gift:!1,medicine_name:a.medicineName,medicineName:a.medicineName}));t("\uD83D\uDCC4 بيانات الفاتورة التجريبية:"),t(JSON.stringify(a,null,2)),t("\uD83D\uDCE6 عناصر الفاتورة التجريبية:"),t(JSON.stringify(b,null,2));let{completeSalesTransaction:d}=await Promise.all([c.e(463),c.e(31)]).then(c.bind(c,31836));t("\uD83D\uDCBE استدعاء completeSalesTransaction...");let e=await d(a,b);if(t("✅ نتيجة الحفظ:"),t(JSON.stringify(e,null,2)),e.success){t("\uD83C\uDF89 تم حفظ الفاتورة التجريبية بنجاح!");let a=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]").slice(-b.length);t("\uD83D\uDD0D العناصر المحفوظة فعلياً:"),a.forEach((a,b)=>{t(`العنصر ${b+1}: ${a.medicine_name||a.medicineName||"غير محدد"}`)}),t("\uD83D\uDDA8️ اختبار عملية استرجاع البيانات للطباعة...");let d=e.data.invoiceId,{getSalesInvoiceForPrint:f}=await Promise.all([c.e(463),c.e(31)]).then(c.bind(c,31836)),g=await f(d);t("\uD83D\uDCC4 نتيجة استرجاع بيانات الطباعة:"),t(JSON.stringify(g,null,2)),g.success&&g.data&&(t("\uD83D\uDD0D أسماء الأدوية في بيانات الطباعة:"),g.data.sales_invoice_items?.forEach((a,b)=>{let c=a.medicine_batches?.medicines?.name||a.medicine_name||a.medicineName||"غير محدد";t(`عنصر الطباعة ${b+1}: ${c}`),t(`  - medicine_name: ${a.medicine_name}`),t(`  - medicineName: ${a.medicineName}`),t(`  - medicine_batches.medicines.name: ${a.medicine_batches?.medicines?.name}`),t(`  - batch_id: ${a.medicine_batch_id}`)}),t("\uD83D\uDDA8️ اختبار قالب الطباعة الفعلي..."),w(g.data),t("\uD83D\uDDA8️ اختبار دالة الطباعة الفعلية..."),v(g.data))}else t(`❌ فشل في حفظ الفاتورة: ${e.error}`)}catch(a){t(`💥 خطأ في اختبار الحفظ: ${a}`)}},v=async a=>{try{t("\uD83D\uDCC4 اختبار دالة الطباعة الفعلية من النظام...");let{printInvoice:b}=await Promise.all([c.e(463),c.e(31),c.e(711)]).then(c.bind(c,97711)),{usePrintSettings:d}=await Promise.all([c.e(463),c.e(31),c.e(711)]).then(c.bind(c,97711)),e={companyName:"صيدلية الشفاء",companyNameEn:"Al-Shifa Pharmacy",companyAddress:"بغداد - الكرادة - شارع الرئيسي",footerText:"شكراً لتعاملكم معنا"};t("⚙️ إعدادات الطباعة:"),t(JSON.stringify(e,null,2)),t("\uD83D\uDCCB بيانات الفاتورة للطباعة:"),t(JSON.stringify(a,null,2)),t("\uD83D\uDDA8️ استدعاء دالة printInvoice..."),await b(a,"sales",e),t("✅ تم استدعاء دالة الطباعة - تحقق من النافذة المفتوحة!")}catch(a){t(`❌ خطأ في اختبار الطباعة الفعلية: ${a}`)}},w=a=>{t("\uD83D\uDCC4 اختبار قالب الطباعة..."),a.sales_invoice_items?.forEach((a,b)=>{let c=a.medicine_batches?.medicines?.name,d=a.medicine_name,e=a.medicineName;t(`قالب الطباعة - العنصر ${b+1}:`),t(`  الطريقة 1 (medicine_batches.medicines.name): ${c}`),t(`  الطريقة 2 (medicine_name): ${d}`),t(`  الطريقة 3 (medicineName): ${e}`),t(`  النتيجة النهائية: ${c||d||e||"غير محدد"}`)}),t("\uD83D\uDDA8️ إنشاء نافذة طباعة تجريبية...");let b=`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>فاتورة تجريبية - ${a.invoice_number}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 20px; }
          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          .items-table th { background-color: #f5f5f5; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>فاتورة تجريبية</h1>
          <h2>رقم الفاتورة: ${a.invoice_number}</h2>
        </div>

        <table class="items-table">
          <thead>
            <tr>
              <th>اسم الدواء</th>
              <th>الكمية</th>
              <th>سعر الوحدة</th>
              <th>الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            ${a.sales_invoice_items?.map(a=>{let b=a.medicine_batches?.medicines?.name||a.medicine_name||a.medicineName||"غير محدد";return`
                <tr>
                  <td>${b}</td>
                  <td>${a.quantity}</td>
                  <td>${a.unit_price.toFixed(2)}</td>
                  <td>${a.total_price.toFixed(2)}</td>
                </tr>
              `}).join("")||'<tr><td colspan="4">لا توجد عناصر</td></tr>'}
          </tbody>
        </table>

        <div style="margin-top: 20px;">
          <p><strong>المجموع النهائي:</strong> ${a.final_amount?.toFixed(2)||"0.00"} جنيه</p>
        </div>
      </body>
      </html>
    `,c=window.open("","_blank");c?(c.document.write(b),c.document.close(),t("✅ تم فتح نافذة الطباعة التجريبية - تحقق من أسماء الأدوية!")):t("❌ فشل في فتح نافذة الطباعة")};return(0,d.jsx)(f.A,{children:(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,d.jsx)("div",{className:"bg-red-50 p-3 rounded-lg",children:(0,d.jsx)(g.A,{className:"h-6 w-6 text-red-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"اختبار عملية إنشاء الفواتير"}),(0,d.jsx)("p",{className:"text-gray-600",children:"اختبار مباشر لعملية حفظ الفواتير وأسماء الأدوية"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اختر دواء:"}),(0,d.jsxs)("select",{value:j?.id||"",onChange:b=>{let c=a.find(a=>a.id===b.target.value);k(c),o(c?.batches[0]?.selling_price||0)},className:"w-full px-3 py-2 border border-gray-300 rounded-lg",children:[(0,d.jsx)("option",{value:"",children:"اختر دواء..."}),a.map(a=>(0,d.jsxs)("option",{value:a.id,children:[a.name," - ",a.batches.length," دفعة"]},a.id))]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الكمية:"}),(0,d.jsx)("input",{type:"number",value:l,onChange:a=>m(Number(a.target.value)),min:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-lg"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"سعر الوحدة:"}),(0,d.jsx)("input",{type:"number",value:n,onChange:a=>o(Number(a.target.value)),min:"0",step:"0.01",className:"w-full px-3 py-2 border border-gray-300 rounded-lg"})]})]})]}),(0,d.jsxs)("div",{className:"flex gap-3 mb-6",children:[(0,d.jsxs)("button",{onClick:()=>{if(!j||!j.batches[0])return void t("❌ يرجى اختيار دواء صحيح");let a=j.batches[0],b={id:`test_${Date.now()}`,batchId:a.id,medicineName:j.name,medicine_name:j.name,quantity:l,unitPrice:n,totalPrice:l*n,batch:a};q(a=>[...a,b]),t(`✅ تم إضافة عنصر: ${j.name} - الكمية: ${l}`)},disabled:!j,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 flex items-center gap-2",children:[(0,d.jsx)(h.A,{className:"h-4 w-4"}),"إضافة عنصر"]}),(0,d.jsxs)("button",{onClick:u,disabled:0===p.length,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 flex items-center gap-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),"اختبار الحفظ"]}),(0,d.jsx)("button",{onClick:()=>{q([]),s([]),k(null),m(1),o(0)},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2",children:"مسح الاختبار"})]}),p.length>0&&(0,d.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"العناصر التجريبية:"}),(0,d.jsx)("div",{className:"space-y-2",children:p.map((a,b)=>(0,d.jsx)("div",{className:"bg-white p-3 rounded border",children:(0,d.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"الدواء:"})," ",a.medicineName]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"الكمية:"})," ",a.quantity]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"السعر:"})," ",a.unitPrice]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"الإجمالي:"})," ",a.totalPrice]})]})},a.id))})]}),(0,d.jsxs)("div",{className:"bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto",children:[(0,d.jsx)("h3",{className:"text-white mb-2",children:"سجل التشخيص:"}),r.map((a,b)=>(0,d.jsx)("div",{className:"mb-1",children:a},b)),0===r.length&&(0,d.jsx)("div",{className:"text-gray-500",children:"لا توجد رسائل تشخيص بعد..."})]})]})})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46809:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\debug-sales\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug-sales\\page.tsx","default")},47025:(a,b,c)=>{Promise.resolve().then(c.bind(c,9295))},54135:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["debug-sales",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,46809)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug-sales\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug-sales\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/debug-sales/page",pathname:"/debug-sales",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/debug-sales/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},81873:(a,b,c)=>{Promise.resolve().then(c.bind(c,46809))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,314,979],()=>b(b.s=54135));module.exports=c})();