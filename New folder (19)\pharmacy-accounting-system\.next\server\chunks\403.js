exports.id=403,exports.ids=[403],exports.modules={4403:(a,b,c)=>{"use strict";c.d(b,{jsPDF:()=>aF});var d=c(21154),e=c.n(d),f=(0,c(8086).createRequire)("/");try{f("worker_threads").Worker}catch(a){}var g=Uint8Array,h=Uint16Array,i=Int32Array,j=new g([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),k=new g([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),l=new g([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),m=function(a,b){for(var c=new h(31),d=0;d<31;++d)c[d]=b+=1<<a[d-1];for(var e=new i(c[30]),d=1;d<30;++d)for(var f=c[d];f<c[d+1];++f)e[f]=f-c[d]<<5|d;return{b:c,r:e}},n=m(j,2),o=n.b,p=n.r;o[28]=258,p[258]=28;for(var q=m(k,0),r=q.b,s=q.r,t=new h(32768),u=0;u<32768;++u){var v=(43690&u)>>1|(21845&u)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,t[u]=((65280&v)>>8|(255&v)<<8)>>1}for(var w=function(a,b,c){for(var d,e=a.length,f=0,g=new h(b);f<e;++f)a[f]&&++g[a[f]-1];var i=new h(b);for(f=1;f<b;++f)i[f]=i[f-1]+g[f-1]<<1;if(c){d=new h(1<<b);var j=15-b;for(f=0;f<e;++f)if(a[f])for(var k=f<<4|a[f],l=b-a[f],m=i[a[f]-1]++<<l,n=m|(1<<l)-1;m<=n;++m)d[t[m]>>j]=k}else for(f=0,d=new h(e);f<e;++f)a[f]&&(d[f]=t[i[a[f]-1]++]>>15-a[f]);return d},x=new g(288),u=0;u<144;++u)x[u]=8;for(var u=144;u<256;++u)x[u]=9;for(var u=256;u<280;++u)x[u]=7;for(var u=280;u<288;++u)x[u]=8;for(var y=new g(32),u=0;u<32;++u)y[u]=5;var z=w(x,9,0),A=w(x,9,1),B=w(y,5,0),C=w(y,5,1),D=function(a){for(var b=a[0],c=1;c<a.length;++c)a[c]>b&&(b=a[c]);return b},E=function(a,b,c){var d=b/8|0;return(a[d]|a[d+1]<<8)>>(7&b)&c},F=function(a,b){var c=b/8|0;return(a[c]|a[c+1]<<8|a[c+2]<<16)>>(7&b)},G=function(a){return(a+7)/8|0},H=function(a,b,c){return(null==b||b<0)&&(b=0),(null==c||c>a.length)&&(c=a.length),new g(a.subarray(b,c))},I=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],J=function(a,b,c){var d=Error(b||I[a]);if(d.code=a,Error.captureStackTrace&&Error.captureStackTrace(d,J),!c)throw d;return d},K=function(a,b,c,d){var e=a.length,f=d?d.length:0;if(!e||b.f&&!b.l)return c||new g(0);var h=!c,i=h||2!=b.i,m=b.i;h&&(c=new g(3*e));var n=function(a){var b=c.length;if(a>b){var d=new g(Math.max(2*b,a));d.set(c),c=d}},p=b.f||0,q=b.p||0,s=b.b||0,t=b.l,u=b.d,v=b.m,x=b.n,y=8*e;do{if(!t){p=E(a,q,1);var z=E(a,q+1,3);if(q+=3,z)if(1==z)t=A,u=C,v=9,x=5;else if(2==z){var B=E(a,q,31)+257,I=E(a,q+10,15)+4,K=B+E(a,q+5,31)+1;q+=14;for(var L=new g(K),M=new g(19),N=0;N<I;++N)M[l[N]]=E(a,q+3*N,7);q+=3*I;for(var O=D(M),P=(1<<O)-1,Q=w(M,O,1),N=0;N<K;){var R=Q[E(a,q,P)];q+=15&R;var S=R>>4;if(S<16)L[N++]=S;else{var T=0,U=0;for(16==S?(U=3+E(a,q,3),q+=2,T=L[N-1]):17==S?(U=3+E(a,q,7),q+=3):18==S&&(U=11+E(a,q,127),q+=7);U--;)L[N++]=T}}var V=L.subarray(0,B),W=L.subarray(B);v=D(V),x=D(W),t=w(V,v,1),u=w(W,x,1)}else J(1);else{var S=G(q)+4,X=a[S-4]|a[S-3]<<8,Y=S+X;if(Y>e){m&&J(0);break}i&&n(s+X),c.set(a.subarray(S,Y),s),b.b=s+=X,b.p=q=8*Y,b.f=p;continue}if(q>y){m&&J(0);break}}i&&n(s+131072);for(var Z=(1<<v)-1,$=(1<<x)-1,_=q;;_=q){var T=t[F(a,q)&Z],aa=T>>4;if((q+=15&T)>y){m&&J(0);break}if(T||J(2),aa<256)c[s++]=aa;else if(256==aa){_=q,t=null;break}else{var ab=aa-254;if(aa>264){var N=aa-257,ac=j[N];ab=E(a,q,(1<<ac)-1)+o[N],q+=ac}var ad=u[F(a,q)&$],ae=ad>>4;ad||J(3),q+=15&ad;var W=r[ae];if(ae>3){var ac=k[ae];W+=F(a,q)&(1<<ac)-1,q+=ac}if(q>y){m&&J(0);break}i&&n(s+131072);var af=s+ab;if(s<W){var ag=f-W,ah=Math.min(W,af);for(ag+s<0&&J(3);s<ah;++s)c[s]=d[ag+s]}for(;s<af;++s)c[s]=c[s-W]}}b.l=t,b.p=_,b.b=s,b.f=p,t&&(p=1,b.m=v,b.d=u,b.n=x)}while(!p);return s!=c.length&&h?H(c,0,s):c.subarray(0,s)},L=function(a,b,c){c<<=7&b;var d=b/8|0;a[d]|=c,a[d+1]|=c>>8},M=function(a,b,c){c<<=7&b;var d=b/8|0;a[d]|=c,a[d+1]|=c>>8,a[d+2]|=c>>16},N=function(a,b){for(var c=[],d=0;d<a.length;++d)a[d]&&c.push({s:d,f:a[d]});var e=c.length,f=c.slice();if(!e)return{t:U,l:0};if(1==e){var i=new g(c[0].s+1);return i[c[0].s]=1,{t:i,l:1}}c.sort(function(a,b){return a.f-b.f}),c.push({s:-1,f:25001});var j=c[0],k=c[1],l=0,m=1,n=2;for(c[0]={s:-1,f:j.f+k.f,l:j,r:k};m!=e-1;)j=c[c[l].f<c[n].f?l++:n++],k=c[l!=m&&c[l].f<c[n].f?l++:n++],c[m++]={s:-1,f:j.f+k.f,l:j,r:k};for(var o=f[0].s,d=1;d<e;++d)f[d].s>o&&(o=f[d].s);var p=new h(o+1),q=O(c[m-1],p,0);if(q>b){var d=0,r=0,s=q-b,t=1<<s;for(f.sort(function(a,b){return p[b.s]-p[a.s]||a.f-b.f});d<e;++d){var u=f[d].s;if(p[u]>b)r+=t-(1<<q-p[u]),p[u]=b;else break}for(r>>=s;r>0;){var v=f[d].s;p[v]<b?r-=1<<b-p[v]++-1:++d}for(;d>=0&&r;--d){var w=f[d].s;p[w]==b&&(--p[w],++r)}q=b}return{t:new g(p),l:q}},O=function(a,b,c){return -1==a.s?Math.max(O(a.l,b,c+1),O(a.r,b,c+1)):b[a.s]=c},P=function(a){for(var b=a.length;b&&!a[--b];);for(var c=new h(++b),d=0,e=a[0],f=1,g=function(a){c[d++]=a},i=1;i<=b;++i)if(a[i]==e&&i!=b)++f;else{if(!e&&f>2){for(;f>138;f-=138)g(32754);f>2&&(g(f>10?f-11<<5|28690:f-3<<5|12305),f=0)}else if(f>3){for(g(e),--f;f>6;f-=6)g(8304);f>2&&(g(f-3<<5|8208),f=0)}for(;f--;)g(e);f=1,e=a[i]}return{c:c.subarray(0,d),n:b}},Q=function(a,b){for(var c=0,d=0;d<b.length;++d)c+=a[d]*b[d];return c},R=function(a,b,c){var d=c.length,e=G(b+2);a[e]=255&d,a[e+1]=d>>8,a[e+2]=255^a[e],a[e+3]=255^a[e+1];for(var f=0;f<d;++f)a[e+f+4]=c[f];return(e+4+d)*8},S=function(a,b,c,d,e,f,g,i,m,n,o){L(b,o++,c),++e[256];for(var p,q,r,s,t=N(e,15),u=t.t,v=t.l,A=N(f,15),C=A.t,D=A.l,E=P(u),F=E.c,G=E.n,H=P(C),I=H.c,J=H.n,K=new h(19),O=0;O<F.length;++O)++K[31&F[O]];for(var O=0;O<I.length;++O)++K[31&I[O]];for(var S=N(K,7),T=S.t,U=S.l,V=19;V>4&&!T[l[V-1]];--V);var W=n+5<<3,X=Q(e,x)+Q(f,y)+g,Y=Q(e,u)+Q(f,C)+g+14+3*V+Q(K,T)+2*K[16]+3*K[17]+7*K[18];if(m>=0&&W<=X&&W<=Y)return R(b,o,a.subarray(m,m+n));if(L(b,o,1+(Y<X)),o+=2,Y<X){p=w(u,v,0),q=u,r=w(C,D,0),s=C;var Z=w(T,U,0);L(b,o,G-257),L(b,o+5,J-1),L(b,o+10,V-4),o+=14;for(var O=0;O<V;++O)L(b,o+3*O,T[l[O]]);o+=3*V;for(var $=[F,I],_=0;_<2;++_)for(var aa=$[_],O=0;O<aa.length;++O){var ab=31&aa[O];L(b,o,Z[ab]),o+=T[ab],ab>15&&(L(b,o,aa[O]>>5&127),o+=aa[O]>>12)}}else p=z,q=x,r=B,s=y;for(var O=0;O<i;++O){var ac=d[O];if(ac>255){var ab=ac>>18&31;M(b,o,p[ab+257]),o+=q[ab+257],ab>7&&(L(b,o,ac>>23&31),o+=j[ab]);var ad=31&ac;M(b,o,r[ad]),o+=s[ad],ad>3&&(M(b,o,ac>>5&8191),o+=k[ad])}else M(b,o,p[ac]),o+=q[ac]}return M(b,o,p[256]),o+q[256]},T=new i([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),U=new g(0),V=function(a,b,c,d,e,f){var l=f.z||a.length,m=new g(d+l+5*(1+Math.ceil(l/7e3))+e),n=m.subarray(d,m.length-e),o=f.l,q=7&(f.r||0);if(b){q&&(n[0]=f.r>>3);for(var r=T[b-1],t=r>>13,u=8191&r,v=(1<<c)-1,w=f.p||new h(32768),x=f.h||new h(v+1),y=Math.ceil(c/3),z=2*y,A=function(b){return(a[b]^a[b+1]<<y^a[b+2]<<z)&v},B=new i(25e3),C=new h(288),D=new h(32),E=0,F=0,I=f.i||0,J=0,K=f.w||0,L=0;I+2<l;++I){var M=A(I),N=32767&I,O=x[M];if(w[N]=O,x[M]=N,K<=I){var P=l-I;if((E>7e3||J>24576)&&(P>423||!o)){q=S(a,n,0,B,C,D,F,J,L,I-L,q),J=E=F=0,L=I;for(var Q=0;Q<286;++Q)C[Q]=0;for(var Q=0;Q<30;++Q)D[Q]=0}var U=2,V=0,W=u,X=N-O&32767;if(P>2&&M==A(I-X))for(var Y=Math.min(t,P)-1,Z=Math.min(32767,I),$=Math.min(258,P);X<=Z&&--W&&N!=O;){if(a[I+U]==a[I+U-X]){for(var _=0;_<$&&a[I+_]==a[I+_-X];++_);if(_>U){if(U=_,V=X,_>Y)break;for(var aa=Math.min(X,_-2),ab=0,Q=0;Q<aa;++Q){var ac=I-X+Q&32767,ad=w[ac],ae=ac-ad&32767;ae>ab&&(ab=ae,O=ac)}}}O=w[N=O],X+=N-O&32767}if(V){B[J++]=0x10000000|p[U]<<18|s[V];var af=31&p[U],ag=31&s[V];F+=j[af]+k[ag],++C[257+af],++D[ag],K=I+U,++E}else B[J++]=a[I],++C[a[I]]}}for(I=Math.max(I,K);I<l;++I)B[J++]=a[I],++C[a[I]];q=S(a,n,o,B,C,D,F,J,L,I-L,q),o||(f.r=7&q|n[q/8|0]<<3,q-=7,f.h=x,f.p=w,f.i=I,f.w=K)}else{for(var I=f.w||0;I<l+o;I+=65535){var ah=I+65535;ah>=l&&(n[q/8|0]=o,ah=l),q=R(n,q+1,a.subarray(I,ah))}f.i=l}return H(m,0,d+G(q)+e)},W=function(){var a=1,b=0;return{p:function(c){for(var d=a,e=b,f=0|c.length,g=0;g!=f;){for(var h=Math.min(g+2655,f);g<h;++g)e+=d+=c[g];d=(65535&d)+15*(d>>16),e=(65535&e)+15*(e>>16)}a=d,b=e},d:function(){return a%=65521,b%=65521,(255&a)<<24|(65280&a)<<8|(255&b)<<8|b>>8}}},X=function(a,b,c,d,e){if(!e&&(e={l:1},b.dictionary)){var f=b.dictionary.subarray(-32768),h=new g(f.length+a.length);h.set(f),h.set(a,f.length),a=h,e.w=f.length}return V(a,null==b.level?6:b.level,null==b.mem?e.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(a.length)))):20:12+b.mem,c,d,e)},Y=function(a,b,c){for(;c;++b)a[b]=c,c>>>=8},Z=function(a,b){var c=b.level;if(a[0]=120,a[1]=(0==c?0:c<6?1:9==c?3:2)<<6|(b.dictionary&&32),a[1]|=31-(a[0]<<8|a[1])%31,b.dictionary){var d=W();d.p(b.dictionary),Y(a,2,d.d())}};function $(a,b){b||(b={});var c=W();c.p(a);var d=X(a,b,b.dictionary?6:2,4);return Z(d,b),Y(d,d.length-4,c.d()),d}var _="undefined"!=typeof TextDecoder&&new TextDecoder;try{_.decode(U,{stream:!0})}catch(a){}"function"==typeof queueMicrotask&&queueMicrotask;var aa=function(){return"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this}();function ab(){aa.console&&"function"==typeof aa.console.log&&aa.console.log.apply(aa.console,arguments)}var ac={log:ab,warn:function(a){aa.console&&("function"==typeof aa.console.warn?aa.console.warn.apply(aa.console,arguments):ab.call(null,arguments))},error:function(a){aa.console&&("function"==typeof aa.console.error?aa.console.error.apply(aa.console,arguments):ab(a))}};function ad(a,b,c){var d=new XMLHttpRequest;d.open("GET",a),d.responseType="blob",d.onload=function(){ai(d.response,b,c)},d.onerror=function(){ac.error("could not download file")},d.send()}function ae(a){var b=new XMLHttpRequest;b.open("HEAD",a,!1);try{b.send()}catch(a){}return b.status>=200&&b.status<=299}function af(a){try{a.dispatchEvent(new MouseEvent("click"))}catch(c){var b=document.createEvent("MouseEvents");b.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),a.dispatchEvent(b)}}var ag,ah,ai=aa.saveAs||("object"!==("undefined"==typeof window?"undefined":e()(window))||window!==aa?function(){}:"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype?function(a,b,c){var d=aa.URL||aa.webkitURL,e=document.createElement("a");e.download=b=b||a.name||"download",e.rel="noopener","string"==typeof a?(e.href=a,e.origin!==location.origin?ae(e.href)?ad(a,b,c):af(e,e.target="_blank"):af(e)):(e.href=d.createObjectURL(a),setTimeout(function(){d.revokeObjectURL(e.href)},4e4),setTimeout(function(){af(e)},0))}:"msSaveOrOpenBlob"in navigator?function(a,b,c){if(b=b||a.name||"download","string"==typeof a)if(ae(a))ad(a,b,c);else{var d,f=document.createElement("a");f.href=a,f.target="_blank",setTimeout(function(){af(f)})}else navigator.msSaveOrOpenBlob((void 0===(d=c)?d={autoBom:!1}:"object"!==e()(d)&&(ac.warn("Deprecated: Expected third argument to be a object"),d={autoBom:!d}),d.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(a.type)?new Blob([String.fromCharCode(65279),a],{type:a.type}):a),b)}:function(a,b,c,d){if((d=d||open("","_blank"))&&(d.document.title=d.document.body.innerText="downloading..."),"string"==typeof a)return ad(a,b,c);var f="application/octet-stream"===a.type,g=/constructor/i.test(aa.HTMLElement)||aa.safari,h=/CriOS\/[\d]+/.test(navigator.userAgent);if((h||f&&g)&&"object"===("undefined"==typeof FileReader?"undefined":e()(FileReader))){var i=new FileReader;i.onloadend=function(){var a=i.result;a=h?a:a.replace(/^data:[^;]*;/,"data:attachment/file;"),d?d.location.href=a:location=a,d=null},i.readAsDataURL(a)}else{var j=aa.URL||aa.webkitURL,k=j.createObjectURL(a);d?d.location=k:location.href=k,d=null,setTimeout(function(){j.revokeObjectURL(k)},4e4)}});function aj(a){var b;a=a||"",this.ok=!1,"#"==a.charAt(0)&&(a=a.substr(1,6)),a=({aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"})[a=(a=a.replace(/ /g,"")).toLowerCase()]||a;for(var c=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(a){return[parseInt(a[1]),parseInt(a[2]),parseInt(a[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(a){return[parseInt(a[1],16),parseInt(a[2],16),parseInt(a[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(a){return[parseInt(a[1]+a[1],16),parseInt(a[2]+a[2],16),parseInt(a[3]+a[3],16)]}}],d=0;d<c.length;d++){var e=c[d].re,f=c[d].process,g=e.exec(a);g&&(b=f(g),this.r=b[0],this.g=b[1],this.b=b[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var a=this.r.toString(16),b=this.g.toString(16),c=this.b.toString(16);return 1==a.length&&(a="0"+a),1==b.length&&(b="0"+b),1==c.length&&(c="0"+c),"#"+a+b+c}}function ak(a,b){var c=a[0],d=a[1],e=a[2],f=a[3];c=am(c,d,e,f,b[0],7,-0x28955b88),f=am(f,c,d,e,b[1],12,-0x173848aa),e=am(e,f,c,d,b[2],17,0x242070db),d=am(d,e,f,c,b[3],22,-0x3e423112),c=am(c,d,e,f,b[4],7,-0xa83f051),f=am(f,c,d,e,b[5],12,0x4787c62a),e=am(e,f,c,d,b[6],17,-0x57cfb9ed),d=am(d,e,f,c,b[7],22,-0x2b96aff),c=am(c,d,e,f,b[8],7,0x698098d8),f=am(f,c,d,e,b[9],12,-0x74bb0851),e=am(e,f,c,d,b[10],17,-42063),d=am(d,e,f,c,b[11],22,-0x76a32842),c=am(c,d,e,f,b[12],7,0x6b901122),f=am(f,c,d,e,b[13],12,-0x2678e6d),e=am(e,f,c,d,b[14],17,-0x5986bc72),c=an(c,d=am(d,e,f,c,b[15],22,0x49b40821),e,f,b[1],5,-0x9e1da9e),f=an(f,c,d,e,b[6],9,-0x3fbf4cc0),e=an(e,f,c,d,b[11],14,0x265e5a51),d=an(d,e,f,c,b[0],20,-0x16493856),c=an(c,d,e,f,b[5],5,-0x29d0efa3),f=an(f,c,d,e,b[10],9,0x2441453),e=an(e,f,c,d,b[15],14,-0x275e197f),d=an(d,e,f,c,b[4],20,-0x182c0438),c=an(c,d,e,f,b[9],5,0x21e1cde6),f=an(f,c,d,e,b[14],9,-0x3cc8f82a),e=an(e,f,c,d,b[3],14,-0xb2af279),d=an(d,e,f,c,b[8],20,0x455a14ed),c=an(c,d,e,f,b[13],5,-0x561c16fb),f=an(f,c,d,e,b[2],9,-0x3105c08),e=an(e,f,c,d,b[7],14,0x676f02d9),c=ao(c,d=an(d,e,f,c,b[12],20,-0x72d5b376),e,f,b[5],4,-378558),f=ao(f,c,d,e,b[8],11,-0x788e097f),e=ao(e,f,c,d,b[11],16,0x6d9d6122),d=ao(d,e,f,c,b[14],23,-0x21ac7f4),c=ao(c,d,e,f,b[1],4,-0x5b4115bc),f=ao(f,c,d,e,b[4],11,0x4bdecfa9),e=ao(e,f,c,d,b[7],16,-0x944b4a0),d=ao(d,e,f,c,b[10],23,-0x41404390),c=ao(c,d,e,f,b[13],4,0x289b7ec6),f=ao(f,c,d,e,b[0],11,-0x155ed806),e=ao(e,f,c,d,b[3],16,-0x2b10cf7b),d=ao(d,e,f,c,b[6],23,0x4881d05),c=ao(c,d,e,f,b[9],4,-0x262b2fc7),f=ao(f,c,d,e,b[12],11,-0x1924661b),e=ao(e,f,c,d,b[15],16,0x1fa27cf8),c=ap(c,d=ao(d,e,f,c,b[2],23,-0x3b53a99b),e,f,b[0],6,-0xbd6ddbc),f=ap(f,c,d,e,b[7],10,0x432aff97),e=ap(e,f,c,d,b[14],15,-0x546bdc59),d=ap(d,e,f,c,b[5],21,-0x36c5fc7),c=ap(c,d,e,f,b[12],6,0x655b59c3),f=ap(f,c,d,e,b[3],10,-0x70f3336e),e=ap(e,f,c,d,b[10],15,-1051523),d=ap(d,e,f,c,b[1],21,-0x7a7ba22f),c=ap(c,d,e,f,b[8],6,0x6fa87e4f),f=ap(f,c,d,e,b[15],10,-0x1d31920),e=ap(e,f,c,d,b[6],15,-0x5cfebcec),d=ap(d,e,f,c,b[13],21,0x4e0811a1),c=ap(c,d,e,f,b[4],6,-0x8ac817e),f=ap(f,c,d,e,b[11],10,-0x42c50dcb),e=ap(e,f,c,d,b[2],15,0x2ad7d2bb),d=ap(d,e,f,c,b[9],21,-0x14792c6f),a[0]=av(c,a[0]),a[1]=av(d,a[1]),a[2]=av(e,a[2]),a[3]=av(f,a[3])}function al(a,b,c,d,e,f){return b=av(av(b,a),av(d,f)),av(b<<e|b>>>32-e,c)}function am(a,b,c,d,e,f,g){return al(b&c|~b&d,a,b,e,f,g)}function an(a,b,c,d,e,f,g){return al(b&d|c&~d,a,b,e,f,g)}function ao(a,b,c,d,e,f,g){return al(b^c^d,a,b,e,f,g)}function ap(a,b,c,d,e,f,g){return al(c^(b|~d),a,b,e,f,g)}function aq(a){var b,c=a.length,d=[0x67452301,-0x10325477,-0x67452302,0x10325476];for(b=64;b<=a.length;b+=64)ak(d,function(a){var b,c=[];for(b=0;b<64;b+=4)c[b>>2]=a.charCodeAt(b)+(a.charCodeAt(b+1)<<8)+(a.charCodeAt(b+2)<<16)+(a.charCodeAt(b+3)<<24);return c}(a.substring(b-64,b)));a=a.substring(b-64);var e=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(b=0;b<a.length;b++)e[b>>2]|=a.charCodeAt(b)<<(b%4<<3);if(e[b>>2]|=128<<(b%4<<3),b>55)for(ak(d,e),b=0;b<16;b++)e[b]=0;return e[14]=8*c,ak(d,e),d}ag=aa.atob.bind(aa),ah=aa.btoa.bind(aa);var ar="0123456789abcdef".split("");function as(a){return String.fromCharCode(255&a,(65280&a)>>8,(0xff0000&a)>>16,(0xff000000&a)>>24)}function at(a){return aq(a).map(as).join("")}var au="5d41402abc4b2a76b9719d911017c592"!=function(a){for(var b=0;b<a.length;b++)a[b]=function(a){for(var b="",c=0;c<4;c++)b+=ar[a>>8*c+4&15]+ar[a>>8*c&15];return b}(a[b]);return a.join("")}(aq("hello"));function av(a,b){if(au){var c=(65535&a)+(65535&b);return(a>>16)+(b>>16)+(c>>16)<<16|65535&c}return a+b|0}function aw(a,b){if(a!==c){for(var c,d,e=Array(1+(256/a.length|0)+1).join(a),f=[],g=0;g<256;g++)f[g]=g;var h=0;for(g=0;g<256;g++){var i=f[g];h=(h+i+e.charCodeAt(g))%256,f[g]=f[h],f[h]=i}c=a,d=f}else f=d;var j=b.length,k=0,l=0,m="";for(g=0;g<j;g++)l=(l+(i=f[k=(k+1)%256]))%256,f[k]=f[l],f[l]=i,e=f[(f[k]+f[l])%256],m+=String.fromCharCode(b.charCodeAt(g)^e);return m}var ax={print:4,modify:8,copy:16,"annot-forms":32};function ay(a,b,c,d){this.v=1,this.r=2;var e=192;a.forEach(function(a){if(void 0!==ax.perm)throw Error("Invalid permission: "+a);e+=ax[a]}),this.padding="(\xbfN^Nu\x8aAd\0NV\xff\xfa\x01\b..\0\xb6\xd0h>\x80/\f\xa9\xfedSiz";var f=(b+this.padding).substr(0,32),g=(c+this.padding).substr(0,32);this.O=this.processOwnerPassword(f,g),this.P=-(1+(255^e)),this.encryptionKey=at(f+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(d)).substr(0,5),this.U=aw(this.encryptionKey,this.padding)}function az(a){if(/[^\u0000-\u00ff]/.test(a))throw Error("Invalid PDF Name Object: "+a+", Only accept ASCII characters.");for(var b="",c=a.length,d=0;d<c;d++){var e=a.charCodeAt(d);e<33||35===e||37===e||40===e||41===e||47===e||60===e||62===e||91===e||93===e||123===e||125===e||e>126?b+="#"+("0"+e.toString(16)).slice(-2):b+=a[d]}return b}function aA(a){if("object"!==e()(a))throw Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var b={};this.subscribe=function(a,c,d){if(d=d||!1,"string"!=typeof a||"function"!=typeof c||"boolean"!=typeof d)throw Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");b.hasOwnProperty(a)||(b[a]={});var e=Math.random().toString(35);return b[a][e]=[c,!!d],e},this.unsubscribe=function(a){for(var c in b)if(b[c][a])return delete b[c][a],0===Object.keys(b[c]).length&&delete b[c],!0;return!1},this.publish=function(c){if(b.hasOwnProperty(c)){var d=Array.prototype.slice.call(arguments,1),e=[];for(var f in b[c]){var g=b[c][f];try{g[0].apply(a,d)}catch(a){aa.console&&ac.error("jsPDF PubSub Error",a.message,a)}g[1]&&e.push(f)}e.length&&e.forEach(this.unsubscribe)}},this.getTopics=function(){return b}}function aB(a){if(!(this instanceof aB))return new aB(a);var b="opacity,stroke-opacity".split(",");for(var c in a)a.hasOwnProperty(c)&&b.indexOf(c)>=0&&(this[c]=a[c]);this.id="",this.objectNumber=-1}function aC(a,b){this.gState=a,this.matrix=b,this.id="",this.objectNumber=-1}function aD(a,b,c,d,e){if(!(this instanceof aD))return new aD(a,b,c,d,e);this.type="axial"===a?2:3,this.coords=b,this.colors=c,aC.call(this,d,e)}function aE(a,b,c,d,e){if(!(this instanceof aE))return new aE(a,b,c,d,e);this.boundingBox=a,this.xStep=b,this.yStep=c,this.stream="",this.cloneIndex=0,aC.call(this,d,e)}function aF(a){var b,c="string"==typeof arguments[0]?arguments[0]:"p",d=arguments[1],f=arguments[2],g=arguments[3],h=[],i=1,j=16,k="S",l=null;"object"===e()(a=a||{})&&(c=a.orientation,d=a.unit||d,f=a.format||f,g=a.compress||a.compressPdf||g,null!==(l=a.encryption||null)&&(l.userPassword=l.userPassword||"",l.ownerPassword=l.ownerPassword||"",l.userPermissions=l.userPermissions||[]),i="number"==typeof a.userUnit?Math.abs(a.userUnit):1,void 0!==a.precision&&(b=a.precision),void 0!==a.floatPrecision&&(j=a.floatPrecision),k=a.defaultPathOperation||"S"),h=a.filters||(!0===g?["FlateEncode"]:h),d=d||"mm",c=(""+(c||"P")).toLowerCase();var m=a.putOnlyUsedFonts||!1,n={},o={internal:{},__private__:{}};o.__private__.PubSub=aA;var p="1.3",q=o.__private__.getPdfVersion=function(){return p};o.__private__.setPdfVersion=function(a){p=a};var r={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};o.__private__.getPageFormats=function(){return r};var s=o.__private__.getPageFormat=function(a){return r[a]};f=f||"a4";var t="compat",u="advanced",v=t;function w(){this.saveGraphicsState(),_(new aT(at,0,0,-at,0,cr()*at).toString()+" cm"),this.setFontSize(this.getFontSize()/at),k="n",v=u}function x(){this.restoreGraphicsState(),k="S",v=t}var y=o.__private__.combineFontStyleAndFontWeight=function(a,b){if("bold"==a&&"normal"==b||"bold"==a&&400==b||"normal"==a&&"italic"==b||"bold"==a&&"italic"==b)throw Error("Invalid Combination of fontweight and fontstyle");return b&&(a=400==b||"normal"===b?"italic"===a?"italic":"normal":700!=b&&"bold"!==b||"normal"!==a?(700==b?"bold":b)+""+a:"bold"),a};o.advancedAPI=function(a){var b=v===t;return b&&w.call(this),"function"!=typeof a||(a(this),b&&x.call(this)),this},o.compatAPI=function(a){var b=v===u;return b&&x.call(this),"function"!=typeof a||(a(this),b&&w.call(this)),this},o.isAdvancedAPI=function(){return v===u};var z,A=function(a){if(v!==u)throw Error(a+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},B=o.roundToPrecision=o.__private__.roundToPrecision=function(a,c){var d=b||c;if(isNaN(a)||isNaN(d))throw Error("Invalid argument passed to jsPDF.roundToPrecision");return a.toFixed(d).replace(/0+$/,"")};z=o.hpf=o.__private__.hpf="number"==typeof j?function(a){if(isNaN(a))throw Error("Invalid argument passed to jsPDF.hpf");return B(a,j)}:"smart"===j?function(a){if(isNaN(a))throw Error("Invalid argument passed to jsPDF.hpf");return B(a,a>-1&&a<1?16:5)}:function(a){if(isNaN(a))throw Error("Invalid argument passed to jsPDF.hpf");return B(a,16)};var C=o.f2=o.__private__.f2=function(a){if(isNaN(a))throw Error("Invalid argument passed to jsPDF.f2");return B(a,2)},D=o.__private__.f3=function(a){if(isNaN(a))throw Error("Invalid argument passed to jsPDF.f3");return B(a,3)},E=o.scale=o.__private__.scale=function(a){if(isNaN(a))throw Error("Invalid argument passed to jsPDF.scale");return v===t?a*at:v===u?a:void 0},F=function(a){return E(v===t?cr()-a:v===u?a:void 0)};o.__private__.setPrecision=o.setPrecision=function(a){"number"==typeof parseInt(a,10)&&(b=parseInt(a,10))};var G,H="00000000000000000000000000000000",I=o.__private__.getFileId=function(){return H},J=o.__private__.setFileId=function(a){return H=void 0!==a&&/^[a-fA-F0-9]{32}$/.test(a)?a.toUpperCase():H.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),null!==l&&(bJ=new ay(l.userPermissions,l.userPassword,l.ownerPassword,H)),H};o.setFileId=function(a){return J(a),this},o.getFileId=function(){return I()};var K=o.__private__.convertDateToPDFDate=function(a){var b=a.getTimezoneOffset(),c=Math.floor(Math.abs(b/60)),d=Math.abs(b%60),e=(b<0?"+":"-")+P(c)+"'"+P(d)+"'";return"D:"+a.getFullYear()+P(a.getMonth()+1)+P(a.getDate())+P(a.getHours())+P(a.getMinutes())+P(a.getSeconds())+e},L=o.__private__.convertPDFDateToDate=function(a){return new Date(parseInt(a.substr(2,4),10),parseInt(a.substr(6,2),10)-1,parseInt(a.substr(8,2),10),parseInt(a.substr(10,2),10),parseInt(a.substr(12,2),10),parseInt(a.substr(14,2),10),0)},M=o.__private__.setCreationDate=function(a){var b;if(void 0===a&&(a=new Date),a instanceof Date)b=K(a);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(a))throw Error("Invalid argument passed to jsPDF.setCreationDate");b=a}return G=b},N=o.__private__.getCreationDate=function(a){var b=G;return"jsDate"===a&&(b=L(G)),b};o.setCreationDate=function(a){return M(a),this},o.getCreationDate=function(a){return N(a)};var O,P=o.__private__.padd2=function(a){return("0"+parseInt(a)).slice(-2)},Q=o.__private__.padd2Hex=function(a){return("00"+(a=a.toString())).substr(a.length)},R=0,S=[],T=[],U=0,V=[],W=[],X=!1,Y=T,Z=function(){R=0,U=0,T=[],S=[],V=[],a_=aY(),a0=aY()};o.__private__.setCustomOutputDestination=function(a){X=!0,Y=a};var $=function(a){X||(Y=a)};o.__private__.resetCustomOutputDestination=function(){X=!1,Y=T};var _=o.__private__.out=function(a){return a=a.toString(),U+=a.length+1,Y.push(a),Y},ab=o.__private__.write=function(a){return _(1==arguments.length?a.toString():Array.prototype.join.call(arguments," "))},ad=o.__private__.getArrayBuffer=function(a){for(var b=a.length,c=new ArrayBuffer(b),d=new Uint8Array(c);b--;)d[b]=a.charCodeAt(b);return c},ae=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];o.__private__.getStandardFonts=function(){return ae};var af=a.fontSize||16;o.__private__.setFontSize=o.setFontSize=function(a){return af=v===u?a/at:a,this};var ag,ak=o.__private__.getFontSize=o.getFontSize=function(){return v===t?af:af*at},al=a.R2L||!1;o.__private__.setR2L=o.setR2L=function(a){return al=a,this},o.__private__.getR2L=o.getR2L=function(){return al};var am,an=o.__private__.setZoomMode=function(a){if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(a))ag=a;else if(isNaN(a)){if(-1===[void 0,null,"fullwidth","fullheight","fullpage","original"].indexOf(a))throw Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+a+'" is not recognized.');ag=a}else ag=parseInt(a,10)};o.__private__.getZoomMode=function(){return ag};var ao,ap=o.__private__.setPageMode=function(a){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(a))throw Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+a+'" is not recognized.');am=a};o.__private__.getPageMode=function(){return am};var aq=o.__private__.setLayoutMode=function(a){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(a))throw Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+a+'" is not recognized.');ao=a};o.__private__.getLayoutMode=function(){return ao},o.__private__.setDisplayMode=o.setDisplayMode=function(a,b,c){return an(a),aq(b),ap(c),this};var ar={title:"",subject:"",author:"",keywords:"",creator:""};o.__private__.getDocumentProperty=function(a){if(-1===Object.keys(ar).indexOf(a))throw Error("Invalid argument passed to jsPDF.getDocumentProperty");return ar[a]},o.__private__.getDocumentProperties=function(){return ar},o.__private__.setDocumentProperties=o.setProperties=o.setDocumentProperties=function(a){for(var b in ar)ar.hasOwnProperty(b)&&a[b]&&(ar[b]=a[b]);return this},o.__private__.setDocumentProperty=function(a,b){if(-1===Object.keys(ar).indexOf(a))throw Error("Invalid arguments passed to jsPDF.setDocumentProperty");return ar[a]=b};var as,at,au,av,aw,ax={},aC={},aG=[],aH={},aI={},aJ={},aK={},aL=null,aM=0,aN=[],aO=new aA(o),aP=a.hotfixes||[],aQ={},aR={},aS=[],aT=function a(b,c,d,e,f,g){if(!(this instanceof a))return new a(b,c,d,e,f,g);isNaN(b)&&(b=1),isNaN(c)&&(c=0),isNaN(d)&&(d=0),isNaN(e)&&(e=1),isNaN(f)&&(f=0),isNaN(g)&&(g=0),this._matrix=[b,c,d,e,f,g]};Object.defineProperty(aT.prototype,"sx",{get:function(){return this._matrix[0]},set:function(a){this._matrix[0]=a}}),Object.defineProperty(aT.prototype,"shy",{get:function(){return this._matrix[1]},set:function(a){this._matrix[1]=a}}),Object.defineProperty(aT.prototype,"shx",{get:function(){return this._matrix[2]},set:function(a){this._matrix[2]=a}}),Object.defineProperty(aT.prototype,"sy",{get:function(){return this._matrix[3]},set:function(a){this._matrix[3]=a}}),Object.defineProperty(aT.prototype,"tx",{get:function(){return this._matrix[4]},set:function(a){this._matrix[4]=a}}),Object.defineProperty(aT.prototype,"ty",{get:function(){return this._matrix[5]},set:function(a){this._matrix[5]=a}}),Object.defineProperty(aT.prototype,"a",{get:function(){return this._matrix[0]},set:function(a){this._matrix[0]=a}}),Object.defineProperty(aT.prototype,"b",{get:function(){return this._matrix[1]},set:function(a){this._matrix[1]=a}}),Object.defineProperty(aT.prototype,"c",{get:function(){return this._matrix[2]},set:function(a){this._matrix[2]=a}}),Object.defineProperty(aT.prototype,"d",{get:function(){return this._matrix[3]},set:function(a){this._matrix[3]=a}}),Object.defineProperty(aT.prototype,"e",{get:function(){return this._matrix[4]},set:function(a){this._matrix[4]=a}}),Object.defineProperty(aT.prototype,"f",{get:function(){return this._matrix[5]},set:function(a){this._matrix[5]=a}}),Object.defineProperty(aT.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(aT.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(aT.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(aT.prototype,"isIdentity",{get:function(){return 1===this.sx&&0===this.shy&&0===this.shx&&1===this.sy&&0===this.tx&&0===this.ty}}),aT.prototype.join=function(a){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(z).join(a)},aT.prototype.multiply=function(a){return new aT(a.sx*this.sx+a.shy*this.shx,a.sx*this.shy+a.shy*this.sy,a.shx*this.sx+a.sy*this.shx,a.shx*this.shy+a.sy*this.sy,a.tx*this.sx+a.ty*this.shx+this.tx,a.tx*this.shy+a.ty*this.sy+this.ty)},aT.prototype.decompose=function(){var a=this.sx,b=this.shy,c=this.shx,d=this.sy,e=this.tx,f=this.ty,g=Math.sqrt(a*a+b*b),h=(a/=g)*c+(b/=g)*d,i=Math.sqrt((c-=a*h)*c+(d-=b*h)*d);return h/=i,a*(d/=i)<b*(c/=i)&&(a=-a,b=-b,h=-h,g=-g),{scale:new aT(g,0,0,i,0,0),translate:new aT(1,0,0,1,e,f),rotate:new aT(a,b,-b,a,0,0),skew:new aT(1,0,h,1,0,0)}},aT.prototype.toString=function(a){return this.join(" ")},aT.prototype.inversed=function(){var a=this.sx,b=this.shy,c=this.shx,d=this.sy,e=this.tx,f=this.ty,g=1/(a*d-b*c),h=d*g,i=-b*g,j=-c*g,k=a*g;return new aT(h,i,j,k,-h*e-j*f,-i*e-k*f)},aT.prototype.applyToPoint=function(a){return new cj(a.x*this.sx+a.y*this.shx+this.tx,a.x*this.shy+a.y*this.sy+this.ty)},aT.prototype.applyToRectangle=function(a){var b=this.applyToPoint(a),c=this.applyToPoint(new cj(a.x+a.w,a.y+a.h));return new ck(b.x,b.y,c.x-b.x,c.y-b.y)},aT.prototype.clone=function(){return new aT(this.sx,this.shy,this.shx,this.sy,this.tx,this.ty)},o.Matrix=aT;var aU=o.matrixMult=function(a,b){return b.multiply(a)},aV=new aT(1,0,0,1,0,0);o.unitMatrix=o.identityMatrix=aV;var aW=function(a,b){if(!aI[a]){var c=(b instanceof aD?"Sh":"P")+(Object.keys(aH).length+1).toString(10);b.id=c,aI[a]=c,aH[c]=b,aO.publish("addPattern",b)}};o.ShadingPattern=aD,o.TilingPattern=aE,o.addShadingPattern=function(a,b){return A("addShadingPattern()"),aW(a,b),this},o.beginTilingPattern=function(a){A("beginTilingPattern()"),cm(a.boundingBox[0],a.boundingBox[1],a.boundingBox[2]-a.boundingBox[0],a.boundingBox[3]-a.boundingBox[1],a.matrix)},o.endTilingPattern=function(a,b){A("endTilingPattern()"),b.stream=W[O].join("\n"),aW(a,b),aO.publish("endTilingPattern",b),aS.pop().restore()};var aX=o.__private__.newObject=function(){var a=aY();return aZ(a,!0),a},aY=o.__private__.newObjectDeferred=function(){return S[++R]=function(){return U},R},aZ=function(a,b){return b="boolean"==typeof b&&b,S[a]=U,b&&_(a+" 0 obj"),a},a$=o.__private__.newAdditionalObject=function(){var a={objId:aY(),content:""};return V.push(a),a},a_=aY(),a0=aY(),a1=o.__private__.decodeColorString=function(a){var b=a.split(" ");if(2!==b.length||"g"!==b[1]&&"G"!==b[1])5===b.length&&("k"===b[4]||"K"===b[4])&&(b=[(1-b[0])*(1-b[3]),(1-b[1])*(1-b[3]),(1-b[2])*(1-b[3]),"r"]);else{var c=parseFloat(b[0]);b=[c,c,c,"r"]}for(var d="#",e=0;e<3;e++)d+=("0"+Math.floor(255*parseFloat(b[e])).toString(16)).slice(-2);return d},a2=o.__private__.encodeColorString=function(a){"string"==typeof a&&(a={ch1:a});var b,c=a.ch1,d=a.ch2,f=a.ch3,g=a.ch4,h="draw"===a.pdfColorType?["G","RG","K"]:["g","rg","k"];if("string"==typeof c&&"#"!==c.charAt(0)){var i=new aj(c);if(i.ok)c=i.toHex();else if(!/^\d*\.?\d*$/.test(c))throw Error('Invalid color "'+c+'" passed to jsPDF.encodeColorString.')}if("string"==typeof c&&/^#[0-9A-Fa-f]{3}$/.test(c)&&(c="#"+c[1]+c[1]+c[2]+c[2]+c[3]+c[3]),"string"==typeof c&&/^#[0-9A-Fa-f]{6}$/.test(c)){var j=parseInt(c.substr(1),16);c=j>>16&255,d=j>>8&255,f=255&j}if(void 0===d||void 0===g&&c===d&&d===f)b="string"==typeof c?c+" "+h[0]:2===a.precision?C(c/255)+" "+h[0]:D(c/255)+" "+h[0];else if(void 0===g||"object"===e()(g)){if(g&&!isNaN(g.a)&&0===g.a)return["1. 1. 1.",h[1]].join(" ");b="string"==typeof c?[c,d,f,h[1]].join(" "):2===a.precision?[C(c/255),C(d/255),C(f/255),h[1]].join(" "):[D(c/255),D(d/255),D(f/255),h[1]].join(" ")}else b="string"==typeof c?[c,d,f,g,h[2]].join(" "):2===a.precision?[C(c),C(d),C(f),C(g),h[2]].join(" "):[D(c),D(d),D(f),D(g),h[2]].join(" ");return b},a3=o.__private__.getFilters=function(){return h},a4=o.__private__.putStream=function(a){var b=(a=a||{}).data||"",c=a.filters||a3(),d=a.alreadyAppliedFilters||[],e=a.addLength1||!1,f=b.length,g=a.objectId,h=function(a){return a};if(null!==l&&void 0===g)throw Error("ObjectId must be passed to putStream for file encryption");null!==l&&(h=bJ.encryptor(g,0));var i={};!0===c&&(c=["FlateEncode"]);var j=a.additionalKeyValues||[],k=(i=void 0!==aF.API.processDataByFilters?aF.API.processDataByFilters(b,c):{data:b,reverseChain:[]}).reverseChain+(Array.isArray(d)?d.join(" "):d.toString());if(0!==i.data.length&&(j.push({key:"Length",value:i.data.length}),!0===e&&j.push({key:"Length1",value:f})),0!=k.length)if(k.split("/").length-1==1)j.push({key:"Filter",value:k});else{j.push({key:"Filter",value:"["+k+"]"});for(var m=0;m<j.length;m+=1)if("DecodeParms"===j[m].key){for(var n=[],o=0;o<i.reverseChain.split("/").length-1;o+=1)n.push("null");n.push(j[m].value),j[m].value="["+n.join(" ")+"]"}}_("<<");for(var p=0;p<j.length;p++)_("/"+j[p].key+" "+j[p].value);_(">>"),0!==i.data.length&&(_("stream"),_(h(i.data)),_("endstream"))},a5=o.__private__.putPage=function(a){var b=a.number,c=a.data,d=a.objId,e=a.contentsObjId;aZ(d,!0),_("<</Type /Page"),_("/Parent "+a.rootDictionaryObjId+" 0 R"),_("/Resources "+a.resourceDictionaryObjId+" 0 R"),_("/MediaBox ["+parseFloat(z(a.mediaBox.bottomLeftX))+" "+parseFloat(z(a.mediaBox.bottomLeftY))+" "+z(a.mediaBox.topRightX)+" "+z(a.mediaBox.topRightY)+"]"),null!==a.cropBox&&_("/CropBox ["+z(a.cropBox.bottomLeftX)+" "+z(a.cropBox.bottomLeftY)+" "+z(a.cropBox.topRightX)+" "+z(a.cropBox.topRightY)+"]"),null!==a.bleedBox&&_("/BleedBox ["+z(a.bleedBox.bottomLeftX)+" "+z(a.bleedBox.bottomLeftY)+" "+z(a.bleedBox.topRightX)+" "+z(a.bleedBox.topRightY)+"]"),null!==a.trimBox&&_("/TrimBox ["+z(a.trimBox.bottomLeftX)+" "+z(a.trimBox.bottomLeftY)+" "+z(a.trimBox.topRightX)+" "+z(a.trimBox.topRightY)+"]"),null!==a.artBox&&_("/ArtBox ["+z(a.artBox.bottomLeftX)+" "+z(a.artBox.bottomLeftY)+" "+z(a.artBox.topRightX)+" "+z(a.artBox.topRightY)+"]"),"number"==typeof a.userUnit&&1!==a.userUnit&&_("/UserUnit "+a.userUnit),aO.publish("putPage",{objId:d,pageContext:aN[b],pageNumber:b,page:c}),_("/Contents "+e+" 0 R"),_(">>"),_("endobj");var f=c.join("\n");return v===u&&(f+="\nQ"),aZ(e,!0),a4({data:f,filters:a3(),objectId:e}),_("endobj"),d},a6=o.__private__.putPages=function(){var a,b,c=[];for(a=1;a<=aM;a++)aN[a].objId=aY(),aN[a].contentsObjId=aY();for(a=1;a<=aM;a++)c.push(a5({number:a,data:W[a],objId:aN[a].objId,contentsObjId:aN[a].contentsObjId,mediaBox:aN[a].mediaBox,cropBox:aN[a].cropBox,bleedBox:aN[a].bleedBox,trimBox:aN[a].trimBox,artBox:aN[a].artBox,userUnit:aN[a].userUnit,rootDictionaryObjId:a_,resourceDictionaryObjId:a0}));aZ(a_,!0),_("<</Type /Pages");var d="/Kids [";for(b=0;b<aM;b++)d+=c[b]+" 0 R ";_(d+"]"),_("/Count "+aM),_(">>"),_("endobj"),aO.publish("postPutPages")},a7=function(a){aO.publish("putFont",{font:a,out:_,newObject:aX,putStream:a4}),!0!==a.isAlreadyPutted&&(a.objectNumber=aX(),_("<<"),_("/Type /Font"),_("/BaseFont /"+az(a.postScriptName)),_("/Subtype /Type1"),"string"==typeof a.encoding&&_("/Encoding /"+a.encoding),_("/FirstChar 32"),_("/LastChar 255"),_(">>"),_("endobj"))},a8=function(){for(var a in ax)ax.hasOwnProperty(a)&&(!1===m||!0===m&&n.hasOwnProperty(a))&&a7(ax[a])},a9=function(a){a.objectNumber=aX();var b=[];b.push({key:"Type",value:"/XObject"}),b.push({key:"Subtype",value:"/Form"}),b.push({key:"BBox",value:"["+[z(a.x),z(a.y),z(a.x+a.width),z(a.y+a.height)].join(" ")+"]"}),b.push({key:"Matrix",value:"["+a.matrix.toString()+"]"}),a4({data:a.pages[1].join("\n"),additionalKeyValues:b,objectId:a.objectNumber}),_("endobj")},ba=function(){for(var a in aQ)aQ.hasOwnProperty(a)&&a9(aQ[a])},bb=function(a,b){var c,d=[],e=1/(b-1);for(c=0;c<1;c+=e)d.push(c);if(d.push(1),0!=a[0].offset){var f={offset:0,color:a[0].color};a.unshift(f)}if(1!=a[a.length-1].offset){var g={offset:1,color:a[a.length-1].color};a.push(g)}for(var h="",i=0,j=0;j<d.length;j++){for(c=d[j];c>a[i+1].offset;)i++;var k=a[i].offset,l=(c-k)/(a[i+1].offset-k),m=a[i].color,n=a[i+1].color;h+=Q(Math.round((1-l)*m[0]+l*n[0]).toString(16))+Q(Math.round((1-l)*m[1]+l*n[1]).toString(16))+Q(Math.round((1-l)*m[2]+l*n[2]).toString(16))}return h.trim()},bc=function(a,b){b||(b=21);var c=aX(),d=bb(a.colors,b),e=[];e.push({key:"FunctionType",value:"0"}),e.push({key:"Domain",value:"[0.0 1.0]"}),e.push({key:"Size",value:"["+b+"]"}),e.push({key:"BitsPerSample",value:"8"}),e.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),e.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),a4({data:d,additionalKeyValues:e,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:c}),_("endobj"),a.objectNumber=aX(),_("<< /ShadingType "+a.type),_("/ColorSpace /DeviceRGB");var f="/Coords ["+z(parseFloat(a.coords[0]))+" "+z(parseFloat(a.coords[1]))+" ";2===a.type?f+=z(parseFloat(a.coords[2]))+" "+z(parseFloat(a.coords[3])):f+=z(parseFloat(a.coords[2]))+" "+z(parseFloat(a.coords[3]))+" "+z(parseFloat(a.coords[4]))+" "+z(parseFloat(a.coords[5])),_(f+="]"),a.matrix&&_("/Matrix ["+a.matrix.toString()+"]"),_("/Function "+c+" 0 R"),_("/Extend [true true]"),_(">>"),_("endobj")},bd=function(a,b){var c=aY(),d=aX();b.push({resourcesOid:c,objectOid:d}),a.objectNumber=d;var e=[];e.push({key:"Type",value:"/Pattern"}),e.push({key:"PatternType",value:"1"}),e.push({key:"PaintType",value:"1"}),e.push({key:"TilingType",value:"1"}),e.push({key:"BBox",value:"["+a.boundingBox.map(z).join(" ")+"]"}),e.push({key:"XStep",value:z(a.xStep)}),e.push({key:"YStep",value:z(a.yStep)}),e.push({key:"Resources",value:c+" 0 R"}),a.matrix&&e.push({key:"Matrix",value:"["+a.matrix.toString()+"]"}),a4({data:a.stream,additionalKeyValues:e,objectId:a.objectNumber}),_("endobj")},be=function(a){var b;for(b in aH)aH.hasOwnProperty(b)&&(aH[b]instanceof aD?bc(aH[b]):aH[b]instanceof aE&&bd(aH[b],a))},bf=function(a){for(var b in a.objectNumber=aX(),_("<<"),a)switch(b){case"opacity":_("/ca "+C(a[b]));break;case"stroke-opacity":_("/CA "+C(a[b]))}_(">>"),_("endobj")},bg=function(){var a;for(a in aJ)aJ.hasOwnProperty(a)&&bf(aJ[a])},bh=function(){for(var a in _("/XObject <<"),aQ)aQ.hasOwnProperty(a)&&aQ[a].objectNumber>=0&&_("/"+a+" "+aQ[a].objectNumber+" 0 R");aO.publish("putXobjectDict"),_(">>")},bi=function(){bJ.oid=aX(),_("<<"),_("/Filter /Standard"),_("/V "+bJ.v),_("/R "+bJ.r),_("/U <"+bJ.toHexString(bJ.U)+">"),_("/O <"+bJ.toHexString(bJ.O)+">"),_("/P "+bJ.P),_(">>"),_("endobj")},bj=function(){for(var a in _("/Font <<"),ax)ax.hasOwnProperty(a)&&(!1===m||!0===m&&n.hasOwnProperty(a))&&_("/"+a+" "+ax[a].objectNumber+" 0 R");_(">>")},bk=function(){if(Object.keys(aH).length>0){for(var a in _("/Shading <<"),aH)aH.hasOwnProperty(a)&&aH[a]instanceof aD&&aH[a].objectNumber>=0&&_("/"+a+" "+aH[a].objectNumber+" 0 R");aO.publish("putShadingPatternDict"),_(">>")}},bl=function(a){if(Object.keys(aH).length>0){for(var b in _("/Pattern <<"),aH)aH.hasOwnProperty(b)&&aH[b]instanceof o.TilingPattern&&aH[b].objectNumber>=0&&aH[b].objectNumber<a&&_("/"+b+" "+aH[b].objectNumber+" 0 R");aO.publish("putTilingPatternDict"),_(">>")}},bm=function(){if(Object.keys(aJ).length>0){var a;for(a in _("/ExtGState <<"),aJ)aJ.hasOwnProperty(a)&&aJ[a].objectNumber>=0&&_("/"+a+" "+aJ[a].objectNumber+" 0 R");aO.publish("putGStateDict"),_(">>")}},bn=function(a){aZ(a.resourcesOid,!0),_("<<"),_("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),bj(),bk(),bl(a.objectOid),bm(),bh(),_(">>"),_("endobj")},bo=function(){var a=[];a8(),bg(),ba(),be(a),aO.publish("putResources"),a.forEach(bn),bn({resourcesOid:a0,objectOid:Number.MAX_SAFE_INTEGER}),aO.publish("postPutResources")},bp=function(){aO.publish("putAdditionalObjects");for(var a=0;a<V.length;a++){var b=V[a];aZ(b.objId,!0),_(b.content),_("endobj")}aO.publish("postPutAdditionalObjects")},bq=function(a){aC[a.fontName]=aC[a.fontName]||{},aC[a.fontName][a.fontStyle]=a.id},br=function(a,b,c,d,e){var f={id:"F"+(Object.keys(ax).length+1).toString(10),postScriptName:a,fontName:b,fontStyle:c,encoding:d,isStandardFont:e||!1,metadata:{}};return aO.publish("addFont",{font:f,instance:this}),ax[f.id]=f,bq(f),f.id},bs=function(a,b){var c,d,e,f,g,h,i,j,k;if(e=(b=b||{}).sourceEncoding||"Unicode",g=b.outputEncoding,(b.autoencode||g)&&ax[as].metadata&&ax[as].metadata[e]&&ax[as].metadata[e].encoding&&(f=ax[as].metadata[e].encoding,!g&&ax[as].encoding&&(g=ax[as].encoding),!g&&f.codePages&&(g=f.codePages[0]),"string"==typeof g&&(g=f[g]),g)){for(i=!1,h=[],c=0,d=a.length;c<d;c++)(j=g[a.charCodeAt(c)])?h.push(String.fromCharCode(j)):h.push(a[c]),h[c].charCodeAt(0)>>8&&(i=!0);a=h.join("")}for(c=a.length;void 0===i&&0!==c;)a.charCodeAt(c-1)>>8&&(i=!0),c--;if(!i)return a;for(h=b.noBOM?[]:[254,255],c=0,d=a.length;c<d;c++){if((k=(j=a.charCodeAt(c))>>8)>>8)throw Error("Character at position "+c+" of string '"+a+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");h.push(k),h.push(j-(k<<8))}return String.fromCharCode.apply(void 0,h)},bt=o.__private__.pdfEscape=o.pdfEscape=function(a,b){return bs(a,b).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},bu=o.__private__.beginPage=function(a){W[++aM]=[],aN[aM]={objId:0,contentsObjId:0,userUnit:Number(i),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(a[0]),topRightY:Number(a[1])}},bx(aM),$(W[O])},bv=function(a,b){var d,e,g;switch(c=b||c,"string"==typeof a&&Array.isArray(d=s(a.toLowerCase()))&&(e=d[0],g=d[1]),Array.isArray(a)&&(e=a[0]*at,g=a[1]*at),isNaN(e)&&(e=f[0],g=f[1]),(e>14400||g>14400)&&(ac.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),e=Math.min(14400,e),g=Math.min(14400,g)),f=[e,g],c.substr(0,1)){case"l":g>e&&(f=[g,e]);break;case"p":e>g&&(f=[g,e])}bu(f),b2(b0),_(ca),0!==cg&&_(cg+" J"),0!==ch&&_(ch+" j"),aO.publish("addPage",{pageNumber:aM})},bw=function(a){a>0&&a<=aM&&(W.splice(a,1),aN.splice(a,1),aM--,O>aM&&(O=aM),this.setPage(O))},bx=function(a){a>0&&a<=aM&&(O=a)},by=o.__private__.getNumberOfPages=o.getNumberOfPages=function(){return W.length-1},bz=function(a,b,c){var d,e=void 0;return c=c||{},a=void 0!==a?a:ax[as].fontName,b=void 0!==b?b:ax[as].fontStyle,void 0!==aC[d=a.toLowerCase()]&&void 0!==aC[d][b]?e=aC[d][b]:void 0!==aC[a]&&void 0!==aC[a][b]?e=aC[a][b]:!1===c.disableWarning&&ac.warn("Unable to look up font label for font '"+a+"', '"+b+"'. Refer to getFontList() for available fonts."),e||c.noFallback||null==(e=aC.times[b])&&(e=aC.times.normal),e},bA=o.__private__.putInfo=function(){var a=aX(),b=function(a){return a};for(var c in null!==l&&(b=bJ.encryptor(a,0)),_("<<"),_("/Producer ("+bt(b("jsPDF "+aF.version))+")"),ar)ar.hasOwnProperty(c)&&ar[c]&&_("/"+c.substr(0,1).toUpperCase()+c.substr(1)+" ("+bt(b(ar[c]))+")");_("/CreationDate ("+bt(b(G))+")"),_(">>"),_("endobj")},bB=o.__private__.putCatalog=function(a){var b=(a=a||{}).rootDictionaryObjId||a_;switch(aX(),_("<<"),_("/Type /Catalog"),_("/Pages "+b+" 0 R"),ag||(ag="fullwidth"),ag){case"fullwidth":_("/OpenAction [3 0 R /FitH null]");break;case"fullheight":_("/OpenAction [3 0 R /FitV null]");break;case"fullpage":_("/OpenAction [3 0 R /Fit]");break;case"original":_("/OpenAction [3 0 R /XYZ null null 1]");break;default:var c=""+ag;"%"===c.substr(c.length-1)&&(ag=parseInt(ag)/100),"number"==typeof ag&&_("/OpenAction [3 0 R /XYZ null null "+C(ag)+"]")}switch(ao||(ao="continuous"),ao){case"continuous":_("/PageLayout /OneColumn");break;case"single":_("/PageLayout /SinglePage");break;case"two":case"twoleft":_("/PageLayout /TwoColumnLeft");break;case"tworight":_("/PageLayout /TwoColumnRight")}am&&_("/PageMode /"+am),aO.publish("putCatalog"),_(">>"),_("endobj")},bC=o.__private__.putTrailer=function(){_("trailer"),_("<<"),_("/Size "+(R+1)),_("/Root "+R+" 0 R"),_("/Info "+(R-1)+" 0 R"),null!==l&&_("/Encrypt "+bJ.oid+" 0 R"),_("/ID [ <"+H+"> <"+H+"> ]"),_(">>")},bD=o.__private__.putHeader=function(){_("%PDF-"+p),_("%\xba\xdf\xac\xe0")},bE=o.__private__.putXRef=function(){var a="0000000000";_("xref"),_("0 "+(R+1)),_("0000000000 65535 f ");for(var b=1;b<=R;b++)"function"==typeof S[b]?_((a+S[b]()).slice(-10)+" 00000 n "):void 0!==S[b]?_((a+S[b]).slice(-10)+" 00000 n "):_("0000000000 00000 n ")},bF=o.__private__.buildDocument=function(){Z(),$(T),aO.publish("buildDocument"),bD(),a6(),bp(),bo(),null!==l&&bi(),bA(),bB();var a=U;return bE(),bC(),_("startxref"),_(""+a),_("%%EOF"),$(W[O]),T.join("\n")},bG=o.__private__.getBlob=function(a){return new Blob([ad(a)],{type:"application/pdf"})},bH=o.output=o.__private__.output=((b$=function(a,b){switch("string"==typeof(b=b||{})?b={filename:b}:b.filename=b.filename||"generated.pdf",a){case void 0:return bF();case"save":o.save(b.filename);break;case"arraybuffer":return ad(bF());case"blob":return bG(bF());case"bloburi":case"bloburl":if(void 0!==aa.URL&&"function"==typeof aa.URL.createObjectURL)return aa.URL&&aa.URL.createObjectURL(bG(bF()))||void 0;ac.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var c="",d=bF();try{c=ah(d)}catch(a){c=ah(unescape(encodeURIComponent(d)))}return"data:application/pdf;filename="+b.filename+";base64,"+c;case"pdfobjectnewwindow":if("[object Window]"===Object.prototype.toString.call(aa)){var e="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",f=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';b.pdfObjectUrl&&(e=b.pdfObjectUrl,f="");var g='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+e+'"'+f+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(b)+");<\/script></body></html>",h=aa.open();return null!==h&&h.document.write(g),h}throw Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if("[object Window]"===Object.prototype.toString.call(aa)){var i='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(b.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+b.filename+'" width="500px" height="400px" /></body></html>',j=aa.open();if(null!==j){j.document.write(i);var k=this;j.document.documentElement.querySelector("#pdfViewer").onload=function(){j.document.title=b.filename,j.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(k.output("bloburl"))}}return j}throw Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if("[object Window]"!==Object.prototype.toString.call(aa))throw Error("The option dataurlnewwindow just works in a browser-environment.");var l='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",b)+'"></iframe></body></html>',m=aa.open();if(null!==m&&(m.document.write(l),m.document.title=b.filename),m||"undefined"==typeof safari)return m;break;case"datauri":case"dataurl":return aa.document.location.href=this.output("datauristring",b);default:return null}}).foo=function(){try{return b$.apply(this,arguments)}catch(c){var a=c.stack||"";~a.indexOf(" at ")&&(a=a.split(" at ")[1]);var b="Error in function "+a.split("\n")[0].split("<")[0]+": "+c.message;if(!aa.console)throw Error(b);aa.console.error(b,c),aa.alert&&alert(b)}},b$.foo.bar=b$,b$.foo),bI=function(a){return!0===Array.isArray(aP)&&aP.indexOf(a)>-1};switch(d){case"pt":at=1;break;case"mm":at=72/25.4;break;case"cm":at=72/2.54;break;case"in":at=72;break;case"px":at=1==bI("px_scaling")?.75:96/72;break;case"pc":case"em":at=12;break;case"ex":at=6;break;default:if("number"!=typeof d)throw Error("Invalid unit: "+d);at=d}var bJ=null;M(),J();var bK=o.__private__.getPageInfo=o.getPageInfo=function(a){if(isNaN(a)||a%1!=0)throw Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:aN[a].objId,pageNumber:a,pageContext:aN[a]}},bL=o.__private__.getPageInfoByObjId=function(a){if(isNaN(a)||a%1!=0)throw Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var b in aN)if(aN[b].objId===a)break;return bK(b)},bM=o.__private__.getCurrentPageInfo=o.getCurrentPageInfo=function(){return{objId:aN[O].objId,pageNumber:O,pageContext:aN[O]}};o.addPage=function(){return bv.apply(this,arguments),this},o.setPage=function(){return bx.apply(this,arguments),$.call(this,W[O]),this},o.insertPage=function(a){return this.addPage(),this.movePage(O,a),this},o.movePage=function(a,b){var c,d;if(a>b){c=W[a],d=aN[a];for(var e=a;e>b;e--)W[e]=W[e-1],aN[e]=aN[e-1];W[b]=c,aN[b]=d,this.setPage(b)}else if(a<b){c=W[a],d=aN[a];for(var f=a;f<b;f++)W[f]=W[f+1],aN[f]=aN[f+1];W[b]=c,aN[b]=d,this.setPage(b)}return this},o.deletePage=function(){return bw.apply(this,arguments),this},o.__private__.text=o.text=function(a,b,c,d,f){var g,h,i,j,k,l,m,o,p,q=(d=d||{}).scope||this;if("number"==typeof a&&"number"==typeof b&&("string"==typeof c||Array.isArray(c))){var r=c;c=b,b=a,a=r}if(arguments[3]instanceof aT==!1?(i=arguments[4],j=arguments[5],"object"===e()(m=arguments[3])&&null!==m||("string"==typeof i&&(j=i,i=null),"string"==typeof m&&(j=m,m=null),"number"==typeof m&&(i=m,m=null),d={flags:m,angle:i,align:j})):(A("The transform parameter of text() with a Matrix value"),p=f),isNaN(b)||isNaN(c)||null==a)throw Error("Invalid arguments passed to jsPDF.text");if(0===a.length)return q;var s="",t=!1,w="number"==typeof d.lineHeightFactor?d.lineHeightFactor:b_,x=q.internal.scaleFactor;function y(a){for(var b,c=a.concat(),d=[],e=c.length;e--;)"string"==typeof(b=c.shift())?d.push(b):Array.isArray(a)&&(1===b.length||void 0===b[1]&&void 0===b[2])?d.push(b[0]):d.push([b[0],b[1],b[2]]);return d}function B(a,b){var c;if("string"==typeof a)c=b(a)[0];else if(Array.isArray(a)){for(var d,e,f=a.concat(),g=[],h=f.length;h--;)"string"==typeof(d=f.shift())?g.push(b(d)[0]):Array.isArray(d)&&"string"==typeof d[0]&&g.push([(e=b(d[0],d[1],d[2]))[0],e[1],e[2]]);c=g}return c}var C=!1,D=!0;if("string"==typeof a)C=!0;else if(Array.isArray(a)){var F=a.concat();h=[];for(var G,H=F.length;H--;)("string"!=typeof(G=F.shift())||Array.isArray(G)&&"string"!=typeof G[0])&&(D=!1);C=D}if(!1===C)throw Error('Type of text must be string or Array. "'+a+'" is not recognized.');"string"==typeof a&&(a=a.match(/[\r?\n]/)?a.split(/\r\n|\r|\n/g):[a]);var I=af/q.internal.scaleFactor,J=I*(w-1);switch(d.baseline){case"bottom":c-=J;break;case"top":c+=I-J;break;case"hanging":c+=I-2*J;break;case"middle":c+=I/2-J}if((l=d.maxWidth||0)>0&&("string"==typeof a?a=q.splitTextToSize(a,l):"[object Array]"===Object.prototype.toString.call(a)&&(a=a.reduce(function(a,b){return a.concat(q.splitTextToSize(b,l))},[]))),g={text:a,x:b,y:c,options:d,mutex:{pdfEscape:bt,activeFontKey:as,fonts:ax,activeFontSize:af}},aO.publish("preProcessText",g),a=g.text,i=(d=g.options).angle,p instanceof aT==!1&&i&&"number"==typeof i){i*=Math.PI/180,0===d.rotationDirection&&(i=-i),v===u&&(i=-i);var K=Math.cos(i),L=Math.sin(i);p=new aT(K,L,-L,K,0,0)}else i&&i instanceof aT&&(p=i);v!==u||p||(p=aV),void 0!==(k=d.charSpace||ce)&&(s+=z(E(k))+" Tc\n",this.setCharSpace(this.getCharSpace()||0)),void 0!==(o=d.horizontalScale)&&(s+=z(100*o)+" Tz\n"),d.lang;var M=-1,N=void 0!==d.renderingMode?d.renderingMode:d.stroke,O=q.internal.getCurrentPageInfo().pageContext;switch(N){case 0:case!1:case"fill":M=0;break;case 1:case!0:case"stroke":M=1;break;case 2:case"fillThenStroke":M=2;break;case 3:case"invisible":M=3;break;case 4:case"fillAndAddForClipping":M=4;break;case 5:case"strokeAndAddPathForClipping":M=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":M=6;break;case 7:case"addToPathForClipping":M=7}var P=void 0!==O.usedRenderingMode?O.usedRenderingMode:-1;-1!==M?s+=M+" Tr\n":-1!==P&&(s+="0 Tr\n"),-1!==M&&(O.usedRenderingMode=M),j=d.align||"left";var Q,R=af*w,S=q.internal.pageSize.getWidth(),T=ax[as];k=d.charSpace||ce,l=d.maxWidth||0,m=Object.assign({autoencode:!0,noBOM:!0},d.flags);var U=[],V=function(a){return q.getStringUnitWidth(a,{font:T,charSpace:k,fontSize:af,doKerning:!1})*af/x};if("[object Array]"===Object.prototype.toString.call(a)){h=y(a),"left"!==j&&(Q=h.map(V));var W,X,Y=0;if("right"===j){b-=Q[0],a=[],H=h.length;for(var Z=0;Z<H;Z++)0===Z?(X=b6(b),W=b7(c)):(X=E(Y-Q[Z]),W=-R),a.push([h[Z],X,W]),Y=Q[Z]}else if("center"===j){b-=Q[0]/2,a=[],H=h.length;for(var $=0;$<H;$++)0===$?(X=b6(b),W=b7(c)):(X=E((Y-Q[$])/2),W=-R),a.push([h[$],X,W]),Y=Q[$]}else if("left"===j){a=[],H=h.length;for(var aa=0;aa<H;aa++)a.push(h[aa])}else if("justify"===j&&"Identity-H"===T.encoding){a=[],H=h.length,l=0!==l?l:S;for(var ab=0,ac=0;ac<H;ac++)if(W=0===ac?b7(c):-R,X=0===ac?b6(b):ab,ac<H-1){var ad=E((l-Q[ac])/(h[ac].split(" ").length-1)),ae=h[ac].split(" ");a.push([ae[0]+" ",X,W]),ab=0;for(var ag=1;ag<ae.length;ag++){var ah=(V(ae[ag-1]+" "+ae[ag])-V(ae[ag]))*x+ad;ag==ae.length-1?a.push([ae[ag],ah,0]):a.push([ae[ag]+" ",ah,0]),ab-=ah}}else a.push([h[ac],X,W]);a.push(["",ab,0])}else{if("justify"!==j)throw Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(a=[],H=h.length,l=0!==l?l:S,ac=0;ac<H;ac++)W=0===ac?b7(c):-R,X=0===ac?b6(b):0,ac<H-1?U.push(z(E((l-Q[ac])/(h[ac].split(" ").length-1)))):U.push(0),a.push([h[ac],X,W])}}!0===("boolean"==typeof d.R2L?d.R2L:al)&&(a=B(a,function(a,b,c){return[a.split("").reverse().join(""),b,c]})),g={text:a,x:b,y:c,options:d,mutex:{pdfEscape:bt,activeFontKey:as,fonts:ax,activeFontSize:af}},aO.publish("postProcessText",g),a=g.text,t=g.mutex.isHex||!1;var ai=ax[as].encoding;"WinAnsiEncoding"!==ai&&"StandardEncoding"!==ai||(a=B(a,function(a,b,c){return[bt(a.split("	").join(Array(d.TabLen||9).join(" ")),m),b,c]})),h=y(a),a=[];for(var aj,ak,am,an=+!!Array.isArray(h[0]),ao="",ap=function(a,b,c){var e="";return c instanceof aT?(c="number"==typeof d.angle?aU(c,new aT(1,0,0,1,a,b)):aU(new aT(1,0,0,1,a,b),c),v===u&&(c=aU(new aT(1,0,0,-1,0,0),c)),e=c.join(" ")+" Tm\n"):e=z(a)+" "+z(b)+" Td\n",e},aq=0;aq<h.length;aq++){switch(ao="",an){case 1:am=(t?"<":"(")+h[aq][0]+(t?">":")"),aj=parseFloat(h[aq][1]),ak=parseFloat(h[aq][2]);break;case 0:am=(t?"<":"(")+h[aq]+(t?">":")"),aj=b6(b),ak=b7(c)}void 0!==U&&void 0!==U[aq]&&(ao=U[aq]+" Tw\n"),0===aq?a.push(ao+ap(aj,ak,p)+am):0===an?a.push(ao+am):1===an&&a.push(ao+ap(aj,ak,p)+am)}a=(0===an?a.join(" Tj\nT* "):a.join(" Tj\n"))+" Tj\n";var ar="BT\n/";return ar+=as+" "+af+" Tf\n",ar+=z(af*w)+" TL\n",ar+=cc+"\n",ar+=s,ar+=a,_(ar+="ET"),n[as]=!0,q};var bN=o.__private__.clip=o.clip=function(a){return _("evenodd"===a?"W*":"W"),this};o.clipEvenOdd=function(){return bN("evenodd")},o.__private__.discardPath=o.discardPath=function(){return _("n"),this};var bO=o.__private__.isValidStyle=function(a){var b=!1;return -1!==[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(a)&&(b=!0),b};o.__private__.setDefaultPathOperation=o.setDefaultPathOperation=function(a){return bO(a)&&(k=a),this};var bP=o.__private__.getStyle=o.getStyle=function(a){var b=k;switch(a){case"D":case"S":b="S";break;case"F":b="f";break;case"FD":case"DF":b="B";break;case"f":case"f*":case"B":case"B*":b=a}return b},bQ=o.close=function(){return _("h"),this};o.stroke=function(){return _("S"),this},o.fill=function(a){return bR("f",a),this},o.fillEvenOdd=function(a){return bR("f*",a),this},o.fillStroke=function(a){return bR("B",a),this},o.fillStrokeEvenOdd=function(a){return bR("B*",a),this};var bR=function(a,b){"object"===e()(b)?bU(b,a):_(a)},bS=function(a){null===a||v===u&&void 0===a||_(a=bP(a))};function bT(a,b,c,d,e){var f=new aE(b||this.boundingBox,c||this.xStep,d||this.yStep,this.gState,e||this.matrix);return f.stream=this.stream,aW(a+"$$"+this.cloneIndex+++"$$",f),f}var bU=function(a,b){var c=aI[a.key],d=aH[c];if(d instanceof aD)_("q"),_(bV(b)),d.gState&&o.setGState(d.gState),_(a.matrix.toString()+" cm"),_("/"+c+" sh"),_("Q");else if(d instanceof aE){var e=new aT(1,0,0,-1,0,cr());a.matrix&&(e=e.multiply(a.matrix||aV),c=bT.call(d,a.key,a.boundingBox,a.xStep,a.yStep,e).id),_("q"),_("/Pattern cs"),_("/"+c+" scn"),d.gState&&o.setGState(d.gState),_(b),_("Q")}},bV=function(a){switch(a){case"f":case"F":case"n":return"W n";case"f*":return"W* n";case"B":case"S":return"W S";case"B*":return"W* S"}},bW=o.moveTo=function(a,b){return _(z(E(a))+" "+z(F(b))+" m"),this},bX=o.lineTo=function(a,b){return _(z(E(a))+" "+z(F(b))+" l"),this},bY=o.curveTo=function(a,b,c,d,e,f){return _([z(E(a)),z(F(b)),z(E(c)),z(F(d)),z(E(e)),z(F(f)),"c"].join(" ")),this};o.__private__.line=o.line=function(a,b,c,d,e){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d)||!bO(e))throw Error("Invalid arguments passed to jsPDF.line");return v===t?this.lines([[c-a,d-b]],a,b,[1,1],e||"S"):this.lines([[c-a,d-b]],a,b,[1,1]).stroke()},o.__private__.lines=o.lines=function(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q,r;if("number"==typeof a&&(r=c,c=b,b=a,a=r),d=d||[1,1],f=f||!1,isNaN(b)||isNaN(c)||!Array.isArray(a)||!Array.isArray(d)||!bO(e)||"boolean"!=typeof f)throw Error("Invalid arguments passed to jsPDF.lines");for(bW(b,c),g=d[0],h=d[1],j=a.length,p=b,q=c,i=0;i<j;i++)2===(k=a[i]).length?bX(p=k[0]*g+p,q=k[1]*h+q):(l=k[0]*g+p,m=k[1]*h+q,n=k[2]*g+p,o=k[3]*h+q,bY(l,m,n,o,p=k[4]*g+p,q=k[5]*h+q));return f&&bQ(),bS(e),this},o.path=function(a){for(var b=0;b<a.length;b++){var c=a[b],d=c.c;switch(c.op){case"m":bW(d[0],d[1]);break;case"l":bX(d[0],d[1]);break;case"c":bY.apply(this,d);break;case"h":bQ()}}return this},o.__private__.rect=o.rect=function(a,b,c,d,e){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d)||!bO(e))throw Error("Invalid arguments passed to jsPDF.rect");return v===t&&(d=-d),_([z(E(a)),z(F(b)),z(E(c)),z(E(d)),"re"].join(" ")),bS(e),this},o.__private__.triangle=o.triangle=function(a,b,c,d,e,f,g){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d)||isNaN(e)||isNaN(f)||!bO(g))throw Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[c-a,d-b],[e-c,f-d],[a-e,b-f]],a,b,[1,1],g,!0),this},o.__private__.roundedRect=o.roundedRect=function(a,b,c,d,e,f,g){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d)||isNaN(e)||isNaN(f)||!bO(g))throw Error("Invalid arguments passed to jsPDF.roundedRect");var h=4/3*(Math.SQRT2-1);return e=Math.min(e,.5*c),f=Math.min(f,.5*d),this.lines([[c-2*e,0],[e*h,0,e,f-f*h,e,f],[0,d-2*f],[0,f*h,-e*h,f,-e,f],[2*e-c,0],[-e*h,0,-e,-f*h,-e,-f],[0,2*f-d],[0,-f*h,e*h,-f,e,-f]],a+e,b,[1,1],g,!0),this},o.__private__.ellipse=o.ellipse=function(a,b,c,d,e){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d)||!bO(e))throw Error("Invalid arguments passed to jsPDF.ellipse");var f=4/3*(Math.SQRT2-1)*c,g=4/3*(Math.SQRT2-1)*d;return bW(a+c,b),bY(a+c,b-g,a+f,b-d,a,b-d),bY(a-f,b-d,a-c,b-g,a-c,b),bY(a-c,b+g,a-f,b+d,a,b+d),bY(a+f,b+d,a+c,b+g,a+c,b),bS(e),this},o.__private__.circle=o.circle=function(a,b,c,d){if(isNaN(a)||isNaN(b)||isNaN(c)||!bO(d))throw Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(a,b,c,c,d)},o.setFont=function(a,b,c){return c&&(b=y(b,c)),as=bz(a,b,{disableWarning:!1}),this};var bZ=o.__private__.getFont=o.getFont=function(){return ax[bz.apply(o,arguments)]};o.__private__.getFontList=o.getFontList=function(){var a,b,c={};for(a in aC)if(aC.hasOwnProperty(a))for(b in c[a]=[],aC[a])aC[a].hasOwnProperty(b)&&c[a].push(b);return c},o.addFont=function(a,b,c,d,e){var f=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&-1!==f.indexOf(arguments[3])?e=arguments[3]:arguments[3]&&-1==f.indexOf(arguments[3])&&(c=y(c,d)),e=e||"Identity-H",br.call(this,a,b,c,e)};var b$,b_,b0=a.lineWidth||.200025,b1=o.__private__.getLineWidth=o.getLineWidth=function(){return b0},b2=o.__private__.setLineWidth=o.setLineWidth=function(a){return b0=a,_(z(E(a))+" w"),this};o.__private__.setLineDash=aF.API.setLineDash=aF.API.setLineDashPattern=function(a,b){if(a=a||[],isNaN(b=b||0)||!Array.isArray(a))throw Error("Invalid arguments passed to jsPDF.setLineDash");return _("["+(a=a.map(function(a){return z(E(a))}).join(" "))+"] "+(b=z(E(b)))+" d"),this};var b3=o.__private__.getLineHeight=o.getLineHeight=function(){return af*b_};o.__private__.getLineHeight=o.getLineHeight=function(){return af*b_};var b4=o.__private__.setLineHeightFactor=o.setLineHeightFactor=function(a){return"number"==typeof(a=a||1.15)&&(b_=a),this},b5=o.__private__.getLineHeightFactor=o.getLineHeightFactor=function(){return b_};b4(a.lineHeight);var b6=o.__private__.getHorizontalCoordinate=function(a){return E(a)},b7=o.__private__.getVerticalCoordinate=function(a){return v===u?a:aN[O].mediaBox.topRightY-aN[O].mediaBox.bottomLeftY-E(a)},b8=o.__private__.getHorizontalCoordinateString=o.getHorizontalCoordinateString=function(a){return z(b6(a))},b9=o.__private__.getVerticalCoordinateString=o.getVerticalCoordinateString=function(a){return z(b7(a))},ca=a.strokeColor||"0 G";o.__private__.getStrokeColor=o.getDrawColor=function(){return a1(ca)},o.__private__.setStrokeColor=o.setDrawColor=function(a,b,c,d){return _(ca=a2({ch1:a,ch2:b,ch3:c,ch4:d,pdfColorType:"draw",precision:2})),this};var cb=a.fillColor||"0 g";o.__private__.getFillColor=o.getFillColor=function(){return a1(cb)},o.__private__.setFillColor=o.setFillColor=function(a,b,c,d){return _(cb=a2({ch1:a,ch2:b,ch3:c,ch4:d,pdfColorType:"fill",precision:2})),this};var cc=a.textColor||"0 g",cd=o.__private__.getTextColor=o.getTextColor=function(){return a1(cc)};o.__private__.setTextColor=o.setTextColor=function(a,b,c,d){return cc=a2({ch1:a,ch2:b,ch3:c,ch4:d,pdfColorType:"text",precision:3}),this};var ce=a.charSpace,cf=o.__private__.getCharSpace=o.getCharSpace=function(){return parseFloat(ce||0)};o.__private__.setCharSpace=o.setCharSpace=function(a){if(isNaN(a))throw Error("Invalid argument passed to jsPDF.setCharSpace");return ce=a,this};var cg=0;o.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},o.__private__.setLineCap=o.setLineCap=function(a){var b=o.CapJoinStyles[a];if(void 0===b)throw Error("Line cap style of '"+a+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return cg=b,_(b+" J"),this};var ch=0;o.__private__.setLineJoin=o.setLineJoin=function(a){var b=o.CapJoinStyles[a];if(void 0===b)throw Error("Line join style of '"+a+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return ch=b,_(b+" j"),this},o.__private__.setLineMiterLimit=o.__private__.setMiterLimit=o.setLineMiterLimit=o.setMiterLimit=function(a){if(isNaN(a=a||0))throw Error("Invalid argument passed to jsPDF.setLineMiterLimit");return _(z(E(a))+" M"),this},o.GState=aB,o.setGState=function(a){(a="string"==typeof a?aJ[aK[a]]:ci(null,a)).equals(aL)||(_("/"+a.id+" gs"),aL=a)};var ci=function(a,b){if(!a||!aK[a]){var c=!1;for(var d in aJ)if(aJ.hasOwnProperty(d)&&aJ[d].equals(b)){c=!0;break}if(c)b=aJ[d];else{var e="GS"+(Object.keys(aJ).length+1).toString(10);aJ[e]=b,b.id=e}return a&&(aK[a]=b.id),aO.publish("addGState",b),b}};o.addGState=function(a,b){return ci(a,b),this},o.saveGraphicsState=function(){return _("q"),aG.push({key:as,size:af,color:cc}),this},o.restoreGraphicsState=function(){_("Q");var a=aG.pop();return as=a.key,af=a.size,cc=a.color,aL=null,this},o.setCurrentTransformationMatrix=function(a){return _(a.toString()+" cm"),this},o.comment=function(a){return _("#"+a),this};var cj=function(a,b){var c=a||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return c},set:function(a){isNaN(a)||(c=parseFloat(a))}});var d=b||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return d},set:function(a){isNaN(a)||(d=parseFloat(a))}});var e="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return e},set:function(a){e=a.toString()}}),this},ck=function(a,b,c,d){cj.call(this,a,b),this.type="rect";var e=c||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return e},set:function(a){isNaN(a)||(e=parseFloat(a))}});var f=d||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return f},set:function(a){isNaN(a)||(f=parseFloat(a))}}),this},cl=function(){this.page=aM,this.currentPage=O,this.pages=W.slice(0),this.pagesContext=aN.slice(0),this.x=au,this.y=av,this.matrix=aw,this.width=cp(O),this.height=cr(O),this.outputDestination=Y,this.id="",this.objectNumber=-1};cl.prototype.restore=function(){aM=this.page,O=this.currentPage,aN=this.pagesContext,W=this.pages,au=this.x,av=this.y,aw=this.matrix,cq(O,this.width),cs(O,this.height),Y=this.outputDestination};var cm=function(a,b,c,d,e){aS.push(new cl),aM=O=0,W=[],au=a,av=b,aw=e,bu([c,d])},cn=function(a){if(aR[a])aS.pop().restore();else{var b=new cl,c="Xo"+(Object.keys(aQ).length+1).toString(10);b.id=c,aR[a]=c,aQ[c]=b,aO.publish("addFormObject",b),aS.pop().restore()}};for(var co in o.beginFormObject=function(a,b,c,d,e){return cm(a,b,c,d,e),this},o.endFormObject=function(a){return cn(a),this},o.doFormObject=function(a,b){var c=aQ[aR[a]];return _("q"),_(b.toString()+" cm"),_("/"+c.id+" Do"),_("Q"),this},o.getFormObject=function(a){var b=aQ[aR[a]];return{x:b.x,y:b.y,width:b.width,height:b.height,matrix:b.matrix}},o.save=function(a,b){return a=a||"generated.pdf",(b=b||{}).returnPromise=b.returnPromise||!1,!1===b.returnPromise?(ai(bG(bF()),a),"function"==typeof ai.unload&&aa.setTimeout&&setTimeout(ai.unload,911),this):new Promise(function(b,c){try{var d=ai(bG(bF()),a);"function"==typeof ai.unload&&aa.setTimeout&&setTimeout(ai.unload,911),b(d)}catch(a){c(a.message)}})},aF.API)aF.API.hasOwnProperty(co)&&("events"===co&&aF.API.events.length?function(a,b){var c,d,e;for(e=b.length-1;-1!==e;e--)c=b[e][0],d=b[e][1],a.subscribe.apply(a,[c].concat("function"==typeof d?[d]:d))}(aO,aF.API.events):o[co]=aF.API[co]);var cp=o.getPageWidth=function(a){return(aN[a=a||O].mediaBox.topRightX-aN[a].mediaBox.bottomLeftX)/at},cq=o.setPageWidth=function(a,b){aN[a].mediaBox.topRightX=b*at+aN[a].mediaBox.bottomLeftX},cr=o.getPageHeight=function(a){return(aN[a=a||O].mediaBox.topRightY-aN[a].mediaBox.bottomLeftY)/at},cs=o.setPageHeight=function(a,b){aN[a].mediaBox.topRightY=b*at+aN[a].mediaBox.bottomLeftY};return o.internal={pdfEscape:bt,getStyle:bP,getFont:bZ,getFontSize:ak,getCharSpace:cf,getTextColor:cd,getLineHeight:b3,getLineHeightFactor:b5,getLineWidth:b1,write:ab,getHorizontalCoordinate:b6,getVerticalCoordinate:b7,getCoordinateString:b8,getVerticalCoordinateString:b9,collections:{},newObject:aX,newAdditionalObject:a$,newObjectDeferred:aY,newObjectDeferredBegin:aZ,getFilters:a3,putStream:a4,events:aO,scaleFactor:at,pageSize:{getWidth:function(){return cp(O)},setWidth:function(a){cq(O,a)},getHeight:function(){return cr(O)},setHeight:function(a){cs(O,a)}},encryptionOptions:l,encryption:bJ,getEncryptor:function(a){return null!==l?bJ.encryptor(a,0):function(a){return a}},output:bH,getNumberOfPages:by,pages:W,out:_,f2:C,f3:D,getPageInfo:bK,getPageInfoByObjId:bL,getCurrentPageInfo:bM,getPDFVersion:q,Point:cj,Rectangle:ck,Matrix:aT,hasHotfix:bI},Object.defineProperty(o.internal.pageSize,"width",{get:function(){return cp(O)},set:function(a){cq(O,a)},enumerable:!0,configurable:!0}),Object.defineProperty(o.internal.pageSize,"height",{get:function(){return cr(O)},set:function(a){cs(O,a)},enumerable:!0,configurable:!0}),(function(a){for(var b=0,c=ae.length;b<c;b++){var d=br.call(this,a[b][0],a[b][1],a[b][2],ae[b][3],!0);!1===m&&(n[d]=!0);var e=a[b][0].split("-");bq({id:d,fontName:e[0],fontStyle:e[1]||""})}aO.publish("addFonts",{fonts:ax,dictionary:aC})}).call(o,ae),as="F1",bv(f,c),aO.publish("initialized"),o}ay.prototype.lsbFirstWord=function(a){return String.fromCharCode((0|a)&255,a>>8&255,a>>16&255,a>>24&255)},ay.prototype.toHexString=function(a){return a.split("").map(function(a){return("0"+(255&a.charCodeAt(0)).toString(16)).slice(-2)}).join("")},ay.prototype.hexToBytes=function(a){for(var b=[],c=0;c<a.length;c+=2)b.push(String.fromCharCode(parseInt(a.substr(c,2),16)));return b.join("")},ay.prototype.processOwnerPassword=function(a,b){return aw(at(b).substr(0,5),a)},ay.prototype.encryptor=function(a,b){var c=at(this.encryptionKey+String.fromCharCode(255&a,a>>8&255,a>>16&255,255&b,b>>8&255)).substr(0,10);return function(a){return aw(c,a)}},aB.prototype.equals=function(a){var b,c="id,objectNumber,equals";if(!a||e()(a)!==e()(this))return!1;var d=0;for(b in this)if(!(c.indexOf(b)>=0)){if(this.hasOwnProperty(b)&&!a.hasOwnProperty(b)||this[b]!==a[b])return!1;d++}for(b in a)a.hasOwnProperty(b)&&0>c.indexOf(b)&&d--;return 0===d},aF.API={events:[]},aF.version="3.0.1";var aG=aF.API,aH=1,aI=function(a){return a.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},aJ=function(a){return a.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},aK=function(a){return a.toFixed(2)},aL=function(a){return a.toFixed(5)};aG.__acroform__={};var aM=function(a,b){a.prototype=Object.create(b.prototype),a.prototype.constructor=a},aN=function(a){return a*aH},aO=function(a){var b=new a4,c=bi.internal.getHeight(a)||0;return b.BBox=[0,0,Number(aK(bi.internal.getWidth(a)||0)),Number(aK(c))],b},aP=aG.__acroform__.setBit=function(a,b){if(b=b||0,isNaN(a=a||0)||isNaN(b))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return a|1<<b},aQ=aG.__acroform__.clearBit=function(a,b){if(b=b||0,isNaN(a=a||0)||isNaN(b))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return a&~(1<<b)},aR=aG.__acroform__.getBit=function(a,b){if(isNaN(a)||isNaN(b))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return+(0!=(a&1<<b))},aS=aG.__acroform__.getBitForPdf=function(a,b){if(isNaN(a)||isNaN(b))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return aR(a,b-1)},aT=aG.__acroform__.setBitForPdf=function(a,b){if(isNaN(a)||isNaN(b))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return aP(a,b-1)},aU=aG.__acroform__.clearBitForPdf=function(a,b){if(isNaN(a)||isNaN(b))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return aQ(a,b-1)},aV=aG.__acroform__.calculateCoordinates=function(a,b){var c=b.internal.getHorizontalCoordinate,d=b.internal.getVerticalCoordinate,e=a[0],f=a[1],g=a[2],h=a[3],i={};return i.lowerLeft_X=c(e)||0,i.lowerLeft_Y=d(f+h)||0,i.upperRight_X=c(e+g)||0,i.upperRight_Y=d(f)||0,[Number(aK(i.lowerLeft_X)),Number(aK(i.lowerLeft_Y)),Number(aK(i.upperRight_X)),Number(aK(i.upperRight_Y))]},aW=function(a){if(a.appearanceStreamContent)return a.appearanceStreamContent;if(a.V||a.DV){var b=[],c=a._V||a.DV,d=aX(a,c),e=a.scope.internal.getFont(a.fontName,a.fontStyle).id;b.push("/Tx BMC"),b.push("q"),b.push("BT"),b.push(a.scope.__private__.encodeColorString(a.color)),b.push("/"+e+" "+aK(d.fontSize)+" Tf"),b.push("1 0 0 1 0 0 Tm"),b.push(d.text),b.push("ET"),b.push("Q"),b.push("EMC");var f=aO(a);return f.scope=a.scope,f.stream=b.join("\n"),f}},aX=function(a,b){var c=0===a.fontSize?a.maxFontSize:a.fontSize,d={text:"",fontSize:""},e=(b=")"==(b="("==b.substr(0,1)?b.substr(1):b).substr(b.length-1)?b.substr(0,b.length-1):b).split(" ");e=a.multiline?e.map(function(a){return a.split("\n")}):e.map(function(a){return[a]});var f=c,g=bi.internal.getHeight(a)||0;g=g<0?-g:g;var h=bi.internal.getWidth(a)||0;h=h<0?-h:h,f++;a:for(;f>0;){b="";var i,j,k=aY("3",a,--f).height,l=a.multiline?g-f:(g-k)/2,m=l+=2,n=0,o=0,p=0;if(f<=0){b="(...) Tj\n",b+="% Width of Text: "+aY(b,a,f=12).width+", FieldWidth:"+h+"\n";break}for(var q="",r=0,s=0;s<e.length;s++)if(e.hasOwnProperty(s)){var t=!1;if(1!==e[s].length&&p!==e[s].length-1){if((k+2)*(r+2)+2>g)continue a;q+=e[s][p],t=!0,o=s,s--}else{q=" "==(q+=e[s][p]+" ").substr(q.length-1)?q.substr(0,q.length-1):q;var u,v,w=parseInt(s),x=(u=q,v=f,w+1<e.length&&aY(u+" "+e[w+1][0],a,v).width<=h-4),y=s>=e.length-1;if(x&&!y){q+=" ",p=0;continue}if(x||y){if(y)o=w;else if(a.multiline&&(k+2)*(r+2)+2>g)continue a}else{if(!a.multiline||(k+2)*(r+2)+2>g)continue a;o=w}}for(var z="",A=n;A<=o;A++){var B=e[A];if(a.multiline){if(A===o){z+=B[p]+" ",p=(p+1)%B.length;continue}if(A===n){z+=B[B.length-1]+" ";continue}}z+=B[0]+" "}switch(j=aY(z=" "==z.substr(z.length-1)?z.substr(0,z.length-1):z,a,f).width,a.textAlign){case"right":i=h-j-2;break;case"center":i=(h-j)/2;break;default:i=2}b+=aK(i)+" "+aK(m)+" Td\n",b+="("+aI(z)+") Tj\n",b+=-aK(i)+" 0 Td\n",m=-(f+2),j=0,n=t?o:o+1,r++,q=""}break}return d.text=b,d.fontSize=f,d},aY=function(a,b,c){var d=b.scope.internal.getFont(b.fontName,b.fontStyle),e=b.scope.getStringUnitWidth(a,{font:d,fontSize:parseFloat(c),charSpace:0})*parseFloat(c);return{height:b.scope.getStringUnitWidth("3",{font:d,fontSize:parseFloat(c),charSpace:0})*parseFloat(c)*1.5,width:e}},aZ={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},a$=function(a,b){var c={type:"reference",object:a};void 0===b.internal.getPageInfo(a.page).pageContext.annotations.find(function(a){return a.type===c.type&&a.object===c.object})&&b.internal.getPageInfo(a.page).pageContext.annotations.push(c)},a_=function(a,b){for(var c in a)if(a.hasOwnProperty(c)){var d=a[c];b.internal.newObjectDeferredBegin(d.objId,!0),"object"===e()(d)&&"function"==typeof d.putStream&&d.putStream(),delete a[c]}},a0=function(a,b){if(b.scope=a,void 0!==a.internal&&(void 0===a.internal.acroformPlugin||!1===a.internal.acroformPlugin.isInitialized)){if(a6.FieldNum=0,a.internal.acroformPlugin=JSON.parse(JSON.stringify(aZ)),a.internal.acroformPlugin.acroFormDictionaryRoot)throw Error("Exception while creating AcroformDictionary");aH=a.internal.scaleFactor,a.internal.acroformPlugin.acroFormDictionaryRoot=new a5,a.internal.acroformPlugin.acroFormDictionaryRoot.scope=a,a.internal.acroformPlugin.acroFormDictionaryRoot._eventID=a.internal.events.subscribe("postPutResources",function(){a.internal.events.unsubscribe(a.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete a.internal.acroformPlugin.acroFormDictionaryRoot._eventID,a.internal.acroformPlugin.printedOut=!0}),a.internal.events.subscribe("buildDocument",function(){a.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var b=a.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var c in b)if(b.hasOwnProperty(c)){var d=b[c];d.objId=void 0,d.hasAnnotation&&a$(d,a)}}),a.internal.events.subscribe("putCatalog",function(){if(void 0===a.internal.acroformPlugin.acroFormDictionaryRoot)throw Error("putCatalogCallback: Root missing.");a.internal.write("/AcroForm "+a.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")}),a.internal.events.subscribe("postPutPages",function(b){!function(a,b){var c=!a;for(var d in a||(b.internal.newObjectDeferredBegin(b.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),b.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),a=a||b.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(a.hasOwnProperty(d)){var f=a[d],g=[],h=f.Rect;if(f.Rect&&(f.Rect=aV(f.Rect,b)),b.internal.newObjectDeferredBegin(f.objId,!0),f.DA=bi.createDefaultAppearanceStream(f),"object"===e()(f)&&"function"==typeof f.getKeyValueListForStream&&(g=f.getKeyValueListForStream()),f.Rect=h,f.hasAppearanceStream&&!f.appearanceStreamContent){var i=aW(f);g.push({key:"AP",value:"<</N "+i+">>"}),b.internal.acroformPlugin.xForms.push(i)}if(f.appearanceStreamContent){var j="";for(var k in f.appearanceStreamContent)if(f.appearanceStreamContent.hasOwnProperty(k)){var l=f.appearanceStreamContent[k];if(j+="/"+k+" <<",Object.keys(l).length>=1||Array.isArray(l)){for(var d in l)if(l.hasOwnProperty(d)){var m=l[d];"function"==typeof m&&(m=m.call(b,f)),j+="/"+d+" "+m+" ",b.internal.acroformPlugin.xForms.indexOf(m)>=0||b.internal.acroformPlugin.xForms.push(m)}}else"function"==typeof(m=l)&&(m=m.call(b,f)),j+="/"+d+" "+m,b.internal.acroformPlugin.xForms.indexOf(m)>=0||b.internal.acroformPlugin.xForms.push(m);j+=">>"}g.push({key:"AP",value:"<<\n"+j+">>"})}b.internal.putStream({additionalKeyValues:g,objectId:f.objId}),b.internal.out("endobj")}c&&a_(b.internal.acroformPlugin.xForms,b)}(b,a)}),a.internal.acroformPlugin.isInitialized=!0}},a1=aG.__acroform__.arrayToPdfArray=function(a,b,c){var d=function(a){return a};if(Array.isArray(a)){for(var f="[",g=0;g<a.length;g++)switch(0!==g&&(f+=" "),e()(a[g])){case"boolean":case"number":case"object":f+=a[g].toString();break;case"string":"/"!==a[g].substr(0,1)?(void 0!==b&&c&&(d=c.internal.getEncryptor(b)),f+="("+aI(d(a[g].toString()))+")"):f+=a[g].toString()}return f+"]"}throw Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},a2=function(a,b,c){var d=function(a){return a};return void 0!==b&&c&&(d=c.internal.getEncryptor(b)),(a=a||"").toString(),a="("+aI(d(a))+")"},a3=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(void 0===this._objId){if(void 0===this.scope)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(a){this._objId=a}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};a3.prototype.toString=function(){return this.objId+" 0 R"},a3.prototype.putStream=function(){var a=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:a,objectId:this.objId}),this.scope.internal.out("endobj")},a3.prototype.getKeyValueListForStream=function(){var a=[],b=Object.getOwnPropertyNames(this).filter(function(a){return"content"!=a&&"appearanceStreamContent"!=a&&"scope"!=a&&"objId"!=a&&"_"!=a.substring(0,1)});for(var c in b)if(!1===Object.getOwnPropertyDescriptor(this,b[c]).configurable){var d=b[c],e=this[d];e&&(Array.isArray(e)?a.push({key:d,value:a1(e,this.objId,this.scope)}):e instanceof a3?(e.scope=this.scope,a.push({key:d,value:e.objId+" 0 R"})):"function"!=typeof e&&a.push({key:d,value:e}))}return a};var a4=function(){a3.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var a,b=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return b},set:function(a){b=a}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(b){a=b.trim()},get:function(){return a||null}})};aM(a4,a3);var a5=function(){a3.call(this);var a,b=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return b.length>0?b:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return b}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(a){var b=function(a){return a};return this.scope&&(b=this.scope.internal.getEncryptor(this.objId)),"("+aI(b(a))+")"}},set:function(b){a=b}})};aM(a5,a3);var a6=function a(){a3.call(this);var b=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return b},set:function(a){if(isNaN(a))throw Error('Invalid value "'+a+'" for attribute F supplied.');b=a}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!aS(b,3)},set:function(a){!0==!!a?this.F=aT(b,3):this.F=aU(b,3)}});var c=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return c},set:function(a){if(isNaN(a))throw Error('Invalid value "'+a+'" for attribute Ff supplied.');c=a}});var d=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==d.length)return d},set:function(a){d=void 0!==a?a:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!d||isNaN(d[0])?0:d[0]},set:function(a){d[0]=a}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!d||isNaN(d[1])?0:d[1]},set:function(a){d[1]=a}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!d||isNaN(d[2])?0:d[2]},set:function(a){d[2]=a}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!d||isNaN(d[3])?0:d[3]},set:function(a){d[3]=a}});var e="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return e},set:function(a){switch(a){case"/Btn":case"/Tx":case"/Ch":case"/Sig":e=a;break;default:throw Error('Invalid value "'+a+'" for attribute FT supplied.')}}});var f=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!f||f.length<1){if(this instanceof be)return;f="FieldObject"+a.FieldNum++}var b=function(a){return a};return this.scope&&(b=this.scope.internal.getEncryptor(this.objId)),"("+aI(b(f))+")"},set:function(a){f=a.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return f},set:function(a){f=a}});var g="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return g},set:function(a){g=a}});var h="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return h},set:function(a){h=a}});var i=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return i},set:function(a){i=a}});var j=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return void 0===j?50/aH:j},set:function(a){j=a}});var k="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return k},set:function(a){k=a}});var l="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!l||this instanceof be||this instanceof bg))return a2(l,this.objId,this.scope)},set:function(a){l=a=a.toString()}});var m=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(m)return this instanceof bb==!1?a2(m,this.objId,this.scope):m},set:function(a){a=a.toString(),m=this instanceof bb==!1?"("===a.substr(0,1)?aJ(a.substr(1,a.length-2)):aJ(a):a}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof bb==!0?aJ(m.substr(1,m.length-1)):m},set:function(a){a=a.toString(),m=this instanceof bb==!0?"/"+a:a}});var n=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(n)return n},set:function(a){this.V=a}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(n)return this instanceof bb==!1?a2(n,this.objId,this.scope):n},set:function(a){a=a.toString(),n=this instanceof bb==!1?"("===a.substr(0,1)?aJ(a.substr(1,a.length-2)):aJ(a):a}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof bb==!0?aJ(n.substr(1,n.length-1)):n},set:function(a){a=a.toString(),n=this instanceof bb==!0?"/"+a:a}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var o,p=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return p},set:function(a){p=a=!!a}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(o)return o},set:function(a){o=a}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,1)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,1):this.Ff=aU(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,2)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,2):this.Ff=aU(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,3)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,3):this.Ff=aU(this.Ff,3)}});var q=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==q)return q},set:function(a){if(-1===[0,1,2].indexOf(a))throw Error('Invalid value "'+a+'" for attribute Q supplied.');q=a}}),Object.defineProperty(this,"textAlign",{get:function(){var a;switch(q){case 0:default:a="left";break;case 1:a="center";break;case 2:a="right"}return a},configurable:!0,enumerable:!0,set:function(a){switch(a){case"right":case 2:q=2;break;case"center":case 1:q=1;break;default:q=0}}})};aM(a6,a3);var a7=function(){a6.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var a=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return a},set:function(b){a=b}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return a},set:function(b){a=b}});var b=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return a1(b,this.objId,this.scope)},set:function(a){var c;c=[],"string"==typeof a&&(c=function(a,b,c){c||(c=1);for(var d,e=[];d=b.exec(a);)e.push(d[c]);return e}(a,/\((.*?)\)/g)),b=c}}),this.getOptions=function(){return b},this.setOptions=function(a){b=a,this.sort&&b.sort()},this.addOption=function(a){a=(a=a||"").toString(),b.push(a),this.sort&&b.sort()},this.removeOption=function(a,c){for(c=c||!1,a=(a=a||"").toString();-1!==b.indexOf(a)&&(b.splice(b.indexOf(a),1),!1!==c););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,18)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,18):this.Ff=aU(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,19)},set:function(a){!0===this.combo&&(!0==!!a?this.Ff=aT(this.Ff,19):this.Ff=aU(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,20)},set:function(a){!0==!!a?(this.Ff=aT(this.Ff,20),b.sort()):this.Ff=aU(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,22)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,22):this.Ff=aU(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,23)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,23):this.Ff=aU(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,27)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,27):this.Ff=aU(this.Ff,27)}}),this.hasAppearanceStream=!1};aM(a7,a6);var a8=function(){a7.call(this),this.fontName="helvetica",this.combo=!1};aM(a8,a7);var a9=function(){a8.call(this),this.combo=!0};aM(a9,a8);var ba=function(){a9.call(this),this.edit=!0};aM(ba,a9);var bb=function(){a6.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,15)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,15):this.Ff=aU(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,16)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,16):this.Ff=aU(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,17)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,17):this.Ff=aU(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,26)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,26):this.Ff=aU(this.Ff,26)}});var a,b={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var a=function(a){return a};if(this.scope&&(a=this.scope.internal.getEncryptor(this.objId)),0!==Object.keys(b).length){var c,d=[];for(c in d.push("<<"),b)d.push("/"+c+" ("+aI(a(b[c]))+")");return d.push(">>"),d.join("\n")}},set:function(a){"object"===e()(a)&&(b=a)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return b.CA||""},set:function(a){"string"==typeof a&&(b.CA=a)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return a},set:function(b){a=b}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return a.substr(1,a.length-1)},set:function(b){a="/"+b}})};aM(bb,a6);var bc=function(){bb.call(this),this.pushButton=!0};aM(bc,bb);var bd=function(){bb.call(this),this.radio=!0,this.pushButton=!1;var a=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return a},set:function(b){a=void 0!==b?b:[]}})};aM(bd,bb);var be=function(){a6.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return a},set:function(b){a=b}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return b},set:function(a){b=a}});var a,b,c,d={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var a=function(a){return a};this.scope&&(a=this.scope.internal.getEncryptor(this.objId));var b,c=[];for(b in c.push("<<"),d)c.push("/"+b+" ("+aI(a(d[b]))+")");return c.push(">>"),c.join("\n")},set:function(a){"object"===e()(a)&&(d=a)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return d.CA||""},set:function(a){"string"==typeof a&&(d.CA=a)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return c},set:function(a){c=a}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return c.substr(1,c.length-1)},set:function(a){c="/"+a}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=bi.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};aM(be,a6),bd.prototype.setAppearance=function(a){if(!("createAppearanceStream"in a)||!("getCA"in a))throw Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var b in this.Kids)if(this.Kids.hasOwnProperty(b)){var c=this.Kids[b];c.appearanceStreamContent=a.createAppearanceStream(c.optionName),c.caption=a.getCA()}},bd.prototype.createOption=function(a){var b=new be;return b.Parent=this,b.optionName=a,this.Kids.push(b),bj.call(this.scope,b),b};var bf=function(){bb.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=bi.CheckBox.createAppearanceStream()};aM(bf,bb);var bg=function(){a6.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,13)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,13):this.Ff=aU(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,21)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,21):this.Ff=aU(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,23)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,23):this.Ff=aU(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,24)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,24):this.Ff=aU(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,25)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,25):this.Ff=aU(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,26)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,26):this.Ff=aU(this.Ff,26)}});var a=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return a},set:function(b){a=b}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return a},set:function(b){Number.isInteger(b)&&(a=b)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};aM(bg,a6);var bh=function(){bg.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!aS(this.Ff,14)},set:function(a){!0==!!a?this.Ff=aT(this.Ff,14):this.Ff=aU(this.Ff,14)}}),this.password=!0};aM(bh,bg);var bi={CheckBox:{createAppearanceStream:function(){return{N:{On:bi.CheckBox.YesNormal},D:{On:bi.CheckBox.YesPushDown,Off:bi.CheckBox.OffPushDown}}},YesPushDown:function(a){var b=aO(a);b.scope=a.scope;var c=[],d=a.scope.internal.getFont(a.fontName,a.fontStyle).id,e=a.scope.__private__.encodeColorString(a.color),f=aX(a,a.caption);return c.push("0.749023 g"),c.push("0 0 "+aK(bi.internal.getWidth(a))+" "+aK(bi.internal.getHeight(a))+" re"),c.push("f"),c.push("BMC"),c.push("q"),c.push("0 0 1 rg"),c.push("/"+d+" "+aK(f.fontSize)+" Tf "+e),c.push("BT"),c.push(f.text),c.push("ET"),c.push("Q"),c.push("EMC"),b.stream=c.join("\n"),b},YesNormal:function(a){var b=aO(a);b.scope=a.scope;var c=a.scope.internal.getFont(a.fontName,a.fontStyle).id,d=a.scope.__private__.encodeColorString(a.color),e=[],f=bi.internal.getHeight(a),g=bi.internal.getWidth(a),h=aX(a,a.caption);return e.push("1 g"),e.push("0 0 "+aK(g)+" "+aK(f)+" re"),e.push("f"),e.push("q"),e.push("0 0 1 rg"),e.push("0 0 "+aK(g-1)+" "+aK(f-1)+" re"),e.push("W"),e.push("n"),e.push("0 g"),e.push("BT"),e.push("/"+c+" "+aK(h.fontSize)+" Tf "+d),e.push(h.text),e.push("ET"),e.push("Q"),b.stream=e.join("\n"),b},OffPushDown:function(a){var b=aO(a);b.scope=a.scope;var c=[];return c.push("0.749023 g"),c.push("0 0 "+aK(bi.internal.getWidth(a))+" "+aK(bi.internal.getHeight(a))+" re"),c.push("f"),b.stream=c.join("\n"),b}},RadioButton:{Circle:{createAppearanceStream:function(a){var b={D:{Off:bi.RadioButton.Circle.OffPushDown},N:{}};return b.N[a]=bi.RadioButton.Circle.YesNormal,b.D[a]=bi.RadioButton.Circle.YesPushDown,b},getCA:function(){return"l"},YesNormal:function(a){var b=aO(a);b.scope=a.scope;var c=[],d=bi.internal.getWidth(a)<=bi.internal.getHeight(a)?bi.internal.getWidth(a)/4:bi.internal.getHeight(a)/4,e=Number(((d=Number((.9*d).toFixed(5)))*bi.internal.Bezier_C).toFixed(5));return c.push("q"),c.push("1 0 0 1 "+aL(bi.internal.getWidth(a)/2)+" "+aL(bi.internal.getHeight(a)/2)+" cm"),c.push(d+" 0 m"),c.push(d+" "+e+" "+e+" "+d+" 0 "+d+" c"),c.push("-"+e+" "+d+" -"+d+" "+e+" -"+d+" 0 c"),c.push("-"+d+" -"+e+" -"+e+" -"+d+" 0 -"+d+" c"),c.push(e+" -"+d+" "+d+" -"+e+" "+d+" 0 c"),c.push("f"),c.push("Q"),b.stream=c.join("\n"),b},YesPushDown:function(a){var b=aO(a);b.scope=a.scope;var c=[],d=bi.internal.getWidth(a)<=bi.internal.getHeight(a)?bi.internal.getWidth(a)/4:bi.internal.getHeight(a)/4,e=Number((2*(d=Number((.9*d).toFixed(5)))).toFixed(5)),f=Number((e*bi.internal.Bezier_C).toFixed(5)),g=Number((d*bi.internal.Bezier_C).toFixed(5));return c.push("0.749023 g"),c.push("q"),c.push("1 0 0 1 "+aL(bi.internal.getWidth(a)/2)+" "+aL(bi.internal.getHeight(a)/2)+" cm"),c.push(e+" 0 m"),c.push(e+" "+f+" "+f+" "+e+" 0 "+e+" c"),c.push("-"+f+" "+e+" -"+e+" "+f+" -"+e+" 0 c"),c.push("-"+e+" -"+f+" -"+f+" -"+e+" 0 -"+e+" c"),c.push(f+" -"+e+" "+e+" -"+f+" "+e+" 0 c"),c.push("f"),c.push("Q"),c.push("0 g"),c.push("q"),c.push("1 0 0 1 "+aL(bi.internal.getWidth(a)/2)+" "+aL(bi.internal.getHeight(a)/2)+" cm"),c.push(d+" 0 m"),c.push(d+" "+g+" "+g+" "+d+" 0 "+d+" c"),c.push("-"+g+" "+d+" -"+d+" "+g+" -"+d+" 0 c"),c.push("-"+d+" -"+g+" -"+g+" -"+d+" 0 -"+d+" c"),c.push(g+" -"+d+" "+d+" -"+g+" "+d+" 0 c"),c.push("f"),c.push("Q"),b.stream=c.join("\n"),b},OffPushDown:function(a){var b=aO(a);b.scope=a.scope;var c=[],d=bi.internal.getWidth(a)<=bi.internal.getHeight(a)?bi.internal.getWidth(a)/4:bi.internal.getHeight(a)/4,e=Number((2*(d=Number((.9*d).toFixed(5)))).toFixed(5)),f=Number((e*bi.internal.Bezier_C).toFixed(5));return c.push("0.749023 g"),c.push("q"),c.push("1 0 0 1 "+aL(bi.internal.getWidth(a)/2)+" "+aL(bi.internal.getHeight(a)/2)+" cm"),c.push(e+" 0 m"),c.push(e+" "+f+" "+f+" "+e+" 0 "+e+" c"),c.push("-"+f+" "+e+" -"+e+" "+f+" -"+e+" 0 c"),c.push("-"+e+" -"+f+" -"+f+" -"+e+" 0 -"+e+" c"),c.push(f+" -"+e+" "+e+" -"+f+" "+e+" 0 c"),c.push("f"),c.push("Q"),b.stream=c.join("\n"),b}},Cross:{createAppearanceStream:function(a){var b={D:{Off:bi.RadioButton.Cross.OffPushDown},N:{}};return b.N[a]=bi.RadioButton.Cross.YesNormal,b.D[a]=bi.RadioButton.Cross.YesPushDown,b},getCA:function(){return"8"},YesNormal:function(a){var b=aO(a);b.scope=a.scope;var c=[],d=bi.internal.calculateCross(a);return c.push("q"),c.push("1 1 "+aK(bi.internal.getWidth(a)-2)+" "+aK(bi.internal.getHeight(a)-2)+" re"),c.push("W"),c.push("n"),c.push(aK(d.x1.x)+" "+aK(d.x1.y)+" m"),c.push(aK(d.x2.x)+" "+aK(d.x2.y)+" l"),c.push(aK(d.x4.x)+" "+aK(d.x4.y)+" m"),c.push(aK(d.x3.x)+" "+aK(d.x3.y)+" l"),c.push("s"),c.push("Q"),b.stream=c.join("\n"),b},YesPushDown:function(a){var b=aO(a);b.scope=a.scope;var c=bi.internal.calculateCross(a),d=[];return d.push("0.749023 g"),d.push("0 0 "+aK(bi.internal.getWidth(a))+" "+aK(bi.internal.getHeight(a))+" re"),d.push("f"),d.push("q"),d.push("1 1 "+aK(bi.internal.getWidth(a)-2)+" "+aK(bi.internal.getHeight(a)-2)+" re"),d.push("W"),d.push("n"),d.push(aK(c.x1.x)+" "+aK(c.x1.y)+" m"),d.push(aK(c.x2.x)+" "+aK(c.x2.y)+" l"),d.push(aK(c.x4.x)+" "+aK(c.x4.y)+" m"),d.push(aK(c.x3.x)+" "+aK(c.x3.y)+" l"),d.push("s"),d.push("Q"),b.stream=d.join("\n"),b},OffPushDown:function(a){var b=aO(a);b.scope=a.scope;var c=[];return c.push("0.749023 g"),c.push("0 0 "+aK(bi.internal.getWidth(a))+" "+aK(bi.internal.getHeight(a))+" re"),c.push("f"),b.stream=c.join("\n"),b}}},createDefaultAppearanceStream:function(a){var b=a.scope.internal.getFont(a.fontName,a.fontStyle).id,c=a.scope.__private__.encodeColorString(a.color);return"/"+b+" "+a.fontSize+" Tf "+c}};bi.internal={Bezier_C:.551915024494,calculateCross:function(a){var b=bi.internal.getWidth(a),c=bi.internal.getHeight(a),d=Math.min(b,c);return{x1:{x:(b-d)/2,y:(c-d)/2+d},x2:{x:(b-d)/2+d,y:(c-d)/2},x3:{x:(b-d)/2,y:(c-d)/2},x4:{x:(b-d)/2+d,y:(c-d)/2+d}}}},bi.internal.getWidth=function(a){var b=0;return"object"===e()(a)&&(b=aN(a.Rect[2])),b},bi.internal.getHeight=function(a){var b=0;return"object"===e()(a)&&(b=aN(a.Rect[3])),b};var bj=aG.addField=function(a){if(a0(this,a),!(a instanceof a6))throw Error("Invalid argument passed to jsPDF.addField.");return a.scope.internal.acroformPlugin.printedOut&&(a.scope.internal.acroformPlugin.printedOut=!1,a.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),a.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(a),a.page=a.scope.internal.getCurrentPageInfo().pageNumber,this};function bk(a){return a.reduce(function(a,b,c){return a[b]=c,a},{})}aG.AcroFormChoiceField=a7,aG.AcroFormListBox=a8,aG.AcroFormComboBox=a9,aG.AcroFormEditBox=ba,aG.AcroFormButton=bb,aG.AcroFormPushButton=bc,aG.AcroFormRadioButton=bd,aG.AcroFormCheckBox=bf,aG.AcroFormTextField=bg,aG.AcroFormPasswordField=bh,aG.AcroFormAppearance=bi,aG.AcroForm={ChoiceField:a7,ListBox:a8,ComboBox:a9,EditBox:ba,Button:bb,PushButton:bc,RadioButton:bd,CheckBox:bf,TextField:bg,PasswordField:bh,Appearance:bi},aF.AcroForm={ChoiceField:a7,ListBox:a8,ComboBox:a9,EditBox:ba,Button:bb,PushButton:bc,RadioButton:bd,CheckBox:bf,TextField:bg,PasswordField:bh,Appearance:bi},aF.AcroForm,function(a){a.__addimage__={};var b="UNKNOWN",c={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},d=a.__addimage__.getImageFileTypeByImageData=function(a,d){var e,f,g,h,i,j=b;if("RGBA"===(d=d||b)||void 0!==a.data&&a.data instanceof Uint8ClampedArray&&"height"in a&&"width"in a)return"RGBA";if(x(a))for(i in c)for(g=c[i],e=0;e<g.length;e+=1){for(h=!0,f=0;f<g[e].length;f+=1)if(void 0!==g[e][f]&&g[e][f]!==a[f]){h=!1;break}if(!0===h){j=i;break}}else for(i in c)for(g=c[i],e=0;e<g.length;e+=1){for(h=!0,f=0;f<g[e].length;f+=1)if(void 0!==g[e][f]&&g[e][f]!==a.charCodeAt(f)){h=!1;break}if(!0===h){j=i;break}}return j===b&&d!==b&&(j=d),j},f=function a(b){for(var c=this.internal.write,d=this.internal.putStream,e=(0,this.internal.getFilters)();-1!==e.indexOf("FlateEncode");)e.splice(e.indexOf("FlateEncode"),1);b.objectId=this.internal.newObject();var f=[];if(f.push({key:"Type",value:"/XObject"}),f.push({key:"Subtype",value:"/Image"}),f.push({key:"Width",value:b.width}),f.push({key:"Height",value:b.height}),b.colorSpace===r.INDEXED?f.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(b.palette.length/3-1)+" "+("sMask"in b&&void 0!==b.sMask?b.objectId+2:b.objectId+1)+" 0 R]"}):(f.push({key:"ColorSpace",value:"/"+b.colorSpace}),b.colorSpace===r.DEVICE_CMYK&&f.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),f.push({key:"BitsPerComponent",value:b.bitsPerComponent}),"decodeParameters"in b&&void 0!==b.decodeParameters&&f.push({key:"DecodeParms",value:"<<"+b.decodeParameters+">>"}),"transparency"in b&&Array.isArray(b.transparency)){for(var g="",h=0,i=b.transparency.length;h<i;h++)g+=b.transparency[h]+" "+b.transparency[h]+" ";f.push({key:"Mask",value:"["+g+"]"})}void 0!==b.sMask&&f.push({key:"SMask",value:b.objectId+1+" 0 R"});var j=void 0!==b.filter?["/"+b.filter]:void 0;if(d({data:b.data,additionalKeyValues:f,alreadyAppliedFilters:j,objectId:b.objectId}),c("endobj"),"sMask"in b&&void 0!==b.sMask){var k="/Predictor "+b.predictor+" /Colors 1 /BitsPerComponent "+b.bitsPerComponent+" /Columns "+b.width,l={width:b.width,height:b.height,colorSpace:"DeviceGray",bitsPerComponent:b.bitsPerComponent,decodeParameters:k,data:b.sMask};"filter"in b&&(l.filter=b.filter),a.call(this,l)}if(b.colorSpace===r.INDEXED){var m=this.internal.newObject();d({data:z(new Uint8Array(b.palette)),objectId:m}),c("endobj")}},g=function(){var a=this.internal.collections.addImage_images;for(var b in a)f.call(this,a[b])},h=function(){var a,b=this.internal.collections.addImage_images,c=this.internal.write;for(var d in b)c("/I"+(a=b[d]).index,a.objectId,"0","R")},i=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",g),this.internal.events.subscribe("putXobjectDict",h))},j=function(){var a=this.internal.collections.addImage_images;return i.call(this),a},k=function(){return Object.keys(this.internal.collections.addImage_images).length},l=function(b){return"function"==typeof a["process"+b.toUpperCase()]},m=function(a){return"object"===e()(a)&&1===a.nodeType},n=function(b,c){if("IMG"===b.nodeName&&b.hasAttribute("src")){var d,e=""+b.getAttribute("src");if(0===e.indexOf("data:image/"))return ag(unescape(e).split("base64,").pop());var f=a.loadFile(e,!0);if(void 0!==f)return f}if("CANVAS"===b.nodeName){if(0===b.width||0===b.height)throw Error("Given canvas must have data. Canvas width: "+b.width+", height: "+b.height);switch(c){case"PNG":d="image/png";break;case"WEBP":d="image/webp";break;default:d="image/jpeg"}return ag(b.toDataURL(d,1).split("base64,").pop())}},o=function(a){var b=this.internal.collections.addImage_images;if(b){for(var c in b)if(a===b[c].alias)return b[c]}},p=function(a,b,c){return a||b||(a=-96,b=-96),a<0&&(a=-1*c.width*72/a/this.internal.scaleFactor),b<0&&(b=-1*c.height*72/b/this.internal.scaleFactor),0===a&&(a=b*c.width/c.height),0===b&&(b=a*c.height/c.width),[a,b]},q=function(a,b,c,d,e,f){var g=p.call(this,c,d,e),h=this.internal.getCoordinateString,i=this.internal.getVerticalCoordinateString,k=j.call(this);if(c=g[0],d=g[1],k[e.index]=e,f)var l=Math.cos(f*=Math.PI/180),m=Math.sin(f),n=function(a){return a.toFixed(4)},o=[n(l),n(m),n(-1*m),n(l),0,0,"cm"];this.internal.write("q"),f?(this.internal.write(["1 0 0 1",h(a),i(b+d),"cm"].join(" ")),this.internal.write(o.join(" ")),this.internal.write([h(c),"0 0",h(d),"0 0 cm"].join(" "))):this.internal.write([h(c),"0 0",h(d),h(a),i(b+d),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write("1 0 0 -1 0 0 cm"),this.internal.write("/I"+e.index+" Do"),this.internal.write("Q")},r=a.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};a.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var s=a.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},t=a.__addimage__.sHashCode=function(a){var b,c,d=0;if("string"==typeof a)for(c=a.length,b=0;b<c;b++)d=(d<<5)-d+a.charCodeAt(b)|0;else if(x(a))for(c=a.byteLength/2,b=0;b<c;b++)d=(d<<5)-d+a[b]|0;return d},u=a.__addimage__.validateStringAsBase64=function(a){(a=a||"").toString().trim();var b=!0;return 0===a.length&&(b=!1),a.length%4!=0&&(b=!1),!1===/^[A-Za-z0-9+/]+$/.test(a.substr(0,a.length-2))&&(b=!1),!1===/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(a.substr(-2))&&(b=!1),b},v=a.__addimage__.extractImageFromDataUrl=function(a){if(null==a||!(a=a.trim()).startsWith("data:"))return null;var b=a.indexOf(",");return b<0?null:a.substring(0,b).trim().endsWith("base64")?a.substring(b+1):null},w=a.__addimage__.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array};a.__addimage__.isArrayBuffer=function(a){return w()&&a instanceof ArrayBuffer};var x=a.__addimage__.isArrayBufferView=function(a){return w()&&"undefined"!=typeof Uint32Array&&(a instanceof Int8Array||a instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&a instanceof Uint8ClampedArray||a instanceof Int16Array||a instanceof Uint16Array||a instanceof Int32Array||a instanceof Uint32Array||a instanceof Float32Array||a instanceof Float64Array)},y=a.__addimage__.binaryStringToUint8Array=function(a){for(var b=a.length,c=new Uint8Array(b),d=0;d<b;d++)c[d]=a.charCodeAt(d);return c},z=a.__addimage__.arrayBufferToBinaryString=function(a){for(var b="",c=x(a)?a:new Uint8Array(a),d=0;d<c.length;d+=8192)b+=String.fromCharCode.apply(null,c.subarray(d,d+8192));return b};a.addImage=function(){if("number"==typeof arguments[1]?(c=b,d=arguments[1],f=arguments[2],g=arguments[3],h=arguments[4],j=arguments[5],k=arguments[6],l=arguments[7]):(c=arguments[1],d=arguments[2],f=arguments[3],g=arguments[4],h=arguments[5],j=arguments[6],k=arguments[7],l=arguments[8]),"object"===e()(a=arguments[0])&&!m(a)&&"imageData"in a){var a,c,d,f,g,h,j,k,l,n=a;a=n.imageData,c=n.format||c||b,d=n.x||d||0,f=n.y||f||0,g=n.w||n.width||g,h=n.h||n.height||h,j=n.alias||j,k=n.compression||k,l=n.rotation||n.angle||l}var o=this.internal.getFilters();if(void 0===k&&-1!==o.indexOf("FlateEncode")&&(k="SLOW"),isNaN(d)||isNaN(f))throw Error("Invalid coordinates passed to jsPDF.addImage");i.call(this);var p=A.call(this,a,c,j,k);return q.call(this,d,f,g,h,p,l),this};var A=function(c,e,f,g){if("string"==typeof c&&d(c)===b){var h,i,j,p,q,r=B(c=unescape(c),!1);(""!==r||void 0!==(r=a.loadFile(c,!0)))&&(c=r)}if(m(c)&&(c=n(c,e)),!l(e=d(c,e)))throw Error("addImage does not support files of type '"+e+"', please ensure that a plugin for '"+e+"' support is added.");if((null==(j=f)||0===j.length)&&(f="string"==typeof(p=c)||x(p)?t(p):x(p.data)?t(p.data):null),(h=o.call(this,f))||(w()&&(c instanceof Uint8Array||"RGBA"===e||(i=c,c=y(c))),h=this["process"+e.toUpperCase()](c,k.call(this),f,((q=g)&&"string"==typeof q&&(q=q.toUpperCase()),q in a.image_compression?q:s.NONE),i)),!h)throw Error("An unknown error occurred whilst processing the image.");return h},B=a.__addimage__.convertBase64ToBinaryString=function(a,b){b="boolean"!=typeof b||b;var c,d,e="";if("string"==typeof a){d=null!=(c=v(a))?c:a;try{e=ag(d)}catch(a){if(b)throw u(d)?Error("atob-Error in jsPDF.convertBase64ToBinaryString "+a.message):Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return e};a.getImageProperties=function(c){var e,f,g="";if(m(c)&&(c=n(c)),"string"==typeof c&&d(c)===b&&(""===(g=B(c,!1))&&(g=a.loadFile(c)||""),c=g),!l(f=d(c)))throw Error("addImage does not support files of type '"+f+"', please ensure that a plugin for '"+f+"' support is added.");if(!w()||c instanceof Uint8Array||(c=y(c)),!(e=this["process"+f.toUpperCase()](c)))throw Error("An unknown error occurred whilst processing the image");return e.fileType=f,e}}(aF.API),function(a){var b=function(a){if(void 0!==a&&""!=a)return!0};aF.API.events.push(["addPage",function(a){this.internal.getPageInfo(a.pageNumber).pageContext.annotations=[]}]),a.events.push(["putPage",function(a){for(var c,d,e,f=this.internal.getCoordinateString,g=this.internal.getVerticalCoordinateString,h=this.internal.getPageInfoByObjId(a.objId),i=a.pageContext.annotations,j=!1,k=0;k<i.length&&!j;k++)switch((c=i[k]).type){case"link":(b(c.options.url)||b(c.options.pageNumber))&&(j=!0);break;case"reference":case"text":case"freetext":j=!0}if(0!=j){this.internal.write("/Annots [");for(var l=0;l<i.length;l++){c=i[l];var m=this.internal.pdfEscape,n=this.internal.getEncryptor(a.objId);switch(c.type){case"reference":this.internal.write(" "+c.object.objId+" 0 R ");break;case"text":var o=this.internal.newAdditionalObject(),p=this.internal.newAdditionalObject(),q=this.internal.getEncryptor(o.objId),r=c.title||"Note";o.content=e="<</Type /Annot /Subtype /Text "+(d="/Rect ["+f(c.bounds.x)+" "+g(c.bounds.y+c.bounds.h)+" "+f(c.bounds.x+c.bounds.w)+" "+g(c.bounds.y)+"] ")+"/Contents ("+m(q(c.contents))+")"+(" /Popup "+p.objId)+" 0 R"+(" /P "+h.objId)+" 0 R"+(" /T ("+m(q(r)))+") >>";var s=o.objId+" 0 R";e="<</Type /Annot /Subtype /Popup "+(d="/Rect ["+f(c.bounds.x+30)+" "+g(c.bounds.y+c.bounds.h)+" "+f(c.bounds.x+c.bounds.w+30)+" "+g(c.bounds.y)+"] ")+" /Parent "+s,c.open&&(e+=" /Open true"),p.content=e+=" >>",this.internal.write(o.objId,"0 R",p.objId,"0 R");break;case"freetext":d="/Rect ["+f(c.bounds.x)+" "+g(c.bounds.y)+" "+f(c.bounds.x+c.bounds.w)+" "+g(c.bounds.y+c.bounds.h)+"] ";var t=c.color||"#000000";e="<</Type /Annot /Subtype /FreeText "+d+"/Contents ("+m(n(c.contents))+")"+(" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+t)+") /Border [0 0 0] >>",this.internal.write(e);break;case"link":if(c.options.name){var u=this.annotations._nameMap[c.options.name];c.options.pageNumber=u.page,c.options.top=u.y}else c.options.top||(c.options.top=0);if(d="/Rect ["+c.finalBounds.x+" "+c.finalBounds.y+" "+c.finalBounds.w+" "+c.finalBounds.h+"] ",e="",c.options.url)e="<</Type /Annot /Subtype /Link "+d+"/Border [0 0 0] /A <</S /URI /URI ("+m(n(c.options.url))+") >>";else if(c.options.pageNumber)switch(e="<</Type /Annot /Subtype /Link "+d+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(c.options.pageNumber).objId+" 0 R",c.options.magFactor=c.options.magFactor||"XYZ",c.options.magFactor){case"Fit":e+=" /Fit]";break;case"FitH":e+=" /FitH "+c.options.top+"]";break;case"FitV":c.options.left=c.options.left||0,e+=" /FitV "+c.options.left+"]";break;default:var v=g(c.options.top);c.options.left=c.options.left||0,void 0===c.options.zoom&&(c.options.zoom=0),e+=" /XYZ "+c.options.left+" "+v+" "+c.options.zoom+"]"}""!=e&&(e+=" >>",this.internal.write(e))}}this.internal.write("]")}}]),a.createAnnotation=function(a){var b=this.internal.getCurrentPageInfo();switch(a.type){case"link":this.link(a.bounds.x,a.bounds.y,a.bounds.w,a.bounds.h,a);break;case"text":case"freetext":b.pageContext.annotations.push(a)}},a.link=function(a,b,c,d,e){var f=this.internal.getCurrentPageInfo(),g=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString;f.pageContext.annotations.push({finalBounds:{x:g(a),y:h(b),w:g(a+c),h:h(b+d)},options:e,type:"link"})},a.textWithLink=function(a,b,c,d){var e,f,g=this.getTextWidth(a),h=this.internal.getLineHeight()/this.internal.scaleFactor;return void 0!==d.maxWidth?(f=d.maxWidth,e=Math.ceil(h*this.splitTextToSize(a,f).length)):(f=g,e=h),this.text(a,b,c,d),c+=.2*h,"center"===d.align&&(b-=g/2),"right"===d.align&&(b-=g),this.link(b,c-h,f,e,d),g},a.getTextWidth=function(a){var b=this.internal.getFontSize();return this.getStringUnitWidth(a)*b/this.internal.scaleFactor}}(aF.API),function(a){var b={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},c={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},d={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},e=[1570,1571,1573,1575];a.__arabicParser__={};var f=a.__arabicParser__.isInArabicSubstitutionA=function(a){return void 0!==b[a.charCodeAt(0)]},g=a.__arabicParser__.isArabicLetter=function(a){return"string"==typeof a&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(a)},h=a.__arabicParser__.isArabicEndLetter=function(a){return g(a)&&f(a)&&b[a.charCodeAt(0)].length<=2},i=a.__arabicParser__.isArabicAlfLetter=function(a){return g(a)&&e.indexOf(a.charCodeAt(0))>=0};a.__arabicParser__.arabicLetterHasIsolatedForm=function(a){return g(a)&&f(a)&&b[a.charCodeAt(0)].length>=1};var j=a.__arabicParser__.arabicLetterHasFinalForm=function(a){return g(a)&&f(a)&&b[a.charCodeAt(0)].length>=2};a.__arabicParser__.arabicLetterHasInitialForm=function(a){return g(a)&&f(a)&&b[a.charCodeAt(0)].length>=3};var k=a.__arabicParser__.arabicLetterHasMedialForm=function(a){return g(a)&&f(a)&&4==b[a.charCodeAt(0)].length},l=a.__arabicParser__.resolveLigatures=function(a){var b=0,d=c,e="",f=0;for(b=0;b<a.length;b+=1)void 0!==d[a.charCodeAt(b)]?(f++,"number"==typeof(d=d[a.charCodeAt(b)])&&(e+=String.fromCharCode(d),d=c,f=0),b===a.length-1&&(d=c,e+=a.charAt(b-(f-1)),b-=f-1,f=0)):(d=c,e+=a.charAt(b-f),b-=f,f=0);return e};a.__arabicParser__.isArabicDiacritic=function(a){return void 0!==a&&void 0!==d[a.charCodeAt(0)]};var m=a.__arabicParser__.getCorrectForm=function(a,b,c){return g(a)?!1===f(a)?-1:!j(a)||!g(b)&&!g(c)||!g(c)&&h(b)||h(a)&&!g(b)||h(a)&&i(b)||h(a)&&h(b)?0:k(a)&&g(b)&&!h(b)&&g(c)&&j(c)?3:h(a)||!g(c)?1:2:-1},n=function(a){var c=0,d=0,e=0,f="",h="",i="",j=(a=a||"").split("\\s+"),k=[];for(c=0;c<j.length;c+=1){for(k.push(""),d=0;d<j[c].length;d+=1)f=j[c][d],h=j[c][d-1],i=j[c][d+1],g(f)?(e=m(f,h,i),k[c]+=-1!==e?String.fromCharCode(b[f.charCodeAt(0)][e]):f):k[c]+=f;k[c]=l(k[c])}return k.join(" ")},o=a.__arabicParser__.processArabic=a.processArabic=function(){var a,b="string"==typeof arguments[0]?arguments[0]:arguments[0].text,c=[];if(Array.isArray(b)){var d=0;for(c=[],d=0;d<b.length;d+=1)Array.isArray(b[d])?c.push([n(b[d][0]),b[d][1],b[d][2]]):c.push([n(b[d])]);a=c}else a=n(b);return"string"==typeof arguments[0]?a:(arguments[0].text=a,arguments[0])};a.events.push(["preProcessText",o])}(aF.API),aF.API.autoPrint=function(a){var b;return((a=a||{}).variant=a.variant||"non-conform","javascript"===a.variant)?this.addJS("print({});"):(this.internal.events.subscribe("postPutResources",function(){b=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+b+" 0 R")})),this},function(a){var b=function(){var a=void 0;Object.defineProperty(this,"pdf",{get:function(){return a},set:function(b){a=b}});var b=150;Object.defineProperty(this,"width",{get:function(){return b},set:function(a){b=isNaN(a)||!1===Number.isInteger(a)||a<0?150:a,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=b+1)}});var c=300;Object.defineProperty(this,"height",{get:function(){return c},set:function(a){c=isNaN(a)||!1===Number.isInteger(a)||a<0?300:a,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=c+1)}});var d=[];Object.defineProperty(this,"childNodes",{get:function(){return d},set:function(a){d=a}});var e={};Object.defineProperty(this,"style",{get:function(){return e},set:function(a){e=a}}),Object.defineProperty(this,"parentNode",{})};b.prototype.getContext=function(a,b){var c;if("2d"!==(a=a||"2d"))return null;for(c in b)this.pdf.context2d.hasOwnProperty(c)&&(this.pdf.context2d[c]=b[c]);return this.pdf.context2d._canvas=this,this.pdf.context2d},b.prototype.toDataURL=function(){throw Error("toDataURL is not implemented.")},a.events.push(["initialized",function(){this.canvas=new b,this.canvas.pdf=this}])}(aF.API),function(a){var b={left:0,top:0,bottom:0,right:0},c=!1,d=function(){void 0===this.internal.__cell__&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},b),this.internal.__cell__.margins.width=this.getPageWidth(),f.call(this))},f=function(){this.internal.__cell__.lastCell=new g,this.internal.__cell__.pages=1},g=function(){var a=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return a},set:function(b){a=b}});var b=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return b},set:function(a){b=a}});var c=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return c},set:function(a){c=a}});var d=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return d},set:function(a){d=a}});var e=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return e},set:function(a){e=a}});var f=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return f},set:function(a){f=a}});var g=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return g},set:function(a){g=a}}),this};g.prototype.clone=function(){return new g(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},g.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},a.setHeaderFunction=function(a){return d.call(this),this.internal.__cell__.headerFunction="function"==typeof a?a:void 0,this},a.getTextDimensions=function(a,b){d.call(this);var c=(b=b||{}).fontSize||this.getFontSize(),e=b.font||this.getFont(),f=b.scaleFactor||this.internal.scaleFactor,g=0,h=0,i=0,j=this;if(!Array.isArray(a)&&"string"!=typeof a){if("number"!=typeof a)throw Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");a=String(a)}var k=b.maxWidth;k>0?"string"==typeof a?a=this.splitTextToSize(a,k):"[object Array]"===Object.prototype.toString.call(a)&&(a=a.reduce(function(a,b){return a.concat(j.splitTextToSize(b,k))},[])):a=Array.isArray(a)?a:[a];for(var l=0;l<a.length;l++)g<(i=this.getStringUnitWidth(a[l],{font:e})*c)&&(g=i);return 0!==g&&(h=a.length),{w:g/=f,h:Math.max((h*c*this.getLineHeightFactor()-c*(this.getLineHeightFactor()-1))/f,0)}},a.cellAddPage=function(){d.call(this),this.addPage();var a=this.internal.__cell__.margins||b;return this.internal.__cell__.lastCell=new g(a.left,a.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var h=a.cell=function(){a=arguments[0]instanceof g?arguments[0]:new g(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),d.call(this);var a,e=this.internal.__cell__.lastCell,f=this.internal.__cell__.padding,h=this.internal.__cell__.margins||b,i=this.internal.__cell__.tableHeaderRow,j=this.internal.__cell__.printHeaders;return void 0!==e.lineNumber&&(e.lineNumber===a.lineNumber?(a.x=(e.x||0)+(e.width||0),a.y=e.y||0):e.y+e.height+a.height+h.bottom>this.getPageHeight()?(this.cellAddPage(),a.y=h.top,j&&i&&(this.printHeaderRow(a.lineNumber,!0),a.y+=i[0].height)):a.y=e.y+e.height||a.y),void 0!==a.text[0]&&(this.rect(a.x,a.y,a.width,a.height,!0===c?"FD":void 0),"right"===a.align?this.text(a.text,a.x+a.width-f,a.y+f,{align:"right",baseline:"top"}):"center"===a.align?this.text(a.text,a.x+a.width/2,a.y+f,{align:"center",baseline:"top",maxWidth:a.width-f-f}):this.text(a.text,a.x+f,a.y+f,{align:"left",baseline:"top",maxWidth:a.width-f-f})),this.internal.__cell__.lastCell=a,this};a.table=function(a,c,j,k,l){if(d.call(this),!j)throw Error("No data for PDF table.");var m,n,o,p,q=[],r=[],s=[],t={},u={},v=[],w=[],x=(l=l||{}).autoSize||!1,y=!1!==l.printHeaders,z=l.css&&void 0!==l.css["font-size"]?16*l.css["font-size"]:l.fontSize||12,A=l.margins||Object.assign({width:this.getPageWidth()},b),B="number"==typeof l.padding?l.padding:3,C=l.headerBackgroundColor||"#c8c8c8",D=l.headerTextColor||"#000";if(f.call(this),this.internal.__cell__.printHeaders=y,this.internal.__cell__.margins=A,this.internal.__cell__.table_font_size=z,this.internal.__cell__.padding=B,this.internal.__cell__.headerBackgroundColor=C,this.internal.__cell__.headerTextColor=D,this.setFontSize(z),null==k)r=q=Object.keys(j[0]),s=q.map(function(){return"left"});else if(Array.isArray(k)&&"object"===e()(k[0]))for(q=k.map(function(a){return a.name}),r=k.map(function(a){return a.prompt||a.name||""}),s=k.map(function(a){return a.align||"left"}),m=0;m<k.length;m+=1)u[k[m].name]=k[m].width*(19.049976/25.4);else Array.isArray(k)&&"string"==typeof k[0]&&(r=q=k,s=q.map(function(){return"left"}));if(x||Array.isArray(k)&&"string"==typeof k[0])for(m=0;m<q.length;m+=1){for(t[p=q[m]]=j.map(function(a){return a[p]}),this.setFont(void 0,"bold"),v.push(this.getTextDimensions(r[m],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),n=t[p],this.setFont(void 0,"normal"),o=0;o<n.length;o+=1)v.push(this.getTextDimensions(n[o],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);u[p]=Math.max.apply(null,v)+B+B,v=[]}if(y){var E={};for(m=0;m<q.length;m+=1)E[q[m]]={},E[q[m]].text=r[m],E[q[m]].align=s[m];var F=i.call(this,E,u);w=q.map(function(b){return new g(a,c,u[b],F,E[b].text,void 0,E[b].align)}),this.setTableHeaderRow(w),this.printHeaderRow(1,!1)}var G=k.reduce(function(a,b){return a[b.name]=b.align,a},{});for(m=0;m<j.length;m+=1){"rowStart"in l&&l.rowStart instanceof Function&&l.rowStart({row:m,data:j[m]},this);var H=i.call(this,j[m],u);for(o=0;o<q.length;o+=1){var I=j[m][q[o]];"cellStart"in l&&l.cellStart instanceof Function&&l.cellStart({row:m,col:o,data:I},this),h.call(this,new g(a,c,u[q[o]],H,I,m+2,G[q[o]]))}}return this.internal.__cell__.table_x=a,this.internal.__cell__.table_y=c,this};var i=function(a,b){var c=this.internal.__cell__.padding,d=this.internal.__cell__.table_font_size,e=this.internal.scaleFactor;return Object.keys(a).map(function(d){var e=a[d];return this.splitTextToSize(e.hasOwnProperty("text")?e.text:e,b[d]-c-c)},this).map(function(a){return this.getLineHeightFactor()*a.length*d/e+c+c},this).reduce(function(a,b){return Math.max(a,b)},0)};a.setTableHeaderRow=function(a){d.call(this),this.internal.__cell__.tableHeaderRow=a},a.printHeaderRow=function(a,b){if(d.call(this),!this.internal.__cell__.tableHeaderRow)throw Error("Property tableHeaderRow does not exist.");if(c=!0,"function"==typeof this.internal.__cell__.headerFunction){var e,f=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new g(f[0],f[1],f[2],f[3],void 0,-1)}this.setFont(void 0,"bold");for(var i=[],j=0;j<this.internal.__cell__.tableHeaderRow.length;j+=1){e=this.internal.__cell__.tableHeaderRow[j].clone(),b&&(e.y=this.internal.__cell__.margins.top||0,i.push(e)),e.lineNumber=a;var k=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),h.call(this,e),this.setTextColor(k)}i.length>0&&this.setTableHeaderRow(i),this.setFont(void 0,"normal"),c=!1}}(aF.API);var bl={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},bm=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],bn=bk(bm),bo=[100,200,300,400,500,600,700,800,900],bp=bk(bo);function bq(a){var b,c,d,e=a.family.replace(/"|'/g,"").toLowerCase(),f=bl[b=(b=a.style)||"normal"]?b:"normal",g=(c=a.weight)?"number"==typeof c?c>=100&&c<=900&&c%100==0?c:400:/^\d00$/.test(c)?parseInt(c):"bold"===c?700:400:400,h="number"==typeof bn[d=(d=a.stretch)||"normal"]?d:"normal";return{family:e,style:f,weight:g,stretch:h,src:a.src||[],ref:a.ref||{name:e,style:[h,f,g].join(" ")}}}function br(a,b,c,d){var e;for(e=c;e>=0&&e<b.length;e+=d)if(a[b[e]])return a[b[e]];for(e=c;e>=0&&e<b.length;e-=d)if(a[b[e]])return a[b[e]]}var bs={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},bt={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function bu(a){return[a.stretch,a.style,a.weight,a.family].join(" ")}function bv(a){return a.trimLeft()}var bw,bx,by,bz=["times"];!function(a){var b,c,d,f,g,h,i,j,k,l=function(a){return a=a||{},this.isStrokeTransparent=a.isStrokeTransparent||!1,this.strokeOpacity=a.strokeOpacity||1,this.strokeStyle=a.strokeStyle||"#000000",this.fillStyle=a.fillStyle||"#000000",this.isFillTransparent=a.isFillTransparent||!1,this.fillOpacity=a.fillOpacity||1,this.font=a.font||"10px sans-serif",this.textBaseline=a.textBaseline||"alphabetic",this.textAlign=a.textAlign||"left",this.lineWidth=a.lineWidth||1,this.lineJoin=a.lineJoin||"miter",this.lineCap=a.lineCap||"butt",this.path=a.path||[],this.transform=void 0!==a.transform?a.transform.clone():new j,this.globalCompositeOperation=a.globalCompositeOperation||"normal",this.globalAlpha=a.globalAlpha||1,this.clip_path=a.clip_path||[],this.currentPoint=a.currentPoint||new h,this.miterLimit=a.miterLimit||10,this.lastPoint=a.lastPoint||new h,this.lineDashOffset=a.lineDashOffset||0,this.lineDash=a.lineDash||[],this.margin=a.margin||[0,0,0,0],this.prevPageLastElemOffset=a.prevPageLastElemOffset||0,this.ignoreClearRect="boolean"!=typeof a.ignoreClearRect||a.ignoreClearRect,this};a.events.push(["initialized",function(){this.context2d=new m(this),b=this.internal.f2,c=this.internal.getCoordinateString,d=this.internal.getVerticalCoordinateString,f=this.internal.getHorizontalCoordinate,g=this.internal.getVerticalCoordinate,h=this.internal.Point,i=this.internal.Rectangle,j=this.internal.Matrix,k=new l}]);var m=function(a){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}}),Object.defineProperty(this,"pdf",{get:function(){return a}});var b=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return b},set:function(a){b=!!a}});var c=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return c},set:function(a){c=!!a}});var d=0;Object.defineProperty(this,"posX",{get:function(){return d},set:function(a){isNaN(a)||(d=a)}});var e=0;Object.defineProperty(this,"posY",{get:function(){return e},set:function(a){isNaN(a)||(e=a)}}),Object.defineProperty(this,"margin",{get:function(){return k.margin},set:function(a){var b;"number"==typeof a?b=[a,a,a,a]:((b=[,,,,])[0]=a[0],b[1]=a.length>=2?a[1]:b[0],b[2]=a.length>=3?a[2]:b[0],b[3]=a.length>=4?a[3]:b[1]),k.margin=b}});var f=!1;Object.defineProperty(this,"autoPaging",{get:function(){return f},set:function(a){f=a}});var g=0;Object.defineProperty(this,"lastBreak",{get:function(){return g},set:function(a){g=a}});var h=[];Object.defineProperty(this,"pageBreaks",{get:function(){return h},set:function(a){h=a}}),Object.defineProperty(this,"ctx",{get:function(){return k},set:function(a){a instanceof l&&(k=a)}}),Object.defineProperty(this,"path",{get:function(){return k.path},set:function(a){k.path=a}});var i=[];Object.defineProperty(this,"ctxStack",{get:function(){return i},set:function(a){i=a}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(a){var b;b=n(a),this.ctx.fillStyle=b.style,this.ctx.isFillTransparent=0===b.a,this.ctx.fillOpacity=b.a,this.pdf.setFillColor(b.r,b.g,b.b,{a:b.a}),this.pdf.setTextColor(b.r,b.g,b.b,{a:b.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(a){var b=n(a);this.ctx.strokeStyle=b.style,this.ctx.isStrokeTransparent=0===b.a,this.ctx.strokeOpacity=b.a,0===b.a?this.pdf.setDrawColor(255,255,255):(b.a,this.pdf.setDrawColor(b.r,b.g,b.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(a){-1!==["butt","round","square"].indexOf(a)&&(this.ctx.lineCap=a,this.pdf.setLineCap(a))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(a){isNaN(a)||(this.ctx.lineWidth=a,this.pdf.setLineWidth(a))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(a){-1!==["bevel","round","miter"].indexOf(a)&&(this.ctx.lineJoin=a,this.pdf.setLineJoin(a))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(a){isNaN(a)||(this.ctx.miterLimit=a,this.pdf.setMiterLimit(a))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(a){this.ctx.textBaseline=a}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(a){-1!==["right","end","center","left","start"].indexOf(a)&&(this.ctx.textAlign=a)}});var j=null,m=null;Object.defineProperty(this,"fontFaces",{get:function(){return m},set:function(a){j=null,m=a}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(a){var b;if(this.ctx.font=a,null!==(b=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(a))){var c=b[1];b[2];var d=b[3],e=b[4];b[5];var f=b[6],g=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(e)[2];e="px"===g?Math.floor(parseFloat(e)*this.pdf.internal.scaleFactor):"em"===g?Math.floor(parseFloat(e)*this.pdf.getFontSize()):Math.floor(parseFloat(e)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(e);var h=function(a){var b,c,d=[],e=a.trim();if(""===e)return bz;if(e in bt)return[bt[e]];for(;""!==e;){switch(c=null,b=(e=bv(e)).charAt(0)){case'"':case"'":c=function(a,b){for(var c=0;c<a.length;){if(a.charAt(c)===b)return[a.substring(0,c),a.substring(c+1)];c+=1}return null}(e.substring(1),b);break;default:c=function(a){var b=a.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return null===b?null:[b[0],a.substring(b[0].length)]}(e)}if(null===c||(d.push(c[0]),""!==(e=bv(c[1]))&&","!==e.charAt(0)))return bz;e=e.replace(/^,/,"")}return d}(f);if(this.fontFaces){var i=function(a,b,c){for(var d=(c=c||{}).defaultFontFamily||"times",e=Object.assign({},bs,c.genericFontFamilies||{}),f=null,g=null,h=0;h<b.length;++h)if(e[(f=bq(b[h])).family]&&(f.family=e[f.family]),a.hasOwnProperty(f.family)){g=a[f.family];break}if(!(g=g||a[d]))throw Error("Could not find a font-family for the rule '"+bu(f)+"' and default family '"+d+"'.");if(g=function(a,b){if(b[a])return b[a];var c=bn[a],d=c<=bn.normal?-1:1,e=br(b,bm,c,d);if(!e)throw Error("Could not find a matching font-stretch value for "+a);return e}(f.stretch,g),g=function(a,b){if(b[a])return b[a];for(var c=bl[a],d=0;d<c.length;++d)if(b[c[d]])return b[c[d]];throw Error("Could not find a matching font-style for "+a)}(f.style,g),!(g=function(a,b){if(b[a])return b[a];if(400===a&&b[500])return b[500];if(500===a&&b[400])return b[400];var c=br(b,bo,bp[a],a<400?-1:1);if(!c)throw Error("Could not find a matching font-weight for value "+a);return c}(f.weight,g)))throw Error("Failed to resolve a font for the rule '"+bu(f)+"'.");return g}(function(a,b){if(null===j){var c,d;j=function(a){for(var b={},c=0;c<a.length;++c){var d=bq(a[c]),e=d.family,f=d.stretch,g=d.style,h=d.weight;b[e]=b[e]||{},b[e][f]=b[e][f]||{},b[e][f][g]=b[e][f][g]||{},b[e][f][g][h]=d}return b}((c=a.getFontList(),d=[],Object.keys(c).forEach(function(a){c[a].forEach(function(b){var c=null;switch(b){case"bold":c={family:a,weight:"bold"};break;case"italic":c={family:a,style:"italic"};break;case"bolditalic":c={family:a,weight:"bold",style:"italic"};break;case"":case"normal":c={family:a}}null!==c&&(c.ref={name:a,style:b},d.push(c))})}),d).concat(b))}return j}(this.pdf,this.fontFaces),h.map(function(a){return{family:a,stretch:"normal",weight:d,style:c}}));this.pdf.setFont(i.ref.name,i.ref.style)}else{var k="";("bold"===d||parseInt(d,10)>=700||"bold"===c)&&(k="bold"),"italic"===c&&(k+="italic"),0===k.length&&(k="normal");for(var l="",m={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},n=0;n<h.length;n++){if(void 0!==this.pdf.internal.getFont(h[n],k,{noFallback:!0,disableWarning:!0})){l=h[n];break}if("bolditalic"===k&&void 0!==this.pdf.internal.getFont(h[n],"bold",{noFallback:!0,disableWarning:!0}))l=h[n],k="bold";else if(void 0!==this.pdf.internal.getFont(h[n],"normal",{noFallback:!0,disableWarning:!0})){l=h[n],k="normal";break}}if(""===l){for(var o=0;o<h.length;o++)if(m[h[o]]){l=m[h[o]];break}}l=""===l?"Times":l,this.pdf.setFont(l,k)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(a){this.ctx.globalCompositeOperation=a}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(a){this.ctx.globalAlpha=a}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(a){this.ctx.lineDashOffset=a,M.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(a){this.ctx.lineDash=a,M.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(a){this.ctx.ignoreClearRect=!!a}})};m.prototype.setLineDash=function(a){this.lineDash=a},m.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},m.prototype.fill=function(){v.call(this,"fill",!1)},m.prototype.stroke=function(){v.call(this,"stroke",!1)},m.prototype.beginPath=function(){this.path=[{type:"begin"}]},m.prototype.moveTo=function(a,b){if(isNaN(a)||isNaN(b))throw ac.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.moveTo");var c=this.ctx.transform.applyToPoint(new h(a,b));this.path.push({type:"mt",x:c.x,y:c.y}),this.ctx.lastPoint=new h(a,b)},m.prototype.closePath=function(){var a=new h(0,0),b=0;for(b=this.path.length-1;-1!==b;b--)if("begin"===this.path[b].type&&"object"===e()(this.path[b+1])&&"number"==typeof this.path[b+1].x){a=new h(this.path[b+1].x,this.path[b+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new h(a.x,a.y)},m.prototype.lineTo=function(a,b){if(isNaN(a)||isNaN(b))throw ac.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.lineTo");var c=this.ctx.transform.applyToPoint(new h(a,b));this.path.push({type:"lt",x:c.x,y:c.y}),this.ctx.lastPoint=new h(c.x,c.y)},m.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),v.call(this,null,!0)},m.prototype.quadraticCurveTo=function(a,b,c,d){if(isNaN(c)||isNaN(d)||isNaN(a)||isNaN(b))throw ac.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var e=this.ctx.transform.applyToPoint(new h(c,d)),f=this.ctx.transform.applyToPoint(new h(a,b));this.path.push({type:"qct",x1:f.x,y1:f.y,x:e.x,y:e.y}),this.ctx.lastPoint=new h(e.x,e.y)},m.prototype.bezierCurveTo=function(a,b,c,d,e,f){if(isNaN(e)||isNaN(f)||isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d))throw ac.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var g=this.ctx.transform.applyToPoint(new h(e,f)),i=this.ctx.transform.applyToPoint(new h(a,b)),j=this.ctx.transform.applyToPoint(new h(c,d));this.path.push({type:"bct",x1:i.x,y1:i.y,x2:j.x,y2:j.y,x:g.x,y:g.y}),this.ctx.lastPoint=new h(g.x,g.y)},m.prototype.arc=function(a,b,c,d,e,f){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d)||isNaN(e))throw ac.error("jsPDF.context2d.arc: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.arc");if(f=!!f,!this.ctx.transform.isIdentity){var g=this.ctx.transform.applyToPoint(new h(a,b));a=g.x,b=g.y;var i=this.ctx.transform.applyToPoint(new h(0,c)),j=this.ctx.transform.applyToPoint(new h(0,0));c=Math.sqrt(Math.pow(i.x-j.x,2)+Math.pow(i.y-j.y,2))}Math.abs(e-d)>=2*Math.PI&&(d=0,e=2*Math.PI),this.path.push({type:"arc",x:a,y:b,radius:c,startAngle:d,endAngle:e,counterclockwise:f})},m.prototype.arcTo=function(a,b,c,d,e){throw Error("arcTo not implemented.")},m.prototype.rect=function(a,b,c,d){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d))throw ac.error("jsPDF.context2d.rect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(a,b),this.lineTo(a+c,b),this.lineTo(a+c,b+d),this.lineTo(a,b+d),this.lineTo(a,b),this.lineTo(a+c,b),this.lineTo(a,b)},m.prototype.fillRect=function(a,b,c,d){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d))throw ac.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!o.call(this)){var e={};"butt"!==this.lineCap&&(e.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(e.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(a,b,c,d),this.fill(),e.hasOwnProperty("lineCap")&&(this.lineCap=e.lineCap),e.hasOwnProperty("lineJoin")&&(this.lineJoin=e.lineJoin)}},m.prototype.strokeRect=function(a,b,c,d){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d))throw ac.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.strokeRect");p.call(this)||(this.beginPath(),this.rect(a,b,c,d),this.stroke())},m.prototype.clearRect=function(a,b,c,d){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d))throw ac.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(a,b,c,d))},m.prototype.save=function(a){a="boolean"!=typeof a||a;for(var b=this.pdf.internal.getCurrentPageInfo().pageNumber,c=0;c<this.pdf.internal.getNumberOfPages();c++)this.pdf.setPage(c+1),this.pdf.internal.out("q");if(this.pdf.setPage(b),a){this.ctx.fontSize=this.pdf.internal.getFontSize();var d=new l(this.ctx);this.ctxStack.push(this.ctx),this.ctx=d}},m.prototype.restore=function(a){a="boolean"!=typeof a||a;for(var b=this.pdf.internal.getCurrentPageInfo().pageNumber,c=0;c<this.pdf.internal.getNumberOfPages();c++)this.pdf.setPage(c+1),this.pdf.internal.out("Q");this.pdf.setPage(b),a&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},m.prototype.toDataURL=function(){throw Error("toDataUrl not implemented.")};var n=function(a){var b,c,d,e;if(!0===a.isCanvasGradient&&(a=a.getColor()),!a)return{r:0,g:0,b:0,a:0,style:a};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(a))b=0,c=0,d=0,e=0;else{var f=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(a);if(null!==f)b=parseInt(f[1]),c=parseInt(f[2]),d=parseInt(f[3]),e=1;else if(null!==(f=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(a)))b=parseInt(f[1]),c=parseInt(f[2]),d=parseInt(f[3]),e=parseFloat(f[4]);else{if(e=1,"string"==typeof a&&"#"!==a.charAt(0)){var g=new aj(a);a=g.ok?g.toHex():"#000000"}4===a.length?(b=a.substring(1,2),b+=b,c=a.substring(2,3),c+=c,d=a.substring(3,4),d+=d):(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7)),b=parseInt(b,16),c=parseInt(c,16),d=parseInt(d,16)}}return{r:b,g:c,b:d,a:e,style:a}},o=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},p=function(){return!!(this.ctx.isStrokeTransparent||0==this.globalAlpha)};m.prototype.fillText=function(a,b,c,d){if(isNaN(b)||isNaN(c)||"string"!=typeof a)throw ac.error("jsPDF.context2d.fillText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.fillText");if(d=isNaN(d)?void 0:d,!o.call(this)){var e=J(this.ctx.transform.rotation);D.call(this,{text:a,x:b,y:c,scale:this.ctx.transform.scaleX,angle:e,align:this.textAlign,maxWidth:d})}},m.prototype.strokeText=function(a,b,c,d){if(isNaN(b)||isNaN(c)||"string"!=typeof a)throw ac.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!p.call(this)){d=isNaN(d)?void 0:d;var e=J(this.ctx.transform.rotation);D.call(this,{text:a,x:b,y:c,scale:this.ctx.transform.scaleX,renderingMode:"stroke",angle:e,align:this.textAlign,maxWidth:d})}},m.prototype.measureText=function(a){if("string"!=typeof a)throw ac.error("jsPDF.context2d.measureText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.measureText");var b=this.pdf,c=this.pdf.internal.scaleFactor,d=b.internal.getFontSize(),e=b.getStringUnitWidth(a)*d/b.internal.scaleFactor;return new function(a){var b=(a=a||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return b}}),this}({width:e*=Math.round(96*c/72*1e4)/1e4})},m.prototype.scale=function(a,b){if(isNaN(a)||isNaN(b))throw ac.error("jsPDF.context2d.scale: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.scale");var c=new j(a,0,0,b,0,0);this.ctx.transform=this.ctx.transform.multiply(c)},m.prototype.rotate=function(a){if(isNaN(a))throw ac.error("jsPDF.context2d.rotate: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.rotate");var b=new j(Math.cos(a),Math.sin(a),-Math.sin(a),Math.cos(a),0,0);this.ctx.transform=this.ctx.transform.multiply(b)},m.prototype.translate=function(a,b){if(isNaN(a)||isNaN(b))throw ac.error("jsPDF.context2d.translate: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.translate");var c=new j(1,0,0,1,a,b);this.ctx.transform=this.ctx.transform.multiply(c)},m.prototype.transform=function(a,b,c,d,e,f){if(isNaN(a)||isNaN(b)||isNaN(c)||isNaN(d)||isNaN(e)||isNaN(f))throw ac.error("jsPDF.context2d.transform: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.transform");var g=new j(a,b,c,d,e,f);this.ctx.transform=this.ctx.transform.multiply(g)},m.prototype.setTransform=function(a,b,c,d,e,f){a=isNaN(a)?1:a,b=isNaN(b)?0:b,c=isNaN(c)?0:c,d=isNaN(d)?1:d,e=isNaN(e)?0:e,f=isNaN(f)?0:f,this.ctx.transform=new j(a,b,c,d,e,f)};var q=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};m.prototype.drawImage=function(a,b,c,d,e,f,g,h,k){var l=this.pdf.getImageProperties(a),m=1,n=1,o=1,p=1;void 0!==d&&void 0!==h&&(o=h/d,p=k/e,m=l.width/d*h/d,n=l.height/e*k/e),void 0===f&&(f=b,g=c,b=0,c=0),void 0!==d&&void 0===h&&(h=d,k=e),void 0===d&&void 0===h&&(h=l.width,k=l.height);for(var s,v=this.ctx.transform.decompose(),x=J(v.rotate.shx),y=new j,z=(y=(y=(y=y.multiply(v.translate)).multiply(v.skew)).multiply(v.scale)).applyToRectangle(new i(f-b*o,g-c*p,d*m,e*n)),A=r.call(this,z),B=[],C=0;C<A.length;C+=1)-1===B.indexOf(A[C])&&B.push(A[C]);if(u(B),this.autoPaging)for(var D=B[0],E=B[B.length-1],F=D;F<E+1;F++){this.pdf.setPage(F);var G=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],H=1===F?this.posY+this.margin[0]:this.margin[0],I=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],K=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],L=1===F?0:I+(F-2)*K;if(0!==this.ctx.clip_path.length){var M=this.path;s=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=t(s,this.posX+this.margin[3],-L+H+this.ctx.prevPageLastElemOffset),w.call(this,"fill",!0),this.path=M}var N=JSON.parse(JSON.stringify(z));N=t([N],this.posX+this.margin[3],-L+H+this.ctx.prevPageLastElemOffset)[0];var O=(F>D||F<E)&&q.call(this);O&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],G,K,null).clip().discardPath()),this.pdf.addImage(a,"JPEG",N.x,N.y,N.w,N.h,null,null,x),O&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(a,"JPEG",z.x,z.y,z.w,z.h,null,null,x)};var r=function(a,b,c){var d=[];b=b||this.pdf.internal.pageSize.width,c=c||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var e=this.posY+this.ctx.prevPageLastElemOffset;switch(a.type){default:case"mt":case"lt":d.push(Math.floor((a.y+e)/c)+1);break;case"arc":d.push(Math.floor((a.y+e-a.radius)/c)+1),d.push(Math.floor((a.y+e+a.radius)/c)+1);break;case"qct":var f=K(this.ctx.lastPoint.x,this.ctx.lastPoint.y,a.x1,a.y1,a.x,a.y);d.push(Math.floor((f.y+e)/c)+1),d.push(Math.floor((f.y+f.h+e)/c)+1);break;case"bct":var g=L(this.ctx.lastPoint.x,this.ctx.lastPoint.y,a.x1,a.y1,a.x2,a.y2,a.x,a.y);d.push(Math.floor((g.y+e)/c)+1),d.push(Math.floor((g.y+g.h+e)/c)+1);break;case"rect":d.push(Math.floor((a.y+e)/c)+1),d.push(Math.floor((a.y+a.h+e)/c)+1)}for(var h=0;h<d.length;h+=1)for(;this.pdf.internal.getNumberOfPages()<d[h];)s.call(this);return d},s=function(){var a=this.fillStyle,b=this.strokeStyle,c=this.font,d=this.lineCap,e=this.lineWidth,f=this.lineJoin;this.pdf.addPage(),this.fillStyle=a,this.strokeStyle=b,this.font=c,this.lineCap=d,this.lineWidth=e,this.lineJoin=f},t=function(a,b,c){for(var d=0;d<a.length;d++)switch(a[d].type){case"bct":a[d].x2+=b,a[d].y2+=c;case"qct":a[d].x1+=b,a[d].y1+=c;default:a[d].x+=b,a[d].y+=c}return a},u=function(a){return a.sort(function(a,b){return a-b})},v=function(a,b){for(var c,d,e=this.fillStyle,f=this.strokeStyle,g=this.lineCap,h=this.lineWidth,i=Math.abs(h*this.ctx.transform.scaleX),j=this.lineJoin,k=JSON.parse(JSON.stringify(this.path)),l=JSON.parse(JSON.stringify(this.path)),m=[],n=0;n<l.length;n++)if(void 0!==l[n].x)for(var o=r.call(this,l[n]),p=0;p<o.length;p+=1)-1===m.indexOf(o[p])&&m.push(o[p]);for(var v=0;v<m.length;v++)for(;this.pdf.internal.getNumberOfPages()<m[v];)s.call(this);if(u(m),this.autoPaging)for(var x=m[0],y=m[m.length-1],z=x;z<y+1;z++){this.pdf.setPage(z),this.fillStyle=e,this.strokeStyle=f,this.lineCap=g,this.lineWidth=i,this.lineJoin=j;var A=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],B=1===z?this.posY+this.margin[0]:this.margin[0],C=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],D=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],E=1===z?0:C+(z-2)*D;if(0!==this.ctx.clip_path.length){var F=this.path;c=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=t(c,this.posX+this.margin[3],-E+B+this.ctx.prevPageLastElemOffset),w.call(this,a,!0),this.path=F}if(d=JSON.parse(JSON.stringify(k)),this.path=t(d,this.posX+this.margin[3],-E+B+this.ctx.prevPageLastElemOffset),!1===b||0===z){var G=(z>x||z<y)&&q.call(this);G&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],A,D,null).clip().discardPath()),w.call(this,a,b),G&&this.pdf.restoreGraphicsState()}this.lineWidth=h}else this.lineWidth=i,w.call(this,a,b),this.lineWidth=h;this.path=k},w=function(a,b){if(("stroke"!==a||b||!p.call(this))&&("stroke"===a||b||!o.call(this))){for(var c,d,e=[],f=this.path,g=0;g<f.length;g++){var h=f[g];switch(h.type){case"begin":e.push({begin:!0});break;case"close":e.push({close:!0});break;case"mt":e.push({start:h,deltas:[],abs:[]});break;case"lt":var i=e.length;if(f[g-1]&&!isNaN(f[g-1].x)&&(c=[h.x-f[g-1].x,h.y-f[g-1].y],i>0)){for(;i>=0;i--)if(!0!==e[i-1].close&&!0!==e[i-1].begin){e[i-1].deltas.push(c),e[i-1].abs.push(h);break}}break;case"bct":c=[h.x1-f[g-1].x,h.y1-f[g-1].y,h.x2-f[g-1].x,h.y2-f[g-1].y,h.x-f[g-1].x,h.y-f[g-1].y],e[e.length-1].deltas.push(c);break;case"qct":var j=f[g-1].x+2/3*(h.x1-f[g-1].x),k=f[g-1].y+2/3*(h.y1-f[g-1].y),l=h.x+2/3*(h.x1-h.x),m=h.y+2/3*(h.y1-h.y),n=h.x,q=h.y;c=[j-f[g-1].x,k-f[g-1].y,l-f[g-1].x,m-f[g-1].y,n-f[g-1].x,q-f[g-1].y],e[e.length-1].deltas.push(c);break;case"arc":e.push({deltas:[],abs:[],arc:!0}),Array.isArray(e[e.length-1].abs)&&e[e.length-1].abs.push(h)}}d=b?null:"stroke"===a?"stroke":"fill";for(var r=!1,s=0;s<e.length;s++)if(e[s].arc)for(var t=e[s].abs,u=0;u<t.length;u++){var v=t[u];"arc"===v.type?z.call(this,v.x,v.y,v.radius,v.startAngle,v.endAngle,v.counterclockwise,void 0,b,!r):E.call(this,v.x,v.y),r=!0}else if(!0===e[s].close)this.pdf.internal.out("h"),r=!1;else if(!0!==e[s].begin){var w=e[s].start.x,x=e[s].start.y;F.call(this,e[s].deltas,w,x),r=!0}d&&A.call(this,d),b&&B.call(this)}},x=function(a){var b=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,c=b*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return a-c;case"top":return a+b-c;case"hanging":return a+b-2*c;case"middle":return a+b/2-c;default:return a}},y=function(a){return a+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};m.prototype.createLinearGradient=function(){var a=function(){};return a.colorStops=[],a.addColorStop=function(a,b){this.colorStops.push([a,b])},a.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},a.isCanvasGradient=!0,a},m.prototype.createPattern=function(){return this.createLinearGradient()},m.prototype.createRadialGradient=function(){return this.createLinearGradient()};var z=function(a,b,c,d,e,f,g,h,i){for(var j=H.call(this,c,d,e,f),k=0;k<j.length;k++){var l=j[k];0===k&&(i?C.call(this,l.x1+a,l.y1+b):E.call(this,l.x1+a,l.y1+b)),G.call(this,a,b,l.x2,l.y2,l.x3,l.y3,l.x4,l.y4)}h?B.call(this):A.call(this,g)},A=function(a){switch(a){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},B=function(){this.pdf.clip(),this.pdf.discardPath()},C=function(a,b){this.pdf.internal.out(c(a)+" "+d(b)+" m")},D=function(a){switch(a.align){case"right":case"end":k="right";break;case"center":k="center";break;default:k="left"}var b=this.pdf.getTextDimensions(a.text),c=x.call(this,a.y),d=y.call(this,c)-b.h,e=this.ctx.transform.applyToPoint(new h(a.x,c)),f=this.ctx.transform.decompose(),g=new j;g=(g=(g=g.multiply(f.translate)).multiply(f.skew)).multiply(f.scale);for(var k,l,m,n,o=this.ctx.transform.applyToRectangle(new i(a.x,c,b.w,b.h)),p=g.applyToRectangle(new i(a.x,d,b.w,b.h)),s=r.call(this,p),v=[],z=0;z<s.length;z+=1)-1===v.indexOf(s[z])&&v.push(s[z]);if(u(v),this.autoPaging)for(var A=v[0],B=v[v.length-1],C=A;C<B+1;C++){this.pdf.setPage(C);var D=1===C?this.posY+this.margin[0]:this.margin[0],E=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],F=this.pdf.internal.pageSize.height-this.margin[2],G=F-this.margin[0],H=this.pdf.internal.pageSize.width-this.margin[1],I=H-this.margin[3],J=1===C?0:E+(C-2)*G;if(0!==this.ctx.clip_path.length){var K=this.path;l=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=t(l,this.posX+this.margin[3],-1*J+D),w.call(this,"fill",!0),this.path=K}var L=t([JSON.parse(JSON.stringify(p))],this.posX+this.margin[3],-J+D+this.ctx.prevPageLastElemOffset)[0];a.scale>=.01&&(m=this.pdf.internal.getFontSize(),this.pdf.setFontSize(m*a.scale),n=this.lineWidth,this.lineWidth=n*a.scale);var M="text"!==this.autoPaging;if(M||L.y+L.h<=F){if(M||L.y>=D&&L.x<=H){var N=M?a.text:this.pdf.splitTextToSize(a.text,a.maxWidth||H-L.x)[0],O=t([JSON.parse(JSON.stringify(o))],this.posX+this.margin[3],-J+D+this.ctx.prevPageLastElemOffset)[0],P=M&&(C>A||C<B)&&q.call(this);P&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],I,G,null).clip().discardPath()),this.pdf.text(N,O.x,O.y,{angle:a.angle,align:k,renderingMode:a.renderingMode}),P&&this.pdf.restoreGraphicsState()}}else L.y<F&&(this.ctx.prevPageLastElemOffset+=F-L.y);a.scale>=.01&&(this.pdf.setFontSize(m),this.lineWidth=n)}else a.scale>=.01&&(m=this.pdf.internal.getFontSize(),this.pdf.setFontSize(m*a.scale),n=this.lineWidth,this.lineWidth=n*a.scale),this.pdf.text(a.text,e.x+this.posX,e.y+this.posY,{angle:a.angle,align:k,renderingMode:a.renderingMode,maxWidth:a.maxWidth}),a.scale>=.01&&(this.pdf.setFontSize(m),this.lineWidth=n)},E=function(a,b,e,f){e=e||0,f=f||0,this.pdf.internal.out(c(a+e)+" "+d(b+f)+" l")},F=function(a,b,c){return this.pdf.lines(a,b,c,null,null)},G=function(a,c,d,e,h,i,j,k){this.pdf.internal.out([b(f(d+a)),b(g(e+c)),b(f(h+a)),b(g(i+c)),b(f(j+a)),b(g(k+c)),"c"].join(" "))},H=function(a,b,c,d){for(var e=2*Math.PI,f=Math.PI/2;b>c;)b-=e;var g=Math.abs(c-b);g<e&&d&&(g=e-g);for(var h=[],i=d?-1:1,j=b;g>1e-5;){var k=j+i*Math.min(g,f);h.push(I.call(this,a,j,k)),g-=Math.abs(k-j),j=k}return h},I=function(a,b,c){var d=(c-b)/2,e=a*Math.cos(d),f=a*Math.sin(d),g=-f,h=e*e+g*g,i=h+e*e+g*f,j=4/3*(Math.sqrt(2*h*i)-i)/(e*f-g*e),k=e-j*g,l=g+j*e,m=-l,n=d+b,o=Math.cos(n),p=Math.sin(n);return{x1:a*Math.cos(b),y1:a*Math.sin(b),x2:k*o-l*p,y2:k*p+l*o,x3:k*o-m*p,y3:k*p+m*o,x4:a*Math.cos(c),y4:a*Math.sin(c)}},J=function(a){return 180*a/Math.PI},K=function(a,b,c,d,e,f){var g=a+.5*(c-a),h=b+.5*(d-b),j=e+.5*(c-e),k=f+.5*(d-f),l=Math.min(a,e,g,j),m=Math.min(b,f,h,k);return new i(l,m,Math.max(a,e,g,j)-l,Math.max(b,f,h,k)-m)},L=function(a,b,c,d,e,f,g,h){var j,k,l,m,n,o,p,q,r,s,t,u,v,w,x=c-a,y=d-b,z=e-c,A=f-d,B=g-e,C=h-f;for(k=0;k<41;k++)r=(p=(l=a+(j=k/40)*x)+j*((n=c+j*z)-l))+j*(n+j*(e+j*B-n)-p),s=(q=(m=b+j*y)+j*((o=d+j*A)-m))+j*(o+j*(f+j*C-o)-q),0==k?(t=r,u=s,v=r,w=s):(t=Math.min(t,r),u=Math.min(u,s),v=Math.max(v,r),w=Math.max(w,s));return new i(Math.round(t),Math.round(u),Math.round(v-t),Math.round(w-u))},M=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var a=JSON.stringify({lineDash:this.ctx.lineDash,lineDashOffset:this.ctx.lineDashOffset});this.prevLineDash!==a&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=a)}}}(aF.API),function(a){var b=function(a){var b,c,d,e,f,g,h,i,j,k;for(/[^\x00-\xFF]/.test(a),c=[],d=0,e=(a+=b="\0\0\0\0".slice(a.length%4||4)).length;e>d;d+=4)0!==(f=(a.charCodeAt(d)<<24)+(a.charCodeAt(d+1)<<16)+(a.charCodeAt(d+2)<<8)+a.charCodeAt(d+3))?(g=(f=((f=((f=((f=(f-(k=f%85))/85)-(j=f%85))/85)-(i=f%85))/85)-(h=f%85))/85)%85,c.push(g+33,h+33,i+33,j+33,k+33)):c.push(122);return function(a,b){for(var c=b;c>0;c--)a.pop()}(c,b.length),String.fromCharCode.apply(String,c)+"~>"},c=function(a){var b,c,d,e,f,g=String,h="length",i="charCodeAt",j="slice",k="replace";for(a[j](-2),a=a[j](0,-2)[k](/\s/g,"")[k]("z","!!!!!"),d=[],e=0,f=(a+=b="uuuuu"[j](a[h]%5||5))[h];f>e;e+=5)c=0x31c84b1*(a[i](e)-33)+614125*(a[i](e+1)-33)+7225*(a[i](e+2)-33)+85*(a[i](e+3)-33)+(a[i](e+4)-33),d.push(255&c>>24,255&c>>16,255&c>>8,255&c);return function(a,b){for(var c=b;c>0;c--)a.pop()}(d,b[h]),g.fromCharCode.apply(g,d)},d=function(a){var b=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(a=a.replace(/\s/g,"")).indexOf(">")&&(a=a.substr(0,a.indexOf(">"))),a.length%2&&(a+="0"),!1===b.test(a))return"";for(var c="",d=0;d<a.length;d+=2)c+=String.fromCharCode("0x"+(a[d]+a[d+1]));return c},e=function(a){for(var b=new Uint8Array(a.length),c=a.length;c--;)b[c]=a.charCodeAt(c);return a=(b=$(b)).reduce(function(a,b){return a+String.fromCharCode(b)},"")};a.processDataByFilters=function(a,f){var g=0,h=a||"",i=[];for("string"==typeof(f=f||[])&&(f=[f]),g=0;g<f.length;g+=1)switch(f[g]){case"ASCII85Decode":case"/ASCII85Decode":h=c(h),i.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":h=b(h),i.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":h=d(h),i.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":h=h.split("").map(function(a){return("0"+a.charCodeAt().toString(16)).slice(-2)}).join("")+">",i.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":h=e(h),i.push("/FlateDecode");break;default:throw Error('The filter: "'+f[g]+'" is not implemented')}return{data:h,reverseChain:i.reverse().join(" ")}}}(aF.API),function(a){a.loadFile=function(a,b,c){var d=b,e=c;d=!1!==d,e="function"==typeof e?e:function(){};var f=void 0;try{f=function(a,b,c){var d=new XMLHttpRequest,e=0,f=function(a){var b=a.length,c=[],d=String.fromCharCode;for(e=0;e<b;e+=1)c.push(d(255&a.charCodeAt(e)));return c.join("")};if(d.open("GET",a,!b),d.overrideMimeType("text/plain; charset=x-user-defined"),!1===b&&(d.onload=function(){200===d.status?c(f(this.responseText)):c(void 0)}),d.send(null),b&&200===d.status)return f(d.responseText)}(a,d,e)}catch(a){}return f},a.loadImageFile=a.loadFile}(aF.API),function(a){function b(){return(aa.html2canvas?Promise.resolve(aa.html2canvas):c.e(424).then(c.bind(c,94424))).catch(function(a){return Promise.reject(Error("Could not load html2canvas: "+a))}).then(function(a){return a.default?a.default:a})}function d(){return(aa.DOMPurify?Promise.resolve(aa.DOMPurify):c.e(514).then(c.bind(c,73514))).catch(function(a){return Promise.reject(Error("Could not load dompurify: "+a))}).then(function(a){return a.default?a.default:a})}var f=function(a){var b=e()(a);return"undefined"===b?"undefined":"string"===b||a instanceof String?"string":"number"===b||a instanceof Number?"number":"function"===b||a instanceof Function?"function":a&&a.constructor===Array?"array":a&&1===a.nodeType?"element":"object"===b?"object":"unknown"},g=function(a,b){var c=document.createElement(a);for(var d in b.className&&(c.className=b.className),b.innerHTML&&b.dompurify&&(c.innerHTML=b.dompurify.sanitize(b.innerHTML)),b.style)c.style[d]=b.style[d];return c},h=function a(b){var c=Object.assign(a.convert(Promise.resolve()),JSON.parse(JSON.stringify(a.template))),d=a.convert(Promise.resolve(),c);return(d=d.setProgress(1,a,1,[a])).set(b)};(h.prototype=Object.create(Promise.prototype)).constructor=h,h.convert=function(a,b){return a.__proto__=b||h.prototype,a},h.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},h.prototype.from=function(a,b){return this.then(function(){switch(b=b||function(a){switch(f(a)){case"string":return"string";case"element":return"canvas"===a.nodeName.toLowerCase()?"canvas":"element";default:return"unknown"}}(a)){case"string":return this.then(d).then(function(b){return this.set({src:g("div",{innerHTML:a,dompurify:b})})});case"element":return this.set({src:a});case"canvas":return this.set({canvas:a});case"img":return this.set({img:a});default:return this.error("Unknown source type.")}})},h.prototype.to=function(a){switch(a){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},h.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var a={position:"relative",display:"inline-block",width:("number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},b=function a(b,c){for(var d=3===b.nodeType?document.createTextNode(b.nodeValue):b.cloneNode(!1),e=b.firstChild;e;e=e.nextSibling)!0!==c&&1===e.nodeType&&"SCRIPT"===e.nodeName||d.appendChild(a(e,c));return 1===b.nodeType&&("CANVAS"===b.nodeName?(d.width=b.width,d.height=b.height,d.getContext("2d").drawImage(b,0,0)):"TEXTAREA"!==b.nodeName&&"SELECT"!==b.nodeName||(d.value=b.value),d.addEventListener("load",function(){d.scrollTop=b.scrollTop,d.scrollLeft=b.scrollLeft},!0)),d}(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===b.tagName&&(a.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=g("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=g("div",{className:"html2pdf__container",style:a}),this.prop.container.appendChild(b),this.prop.container.firstChild.appendChild(g("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},h.prototype.toCanvas=function(){var a=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(a).then(b).then(function(a){var b=Object.assign({},this.opt.html2canvas);return delete b.onrendered,a(this.prop.container,b)}).then(function(a){(this.opt.html2canvas.onrendered||function(){})(a),this.prop.canvas=a,document.body.removeChild(this.prop.overlay)})},h.prototype.toContext2d=function(){var a=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(a).then(b).then(function(a){var b=this.opt.jsPDF,c=this.opt.fontFaces,d=Object.assign({async:!0,allowTaint:!0,scale:"number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete d.onrendered,b.context2d.autoPaging=void 0===this.opt.autoPaging||this.opt.autoPaging,b.context2d.posX=this.opt.x,b.context2d.posY=this.opt.y,b.context2d.margin=this.opt.margin,b.context2d.fontFaces=c,c)for(var e=0;e<c.length;++e){var f=c[e],g=f.src.find(function(a){return"truetype"===a.format});g&&b.addFont(g.url,f.ref.name,f.ref.style)}return d.windowHeight=d.windowHeight||0,d.windowHeight=0==d.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):d.windowHeight,b.context2d.save(!0),a(this.prop.container,d)}).then(function(a){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(a),this.prop.canvas=a,document.body.removeChild(this.prop.overlay)})},h.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var a=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=a})},h.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},h.prototype.output=function(a,b,c){return"img"===(c=c||"pdf").toLowerCase()||"image"===c.toLowerCase()?this.outputImg(a,b):this.outputPdf(a,b)},h.prototype.outputPdf=function(a,b){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(a,b)})},h.prototype.outputImg=function(a){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(a){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+a+'" is not supported.'}})},h.prototype.save=function(a){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(a?{filename:a}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},h.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},h.prototype.set=function(a){if("object"!==f(a))return this;var b=Object.keys(a||{}).map(function(b){if(b in h.template.prop)return function(){this.prop[b]=a[b]};switch(b){case"margin":return this.setMargin.bind(this,a.margin);case"jsPDF":return function(){return this.opt.jsPDF=a.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,a.pageSize);default:return function(){this.opt[b]=a[b]}}},this);return this.then(function(){return this.thenList(b)})},h.prototype.get=function(a,b){return this.then(function(){var c=a in h.template.prop?this.prop[a]:this.opt[a];return b?b(c):c})},h.prototype.setMargin=function(a){return this.then(function(){switch(f(a)){case"number":a=[a,a,a,a];case"array":if(2===a.length&&(a=[a[0],a[1],a[0],a[1]]),4===a.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=a}).then(this.setPageSize)},h.prototype.setPageSize=function(a){function b(a,b){return Math.floor(a*b/72*96)}return this.then(function(){(a=a||aF.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(a.inner={width:a.width-this.opt.margin[1]-this.opt.margin[3],height:a.height-this.opt.margin[0]-this.opt.margin[2]},a.inner.px={width:b(a.inner.width,a.k),height:b(a.inner.height,a.k)},a.inner.ratio=a.inner.height/a.inner.width),this.prop.pageSize=a})},h.prototype.setProgress=function(a,b,c,d){return null!=a&&(this.progress.val=a),null!=b&&(this.progress.state=b),null!=c&&(this.progress.n=c),null!=d&&(this.progress.stack=d),this.progress.ratio=this.progress.val/this.progress.state,this},h.prototype.updateProgress=function(a,b,c,d){return this.setProgress(a?this.progress.val+a:null,b||null,c?this.progress.n+c:null,d?this.progress.stack.concat(d):null)},h.prototype.then=function(a,b){var c=this;return this.thenCore(a,b,function(a,b){return c.updateProgress(null,null,1,[a]),Promise.prototype.then.call(this,function(b){return c.updateProgress(null,a),b}).then(a,b).then(function(a){return c.updateProgress(1),a})})},h.prototype.thenCore=function(a,b,c){c=c||Promise.prototype.then,a&&(a=a.bind(this)),b&&(b=b.bind(this));var d=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?this:h.convert(Object.assign({},this),Promise.prototype),e=c.call(d,a,b);return h.convert(e,this.__proto__)},h.prototype.thenExternal=function(a,b){return Promise.prototype.then.call(this,a,b)},h.prototype.thenList=function(a){var b=this;return a.forEach(function(a){b=b.thenCore(a)}),b},h.prototype.catch=function(a){a&&(a=a.bind(this));var b=Promise.prototype.catch.call(this,a);return h.convert(b,this)},h.prototype.catchExternal=function(a){return Promise.prototype.catch.call(this,a)},h.prototype.error=function(a){return this.then(function(){throw Error(a)})},h.prototype.using=h.prototype.set,h.prototype.saveAs=h.prototype.save,h.prototype.export=h.prototype.output,h.prototype.run=h.prototype.then,aF.getPageSize=function(a,b,c){if("object"===e()(a)){var d=a;a=d.orientation,b=d.unit||b,c=d.format||c}b=b||"mm",c=c||"a4",a=(""+(a||"P")).toLowerCase();var f,g=(""+c).toLowerCase(),h={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(b){case"pt":f=1;break;case"mm":f=72/25.4;break;case"cm":f=72/2.54;break;case"in":f=72;break;case"px":f=.75;break;case"pc":case"em":f=12;break;case"ex":f=6;break;default:throw"Invalid unit: "+b}var i,j=0,k=0;if(h.hasOwnProperty(g))j=h[g][1]/f,k=h[g][0]/f;else try{j=c[1],k=c[0]}catch(a){throw Error("Invalid format: "+c)}if("p"===a||"portrait"===a)a="p",k>j&&(i=k,k=j,j=i);else{if("l"!==a&&"landscape"!==a)throw"Invalid orientation: "+a;a="l",j>k&&(i=k,k=j,j=i)}return{width:k,height:j,unit:b,k:f,orientation:a}},a.html=function(a,b){(b=b||{}).callback=b.callback||function(){},b.html2canvas=b.html2canvas||{},b.html2canvas.canvas=b.html2canvas.canvas||this.canvas,b.jsPDF=b.jsPDF||this,b.fontFaces=b.fontFaces?b.fontFaces.map(bq):null;var c=new h(b);return b.worker?c:c.from(a).doCallback()}}(aF.API),aF.API.addJS=function(a){return by=a,this.internal.events.subscribe("postPutResources",function(){bw=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(bw+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),bx=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+by+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){void 0!==bw&&void 0!==bx&&this.internal.out("/Names <</JavaScript "+bw+" 0 R>>")}),this},function(a){var b;a.events.push(["postPutResources",function(){var a=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var c=this.outline.render().split(/\r\n/),d=0;d<c.length;d++){var e=c[d],f=a.exec(e);if(null!=f){var g=f[1];this.internal.newObjectDeferredBegin(g,!1)}this.internal.write(e)}if(this.outline.createNamedDestinations){var h=this.internal.pages.length,i=[];for(d=0;d<h;d++){var j=this.internal.newObject();i.push(j);var k=this.internal.getPageInfo(d+1);this.internal.write("<< /D["+k.objId+" 0 R /XYZ null null null]>> endobj")}var l=this.internal.newObject();for(this.internal.write("<< /Names [ "),d=0;d<i.length;d++)this.internal.write("(page_"+(d+1)+")"+i[d]+" 0 R");this.internal.write(" ] >>","endobj"),b=this.internal.newObject(),this.internal.write("<< /Dests "+l+" 0 R"),this.internal.write(">>","endobj")}}]),a.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+b+" 0 R"))}]),a.events.push(["initialized",function(){var a=this;a.outline={createNamedDestinations:!1,root:{children:[]}},a.outline.add=function(a,b,c){var d={title:b,options:c,children:[]};return null==a&&(a=this.root),a.children.push(d),d},a.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=a,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},a.outline.genIds_r=function(b){b.id=a.internal.newObjectDeferred();for(var c=0;c<b.children.length;c++)this.genIds_r(b.children[c])},a.outline.renderRoot=function(a){this.objStart(a),this.line("/Type /Outlines"),a.children.length>0&&(this.line("/First "+this.makeRef(a.children[0])),this.line("/Last "+this.makeRef(a.children[a.children.length-1]))),this.line("/Count "+this.count_r({count:0},a)),this.objEnd()},a.outline.renderItems=function(b){for(var c=this.ctx.pdf.internal.getVerticalCoordinateString,d=0;d<b.children.length;d++){var e=b.children[d];this.objStart(e),this.line("/Title "+this.makeString(e.title)),this.line("/Parent "+this.makeRef(b)),d>0&&this.line("/Prev "+this.makeRef(b.children[d-1])),d<b.children.length-1&&this.line("/Next "+this.makeRef(b.children[d+1])),e.children.length>0&&(this.line("/First "+this.makeRef(e.children[0])),this.line("/Last "+this.makeRef(e.children[e.children.length-1])));var f=this.count=this.count_r({count:0},e);if(f>0&&this.line("/Count "+f),e.options&&e.options.pageNumber){var g=a.internal.getPageInfo(e.options.pageNumber);this.line("/Dest ["+g.objId+" 0 R /XYZ 0 "+c(0)+" 0]")}this.objEnd()}for(var h=0;h<b.children.length;h++)this.renderItems(b.children[h])},a.outline.line=function(a){this.ctx.val+=a+"\r\n"},a.outline.makeRef=function(a){return a.id+" 0 R"},a.outline.makeString=function(b){return"("+a.internal.pdfEscape(b)+")"},a.outline.objStart=function(a){this.ctx.val+="\r\n"+a.id+" 0 obj\r\n<<\r\n"},a.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},a.outline.count_r=function(a,b){for(var c=0;c<b.children.length;c++)a.count++,this.count_r(a,b.children[c]);return a.count}}])}(aF.API),function(a){var b=[192,193,194,195,196,197,198,199];a.processJPEG=function(a,c,d,e,f,g){var h,i=this.decode.DCT_DECODE,j=null;if("string"==typeof a||this.__addimage__.isArrayBuffer(a)||this.__addimage__.isArrayBufferView(a)){switch(a=f||a,a=this.__addimage__.isArrayBuffer(a)?new Uint8Array(a):a,(h=function(a){for(var c,d=256*a.charCodeAt(4)+a.charCodeAt(5),e=a.length,f={width:0,height:0,numcomponents:1},g=4;g<e;g+=2){if(g+=d,-1!==b.indexOf(a.charCodeAt(g+1))){c=256*a.charCodeAt(g+5)+a.charCodeAt(g+6),f={width:256*a.charCodeAt(g+7)+a.charCodeAt(g+8),height:c,numcomponents:a.charCodeAt(g+9)};break}d=256*a.charCodeAt(g+2)+a.charCodeAt(g+3)}return f}(a=this.__addimage__.isArrayBufferView(a)?this.__addimage__.arrayBufferToBinaryString(a):a)).numcomponents){case 1:g=this.color_spaces.DEVICE_GRAY;break;case 4:g=this.color_spaces.DEVICE_CMYK;break;case 3:g=this.color_spaces.DEVICE_RGB}j={data:a,width:h.width,height:h.height,colorSpace:g,bitsPerComponent:8,filter:i,index:c,alias:d}}return j}}(aF.API);var bA,bB,bC,bD,bE,bF=function(){function a(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o;for(this.data=a,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},g=null;;){switch(b=this.readUInt32(),j=(function(){var a,b;for(b=[],a=0;a<4;++a)b.push(String.fromCharCode(this.data[this.pos++]));return b}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(b);break;case"fcTL":g&&this.animation.frames.push(g),this.pos+=4,g={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},f=this.readUInt16(),e=this.readUInt16()||100,g.delay=1e3*f/e,g.disposeOp=this.data[this.pos++],g.blendOp=this.data[this.pos++],g.data=[];break;case"IDAT":case"fdAT":for("fdAT"===j&&(this.pos+=4,b-=4),a=(null!=g?g.data:void 0)||this.imgData,m=0;0<=b?m<b:m>b;0<=b?++m:--m)a.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(d=this.palette.length/3,this.transparency.indexed=this.read(b),this.transparency.indexed.length>d)throw Error("More transparent colors than palette size");if((k=d-this.transparency.indexed.length)>0)for(n=0;0<=k?n<k:n>k;0<=k?++n:--n)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(b)[0];break;case 2:this.transparency.rgb=this.read(b)}break;case"tEXt":h=(l=this.read(b)).indexOf(0),i=String.fromCharCode.apply(String,l.slice(0,h)),this.text[i]=String.fromCharCode.apply(String,l.slice(h+1));break;case"IEND":return g&&this.animation.frames.push(g),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=4===(o=this.colorType)||6===o,c=this.colors+ +!!this.hasAlphaChannel,this.pixelBitlength=this.bits*c,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=b}if(this.pos+=4,this.pos>this.data.length)throw Error("Incomplete or corrupt PNG file")}}a.prototype.read=function(a){var b,c;for(c=[],b=0;0<=a?b<a:b>a;0<=a?++b:--b)c.push(this.data[this.pos++]);return c},a.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.decodePixels=function(a){var b,c,d,e=this.pixelBitlength/8,f=new Uint8Array(this.width*this.height*e),g=0,h=this;if(null==a&&(a=this.imgData),0===a.length)return new Uint8Array(0);function i(b,c,d,i){var j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E=Math.ceil((h.width-b)/d),F=Math.ceil((h.height-c)/i),G=h.width==E&&h.height==F;for(w=e*E,u=G?f:new Uint8Array(w*F),o=a.length,v=0,k=0;v<F&&g<o;){switch(a[g++]){case 0:for(m=z=0;z<w;m=z+=1)u[k++]=a[g++];break;case 1:for(m=A=0;A<w;m=A+=1)j=a[g++],n=m<e?0:u[k-e],u[k++]=(j+n)%256;break;case 2:for(m=B=0;B<w;m=B+=1)j=a[g++],l=(m-m%e)/e,x=v&&u[(v-1)*w+l*e+m%e],u[k++]=(x+j)%256;break;case 3:for(m=C=0;C<w;m=C+=1)j=a[g++],l=(m-m%e)/e,n=m<e?0:u[k-e],x=v&&u[(v-1)*w+l*e+m%e],u[k++]=(j+Math.floor((n+x)/2))%256;break;case 4:for(m=D=0;D<w;m=D+=1)j=a[g++],l=(m-m%e)/e,n=m<e?0:u[k-e],0===v?x=y=0:(x=u[(v-1)*w+l*e+m%e],y=l&&u[(v-1)*w+(l-1)*e+m%e]),q=Math.abs((p=n+x-y)-n),s=Math.abs(p-x),t=Math.abs(p-y),r=q<=s&&q<=t?n:s<=t?x:y,u[k++]=(j+r)%256;break;default:throw Error("Invalid filter algorithm: "+a[g-1])}if(!G){var H=((c+v*i)*h.width+b)*e,I=v*w;for(m=0;m<E;m+=1){for(var J=0;J<e;J+=1)f[H++]=u[I++];H+=(d-1)*e}}v++}}return a=K((b=a).subarray((d=void 0,((15&b[0])!=8||b[0]>>4>7||(b[0]<<8|b[1])%31)&&J(6,"invalid zlib data"),(b[1]>>5&1)==+!d&&J(6,"invalid zlib data: "+(32&b[1]?"need":"unexpected")+" dictionary"),(b[1]>>3&4)+2),-4),{i:2},c&&c.out,c&&c.dictionary),1==h.interlaceMethod?(i(0,0,8,8),i(4,0,8,8),i(0,4,4,8),i(2,0,4,4),i(0,2,2,4),i(1,0,2,2),i(0,1,1,2)):i(0,0,1,1),f},a.prototype.decodePalette=function(){var a,b,c,d,e,f,g,h,i;for(c=this.palette,e=new Uint8Array(((f=this.transparency.indexed||[]).length||0)+c.length),d=0,a=0,b=g=0,h=c.length;g<h;b=g+=3)e[d++]=c[b],e[d++]=c[b+1],e[d++]=c[b+2],e[d++]=null!=(i=f[a++])?i:255;return e},a.prototype.copyToImageData=function(a,b){var c,d,e,f,g,h,i,j,k,l,m;if(d=this.colors,k=null,c=this.hasAlphaChannel,this.palette.length&&(k=null!=(m=this._decodedPalette)?m:this._decodedPalette=this.decodePalette(),d=4,c=!0),j=(e=a.data||a).length,g=k||b,f=h=0,1===d)for(;f<j;)i=k?4*b[f/4]:h,l=g[i++],e[f++]=l,e[f++]=l,e[f++]=l,e[f++]=c?g[i++]:255,h=i;else for(;f<j;)i=k?4*b[f/4]:h,e[f++]=g[i++],e[f++]=g[i++],e[f++]=g[i++],e[f++]=c?g[i++]:255,h=i},a.prototype.decode=function(){var a;return a=new Uint8Array(this.width*this.height*4),this.copyToImageData(a,this.decodePixels()),a};var b,c,d,e=function(){if("[object Window]"===Object.prototype.toString.call(aa)){try{d=(c=aa.document.createElement("canvas")).getContext("2d")}catch(a){return!1}return!0}return!1};return e(),b=function(a){var b;if(!0===e())return d.width=a.width,d.height=a.height,d.clearRect(0,0,a.width,a.height),d.putImageData(a,0,0),(b=new Image).src=c.toDataURL(),b;throw Error("This method requires a Browser with Canvas-capability.")},a.prototype.decodeFrames=function(a){var c,d,e,f,g,h,i,j;if(this.animation){for(j=[],d=g=0,h=(i=this.animation.frames).length;g<h;d=++g)c=i[d],e=a.createImageData(c.width,c.height),f=this.decodePixels(new Uint8Array(c.data)),this.copyToImageData(e,f),c.imageData=e,j.push(c.image=b(e));return j}},a.prototype.renderFrame=function(a,b){var c,d,e;return c=(d=this.animation.frames)[b],e=d[b-1],0===b&&a.clearRect(0,0,this.width,this.height),1===(null!=e?e.disposeOp:void 0)?a.clearRect(e.xOffset,e.yOffset,e.width,e.height):2===(null!=e?e.disposeOp:void 0)&&a.putImageData(e.imageData,e.xOffset,e.yOffset),0===c.blendOp&&a.clearRect(c.xOffset,c.yOffset,c.width,c.height),a.drawImage(c.image,c.xOffset,c.yOffset)},a.prototype.animate=function(a){var b,c,d,e,f,g,h=this;return c=0,e=(g=this.animation).numFrames,d=g.frames,f=g.numPlays,(b=function(){var g,i;if(i=d[g=c++%e],h.renderFrame(a,g),e>1&&c/e<f)return h.animation._timeout=setTimeout(b,i.delay)})()},a.prototype.stopAnimation=function(){var a;return clearTimeout(null!=(a=this.animation)?a._timeout:void 0)},a.prototype.render=function(a){var b,c;return a._png&&a._png.stopAnimation(),a._png=this,a.width=this.width,a.height=this.height,b=a.getContext("2d"),this.animation?(this.decodeFrames(b),this.animate(b)):(c=b.createImageData(this.width,this.height),this.copyToImageData(c,this.decodePixels()),b.putImageData(c,0,0))},a}();function bG(a){var b=0;if(71!==a[b++]||73!==a[b++]||70!==a[b++]||56!==a[b++]||56!=(a[b++]+1&253)||97!==a[b++])throw Error("Invalid GIF 87a/89a header.");var c=a[b++]|a[b++]<<8,d=a[b++]|a[b++]<<8,e=a[b++],f=1<<(7&e)+1;a[b++],a[b++];var g=null,h=null;e>>7&&(g=b,h=f,b+=3*f);var i=!0,j=[],k=0,l=null,m=0,n=null;for(this.width=c,this.height=d;i&&b<a.length;)switch(a[b++]){case 33:switch(a[b++]){case 255:if(11!==a[b]||78==a[b+1]&&69==a[b+2]&&84==a[b+3]&&83==a[b+4]&&67==a[b+5]&&65==a[b+6]&&80==a[b+7]&&69==a[b+8]&&50==a[b+9]&&46==a[b+10]&&48==a[b+11]&&3==a[b+12]&&1==a[b+13]&&0==a[b+16])b+=14,n=a[b++]|a[b++]<<8,b++;else for(b+=12;;){if(!((p=a[b++])>=0))throw Error("Invalid block size");if(0===p)break;b+=p}break;case 249:if(4!==a[b++]||0!==a[b+4])throw Error("Invalid graphics extension block.");var o=a[b++];k=a[b++]|a[b++]<<8,l=a[b++],0==(1&o)&&(l=null),m=o>>2&7,b++;break;case 254:for(;;){if(!((p=a[b++])>=0))throw Error("Invalid block size");if(0===p)break;b+=p}break;default:throw Error("Unknown graphic control label: 0x"+a[b-1].toString(16))}break;case 44:var p,q=a[b++]|a[b++]<<8,r=a[b++]|a[b++]<<8,s=a[b++]|a[b++]<<8,t=a[b++]|a[b++]<<8,u=a[b++],v=u>>6&1,w=1<<(7&u)+1,x=g,y=h,z=!1;u>>7&&(z=!0,x=b,y=w,b+=3*w);var A=b;for(b++;;){if(!((p=a[b++])>=0))throw Error("Invalid block size");if(0===p)break;b+=p}j.push({x:q,y:r,width:s,height:t,has_local_palette:z,palette_offset:x,palette_size:y,data_offset:A,data_length:b-A,transparent_index:l,interlaced:!!v,delay:k,disposal:m});break;case 59:i=!1;break;default:throw Error("Unknown gif block: 0x"+a[b-1].toString(16))}this.numFrames=function(){return j.length},this.loopCount=function(){return n},this.frameInfo=function(a){if(a<0||a>=j.length)throw Error("Frame index out of range.");return j[a]},this.decodeAndBlitFrameBGRA=function(b,d){var e=this.frameInfo(b),f=e.width*e.height,g=new Uint8Array(f);bH(a,e.data_offset,g,f);var h=e.palette_offset,i=e.transparent_index;null===i&&(i=256);var j=e.width,k=c-j,l=j,m=4*(e.y*c+e.x),n=4*((e.y+e.height)*c+e.x),o=m,p=4*k;!0===e.interlaced&&(p+=4*c*7);for(var q=8,r=0,s=g.length;r<s;++r){var t=g[r];if(0===l&&(l=j,(o+=p)>=n&&(p=4*k+4*c*(q-1),o=m+(j+k)*(q<<1),q>>=1)),t===i)o+=4;else{var u=a[h+3*t],v=a[h+3*t+1],w=a[h+3*t+2];d[o++]=w,d[o++]=v,d[o++]=u,d[o++]=255}--l}},this.decodeAndBlitFrameRGBA=function(b,d){var e=this.frameInfo(b),f=e.width*e.height,g=new Uint8Array(f);bH(a,e.data_offset,g,f);var h=e.palette_offset,i=e.transparent_index;null===i&&(i=256);var j=e.width,k=c-j,l=j,m=4*(e.y*c+e.x),n=4*((e.y+e.height)*c+e.x),o=m,p=4*k;!0===e.interlaced&&(p+=4*c*7);for(var q=8,r=0,s=g.length;r<s;++r){var t=g[r];if(0===l&&(l=j,(o+=p)>=n&&(p=4*k+4*c*(q-1),o=m+(j+k)*(q<<1),q>>=1)),t===i)o+=4;else{var u=a[h+3*t],v=a[h+3*t+1],w=a[h+3*t+2];d[o++]=u,d[o++]=v,d[o++]=w,d[o++]=255}--l}}}function bH(a,b,c,d){for(var e=a[b++],f=1<<e,g=f+1,h=g+1,i=e+1,j=(1<<i)-1,k=0,l=0,m=0,n=a[b++],o=new Int32Array(4096),p=null;;){for(;k<16&&0!==n;)l|=a[b++]<<k,k+=8,1===n?n=a[b++]:--n;if(k<i)break;var q=l&j;if(l>>=i,k-=i,q!==f){if(q===g)break;for(var r=q<h?q:p,s=0,t=r;t>f;)t=o[t]>>8,++s;var u=t;if(m+s+ +(r!==q)>d)return void ac.log("Warning, gif stream longer than expected.");c[m++]=u;var v=m+=s;for(r!==q&&(c[m++]=u),t=r;s--;)t=o[t],c[--v]=255&t,t>>=8;null!==p&&h<4096&&(o[h++]=p<<8|u,h>=j+1&&i<12&&(++i,j=j<<1|1)),p=q}else h=g+1,j=(1<<(i=e+1))-1,p=null}return m!==d&&ac.log("Warning, gif stream shorter than expected."),c}function bI(a){var b,c,d,e,f,g=Math.floor,h=Array(64),i=Array(64),j=Array(64),k=Array(64),l=Array(65535),m=Array(65535),n=Array(64),o=Array(64),p=[],q=0,r=7,s=Array(64),t=Array(64),u=Array(64),v=Array(256),w=Array(2048),x=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],y=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],z=[0,1,2,3,4,5,6,7,8,9,10,11],A=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],B=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],C=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],D=[0,1,2,3,4,5,6,7,8,9,10,11],E=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],F=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function G(a,b){for(var c=0,d=0,e=[],f=1;f<=16;f++){for(var g=1;g<=a[f];g++)e[b[d]]=[],e[b[d]][0]=c,e[b[d]][1]=f,d++,c++;c*=2}return e}function H(a){for(var b=a[0],c=a[1]-1;c>=0;)b&1<<c&&(q|=1<<r),c--,--r<0&&(255==q?(I(255),I(0)):I(q),r=7,q=0)}function I(a){p.push(a)}function J(a){I(a>>8&255),I(255&a)}function K(a,b,c,d,e){for(var f,g=e[0],h=e[240],i=function(a,b){var c,d,e,f,g,h,i,j,k,l,m=0;for(k=0;k<8;++k){c=a[m],d=a[m+1],e=a[m+2],f=a[m+3],g=a[m+4],h=a[m+5],i=a[m+6];var o=c+(j=a[m+7]),p=c-j,q=d+i,r=d-i,s=e+h,t=e-h,u=f+g,v=f-g,w=o+u,x=o-u,y=q+s,z=q-s;a[m]=w+y,a[m+4]=w-y;var A=.707106781*(z+x);a[m+2]=x+A,a[m+6]=x-A;var B=.382683433*((w=v+t)-(z=r+p)),C=.5411961*w+B,D=1.306562965*z+B,E=.707106781*(y=t+r),F=p+E,G=p-E;a[m+5]=G+C,a[m+3]=G-C,a[m+1]=F+D,a[m+7]=F-D,m+=8}for(m=0,k=0;k<8;++k){c=a[m],d=a[m+8],e=a[m+16],f=a[m+24],g=a[m+32],h=a[m+40],i=a[m+48];var H=c+(j=a[m+56]),I=c-j,J=d+i,K=d-i,L=e+h,M=e-h,N=f+g,O=f-g,P=H+N,Q=H-N,R=J+L,S=J-L;a[m]=P+R,a[m+32]=P-R;var T=.707106781*(S+Q);a[m+16]=Q+T,a[m+48]=Q-T;var U=.382683433*((P=O+M)-(S=K+I)),V=.5411961*P+U,W=1.306562965*S+U,X=.707106781*(R=M+K),Y=I+X,Z=I-X;a[m+40]=Z+V,a[m+24]=Z-V,a[m+8]=Y+W,a[m+56]=Y-W,m++}for(k=0;k<64;++k)l=a[k]*b[k],n[k]=l>0?l+.5|0:l-.5|0;return n}(a,b),j=0;j<64;++j)o[x[j]]=i[j];var k=o[0]-c;c=o[0],0==k?H(d[0]):(H(d[m[f=32767+k]]),H(l[f]));for(var p=63;p>0&&0==o[p];)p--;if(0==p)return H(g),c;for(var q,r=1;r<=p;){for(var s=r;0==o[r]&&r<=p;)++r;var t=r-s;if(t>=16){q=t>>4;for(var u=1;u<=q;++u)H(h);t&=15}H(e[(t<<4)+m[f=32767+o[r]]]),H(l[f]),r++}return 63!=p&&H(g),c}function L(a){f!=(a=Math.min(Math.max(a,1),100))&&(!function(a){for(var b=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],c=0;c<64;c++){var d=g((b[c]*a+50)/100);d=Math.min(Math.max(d,1),255),h[x[c]]=d}for(var e=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],f=0;f<64;f++){var l=g((e[f]*a+50)/100);l=Math.min(Math.max(l,1),255),i[x[f]]=l}for(var m=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],n=0,o=0;o<8;o++)for(var p=0;p<8;p++)j[n]=1/(h[x[n]]*m[o]*m[p]*8),k[n]=1/(i[x[n]]*m[o]*m[p]*8),n++}(a<50?Math.floor(5e3/a):Math.floor(200-2*a)),f=a)}this.encode=function(a,f){f&&L(f),p=[],q=0,r=7,J(65496),J(65504),J(16),I(74),I(70),I(73),I(70),I(0),I(1),I(1),I(0),J(1),J(1),I(0),I(0),function(){J(65499),J(132),I(0);for(var a=0;a<64;a++)I(h[a]);I(1);for(var b=0;b<64;b++)I(i[b])}(),n=a.width,o=a.height,J(65472),J(17),I(8),J(o),J(n),I(3),I(1),I(17),I(0),I(2),I(17),I(1),I(3),I(17),I(1),function(){J(65476),J(418),I(0);for(var a=0;a<16;a++)I(y[a+1]);for(var b=0;b<=11;b++)I(z[b]);I(16);for(var c=0;c<16;c++)I(A[c+1]);for(var d=0;d<=161;d++)I(B[d]);I(1);for(var e=0;e<16;e++)I(C[e+1]);for(var f=0;f<=11;f++)I(D[f]);I(17);for(var g=0;g<16;g++)I(E[g+1]);for(var h=0;h<=161;h++)I(F[h])}(),J(65498),J(12),I(3),I(1),I(0),I(2),I(17),I(3),I(17),I(0),I(63),I(0);var g=0,l=0,m=0;q=0,r=7,this.encode.displayName="_encode_";for(var n,o,v,x,G,M,N,O,P,Q,R,S=a.data,T=a.width,U=a.height,V=4*T,W=0;W<U;){for(v=0;v<V;){for(N=V*W+v,P=-1,Q=0,R=0;R<64;R++)O=N+(Q=R>>3)*V+(P=4*(7&R)),W+Q>=U&&(O-=V*(W+1+Q-U)),v+P>=V&&(O-=v+P-V+4),x=S[O++],G=S[O++],M=S[O++],s[R]=(w[x]+w[G+256|0]+w[M+512|0]>>16)-128,t[R]=(w[x+768|0]+w[G+1024|0]+w[M+1280|0]>>16)-128,u[R]=(w[x+1280|0]+w[G+1536|0]+w[M+1792|0]>>16)-128;g=K(s,j,g,b,d),l=K(t,k,l,c,e),m=K(u,k,m,c,e),v+=32}W+=8}if(r>=0){var X=[];X[1]=r+1,X[0]=(1<<r+1)-1,H(X)}return J(65497),new Uint8Array(p)},a=a||50,function(){for(var a=String.fromCharCode,b=0;b<256;b++)v[b]=a(b)}(),b=G(y,z),c=G(C,D),d=G(A,B),e=G(E,F),function(){for(var a=1,b=2,c=1;c<=15;c++){for(var d=a;d<b;d++)m[32767+d]=c,l[32767+d]=[],l[32767+d][1]=c,l[32767+d][0]=d;for(var e=-(b-1);e<=-a;e++)m[32767+e]=c,l[32767+e]=[],l[32767+e][1]=c,l[32767+e][0]=b-1+e;a<<=1,b<<=1}}(),function(){for(var a=0;a<256;a++)w[a]=19595*a,w[a+256|0]=38470*a,w[a+512|0]=7471*a+32768,w[a+768|0]=-11059*a,w[a+1024|0]=-21709*a,w[a+1280|0]=32768*a+8421375,w[a+1536|0]=-27439*a,w[a+1792|0]=-5329*a}(),L(a)}function bJ(a,b){if(this.pos=0,this.buffer=a,this.datav=new DataView(a.buffer),this.is_with_alpha=!!b,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function bK(a){function b(a){if(!a)throw Error("assert :P")}function c(a,b,c){for(var d=0;4>d;d++)if(a[b+d]!=c.charCodeAt(d))return!0;return!1}function d(a,b,c,d,e){for(var f=0;f<e;f++)a[b+f]=c[d+f]}function e(a,b,c,d){for(var e=0;e<d;e++)a[b+e]=c}function f(a){return new Int32Array(a)}function g(a,b){for(var c=[],d=0;d<a;d++)c.push(new b);return c}function h(a,b){var c=[];return function a(c,d,e){for(var f=e[d],g=0;g<f&&(c.push(e.length>d+1?[]:new b),!(e.length<d+1));g++)a(c[g],d+1,e)}(c,0,a),c}var i=function(){var a=this;function i(a,b){for(var c=1<<b-1>>>0;a&c;)c>>>=1;return c?(a&c-1)+c:a}function j(a,c,d,e,f){b(!(e%d));do a[c+(e-=d)]=f;while(0<e)}function k(a,c,d,e,g){if(b(2328>=g),512>=g)var h=f(512);else if(null==(h=f(g)))return 0;return function(a,c,d,e,g,h){var k,m,n=c,o=1<<d,p=f(16),q=f(16);for(b(0!=g),b(null!=e),b(null!=a),b(0<d),m=0;m<g;++m){if(15<e[m])return 0;++p[e[m]]}if(p[0]==g)return 0;for(q[1]=0,k=1;15>k;++k){if(p[k]>1<<k)return 0;q[k+1]=q[k]+p[k]}for(m=0;m<g;++m)k=e[m],0<e[m]&&(h[q[k]++]=m);if(1==q[15])return(e=new l).g=0,e.value=h[0],j(a,n,1,o,e),o;var r,s=-1,t=o-1,u=0,v=1,w=1,x=1<<d;for(m=0,k=1,g=2;k<=d;++k,g<<=1){if(v+=w<<=1,0>(w-=p[k]))return 0;for(;0<p[k];--p[k])(e=new l).g=k,e.value=h[m++],j(a,n+u,g,x,e),u=i(u,k)}for(k=d+1,g=2;15>=k;++k,g<<=1){if(v+=w<<=1,0>(w-=p[k]))return 0;for(;0<p[k];--p[k]){if(e=new l,(u&t)!=s){for(n+=x,r=1<<(s=k)-d;15>s&&!(0>=(r-=p[s]));)++s,r<<=1;o+=x=1<<(r=s-d),a[c+(s=u&t)].g=r+d,a[c+s].value=n-c-s}e.g=k-d,e.value=h[m++],j(a,n+(u>>d),g,x,e),u=i(u,k)}}return v!=2*q[15]-1?0:o}(a,c,d,e,g,h)}function l(){this.value=this.g=0}function m(){this.value=this.g=0}function n(){this.G=g(5,l),this.H=f(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=g(cq,m)}function o(a,c,d,e){b(null!=a),b(null!=c),b(0x80000000>e),a.Ca=254,a.I=0,a.b=-8,a.Ka=0,a.oa=c,a.pa=d,a.Jd=c,a.Yc=d+e,a.Zc=4<=e?d+e-4+1:d,A(a)}function p(a,b){for(var c=0;0<b--;)c|=C(a,128)<<b;return c}function q(a,b){var c=p(a,b);return B(a)?-c:c}function r(a,c,d,e){var f,g=0;for(b(null!=a),b(null!=c),b(0xfffffff8>e),a.Sb=e,a.Ra=0,a.u=0,a.h=0,4<e&&(e=4),f=0;f<e;++f)g+=c[d+f]<<8*f;a.Ra=g,a.bb=e,a.oa=c,a.pa=d}function s(a){for(;8<=a.u&&a.bb<a.Sb;)a.Ra>>>=8,a.Ra+=a.oa[a.pa+a.bb]<<ct-8>>>0,++a.bb,a.u-=8;x(a)&&(a.h=1,a.u=0)}function t(a,c){if(b(0<=c),!a.h&&c<=cs){var d=w(a)&cr[c];return a.u+=c,s(a),d}return a.h=1,a.u=0}function u(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function v(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function w(a){return a.Ra>>>(a.u&ct-1)>>>0}function x(a){return b(a.bb<=a.Sb),a.h||a.bb==a.Sb&&a.u>ct}function y(a,b){a.u=b,a.h=x(a)}function z(a){a.u>=cu&&(b(a.u>=cu),s(a))}function A(a){b(null!=a&&null!=a.oa),a.pa<a.Zc?(a.I=(a.oa[a.pa++]|a.I<<8)>>>0,a.b+=8):(b(null!=a&&null!=a.oa),a.pa<a.Yc?(a.b+=8,a.I=a.oa[a.pa++]|a.I<<8):a.Ka?a.b=0:(a.I<<=8,a.b+=8,a.Ka=1))}function B(a){return p(a,1)}function C(a,b){var c=a.Ca;0>a.b&&A(a);var d=a.b,e=c*b>>>8,f=(a.I>>>d>e)+0;for(f?(c-=e,a.I-=e+1<<d>>>0):c=e+1,d=c,e=0;256<=d;)e+=8,d>>=8;return d=7^e+cv[d],a.b-=d,a.Ca=(c<<d)-1,f}function D(a,b,c){a[b+0]=c>>24&255,a[b+1]=c>>16&255,a[b+2]=c>>8&255,a[b+3]=(0|c)&255}function E(a,b){return 0|a[b+0]|a[b+1]<<8}function F(a,b){return E(a,b)|a[b+2]<<16}function G(a,b){return E(a,b)|E(a,b+2)<<16}function H(a,c){return b(null!=a),b(0<c),a.X=f(1<<c),null==a.X?0:(a.Mb=32-c,a.Xa=c,1)}function I(a,c){b(null!=a),b(null!=c),b(a.Xa==c.Xa),d(c.X,0,a.X,0,1<<c.Xa)}function J(){this.X=[],this.Xa=this.Mb=0}function K(a,c,d,e){b(null!=d),b(null!=e);var f=d[0],g=e[0];return 0==f&&(f=(a*g+c/2)/c),0==g&&(g=(c*f+a/2)/a),0>=f||0>=g?0:(d[0]=f,e[0]=g,1)}function L(a,b){return a+(1<<b)-1>>>b}function M(a,b){return((0xff00ff00&a)+(0xff00ff00&b)>>>0&0xff00ff00)+((0xff00ff&a)+(0xff00ff&b)>>>0&0xff00ff)>>>0}function N(b,c){a[c]=function(c,d,e,f,g,h,i){var j;for(j=0;j<g;++j){var k=a[b](h[i+j-1],e,f+j);h[i+j]=M(c[d+j],k)}}}function O(){this.ud=this.hd=this.jd=0}function P(a,b){return((0xfefefefe&(a^b))>>>1)+(a&b)>>>0}function Q(a){return 0<=a&&256>a?a:0>a?0:255<a?255:void 0}function R(a,b){return Q(a+(a-b+.5>>1))}function S(a,b,c){return Math.abs(b-c)-Math.abs(a-c)}function T(a,b,c,d,e,f,g){for(d=f[g-1],c=0;c<e;++c)f[g+c]=d=M(a[b+c],d)}function U(a,b,c,d,e){var f;for(f=0;f<c;++f){var g=a[b+f],h=g>>8&255,i=0xff00ff&(i=(i=0xff00ff&g)+((h<<16)+h));d[e+f]=(0xff00ff00&g)+i>>>0}}function V(a,b){b.jd=(0|a)&255,b.hd=a>>8&255,b.ud=a>>16&255}function W(a,b,c,d,e,f){var g;for(g=0;g<d;++g){var h=b[c+g],i=h>>>8,j=h,k=255&(k=(k=h>>>16)+((a.jd<<24>>24)*(i<<24>>24)>>>5));j=255&(j=(j+=(a.hd<<24>>24)*(i<<24>>24)>>>5)+((a.ud<<24>>24)*(k<<24>>24)>>>5)),e[f+g]=(0xff00ff00&h)+(k<<16)+j}}function X(b,c,d,e,f){a[c]=function(a,b,c,d,g,h,i,j,k){for(d=i;d<j;++d)for(i=0;i<k;++i)g[h++]=f(c[e(a[b++])])},a[b]=function(b,c,g,h,i,j,k){var l=8>>b.b,m=b.Ea,n=b.K[0],o=b.w;if(8>l)for(b=(1<<b.b)-1,o=(1<<l)-1;c<g;++c){var p,q=0;for(p=0;p<m;++p)p&b||(q=e(h[i++])),j[k++]=f(n[q&o]),q>>=l}else a["VP8LMapColor"+d](h,i,n,o,j,k,c,g,m)}}function Y(a,b,c,d,e){for(c=b+c;b<c;){var f=a[b++];d[e++]=f>>16&255,d[e++]=f>>8&255,d[e++]=(0|f)&255}}function Z(a,b,c,d,e){for(c=b+c;b<c;){var f=a[b++];d[e++]=f>>16&255,d[e++]=f>>8&255,d[e++]=(0|f)&255,d[e++]=f>>24&255}}function $(a,b,c,d,e){for(c=b+c;b<c;){var f=(g=a[b++])>>16&240|g>>12&15,g=(0|g)&240|g>>28&15;d[e++]=f,d[e++]=g}}function _(a,b,c,d,e){for(c=b+c;b<c;){var f=(g=a[b++])>>16&248|g>>13&7,g=g>>5&224|g>>3&31;d[e++]=f,d[e++]=g}}function aa(a,b,c,d,e){for(c=b+c;b<c;){var f=a[b++];d[e++]=(0|f)&255,d[e++]=f>>8&255,d[e++]=f>>16&255}}function ab(a,b,c,e,f,g){if(0==g)for(c=b+c;b<c;)D(e,((g=a[b++])[0]>>24|g[1]>>8&65280|g[2]<<8&0xff0000|g[3]<<24)>>>0),f+=32;else d(e,f,a,b,c)}function ac(b,c){a[c][0]=a[b+"0"],a[c][1]=a[b+"1"],a[c][2]=a[b+"2"],a[c][3]=a[b+"3"],a[c][4]=a[b+"4"],a[c][5]=a[b+"5"],a[c][6]=a[b+"6"],a[c][7]=a[b+"7"],a[c][8]=a[b+"8"],a[c][9]=a[b+"9"],a[c][10]=a[b+"10"],a[c][11]=a[b+"11"],a[c][12]=a[b+"12"],a[c][13]=a[b+"13"],a[c][14]=a[b+"0"],a[c][15]=a[b+"0"]}function ad(a){return a==dk||a==dl||a==dm||a==dn}function ae(){this.eb=[],this.size=this.A=this.fb=0}function af(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function ag(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new ae,this.f.kb=new af,this.sd=null}function ah(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function ai(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function aj(a){return alert("todo:WebPSamplerProcessPlane"),a.T}function ak(a,b){var c=a.T,e=b.ba.f.RGBA,f=e.eb,g=e.fb+a.ka*e.A,h=dN[b.ba.S],i=a.y,j=a.O,k=a.f,l=a.N,m=a.ea,n=a.W,o=b.cc,p=b.dc,q=b.Mc,r=b.Nc,s=a.ka,t=a.ka+a.T,u=a.U,v=u+1>>1;for(0==s?h(i,j,null,null,k,l,m,n,k,l,m,n,f,g,null,null,u):(h(b.ec,b.fc,i,j,o,p,q,r,k,l,m,n,f,g-e.A,f,g,u),++c);s+2<t;s+=2)o=k,p=l,q=m,r=n,l+=a.Rc,n+=a.Rc,g+=2*e.A,h(i,(j+=2*a.fa)-a.fa,i,j,o,p,q,r,k,l,m,n,f,g-e.A,f,g,u);return j+=a.fa,a.j+t<a.o?(d(b.ec,b.fc,i,j,u),d(b.cc,b.dc,k,l,v),d(b.Mc,b.Nc,m,n,v),c--):1&t||h(i,j,null,null,k,l,m,n,k,l,m,n,f,g+e.A,null,null,u),c}function al(a,c,d){var e=a.F,f=[a.J];if(null!=e){var g=a.U,h=c.ba.S,i=h==dh||h==dm;c=c.ba.f.RGBA;var j=[0],k=a.ka;j[0]=a.T,a.Kb&&(0==k?--j[0]:(--k,f[0]-=a.width),a.j+a.ka+a.T==a.o&&(j[0]=a.o-a.j-k));var l=c.eb;k=c.fb+k*c.A,a=c2(e,f[0],a.width,g,j,l,k+3*!i,c.A),b(d==j),a&&ad(h)&&c0(l,k,i,g,j,c.A)}return 0}function am(a){var b=a.ma,c=b.ba.S,d=11>c,e=c==de||c==dg||c==dh||c==di||12==c||ad(c);if(b.memory=null,b.Ib=null,b.Jb=null,b.Nd=null,!cn(b.Oa,a,e?11:12))return 0;if(e&&ad(c)&&b6(),a.da)alert("todo:use_scaling");else{if(d){if(b.Ib=aj,a.Kb){if(c=a.U+1>>1,b.memory=f(a.U+2*c),null==b.memory)return 0;b.ec=b.memory,b.fc=0,b.cc=b.ec,b.dc=b.fc+a.U,b.Mc=b.cc,b.Nc=b.dc+c,b.Ib=ak,b6()}}else alert("todo:EmitYUV");e&&(b.Jb=al,d&&b4())}if(d&&!d_){for(a=0;256>a;++a)d0[a]=89858*(a-128)+dW>>dV,d3[a]=-22014*(a-128)+dW,d2[a]=-45773*(a-128),d1[a]=113618*(a-128)+dW>>dV;for(a=dX;a<dY;++a)b=76283*(a-16)+dW>>dV,d4[a-dX]=aT(b,255),d5[a-dX]=aT(b+8>>4,15);d_=1}return 1}function an(a){var c=a.ma,d=a.U,e=a.T;return b(!(1&a.ka)),0>=d||0>=e?0:(d=c.Ib(a,c),null!=c.Jb&&c.Jb(a,c,d),c.Dc+=d,1)}function ao(a){a.ma.memory=null}function ap(a,b,c,d){return 47!=t(a,8)?0:(b[0]=t(a,14)+1,c[0]=t(a,14)+1,d[0]=t(a,1),0!=t(a,3)?0:!a.h)}function aq(a,b){if(4>a)return a+1;var c=a-2>>1;return(2+(1&a)<<c)+t(b,c)+1}function ar(a,b){var c;return 120<b?b-120:1<=(c=((c=du[b-1])>>4)*a+(8-(15&c)))?c:1}function as(a,b,c){var d=w(c),e=a[b+=255&d].g-8;return 0<e&&(y(c,c.u+8),d=w(c),b+=a[b].value,b+=d&(1<<e)-1),y(c,c.u+a[b].g),a[b].value}function at(a,c,d){return d.g+=a.g,d.value+=a.value<<c>>>0,b(8>=d.g),a.g}function au(a,c,d){var e=a.xc;return b((c=0==e?0:a.vc[a.md*(d>>e)+(c>>e)])<a.Wb),a.Ya[c]}function av(a,c,e,f){var g=a.ab,h=a.c*c,i=a.C;c=i+c;var j=e,k=f;for(f=a.Ta,e=a.Ua;0<g--;){var l=a.gc[g],m=i,n=c,o=j,p=k,q=(k=f,j=e,l.Ea);switch(b(m<n),b(n<=l.nc),l.hc){case 2:cy(o,p,(n-m)*q,k,j);break;case 0:var r=m,s=n,t=k,u=j,v=(A=l).Ea;0==r&&(cw(o,p,null,null,1,t,u),T(o,p+1,0,0,v-1,t,u+1),p+=v,u+=v,++r);for(var w=1<<A.b,x=w-1,y=L(v,A.b),z=A.K,A=A.w+(r>>A.b)*y;r<s;){var B=z,C=A,D=1;for(cx(o,p,t,u-v,1,t,u);D<v;){var E=(D&~x)+w;E>v&&(E=v),(0,cD[B[C++]>>8&15])(o,p+ +D,t,u+D-v,E-D,t,u+D),D=E}p+=v,u+=v,++r&x||(A+=y)}n!=l.nc&&d(k,j-q,k,j+(n-m-1)*q,q);break;case 1:for(q=o,s=p,v=(o=l.Ea)-(u=o&~(t=(p=1<<l.b)-1)),r=L(o,l.b),w=l.K,l=l.w+(m>>l.b)*r;m<n;){for(x=w,y=l,z=new O,A=s+u,B=s+o;s<A;)V(x[y++],z),cE(z,q,s,p,k,j),s+=p,j+=p;s<B&&(V(x[y++],z),cE(z,q,s,v,k,j),s+=v,j+=v),++m&t||(l+=r)}break;case 3:if(o==k&&p==j&&0<l.b){for(s=k,o=q=j+(n-m)*q-(u=(n-m)*L(l.Ea,l.b)),p=k,t=j,r=[],u=(v=u)-1;0<=u;--u)r[u]=p[t+u];for(u=v-1;0<=u;--u)s[o+u]=r[u];cz(l,m,n,k,q,k,j)}else cz(l,m,n,o,p,k,j)}j=f,k=e}k!=e&&d(f,e,j,k,h)}function aw(a,c){var d=a.V,e=a.Ba+a.c*a.C,f=c-a.C;if(b(c<=a.l.o),b(16>=f),0<f){var g=a.l,h=a.Ta,i=a.Ua,j=g.width;if(av(a,f,d,e),f=i=[i],b((d=a.C)<(e=c)),b(g.v<g.va),e>g.o&&(e=g.o),d<g.j){var k=g.j-d;d=g.j,f[0]+=k*j}if(d>=e?d=0:(f[0]+=4*g.v,g.ka=d-g.j,g.U=g.va-g.v,g.T=e-d,d=1),d){if(i=i[0],11>(d=a.ca).S){var l=d.f.RGBA,m=(e=d.S,f=g.U,g=g.T,k=l.eb,l.A),n=g;for(l=l.fb+a.Ma*l.A;0<n--;){var o=i,p=f,q=k,r=l;switch(e){case dd:cF(h,o,p,q,r);break;case de:cG(h,o,p,q,r);break;case dk:cG(h,o,p,q,r),c0(q,r,0,p,1,0);break;case df:cJ(h,o,p,q,r);break;case dg:ab(h,o,p,q,r,1);break;case dl:ab(h,o,p,q,r,1),c0(q,r,0,p,1,0);break;case dh:ab(h,o,p,q,r,0);break;case dm:ab(h,o,p,q,r,0),c0(q,r,1,p,1,0);break;case di:cH(h,o,p,q,r);break;case dn:cH(h,o,p,q,r),c1(q,r,p,1,0);break;case dj:cI(h,o,p,q,r);break;default:b(0)}i+=j,l+=m}a.Ma+=g}else alert("todo:EmitRescaledRowsYUVA");b(a.Ma<=d.height)}}a.C=c,b(a.C<=a.i)}function ax(a){var b;if(0<a.ua)return 0;for(b=0;b<a.Wb;++b){var c=a.Ya[b].G,d=a.Ya[b].H;if(0<c[1][d[1]+0].g||0<c[2][d[2]+0].g||0<c[3][d[3]+0].g)return 0}return 1}function ay(a,c,d,e,f,g){if(0!=a.Z){var h=a.qd,i=a.rd;for(b(null!=dM[a.Z]);c<d;++c)dM[a.Z](h,i,e,f,e,f,g),h=e,i=f,f+=g;a.qd=h,a.rd=i}}function az(a,c){var d=a.l.ma,e=0==d.Z||1==d.Z?a.l.j:a.C;if(e=a.C<e?e:a.C,b(c<=a.l.o),c>e){var f=a.l.width,g=d.ca,h=d.tb+f*e,i=a.V,j=a.Ba+a.c*e,k=a.gc;b(1==a.ab),b(3==k[0].hc),cB(k[0],e,c,i,j,g,h),ay(d,e,c,g,h,f)}a.C=a.Ma=c}function aA(a,c,d,e,f,g,h){var i=a.$/e,j=a.$%e,k=a.m,l=a.s,m=d+a.$,n=m;f=d+e*f;var o=d+e*g,p=280+l.ua,q=a.Pb?i:0x1000000,r=0<l.ua?l.Wa:null,s=l.wc,t=m<o?au(l,j,i):null;b(a.C<g),b(o<=f);var u=!1;a:for(;;){for(;u||m<o;){var v=0;if(i>=q){var A=m-d;b((q=a).Pb),q.wd=q.m,q.xd=A,0<q.s.ua&&I(q.s.Wa,q.s.vb),q=i+dw}if(j&s||(t=au(l,j,i)),b(null!=t),t.Qb&&(c[m]=t.qb,u=!0),!u)if(z(k),t.jc){v=k,A=c;var B=m,C=t.pd[w(v)&cq-1];b(t.jc),256>C.g?(y(v,v.u+C.g),A[B]=C.value,v=0):(y(v,v.u+C.g-256),b(256<=C.value),v=C.value),0==v&&(u=!0)}else v=as(t.G[0],t.H[0],k);if(k.h)break;if(u||256>v){if(!u)if(t.nd)c[m]=(t.qb|v<<8)>>>0;else{if(z(k),u=as(t.G[1],t.H[1],k),z(k),A=as(t.G[2],t.H[2],k),B=as(t.G[3],t.H[3],k),k.h)break;c[m]=(B<<24|u<<16|v<<8|A)>>>0}if(u=!1,++m,++j>=e&&(j=0,++i,null!=h&&i<=g&&!(i%16)&&h(a,i),null!=r))for(;n<m;)v=c[n++],r.X[(0x1e35a7bd*v|0)>>>r.Mb]=v}else if(280>v){if(v=aq(v-256,k),A=as(t.G[4],t.H[4],k),z(k),A=ar(e,A=aq(A,k)),k.h)break;if(m-d<A||f-m<v)break a;for(B=0;B<v;++B)c[m+B]=c[m+B-A];for(m+=v,j+=v;j>=e;)j-=e,++i,null!=h&&i<=g&&!(i%16)&&h(a,i);if(b(m<=f),j&s&&(t=au(l,j,i)),null!=r)for(;n<m;)v=c[n++],r.X[(0x1e35a7bd*v|0)>>>r.Mb]=v}else{if(!(v<p))break a;for(u=v-280,b(null!=r);n<m;)v=c[n++],r.X[(0x1e35a7bd*v|0)>>>r.Mb]=v;v=m,b(!(u>>>(A=r).Xa)),c[v]=A.X[u],u=!0}u||b(k.h==x(k))}if(a.Pb&&k.h&&m<f)b(a.m.h),a.a=5,a.m=a.wd,a.$=a.xd,0<a.s.ua&&I(a.s.vb,a.s.Wa);else{if(k.h)break;null!=h&&h(a,i>g?g:i),a.a=0,a.$=m-d}return 1}return a.a=3,0}function aB(a){b(null!=a),a.vc=null,a.yc=null,a.Ya=null;var c=a.Wa;null!=c&&(c.X=null),a.vb=null,b(null!=a)}function aC(){var b=new bX;return null==b?null:(b.a=0,b.xb=dL,ac("Predictor","VP8LPredictors"),ac("Predictor","VP8LPredictors_C"),ac("PredictorAdd","VP8LPredictorsAdd"),ac("PredictorAdd","VP8LPredictorsAdd_C"),cy=U,cE=W,cF=Y,cG=Z,cH=$,cI=_,cJ=aa,a.VP8LMapColor32b=cA,a.VP8LMapColor8b=cC,b)}function aD(a,c,d,h,i){for(var j=1,m=[a],o=[c],p=h.m,q=h.s,r=null,s=0;;){if(d)for(;j&&t(p,1);){var u=m,v=o,x=1,A=h.m,B=h.gc[h.ab],C=t(A,2);if(h.Oc&1<<C)j=0;else{switch(h.Oc|=1<<C,B.hc=C,B.Ea=u[0],B.nc=v[0],B.K=[null],++h.ab,b(4>=h.ab),C){case 0:case 1:B.b=t(A,3)+2,x=aD(L(B.Ea,B.b),L(B.nc,B.b),0,h,B.K),B.K=B.K[0];break;case 3:var D,E=t(A,8)+1,F=16<E?0:4<E?1:2<E?2:3;if(u[0]=L(B.Ea,F),B.b=F,D=x=aD(E,1,0,h,B.K)){var G,I=1<<(8>>B.b),J=f(I);if(null==J)D=0;else{var K=B.K[0],N=B.w;for(J[0]=B.K[0][0],G=1;G<+E;++G)J[G]=M(K[N+G],J[G-1]);for(;G<4*I;++G)J[G]=0;B.K[0]=null,B.K[0]=J,D=1}}x=D;break;case 2:break;default:b(0)}j=x}}if(m=m[0],o=o[0],j&&t(p,1)&&!(j=1<=(s=t(p,4))&&11>=s)){h.a=3;break}if(O=j)b:{var O,P,Q,R,S=m,T=o,U=s,V=h.m,W=h.s,X=[null],Y=1,Z=0,$=dv[U];c:for(;;){if(d&&t(V,1)){var _=t(V,3)+2,aa=L(S,_),ab=L(T,_),ac=aa*ab;if(!aD(aa,ab,0,h,X))break;for(X=X[0],W.xc=_,P=0;P<ac;++P){var ad=X[P]>>8&65535;X[P]=ad,ad>=Y&&(Y=ad+1)}}if(V.h)break;for(Q=0;5>Q;++Q){var ae=dr[Q];!Q&&0<U&&(ae+=1<<U),Z<ae&&(Z=ae)}var af=g(Y*$,l),ag=Y,ah=g(ag,n);if(null==ah)var ai=null;else b(65536>=ag),ai=ah;var aj=f(Z);if(null==ai||null==aj||null==af){h.a=1;break}for(P=R=0;P<Y;++P){var ak,al=ai[P],am=al.G,an=al.H,ao=0,ap=1,aq=0;for(Q=0;5>Q;++Q){ae=dr[Q],am[Q]=af,an[Q]=R,!Q&&0<U&&(ae+=1<<U);d:{var ar,as=ae,au=R,av=0,aw=h.m,ax=t(aw,1);if(e(aj,0,0,as),ax){var ay=t(aw,1)+1,az=t(aw,1),aC=t(aw,0==az?1:8);aj[aC]=1,2==ay&&(aj[aC=t(aw,8)]=1);var aE=1}else{var aF=f(19),aG=t(aw,4)+4;if(19<aG){h.a=3;var aH=0;break d}for(ar=0;ar<aG;++ar)aF[dt[ar]]=t(aw,3);var aI=void 0,aJ=void 0,aK=0,aL=h.m,aM=8,aN=g(128,l);e:for(;k(aN,0,7,aF,19);){if(t(aL,1)){var aO=2+2*t(aL,3);if((aI=2+t(aL,aO))>as)break}else aI=as;for(aJ=0;aJ<as&&aI--;){z(aL);var aP=aN[0+(127&w(aL))];y(aL,aL.u+aP.g);var aQ=aP.value;if(16>aQ)aj[aJ++]=aQ,0!=aQ&&(aM=aQ);else{var aR=16==aQ,aS=aQ-16,aT=dq[aS],aU=t(aL,dp[aS])+aT;if(aJ+aU>as)break e;for(var aV=aR?aM:0;0<aU--;)aj[aJ++]=aV}}aK=1;break}aK||(h.a=3),aE=aK}(aE=aE&&!aw.h)&&(av=k(af,au,8,aj,as)),aE&&0!=av?aH=av:(h.a=3,aH=0)}if(0==aH)break c;if(ap&&1==ds[Q]&&(ap=0==af[R].g),ao+=af[R].g,R+=aH,3>=Q){var aW,aX=aj[0];for(aW=1;aW<ae;++aW)aj[aW]>aX&&(aX=aj[aW]);aq+=aX}}if(al.nd=ap,al.Qb=0,ap&&(al.qb=(am[3][an[3]+0].value<<24|am[1][an[1]+0].value<<16|am[2][an[2]+0].value)>>>0,0==ao&&256>am[0][an[0]+0].value&&(al.Qb=1,al.qb+=am[0][an[0]+0].value<<8)),al.jc=!al.Qb&&6>aq,al.jc)for(ak=0;ak<cq;++ak){var aY=ak,aZ=al.pd[aY],a$=al.G[0][al.H[0]+aY];256<=a$.value?(aZ.g=a$.g+256,aZ.value=a$.value):(aZ.g=0,aZ.value=0,aY>>=at(a$,8,aZ),aY>>=at(al.G[1][al.H[1]+aY],16,aZ),aY>>=at(al.G[2][al.H[2]+aY],0,aZ),at(al.G[3][al.H[3]+aY],24,aZ))}}W.vc=X,W.Wb=Y,W.Ya=ai,W.yc=af,O=1;break b}O=0}if(!(j=O)){h.a=3;break}if(0<s){if(q.ua=1<<s,!H(q.Wa,s)){h.a=1,j=0;break}}else q.ua=0;var a_=m,a0=o,a1=h.s,a2=a1.xc;if(h.c=a_,h.i=a0,a1.md=L(a_,a2),a1.wc=0==a2?-1:(1<<a2)-1,d){h.xb=dK;break}if(null==(r=f(m*o))){h.a=1,j=0;break}j=(j=aA(h,r,0,m,o,o,null))&&!p.h;break}return j?(null!=i?i[0]=r:(b(null==r),b(d)),h.$=0,d||aB(q)):aB(q),j}function aE(a,c){var d=a.c*a.i;return b(a.c<=c),a.V=f(d+c+16*c),null==a.V?(a.Ta=null,a.Ua=0,a.a=1,0):(a.Ta=a.V,a.Ua=a.Ba+d+c,1)}function aF(a,c){var d=a.C,e=c-d,f=a.V,g=a.Ba+a.c*d;for(b(c<=a.l.o);0<e;){var h=16<e?16:e,i=a.l.ma,j=a.l.width,k=j*h,l=i.ca,m=i.tb+j*d,n=a.Ta,o=a.Ua;av(a,h,f,g),c3(n,o,l,m,k),ay(i,d,d+h,l,m,j),e-=h,f+=h*a.c,d+=h}b(d==c),a.C=a.Ma=c}function aG(){this.ub=this.yd=this.td=this.Rb=0}function aH(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function aI(){this.Fb=this.Bb=this.Cb=0,this.Zb=f(4),this.Lb=f(4)}function aJ(){var a;this.Yb=(function a(b,c,d){for(var e=d[c],f=0;f<e&&(b.push(d.length>c+1?[]:0),!(d.length<c+1));f++)a(b[f],c+1,d)}(a=[],0,[3,11]),a)}function aK(){this.jb=f(3),this.Wc=h([4,8],aJ),this.Xc=h([4,17],aJ)}function aL(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new f(4),this.od=new f(4)}function aM(){this.ld=this.La=this.dd=this.tc=0}function aN(){this.Na=this.la=0}function aO(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function aP(){this.ad=f(384),this.Za=0,this.Ob=f(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function aQ(){this.uc=this.M=this.Nb=0,this.wa=Array(new aM),this.Y=0,this.ya=Array(new aP),this.aa=0,this.l=new aU}function aR(){this.y=f(16),this.f=f(8),this.ea=f(8)}function aS(){this.cb=this.a=0,this.sc="",this.m=new u,this.Od=new aG,this.Kc=new aH,this.ed=new aL,this.Qa=new aI,this.Ic=this.$c=this.Aa=0,this.D=new aQ,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=g(8,u),this.ia=0,this.pb=g(4,aO),this.Pa=new aK,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new aR),this.Hd=0,this.rb=Array(new aN),this.sb=0,this.wa=Array(new aM),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new aP),this.L=this.aa=0,this.gd=h([4,2],aM),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function aT(a,b){return 0>a?0:a>b?b:a}function aU(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function aV(){var a=new aS;return null!=a&&(a.a=0,a.sc="OK",a.cb=0,a.Xb=0,dz||(dz=aZ)),a}function aW(a,b,c){return 0==a.a&&(a.a=b,a.sc=c,a.cb=0),0}function aX(a,b,c){return 3<=c&&157==a[b+0]&&1==a[b+1]&&42==a[b+2]}function aY(a,c){if(null==a)return 0;if(a.a=0,a.sc="OK",null==c)return aW(a,2,"null VP8Io passed to VP8GetHeaders()");var d=c.data,f=c.w,g=c.ha;if(4>g)return aW(a,7,"Truncated header.");var h=d[f+0]|d[f+1]<<8|d[f+2]<<16,i=a.Od;if(i.Rb=!(1&h),i.td=h>>1&7,i.yd=h>>4&1,i.ub=h>>5,3<i.td)return aW(a,3,"Incorrect keyframe parameters.");if(!i.yd)return aW(a,4,"Frame not displayable.");f+=3,g-=3;var j=a.Kc;if(i.Rb){if(7>g)return aW(a,7,"cannot parse picture header");if(!aX(d,f,g))return aW(a,3,"Bad code word");j.c=16383&(d[f+4]<<8|d[f+3]),j.Td=d[f+4]>>6,j.i=16383&(d[f+6]<<8|d[f+5]),j.Ud=d[f+6]>>6,f+=7,g-=7,a.za=j.c+15>>4,a.Ub=j.i+15>>4,c.width=j.c,c.height=j.i,c.Da=0,c.j=0,c.v=0,c.va=c.width,c.o=c.height,c.da=0,c.ib=c.width,c.hb=c.height,c.U=c.width,c.T=c.height,e((h=a.Pa).jb,0,255,h.jb.length),b(null!=(h=a.Qa)),h.Cb=0,h.Bb=0,h.Fb=1,e(h.Zb,0,0,h.Zb.length),e(h.Lb,0,0,h.Lb)}if(i.ub>g)return aW(a,7,"bad partition length");o(h=a.m,d,f,i.ub),f+=i.ub,g-=i.ub,i.Rb&&(j.Ld=B(h),j.Kd=B(h)),j=a.Qa;var k,l=a.Pa;if(b(null!=h),b(null!=j),j.Cb=B(h),j.Cb){if(j.Bb=B(h),B(h)){for(j.Fb=B(h),k=0;4>k;++k)j.Zb[k]=B(h)?q(h,7):0;for(k=0;4>k;++k)j.Lb[k]=B(h)?q(h,6):0}if(j.Bb)for(k=0;3>k;++k)l.jb[k]=B(h)?p(h,8):255}else j.Bb=0;if(h.Ka)return aW(a,3,"cannot parse segment header");if((j=a.ed).zd=B(h),j.Tb=p(h,6),j.wb=p(h,3),j.Pc=B(h),j.Pc&&B(h)){for(l=0;4>l;++l)B(h)&&(j.vd[l]=q(h,6));for(l=0;4>l;++l)B(h)&&(j.od[l]=q(h,6))}if(a.L=0==j.Tb?0:j.zd?1:2,h.Ka)return aW(a,3,"cannot parse filter header");var m=g;if(g=k=f,f=k+m,j=m,a.Xb=(1<<p(a.m,2))-1,m<3*(l=a.Xb))d=7;else{for(k+=3*l,j-=3*l,m=0;m<l;++m){var n=d[g+0]|d[g+1]<<8|d[g+2]<<16;n>j&&(n=j),o(a.Jc[+m],d,k,n),k+=n,j-=n,g+=3}o(a.Jc[+l],d,k,j),d=k<f?0:5}if(0!=d)return aW(a,d,"cannot parse partitions");for(d=p(k=a.m,7),g=B(k)?q(k,4):0,f=B(k)?q(k,4):0,j=B(k)?q(k,4):0,l=B(k)?q(k,4):0,k=B(k)?q(k,4):0,m=a.Qa,n=0;4>n;++n){if(m.Cb){var r=m.Zb[n];m.Fb||(r+=d)}else{if(0<n){a.pb[n]=a.pb[0];continue}r=d}var s=a.pb[n];s.Sc[0]=dx[aT(r+g,127)],s.Sc[1]=dy[aT(r+0,127)],s.Eb[0]=2*dx[aT(r+f,127)],s.Eb[1]=101581*dy[aT(r+j,127)]>>16,8>s.Eb[1]&&(s.Eb[1]=8),s.Qc[0]=dx[aT(r+l,117)],s.Qc[1]=dy[aT(r+k,127)],s.lc=r+k}if(!i.Rb)return aW(a,4,"Not a key frame.");for(B(h),i=a.Pa,d=0;4>d;++d){for(g=0;8>g;++g)for(f=0;3>f;++f)for(j=0;11>j;++j)l=C(h,dF[d][g][f][j])?p(h,8):dD[d][g][f][j],i.Wc[d][g].Yb[f][j]=l;for(g=0;17>g;++g)i.Xc[d][g]=i.Wc[d][dG[g]]}return a.kc=B(h),a.kc&&(a.Bd=p(h,8)),a.cb=1}function aZ(a,b,c,d,e,f,g){var h=b[e].Yb[c];for(c=0;16>e;++e){if(!C(a,h[c+0]))return e;for(;!C(a,h[c+1]);)if(h=b[++e].Yb[0],c=0,16==e)return 16;var i=b[e+1].Yb;if(C(a,h[c+2])){var j=a,k=0;if(C(j,(m=h)[(l=c)+3]))if(C(j,m[l+6])){for(h=0,l=2*(k=C(j,m[l+8]))+(m=C(j,m[l+9+k])),k=0,m=dA[l];m[h];++h)k+=k+C(j,m[h]);k+=3+(8<<l)}else k=C(j,m[l+7])?7+2*C(j,165)+C(j,145):5+C(j,159);else k=C(j,m[l+4])?3+C(j,m[l+5]):2;h=i[2]}else k=1,h=i[1];i=g+dB[e],0>(j=a).b&&A(j);var l,m=j.b,n=(l=j.Ca>>1)-(j.I>>m)>>31;--j.b,j.Ca+=n,j.Ca|=1,j.I-=(l+1&n)<<m,f[i]=((k^n)-n)*d[(0<e)+0]}return 16}function a$(a){var b=a.rb[a.sb-1];b.la=0,b.Na=0,e(a.zc,0,0,a.zc.length),a.ja=0}function a_(a,b,c,d,e){e=a[b+c+32*d]+(e>>3),a[b+c+32*d]=-256&e?0>e?0:255:e}function a0(a,b,c,d,e,f){a_(a,b,0,c,d+e),a_(a,b,1,c,d+f),a_(a,b,2,c,d-f),a_(a,b,3,c,d-e)}function a1(a){return(20091*a>>16)+a}function a2(a,b,c,d){var e,g=0,h=f(16);for(e=0;4>e;++e){var i=a[b+0]+a[b+8],j=a[b+0]-a[b+8],k=(35468*a[b+4]>>16)-a1(a[b+12]),l=a1(a[b+4])+(35468*a[b+12]>>16);h[g+0]=i+l,h[g+1]=j+k,h[g+2]=j-k,h[g+3]=i-l,g+=4,b++}for(e=g=0;4>e;++e)i=(a=h[g+0]+4)+h[g+8],j=a-h[g+8],k=(35468*h[g+4]>>16)-a1(h[g+12]),a_(c,d,0,0,i+(l=a1(h[g+4])+(35468*h[g+12]>>16))),a_(c,d,1,0,j+k),a_(c,d,2,0,j-k),a_(c,d,3,0,i-l),g++,d+=32}function a3(a,b,c,d){var e=a[b+0]+4,f=35468*a[b+4]>>16,g=a1(a[b+4]),h=35468*a[b+1]>>16;a0(c,d,0,e+g,a=a1(a[b+1]),h),a0(c,d,1,e+f,a,h),a0(c,d,2,e-f,a,h),a0(c,d,3,e-g,a,h)}function a4(a,b,c,d,e){a2(a,b,c,d),e&&a2(a,b+16,c,d+4)}function a5(a,b,c,d){cL(a,b+0,c,d,1),cL(a,b+32,c,d+128,1)}function a6(a,b,c,d){var e;for(a=a[b+0]+4,e=0;4>e;++e)for(b=0;4>b;++b)a_(c,d,b,e,a)}function a7(a,b,c,d){a[b+0]&&cO(a,b+0,c,d),a[b+16]&&cO(a,b+16,c,d+4),a[b+32]&&cO(a,b+32,c,d+128),a[b+48]&&cO(a,b+48,c,d+128+4)}function a8(a,b,c,d){var e,g=f(16);for(e=0;4>e;++e){var h=a[b+0+e]+a[b+12+e],i=a[b+4+e]+a[b+8+e],j=a[b+4+e]-a[b+8+e],k=a[b+0+e]-a[b+12+e];g[0+e]=h+i,g[8+e]=h-i,g[4+e]=k+j,g[12+e]=k-j}for(e=0;4>e;++e)h=(a=g[0+4*e]+3)+g[3+4*e],i=g[1+4*e]+g[2+4*e],j=g[1+4*e]-g[2+4*e],k=a-g[3+4*e],c[d+0]=h+i>>3,c[d+16]=k+j>>3,c[d+32]=h-i>>3,c[d+48]=k-j>>3,d+=64}function a9(a,b,c){var d,e=b-32,f=255-a[e-1];for(d=0;d<c;++d){var g,h=f+a[b-1];for(g=0;g<c;++g)a[b+g]=db[h+a[e+g]];b+=32}}function ba(a,b){a9(a,b,4)}function bb(a,b){a9(a,b,8)}function bc(a,b){a9(a,b,16)}function bd(a,b){var c;for(c=0;16>c;++c)d(a,b+32*c,a,b-32,16)}function be(a,b){var c;for(c=16;0<c;--c)e(a,b,a[b-1],16),b+=32}function bf(a,b,c){var d;for(d=0;16>d;++d)e(b,c+32*d,a,16)}function bg(a,b){var c,d=16;for(c=0;16>c;++c)d+=a[b-1+32*c]+a[b+c-32];bf(d>>5,a,b)}function bh(a,b){var c,d=8;for(c=0;16>c;++c)d+=a[b-1+32*c];bf(d>>4,a,b)}function bi(a,b){var c,d=8;for(c=0;16>c;++c)d+=a[b+c-32];bf(d>>4,a,b)}function bj(a,b){bf(128,a,b)}function bk(a,b,c){return a+2*b+c+2>>2}function bl(a,b){var c,e=b-32;for(c=0,e=new Uint8Array([bk(a[e-1],a[e+0],a[e+1]),bk(a[e+0],a[e+1],a[e+2]),bk(a[e+1],a[e+2],a[e+3]),bk(a[e+2],a[e+3],a[e+4])]);4>c;++c)d(a,b+32*c,e,0,e.length)}function bm(a,b){var c=a[b-1],d=a[b-1+32],e=a[b-1+64],f=a[b-1+96];D(a,b+0,0x1010101*bk(a[b-1-32],c,d)),D(a,b+32,0x1010101*bk(c,d,e)),D(a,b+64,0x1010101*bk(d,e,f)),D(a,b+96,0x1010101*bk(e,f,f))}function bn(a,b){var c,d=4;for(c=0;4>c;++c)d+=a[b+c-32]+a[b-1+32*c];for(d>>=3,c=0;4>c;++c)e(a,b+32*c,d,4)}function bo(a,b){var c=a[b-1+0],d=a[b-1+32],e=a[b-1+64],f=a[b-1-32],g=a[b+0-32],h=a[b+1-32],i=a[b+2-32],j=a[b+3-32];a[b+0+96]=bk(d,e,a[b-1+96]),a[b+1+96]=a[b+0+64]=bk(c,d,e),a[b+2+96]=a[b+1+64]=a[b+0+32]=bk(f,c,d),a[b+3+96]=a[b+2+64]=a[b+1+32]=a[b+0+0]=bk(g,f,c),a[b+3+64]=a[b+2+32]=a[b+1+0]=bk(h,g,f),a[b+3+32]=a[b+2+0]=bk(i,h,g),a[b+3+0]=bk(j,i,h)}function bp(a,b){var c=a[b+1-32],d=a[b+2-32],e=a[b+3-32],f=a[b+4-32],g=a[b+5-32],h=a[b+6-32],i=a[b+7-32];a[b+0+0]=bk(a[b+0-32],c,d),a[b+1+0]=a[b+0+32]=bk(c,d,e),a[b+2+0]=a[b+1+32]=a[b+0+64]=bk(d,e,f),a[b+3+0]=a[b+2+32]=a[b+1+64]=a[b+0+96]=bk(e,f,g),a[b+3+32]=a[b+2+64]=a[b+1+96]=bk(f,g,h),a[b+3+64]=a[b+2+96]=bk(g,h,i),a[b+3+96]=bk(h,i,i)}function bq(a,b){var c=a[b-1+0],d=a[b-1+32],e=a[b-1+64],f=a[b-1-32],g=a[b+0-32],h=a[b+1-32],i=a[b+2-32],j=a[b+3-32];a[b+0+0]=a[b+1+64]=f+g+1>>1,a[b+1+0]=a[b+2+64]=g+h+1>>1,a[b+2+0]=a[b+3+64]=h+i+1>>1,a[b+3+0]=i+j+1>>1,a[b+0+96]=bk(e,d,c),a[b+0+64]=bk(d,c,f),a[b+0+32]=a[b+1+96]=bk(c,f,g),a[b+1+32]=a[b+2+96]=bk(f,g,h),a[b+2+32]=a[b+3+96]=bk(g,h,i),a[b+3+32]=bk(h,i,j)}function br(a,b){var c=a[b+0-32],d=a[b+1-32],e=a[b+2-32],f=a[b+3-32],g=a[b+4-32],h=a[b+5-32],i=a[b+6-32],j=a[b+7-32];a[b+0+0]=c+d+1>>1,a[b+1+0]=a[b+0+64]=d+e+1>>1,a[b+2+0]=a[b+1+64]=e+f+1>>1,a[b+3+0]=a[b+2+64]=f+g+1>>1,a[b+0+32]=bk(c,d,e),a[b+1+32]=a[b+0+96]=bk(d,e,f),a[b+2+32]=a[b+1+96]=bk(e,f,g),a[b+3+32]=a[b+2+96]=bk(f,g,h),a[b+3+64]=bk(g,h,i),a[b+3+96]=bk(h,i,j)}function bs(a,b){var c=a[b-1+0],d=a[b-1+32],e=a[b-1+64],f=a[b-1+96];a[b+0+0]=c+d+1>>1,a[b+2+0]=a[b+0+32]=d+e+1>>1,a[b+2+32]=a[b+0+64]=e+f+1>>1,a[b+1+0]=bk(c,d,e),a[b+3+0]=a[b+1+32]=bk(d,e,f),a[b+3+32]=a[b+1+64]=bk(e,f,f),a[b+3+64]=a[b+2+64]=a[b+0+96]=a[b+1+96]=a[b+2+96]=a[b+3+96]=f}function bt(a,b){var c=a[b-1+0],d=a[b-1+32],e=a[b-1+64],f=a[b-1+96],g=a[b-1-32],h=a[b+0-32],i=a[b+1-32],j=a[b+2-32];a[b+0+0]=a[b+2+32]=c+g+1>>1,a[b+0+32]=a[b+2+64]=d+c+1>>1,a[b+0+64]=a[b+2+96]=e+d+1>>1,a[b+0+96]=f+e+1>>1,a[b+3+0]=bk(h,i,j),a[b+2+0]=bk(g,h,i),a[b+1+0]=a[b+3+32]=bk(c,g,h),a[b+1+32]=a[b+3+64]=bk(d,c,g),a[b+1+64]=a[b+3+96]=bk(e,d,c),a[b+1+96]=bk(f,e,d)}function bu(a,b){var c;for(c=0;8>c;++c)d(a,b+32*c,a,b-32,8)}function bv(a,b){var c;for(c=0;8>c;++c)e(a,b,a[b-1],8),b+=32}function bw(a,b,c){var d;for(d=0;8>d;++d)e(b,c+32*d,a,8)}function bx(a,b){var c,d=8;for(c=0;8>c;++c)d+=a[b+c-32]+a[b-1+32*c];bw(d>>4,a,b)}function by(a,b){var c,d=4;for(c=0;8>c;++c)d+=a[b+c-32];bw(d>>3,a,b)}function bz(a,b){var c,d=4;for(c=0;8>c;++c)d+=a[b-1+32*c];bw(d>>3,a,b)}function bA(a,b){bw(128,a,b)}function bB(a,b,c){var d=a[b-c],e=a[b+0],f=3*(e-d)+c9[1020+a[b-2*c]-a[b+c]],g=da[112+(f+4>>3)];a[b-c]=db[255+d+da[112+(f+3>>3)]],a[b+0]=db[255+e-g]}function bC(a,b,c,d){var e=a[b+0],f=a[b+c];return dc[255+a[b-2*c]-a[b-c]]>d||dc[255+f-e]>d}function bD(a,b,c,d){return 4*dc[255+a[b-c]-a[b+0]]+dc[255+a[b-2*c]-a[b+c]]<=d}function bE(a,b,c,d,e){var f=a[b-3*c],g=a[b-2*c],h=a[b-c],i=a[b+0],j=a[b+c],k=a[b+2*c],l=a[b+3*c];return 4*dc[255+h-i]+dc[255+g-j]>d?0:dc[255+a[b-4*c]-f]<=e&&dc[255+f-g]<=e&&dc[255+g-h]<=e&&dc[255+l-k]<=e&&dc[255+k-j]<=e&&dc[255+j-i]<=e}function bF(a,b,c,d){var e=2*d+1;for(d=0;16>d;++d)bD(a,b+d,c,e)&&bB(a,b+d,c)}function bG(a,b,c,d){var e=2*d+1;for(d=0;16>d;++d)bD(a,b+d*c,1,e)&&bB(a,b+d*c,1)}function bH(a,b,c,d){var e;for(e=3;0<e;--e)bF(a,b+=4*c,c,d)}function bI(a,b,c,d){var e;for(e=3;0<e;--e)bG(a,b+=4,c,d)}function bJ(a,b,c,d,e,f,g,h){for(f=2*f+1;0<e--;){if(bE(a,b,c,f,g))if(bC(a,b,c,h))bB(a,b,c);else{var i=b,j=a[i-2*c],k=a[i-c],l=a[i+0],m=a[i+c],n=a[i+2*c],o=27*(q=c9[1020+3*(l-k)+c9[1020+j-m]])+63>>7,p=18*q+63>>7,q=9*q+63>>7;a[i-3*c]=db[255+a[i-3*c]+q],a[i-2*c]=db[255+j+p],a[i-c]=db[255+k+o],a[i+0]=db[255+l-o],a[i+c]=db[255+m-p],a[i+2*c]=db[255+n-q]}b+=d}}function bK(a,b,c,d,e,f,g,h){for(f=2*f+1;0<e--;){if(bE(a,b,c,f,g))if(bC(a,b,c,h))bB(a,b,c);else{var i=b,j=a[i-c],k=a[i+0],l=a[i+c],m=da[112+((n=3*(k-j))+4>>3)],n=da[112+(n+3>>3)],o=m+1>>1;a[i-2*c]=db[255+a[i-2*c]+o],a[i-c]=db[255+j+n],a[i+0]=db[255+k-m],a[i+c]=db[255+l-o]}b+=d}}function bL(a,b,c,d,e,f){bJ(a,b,c,1,16,d,e,f)}function bM(a,b,c,d,e,f){bJ(a,b,1,c,16,d,e,f)}function bN(a,b,c,d,e,f){var g;for(g=3;0<g;--g)bK(a,b+=4*c,c,1,16,d,e,f)}function bO(a,b,c,d,e,f){var g;for(g=3;0<g;--g)bK(a,b+=4,1,c,16,d,e,f)}function bP(a,b,c,d,e,f,g,h){bJ(a,b,e,1,8,f,g,h),bJ(c,d,e,1,8,f,g,h)}function bQ(a,b,c,d,e,f,g,h){bJ(a,b,1,e,8,f,g,h),bJ(c,d,1,e,8,f,g,h)}function bR(a,b,c,d,e,f,g,h){bK(a,b+4*e,e,1,8,f,g,h),bK(c,d+4*e,e,1,8,f,g,h)}function bS(a,b,c,d,e,f,g,h){bK(a,b+4,1,e,8,f,g,h),bK(c,d+4,1,e,8,f,g,h)}function bT(){this.ba=new ag,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new ai,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function bU(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function bV(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function bW(){this.ua=0,this.Wa=new J,this.vb=new J,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new n,this.yc=new l}function bX(){this.xb=this.a=0,this.l=new aU,this.ca=new ag,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new v,this.Pb=0,this.wd=new v,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new bW,this.ab=0,this.gc=g(4,bV),this.Oc=0}function bY(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new aU,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function bZ(a,b,c,d,e,f,g){for(a=null==a?0:a[b+0],b=0;b<g;++b)e[f+b]=a+c[d+b]&255,a=e[f+b]}function b$(a,b,c,d,e,f,g){var h;if(null==a)bZ(null,null,c,d,e,f,g);else for(h=0;h<g;++h)e[f+h]=a[b+h]+c[d+h]&255}function b_(a,b,c,d,e,f,g){if(null==a)bZ(null,null,c,d,e,f,g);else{var h,i=a[b+0],j=i,k=i;for(h=0;h<g;++h)j=k+(i=a[b+h])-j,k=c[d+h]+(-256&j?0>j?0:255:j)&255,j=i,e[f+h]=k}}function b0(a,b,c,d,e,f){for(;0<e--;){var g,h=b+ +!!c,i=b+3*!c;for(g=0;g<d;++g){var j=a[i+4*g];255!=j&&(j*=32897,a[h+4*g+0]=a[h+4*g+0]*j>>23,a[h+4*g+1]=a[h+4*g+1]*j>>23,a[h+4*g+2]=a[h+4*g+2]*j>>23)}b+=f}}function b1(a,b,c,d,e){for(;0<d--;){var f;for(f=0;f<c;++f){var g=a[b+2*f+0],h=15&(j=a[b+2*f+1]),i=4369*h,j=(240&j|j>>4)*i>>16;a[b+2*f+0]=(240&g|g>>4)*i>>16&240|(15&g|g<<4)*i>>16>>4&15,a[b+2*f+1]=240&j|h}b+=e}}function b2(a,b,c,d,e,f,g,h){var i,j,k=255;for(j=0;j<e;++j){for(i=0;i<d;++i){var l=a[b+i];f[g+4*i]=l,k&=l}b+=c,g+=h}return 255!=k}function b3(a,b,c,d,e){var f;for(f=0;f<e;++f)c[d+f]=a[b+f]>>8}function b4(){c0=b0,c1=b1,c2=b2,c3=b3}function b5(c,d,e){a[c]=function(a,c,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t){var u,v=t-1>>1,w=h[i+0]|j[k+0]<<16,x=l[m+0]|n[o+0]<<16;b(null!=a);var y=3*w+x+131074>>2;for(d(a[c+0],255&y,y>>16,p,q),null!=f&&(y=3*x+w+131074>>2,d(f[g+0],255&y,y>>16,r,s)),u=1;u<=v;++u){var z=h[i+u]|j[k+u]<<16,A=l[m+u]|n[o+u]<<16,B=w+z+x+A+524296,C=B+2*(z+x)>>3;y=C+w>>1,w=(B=B+2*(w+A)>>3)+z>>1,d(a[c+2*u-1],255&y,y>>16,p,q+(2*u-1)*e),d(a[c+2*u-0],255&w,w>>16,p,q+(2*u-0)*e),null!=f&&(y=B+x>>1,w=C+A>>1,d(f[g+2*u-1],255&y,y>>16,r,s+(2*u-1)*e),d(f[g+2*u+0],255&w,w>>16,r,s+(2*u+0)*e)),w=z,x=A}1&t||(y=3*w+x+131074>>2,d(a[c+t-1],255&y,y>>16,p,q+(t-1)*e),null!=f&&(y=3*x+w+131074>>2,d(f[g+t-1],255&y,y>>16,r,s+(t-1)*e)))}}function b6(){dN[dd]=dO,dN[de]=dQ,dN[df]=dP,dN[dg]=dR,dN[dh]=dS,dN[di]=dT,dN[dj]=dU,dN[dk]=dQ,dN[dl]=dR,dN[dm]=dS,dN[dn]=dT}function b7(a){return a&~d$?0>a?0:255:a>>dZ}function b8(a,b){return b7((19077*a>>8)+(26149*b>>8)-14234)}function b9(a,b,c){return b7((19077*a>>8)-(6419*b>>8)-(13320*c>>8)+8708)}function ca(a,b){return b7((19077*a>>8)+(33050*b>>8)-17685)}function cb(a,b,c,d,e){d[e+0]=b8(a,c),d[e+1]=b9(a,b,c),d[e+2]=ca(a,b)}function cc(a,b,c,d,e){d[e+0]=ca(a,b),d[e+1]=b9(a,b,c),d[e+2]=b8(a,c)}function cd(a,b,c,d,e){var f=b9(a,b,c);b=f<<3&224|ca(a,b)>>3,d[e+0]=248&b8(a,c)|f>>5,d[e+1]=b}function ce(a,b,c,d,e){var f=240&ca(a,b)|15;d[e+0]=240&b8(a,c)|b9(a,b,c)>>4,d[e+1]=f}function cf(a,b,c,d,e){d[e+0]=255,cb(a,b,c,d,e+1)}function cg(a,b,c,d,e){cc(a,b,c,d,e),d[e+3]=255}function ch(a,b,c,d,e){cb(a,b,c,d,e),d[e+3]=255}function aT(a,b){return 0>a?0:a>b?b:a}function ci(b,c,d){a[b]=function(a,b,e,f,g,h,i,j,k){for(var l=j+(-2&k)*d;j!=l;)c(a[b+0],e[f+0],g[h+0],i,j),c(a[b+1],e[f+0],g[h+0],i,j+d),b+=2,++f,++h,j+=2*d;1&k&&c(a[b+0],e[f+0],g[h+0],i,j)}}function cj(a,b,c){return 0==c?0==a?0==b?6:5:4*(0==b):c}function ck(a,b,c,d,e){switch(a>>>30){case 3:cL(b,c,d,e,0);break;case 2:cM(b,c,d,e);break;case 1:cO(b,c,d,e)}}function cl(a,b){var c,f,g=b.M,h=b.Nb,i=a.oc,j=a.pc+40,k=a.oc,l=a.pc+584,m=a.oc,n=a.pc+600;for(c=0;16>c;++c)i[j+32*c-1]=129;for(c=0;8>c;++c)k[l+32*c-1]=129,m[n+32*c-1]=129;for(0<g?i[j-1-32]=k[l-1-32]=m[n-1-32]=129:(e(i,j-32-1,127,21),e(k,l-32-1,127,9),e(m,n-32-1,127,9)),f=0;f<a.za;++f){var o=b.ya[b.aa+f];if(0<f){for(c=-1;16>c;++c)d(i,j+32*c-4,i,j+32*c+12,4);for(c=-1;8>c;++c)d(k,l+32*c-4,k,l+32*c+4,4),d(m,n+32*c-4,m,n+32*c+4,4)}var p=a.Gd,q=a.Hd+f,r=o.ad,s=o.Hc;if(0<g&&(d(i,j-32,p[q].y,0,16),d(k,l-32,p[q].f,0,8),d(m,n-32,p[q].ea,0,8)),o.Za){var t=i,u=j-32+16;for(0<g&&(f>=a.za-1?e(t,u,p[q].y[15],4):d(t,u,p[q+1].y,0,4)),c=0;4>c;c++)t[u+128+c]=t[u+256+c]=t[u+384+c]=t[u+0+c];for(c=0;16>c;++c,s<<=2)t=i,u=j+d6[c],dI[o.Ob[c]](t,u),ck(s,r,16*c,t,u)}else if(dH[t=cj(f,g,o.Ob[0])](i,j),0!=s)for(c=0;16>c;++c,s<<=2)ck(s,r,16*c,i,j+d6[c]);for(c=o.Gc,dJ[t=cj(f,g,o.Dd)](k,l),dJ[t](m,n),s=r,t=k,u=l,255&(o=0|c)&&(170&o?cN(s,256,t,u):cP(s,256,t,u)),o=m,s=n,255&(c>>=8)&&(170&c?cN(r,320,o,s):cP(r,320,o,s)),g<a.Ub-1&&(d(p[q].y,0,i,j+480,16),d(p[q].f,0,k,l+224,8),d(p[q].ea,0,m,n+224,8)),c=8*h*a.B,p=a.sa,q=a.ta+16*f+16*h*a.R,r=a.qa,o=a.ra+8*f+c,s=a.Ha,t=a.Ia+8*f+c,c=0;16>c;++c)d(p,q+c*a.R,i,j+32*c,16);for(c=0;8>c;++c)d(r,o+c*a.B,k,l+32*c,8),d(s,t+c*a.B,m,n+32*c,8)}}function cm(a,d,e,f,g,h,i,j,k){var l=[0],m=[0],n=0,o=null!=k?k.kd:0,p=null!=k?k:new bU;if(null==a||12>e)return 7;p.data=a,p.w=d,p.ha=e,d=[d],e=[e],p.gb=[p.gb];a:{var q=d,s=e,t=p.gb;if(b(null!=a),b(null!=s),b(null!=t),t[0]=0,12<=s[0]&&!c(a,q[0],"RIFF")){if(c(a,q[0]+8,"WEBP")){t=3;break a}var u=G(a,q[0]+4);if(12>u||0xfffffff6<u){t=3;break a}if(o&&u>s[0]-8){t=7;break a}t[0]=u,q[0]+=12,s[0]-=12}t=0}if(0!=t)return t;for(u=0<p.gb[0],e=e[0];;){a:{var w=a;s=d,t=e;var x=l,y=m,z=q=[0];if((C=n=[n])[0]=0,8>t[0])t=7;else{if(!c(w,s[0],"VP8X")){if(10!=G(w,s[0]+4)){t=3;break a}if(18>t[0]){t=7;break a}var A=G(w,s[0]+8),B=1+F(w,s[0]+12);if(0x80000000<=B*(w=1+F(w,s[0]+15))){t=3;break a}null!=z&&(z[0]=A),null!=x&&(x[0]=B),null!=y&&(y[0]=w),s[0]+=18,t[0]-=18,C[0]=1}t=0}}if(n=n[0],q=q[0],0!=t)return t;if(s=!!(2&q),!u&&n)return 3;if(null!=h&&(h[0]=!!(16&q)),null!=i&&(i[0]=s),null!=j&&(j[0]=0),i=l[0],q=m[0],n&&s&&null==k){t=0;break}if(4>e){t=7;break}if(u&&n||!u&&!n&&!c(a,d[0],"ALPH")){e=[e],p.na=[p.na],p.P=[p.P],p.Sa=[p.Sa];a:{A=a,t=d,u=e;var C=p.gb;x=p.na,y=p.P,z=p.Sa,B=22,b(null!=A),b(null!=u),w=t[0];var D=u[0];for(b(null!=x),b(null!=z),x[0]=null,y[0]=null,z[0]=0;;){if(t[0]=w,u[0]=D,8>D){t=7;break a}var E=G(A,w+4);if(0xfffffff6<E){t=3;break a}var H=8+E+1&-2;if(B+=H,0<C&&B>C){t=3;break a}if(!c(A,w,"VP8 ")||!c(A,w,"VP8L")){t=0;break a}if(D[0]<H){t=7;break a}c(A,w,"ALPH")||(x[0]=A,y[0]=w+8,z[0]=E),w+=H,D-=H}}if(e=e[0],p.na=p.na[0],p.P=p.P[0],p.Sa=p.Sa[0],0!=t)break}e=[e],p.Ja=[p.Ja],p.xa=[p.xa];a:if(C=a,t=d,u=e,x=p.gb[0],y=p.Ja,z=p.xa,w=!c(C,A=t[0],"VP8 "),B=!c(C,A,"VP8L"),b(null!=C),b(null!=u),b(null!=y),b(null!=z),8>u[0])t=7;else{if(w||B){if(C=G(C,A+4),12<=x&&C>x-12){t=3;break a}if(o&&C>u[0]-8){t=7;break a}y[0]=C,t[0]+=8,u[0]-=8,z[0]=B}else z[0]=5<=u[0]&&47==C[A+0]&&!(C[A+4]>>5),y[0]=u[0];t=0}if(e=e[0],p.Ja=p.Ja[0],p.xa=p.xa[0],d=d[0],0!=t)break;if(0xfffffff6<p.Ja)return 3;if(null==j||s||(j[0]=p.xa?2:1),i=[i],q=[q],p.xa){if(5>e){t=7;break}j=i,o=q,s=h,null==a||5>e?a=0:5<=e&&47==a[d+0]&&!(a[d+4]>>5)?(u=[0],C=[0],x=[0],r(y=new v,a,d,e),ap(y,u,C,x)?(null!=j&&(j[0]=u[0]),null!=o&&(o[0]=C[0]),null!=s&&(s[0]=x[0]),a=1):a=0):a=0}else{if(10>e){t=7;break}j=q,null==a||10>e||!aX(a,d+3,e-3)?a=0:(o=a[d+0]|a[d+1]<<8|a[d+2]<<16,s=16383&(a[d+7]<<8|a[d+6]),a=16383&(a[d+9]<<8|a[d+8]),1&o||3<(o>>1&7)||!(o>>4&1)||o>>5>=p.Ja||!s||!a?a=0:(i&&(i[0]=s),j&&(j[0]=a),a=1))}if(!a||(i=i[0],q=q[0],n&&(l[0]!=i||m[0]!=q)))return 3;null!=k&&(k[0]=p,k.offset=d-k.w,b(0xfffffff6>d-k.w),b(k.offset==k.ha-e));break}return 0==t||7==t&&n&&null==k?(null!=h&&(h[0]|=null!=p.na&&0<p.na.length),null!=f&&(f[0]=i),null!=g&&(g[0]=q),0):t}function cn(a,b,c){var d=b.width,e=b.height,f=0,g=0,h=d,i=e;if(b.Da=null!=a&&0<a.Da,b.Da&&(h=a.cd,i=a.bd,f=a.v,g=a.j,11>c||(f&=-2,g&=-2),0>f||0>g||0>=h||0>=i||f+h>d||g+i>e))return 0;if(b.v=f,b.j=g,b.va=f+h,b.o=g+i,b.U=h,b.T=i,b.da=null!=a&&0<a.da,b.da){if(!K(h,i,c=[a.ib],f=[a.hb]))return 0;b.ib=c[0],b.hb=f[0]}return b.ob=null!=a&&a.ob,b.Kb=null==a||!a.Sd,b.da&&(b.ob=b.ib<3*d/4&&b.hb<3*e/4,b.Kb=0),1}function co(a){if(null==a)return 2;if(11>a.S){var b=a.f.RGBA;b.fb+=(a.height-1)*b.A,b.A=-b.A}else b=a.f.kb,a=a.height,b.O+=(a-1)*b.fa,b.fa=-b.fa,b.N+=(a-1>>1)*b.Ab,b.Ab=-b.Ab,b.W+=(a-1>>1)*b.Db,b.Db=-b.Db,null!=b.F&&(b.J+=(a-1)*b.lb,b.lb=-b.lb);return 0}function cp(a,b,c,d){if(null==d||0>=a||0>=b)return 2;if(null!=c){if(c.Da){var e=c.cd,g=c.bd,h=-2&c.v,i=-2&c.j;if(0>h||0>i||0>=e||0>=g||h+e>a||i+g>b)return 2;a=e,b=g}if(c.da){if(!K(a,b,e=[c.ib],g=[c.hb]))return 2;a=e[0],b=g[0]}}d.width=a,d.height=b;a:{var j=d.width,k=d.height;if(a=d.S,0>=j||0>=k||!(a>=dd&&13>a))a=2;else{if(0>=d.Rd&&null==d.sd){h=g=e=b=0;var l=(i=j*d9[a])*k;if(11>a||(g=(k+1)/2*(b=(j+1)/2),12==a&&(h=(e=j)*k)),null==(k=f(l+2*g+h))){a=1;break a}d.sd=k,11>a?((j=d.f.RGBA).eb=k,j.fb=0,j.A=i,j.size=l):((j=d.f.kb).y=k,j.O=0,j.fa=i,j.Fd=l,j.f=k,j.N=0+l,j.Ab=b,j.Cd=g,j.ea=k,j.W=0+l+g,j.Db=b,j.Ed=g,12==a&&(j.F=k,j.J=0+l+2*g),j.Tc=h,j.lb=e)}if(b=1,e=d.S,g=d.width,h=d.height,e>=dd&&13>e)if(11>e)b&=(i=Math.abs((a=d.f.RGBA).A))*(h-1)+g<=a.size,b&=i>=g*d9[e],b&=null!=a.eb;else{a=d.f.kb,i=(g+1)/2,l=(h+1)/2,j=Math.abs(a.fa),k=Math.abs(a.Ab);var m=Math.abs(a.Db),n=Math.abs(a.lb),o=n*(h-1)+g;b&=j*(h-1)+g<=a.Fd,b&=k*(l-1)+i<=a.Cd,b=(b&=m*(l-1)+i<=a.Ed)&j>=g&k>=i&m>=i&null!=a.y&null!=a.f&null!=a.ea,12==e&&(b&=n>=g,b&=o<=a.Tc,b&=null!=a.F)}else b=0;a=2*!b}}return 0!=a||null!=c&&c.fd&&(a=co(d)),a}var cq=64,cr=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,0xffffff],cs=24,ct=32,cu=8,cv=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];N("Predictor0","PredictorAdd0"),a.Predictor0=function(){return 0xff000000},a.Predictor1=function(a){return a},a.Predictor2=function(a,b,c){return b[c+0]},a.Predictor3=function(a,b,c){return b[c+1]},a.Predictor4=function(a,b,c){return b[c-1]},a.Predictor5=function(a,b,c){return P(P(a,b[c+1]),b[c+0])},a.Predictor6=function(a,b,c){return P(a,b[c-1])},a.Predictor7=function(a,b,c){return P(a,b[c+0])},a.Predictor8=function(a,b,c){return P(b[c-1],b[c+0])},a.Predictor9=function(a,b,c){return P(b[c+0],b[c+1])},a.Predictor10=function(a,b,c){return P(P(a,b[c-1]),P(b[c+0],b[c+1]))},a.Predictor11=function(a,b,c){var d=b[c+0];return 0>=S(d>>24&255,a>>24&255,(b=b[c-1])>>24&255)+S(d>>16&255,a>>16&255,b>>16&255)+S(d>>8&255,a>>8&255,b>>8&255)+S(255&d,255&a,255&b)?d:a},a.Predictor12=function(a,b,c){var d=b[c+0];return(Q((a>>24&255)+(d>>24&255)-((b=b[c-1])>>24&255))<<24|Q((a>>16&255)+(d>>16&255)-(b>>16&255))<<16|Q((a>>8&255)+(d>>8&255)-(b>>8&255))<<8|Q((255&a)+(255&d)-(255&b)))>>>0},a.Predictor13=function(a,b,c){var d=b[c-1];return(R((a=P(a,b[c+0]))>>24&255,d>>24&255)<<24|R(a>>16&255,d>>16&255)<<16|R(a>>8&255,d>>8&255)<<8|R((0|a)&255,(0|d)&255))>>>0};var cw=a.PredictorAdd0;a.PredictorAdd1=T,N("Predictor2","PredictorAdd2"),N("Predictor3","PredictorAdd3"),N("Predictor4","PredictorAdd4"),N("Predictor5","PredictorAdd5"),N("Predictor6","PredictorAdd6"),N("Predictor7","PredictorAdd7"),N("Predictor8","PredictorAdd8"),N("Predictor9","PredictorAdd9"),N("Predictor10","PredictorAdd10"),N("Predictor11","PredictorAdd11"),N("Predictor12","PredictorAdd12"),N("Predictor13","PredictorAdd13");var cx=a.PredictorAdd2;X("ColorIndexInverseTransform","MapARGB","32b",function(a){return a>>8&255},function(a){return a}),X("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(a){return a},function(a){return a>>8&255});var cy,cz=a.ColorIndexInverseTransform,cA=a.MapARGB,cB=a.VP8LColorIndexInverseTransformAlpha,cC=a.MapAlpha,cD=a.VP8LPredictorsAdd=[];cD.length=16,(a.VP8LPredictors=[]).length=16,(a.VP8LPredictorsAdd_C=[]).length=16,(a.VP8LPredictors_C=[]).length=16;var cE,cF,cG,cH,cI,cJ,cK,cL,cM,cN,cO,cP,cQ,cR,cS,cT,cU,cV,cW,cX,cY,cZ,c$,c_,c0,c1,c2,c3,c4=f(511),c5=f(2041),c6=f(225),c7=f(767),c8=0,c9=c5,da=c6,db=c7,dc=c4,dd=0,de=1,df=2,dg=3,dh=4,di=5,dj=6,dk=7,dl=8,dm=9,dn=10,dp=[2,3,7],dq=[3,3,11],dr=[280,256,256,256,40],ds=[0,1,1,1,0],dt=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],du=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],dv=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],dw=8,dx=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],dy=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],dz=null,dA=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],dB=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],dC=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],dD=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],dE=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],dF=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],dG=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],dH=[],dI=[],dJ=[],dK=1,dL=2,dM=[],dN=[];b5("UpsampleRgbLinePair",cb,3),b5("UpsampleBgrLinePair",cc,3),b5("UpsampleRgbaLinePair",ch,4),b5("UpsampleBgraLinePair",cg,4),b5("UpsampleArgbLinePair",cf,4),b5("UpsampleRgba4444LinePair",ce,2),b5("UpsampleRgb565LinePair",cd,2);var dO=a.UpsampleRgbLinePair,dP=a.UpsampleBgrLinePair,dQ=a.UpsampleRgbaLinePair,dR=a.UpsampleBgraLinePair,dS=a.UpsampleArgbLinePair,dT=a.UpsampleRgba4444LinePair,dU=a.UpsampleRgb565LinePair,dV=16,dW=32768,dX=-227,dY=482,dZ=6,d$=16383,d_=0,d0=f(256),d1=f(256),d2=f(256),d3=f(256),d4=f(dY-dX),d5=f(dY-dX);ci("YuvToRgbRow",cb,3),ci("YuvToBgrRow",cc,3),ci("YuvToRgbaRow",ch,4),ci("YuvToBgraRow",cg,4),ci("YuvToArgbRow",cf,4),ci("YuvToRgba4444Row",ce,2),ci("YuvToRgb565Row",cd,2);var d6=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],d7=[0,2,8],d8=[8,7,6,4,4,2,2,2,1,1,1,1];this.WebPDecodeRGBA=function(a,c,h,i,j){var k=de,l=new bT,m=new ag;l.ba=m,m.S=k,m.width=[m.width],m.height=[m.height];var n=m.width,o=m.height,p=new ah;if(null==p||null==a)var q=2;else b(null!=p),q=cm(a,c,h,p.width,p.height,p.Pd,p.Qd,p.format,null);if(0!=q?n=0:(null!=n&&(n[0]=p.width[0]),null!=o&&(o[0]=p.height[0]),n=1),n){m.width=m.width[0],m.height=m.height[0],null!=i&&(i[0]=m.width),null!=j&&(j[0]=m.height);a:{if(i=new aU,(j=new bU).data=a,j.w=c,j.ha=h,j.kd=1,c=[0],b(null!=j),(0==(a=cm(j.data,j.w,j.ha,null,null,null,c,null,j))||7==a)&&c[0]&&(a=4),0==(c=a)){if(b(null!=l),i.data=j.data,i.w=j.w+j.offset,i.ha=j.ha-j.offset,i.put=an,i.ac=am,i.bc=ao,i.ma=l,j.xa){if(null==(a=aC())){l=1;break a}if(function(a,c){for(var d=[0],e=[0],f=[0];;){if(null==a)return 0;if(null==c)return a.a=2,0;if(a.l=c,a.a=0,r(a.m,c.data,c.w,c.ha),!ap(a.m,d,e,f)){a.a=3;break}if(a.xb=dL,c.width=d[0],c.height=e[0],!aD(d[0],e[0],1,a,null))break;return 1}return b(0!=a.a),0}(a,i)){if(i=0==(c=cp(i.width,i.height,l.Oa,l.ba))){b:{for(i=a;;){if(null==i){i=0;break b}if(b(null!=i.s.yc),b(null!=i.s.Ya),b(0<i.s.Wb),b(null!=(h=i.l)),b(null!=(j=h.ma)),0!=i.xb){if(i.ca=j.ba,i.tb=j.tb,b(null!=i.ca),!cn(j.Oa,h,dg)){i.a=2;break}if(!aE(i,h.width)||h.da)break;if((h.da||ad(i.ca.S))&&b4(),11>i.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),null!=i.ca.f.kb.F&&b4()),i.Pb&&0<i.s.ua&&null==i.s.vb.X&&!H(i.s.vb,i.s.Wa.Xa)){i.a=1;break}i.xb=0}if(!aA(i,i.V,i.Ba,i.c,i.i,h.o,aw))break;j.Dc=i.Ma,i=1;break b}b(0!=i.a),i=0}i=!i}i&&(c=a.a)}else c=a.a}else if((a=new aV).Fa=j.na,a.P=j.P,a.qc=j.Sa,aY(a,i)){if(0==(c=cp(i.width,i.height,l.Oa,l.ba))){if(a.Aa=0,h=l.Oa,b(null!=(j=a)),null!=h){if(0<(n=0>(n=h.Md)?0:100<n?255:255*n/100)){for(o=p=0;4>o;++o)12>(q=j.pb[o]).lc&&(q.ia=n*d8[0>q.lc?0:q.lc]>>3),p|=q.ia;p&&(alert("todo:VP8InitRandom"),j.ia=1)}j.Ga=h.Id,100<j.Ga?j.Ga=100:0>j.Ga&&(j.Ga=0)}(function(a,c){if(null==a)return 0;if(null==c)return aW(a,2,"NULL VP8Io parameter in VP8Decode().");if(!a.cb&&!aY(a,c))return 0;if(b(a.cb),null==c.ac||c.ac(c)){c.ob&&(a.L=0);var h=d7[a.L];if(2==a.L?(a.yb=0,a.zb=0):(a.yb=c.v-h>>4,a.zb=c.j-h>>4,0>a.yb&&(a.yb=0),0>a.zb&&(a.zb=0)),a.Va=c.o+15+h>>4,a.Hb=c.va+15+h>>4,a.Hb>a.za&&(a.Hb=a.za),a.Va>a.Ub&&(a.Va=a.Ub),0<a.L){var i,j=a.ed;for(h=0;4>h;++h){if(a.Qa.Cb){var k=a.Qa.Lb[h];a.Qa.Fb||(k+=j.Tb)}else k=j.Tb;for(i=0;1>=i;++i){var l=a.gd[h][i],m=k;if(j.Pc&&(m+=j.vd[0],i&&(m+=j.od[0])),0<(m=0>m?0:63<m?63:m)){var n=m;0<j.wb&&(n=4<j.wb?n>>2:n>>1)>9-j.wb&&(n=9-j.wb),1>n&&(n=1),l.dd=n,l.tc=2*m+n,l.ld=40<=m?2:+(15<=m)}else l.tc=0;l.La=i}}}h=0}else aW(a,6,"Frame setup failed"),h=a.a;if(h=0==h){if(h){a.$c=0,0<a.Aa||(a.Ic=1);a:{h=a.Ic,j=4*(n=a.za);var o=32*n,p=n+1,q=0<a.L?n*(0<a.Aa?2:1):0,s=(2==a.Aa?2:1)*n;if((l=j+832+(i=3*(16*h+d7[a.L])/2*o)+(k=null!=a.Fa&&0<a.Fa.length?a.Kc.c*a.Kc.i:0))!=l)h=0;else{if(l>a.Vb){if(a.Vb=0,a.Ec=f(l),a.Fc=0,null==a.Ec){h=aW(a,1,"no memory during frame initialization.");break a}a.Vb=l}l=a.Ec,m=a.Fc,a.Ac=l,a.Bc=m,m+=j,a.Gd=g(o,aR),a.Hd=0,a.rb=g(p+1,aN),a.sb=1,a.wa=q?g(q,aM):null,a.Y=0,a.D.Nb=0,a.D.wa=a.wa,a.D.Y=a.Y,0<a.Aa&&(a.D.Y+=n),b(!0),a.oc=l,a.pc=m,m+=832,a.ya=g(s,aP),a.aa=0,a.D.ya=a.ya,a.D.aa=a.aa,2==a.Aa&&(a.D.aa+=n),a.R=16*n,a.B=8*n,n=(o=d7[a.L])*a.R,o=o/2*a.B,a.sa=l,a.ta=m+n,a.qa=a.sa,a.ra=a.ta+16*h*a.R+o,a.Ha=a.qa,a.Ia=a.ra+8*h*a.B+o,a.$c=0,m+=i,a.mb=k?l:null,a.nb=k?m:null,b(m+k<=a.Fc+a.Vb),a$(a),e(a.Ac,a.Bc,0,j),h=1}}if(h){if(c.ka=0,c.y=a.sa,c.O=a.ta,c.f=a.qa,c.N=a.ra,c.ea=a.Ha,c.Vd=a.Ia,c.fa=a.R,c.Rc=a.B,c.F=null,c.J=0,!c8){for(h=-255;255>=h;++h)c4[255+h]=0>h?-h:h;for(h=-1020;1020>=h;++h)c5[1020+h]=-128>h?-128:127<h?127:h;for(h=-112;112>=h;++h)c6[112+h]=-16>h?-16:15<h?15:h;for(h=-255;510>=h;++h)c7[255+h]=0>h?0:255<h?255:h;c8=1}cK=a8,cL=a4,cN=a5,cO=a6,cP=a7,cM=a3,cQ=bL,cR=bM,cS=bP,cT=bQ,cU=bN,cV=bO,cW=bR,cX=bS,cY=bF,cZ=bG,c$=bH,c_=bI,dI[0]=bn,dI[1]=ba,dI[2]=bl,dI[3]=bm,dI[4]=bo,dI[5]=bq,dI[6]=bp,dI[7]=br,dI[8]=bt,dI[9]=bs,dH[0]=bg,dH[1]=bc,dH[2]=bd,dH[3]=be,dH[4]=bh,dH[5]=bi,dH[6]=bj,dJ[0]=bx,dJ[1]=bb,dJ[2]=bu,dJ[3]=bv,dJ[4]=bz,dJ[5]=by,dJ[6]=bA,h=1}else h=0}h&&(h=function(a,c){for(a.M=0;a.M<a.Va;++a.M){var g,h=a.Jc[a.M&a.Xb],i=a.m,j=a;for(g=0;g<j.za;++g){var k=i,l=j,m=l.Ac,n=l.Bc+4*g,o=l.zc,p=l.ya[l.aa+g];if(l.Qa.Bb?p.$b=C(k,l.Pa.jb[0])?2+C(k,l.Pa.jb[2]):C(k,l.Pa.jb[1]):p.$b=0,l.kc&&(p.Ad=C(k,l.Bd)),p.Za=!C(k,145)+0,p.Za){var q=p.Ob,s=0;for(l=0;4>l;++l){var t,u=o[0+l];for(t=0;4>t;++t){u=dE[m[n+t]][u];for(var v=dC[C(k,u[0])];0<v;)v=dC[2*v+C(k,u[v])];u=-v,m[n+t]=u}d(q,s,m,n,4),s+=4,o[0+l]=u}}else u=C(k,156)?C(k,128)?1:3:2*!!C(k,163),p.Ob[0]=u,e(m,n,u,4),e(o,0,u,4);p.Dd=C(k,142)?C(k,114)?C(k,183)?1:3:2:0}if(j.m.Ka)return aW(a,7,"Premature end-of-partition0 encountered.");for(;a.ja<a.za;++a.ja){if(j=h,k=(i=a).rb[i.sb-1],m=i.rb[i.sb+i.ja],g=i.ya[i.aa+i.ja],n=i.kc?g.Ad:0)k.la=m.la=0,g.Za||(k.Na=m.Na=0),g.Hc=0,g.Gc=0,g.ia=0;else{if(k=m,m=j,n=i.Pa.Xc,o=i.ya[i.aa+i.ja],p=i.pb[o.$b],l=o.ad,q=0,s=i.rb[i.sb-1],u=t=0,e(l,q,0,384),o.Za)var w,y,A=0,B=n[3];else{v=f(16);var D=k.Na+s.Na;if(D=dz(m,n[1],D,p.Eb,0,v,0),k.Na=s.Na=(0<D)+0,1<D)cK(v,0,l,q);else{var E=v[0]+3>>3;for(v=0;256>v;v+=16)l[q+v]=E}A=1,B=n[0]}var F=15&k.la,G=15&s.la;for(v=0;4>v;++v){var H=1&G;for(E=y=0;4>E;++E)F=F>>1|(H=(D=dz(m,B,D=H+(1&F),p.Sc,A,l,q))>A)<<7,y=y<<2|(3<D?3:1<D?2:0!=l[q+0]),q+=16;F>>=4,G=G>>1|H<<7,t=(t<<8|y)>>>0}for(B=F,A=G>>4,w=0;4>w;w+=2){for(y=0,F=k.la>>4+w,G=s.la>>4+w,v=0;2>v;++v){for(H=1&G,E=0;2>E;++E)D=H+(1&F),F=F>>1|(H=0<(D=dz(m,n[2],D,p.Qc,0,l,q)))<<3,y=y<<2|(3<D?3:1<D?2:0!=l[q+0]),q+=16;F>>=2,G=G>>1|H<<5}u|=y<<4*w,B|=F<<4<<w,A|=(240&G)<<w}k.la=B,s.la=A,o.Hc=t,o.Gc=u,o.ia=43690&u?0:p.ia,n=!(t|u)}if(0<i.L&&(i.wa[i.Y+i.ja]=i.gd[g.$b][g.Za],i.wa[i.Y+i.ja].La|=!n),j.Ka)return aW(a,7,"Premature end-of-file encountered.")}if(a$(a),i=c,j=1,g=(h=a).D,k=0<h.L&&h.M>=h.zb&&h.M<=h.Va,0==h.Aa)a:{if(g.M=h.M,g.uc=k,cl(h,g),j=1,g=(y=h.D).Nb,k=(u=d7[h.L])*h.R,m=u/2*h.B,v=16*g*h.R,E=8*g*h.B,n=h.sa,o=h.ta-k+v,p=h.qa,l=h.ra-m+E,q=h.Ha,s=h.Ia-m+E,G=0==(F=y.M),t=F>=h.Va-1,2==h.Aa&&cl(h,y),y.uc)for(H=(D=h).D.M,b(D.D.uc),y=D.yb;y<D.Hb;++y){A=y,B=H;var I=(J=(S=D).D).Nb;w=S.R;var J=J.wa[J.Y+A],K=S.sa,L=S.ta+16*I*w+16*A,M=J.dd,N=J.tc;if(0!=N)if(b(3<=N),1==S.L)0<A&&cZ(K,L,w,N+4),J.La&&c_(K,L,w,N),0<B&&cY(K,L,w,N+4),J.La&&c$(K,L,w,N);else{var O=S.B,P=S.qa,Q=S.ra+8*I*O+8*A,R=S.Ha,S=S.Ia+8*I*O+8*A;I=J.ld,0<A&&(cR(K,L,w,N+4,M,I),cT(P,Q,R,S,O,N+4,M,I)),J.La&&(cV(K,L,w,N,M,I),cX(P,Q,R,S,O,N,M,I)),0<B&&(cQ(K,L,w,N+4,M,I),cS(P,Q,R,S,O,N+4,M,I)),J.La&&(cU(K,L,w,N,M,I),cW(P,Q,R,S,O,N,M,I))}}if(h.ia&&alert("todo:DitherRow"),null!=i.put){if(y=16*F,F=16*(F+1),G?(i.y=h.sa,i.O=h.ta+v,i.f=h.qa,i.N=h.ra+E,i.ea=h.Ha,i.W=h.Ia+E):(y-=u,i.y=n,i.O=o,i.f=p,i.N=l,i.ea=q,i.W=s),t||(F-=u),F>i.o&&(F=i.o),i.F=null,i.J=null,null!=h.Fa&&0<h.Fa.length&&y<F&&(i.J=function(a,c,e,g){var h=c.width,i=c.o;if(b(null!=a&&null!=c),0>e||0>=g||e+g>i)return null;if(!a.Cc){if(null==a.ga){if(a.ga=new bY,(A=null==a.ga)||(A=c.width*c.o,b(0==a.Gb.length),a.Gb=f(A),a.Uc=0,null==a.Gb?A=0:(a.mb=a.Gb,a.nb=a.Uc,a.rc=null,A=1),A=!A),!A){A=a.ga;var j=a.Fa,k=a.P,l=a.qc,m=a.mb,n=a.nb,o=k+1,p=l-1,q=A.l;if(b(null!=j&&null!=m&&null!=c),dM[0]=null,dM[1]=bZ,dM[2]=b$,dM[3]=b_,A.ca=m,A.tb=n,A.c=c.width,A.i=c.height,b(0<A.c&&0<A.i),1>=l)c=0;else if(A.$a=(0|j[k+0])&3,A.Z=j[k+0]>>2&3,A.Lc=j[k+0]>>4&3,k=j[k+0]>>6&3,0>A.$a||1<A.$a||4<=A.Z||1<A.Lc||k)c=0;else if(q.put=an,q.ac=am,q.bc=ao,q.ma=A,q.width=c.width,q.height=c.height,q.Da=c.Da,q.v=c.v,q.va=c.va,q.j=c.j,q.o=c.o,A.$a)a:{for(b(1==A.$a),c=aC();;){if(null==c){c=0;break a}if(b(null!=A),A.mc=c,c.c=A.c,c.i=A.i,c.l=A.l,c.l.ma=A,c.l.width=A.c,c.l.height=A.i,c.a=0,r(c.m,j,o,p),!aD(A.c,A.i,1,c,null)||(1==c.ab&&3==c.gc[0].hc&&ax(c.s)?(A.ic=1,j=c.c*c.i,c.Ta=null,c.Ua=0,c.V=f(j),c.Ba=0,null==c.V?(c.a=1,c=0):c=1):(A.ic=0,c=aE(c,A.c)),!c))break;c=1;break a}A.mc=null,c=0}else c=p>=A.c*A.i;A=!c}if(A)return null;1!=a.ga.Lc?a.Ga=0:g=i-e}b(null!=a.ga),b(e+g<=i);a:{if(c=(j=a.ga).c,i=j.l.o,0==j.$a){if(o=a.rc,p=a.Vc,q=a.Fa,k=a.P+1+e*c,l=a.mb,m=a.nb+e*c,b(k<=a.P+a.qc),0!=j.Z)for(b(null!=dM[j.Z]),A=0;A<g;++A)dM[j.Z](o,p,q,k,l,m,c),o=l,p=m,m+=c,k+=c;else for(A=0;A<g;++A)d(l,m,q,k,c),o=l,p=m,m+=c,k+=c;a.rc=o,a.Vc=p}else{if(b(null!=j.mc),c=e+g,b(null!=(A=j.mc)),b(c<=A.i),A.C>=c)c=1;else if(j.ic||b4(),j.ic){j=A.V,o=A.Ba,p=A.c;var s=A.i,t=(q=1,k=A.$/p,l=A.$%p,m=A.m,n=A.s,A.$),u=p*s,v=p*c,w=n.wc,y=t<v?au(n,l,k):null;b(t<=u),b(c<=s),b(ax(n));b:for(;;){for(;!m.h&&t<v;){if(l&w||(y=au(n,l,k)),b(null!=y),z(m),256>(s=as(y.G[0],y.H[0],m)))j[o+t]=s,++t,++l>=p&&(l=0,++k<=c&&!(k%16)&&az(A,k));else{if(!(280>s)){q=0;break b}s=aq(s-256,m);var A,B,C=as(y.G[4],y.H[4],m);if(z(m),!(t>=(C=ar(p,C=aq(C,m)))&&u-t>=s)){q=0;break b}for(B=0;B<s;++B)j[o+t+B]=j[o+t+B-C];for(t+=s,l+=s;l>=p;)l-=p,++k<=c&&!(k%16)&&az(A,k);t<v&&l&w&&(y=au(n,l,k))}b(m.h==x(m))}az(A,k>c?c:k);break}!q||m.h&&t<u?(q=0,A.a=m.h?5:3):A.$=t,c=q}else c=aA(A,A.V,A.Ba,A.c,A.i,c,aF);if(!c){g=0;break a}}e+g>=i&&(a.Cc=1),g=1}if(!g)return null;if(a.Cc&&(null!=(g=a.ga)&&(g.mc=null),a.ga=null,0<a.Ga))return alert("todo:WebPDequantizeLevels"),null}return a.nb+e*h}(h,i,y,F-y),i.F=h.mb,null==i.F&&0==i.F.length)){j=aW(h,3,"Could not decode alpha data.");break a}y<i.j&&(u=i.j-y,y=i.j,b(!(1&u)),i.O+=h.R*u,i.N+=h.B*(u>>1),i.W+=h.B*(u>>1),null!=i.F&&(i.J+=i.width*u)),y<F&&(i.O+=i.v,i.N+=i.v>>1,i.W+=i.v>>1,null!=i.F&&(i.J+=i.v),i.ka=y-i.j,i.U=i.va-i.v,i.T=F-y,j=i.put(i))}g+1!=h.Ic||t||(d(h.sa,h.ta-k,n,o+16*h.R,k),d(h.qa,h.ra-m,p,l+8*h.B,m),d(h.Ha,h.Ia-m,q,s+8*h.B,m))}if(!j)return aW(a,6,"Output aborted.")}return 1}(a,c)),null!=c.bc&&c.bc(c),h&=1}return h?(a.cb=0,h):0})(a,i)||(c=a.a)}}else c=a.a;0==c&&null!=l.Oa&&l.Oa.fd&&(c=co(l.ba))}l=c}k=0!=l?null:11>k?m.f.RGBA.eb:m.f.kb.y}else k=null;return k};var d9=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function j(a,b){return(0|a[b+0]|a[b+1]<<8|a[b+2]<<16)>>>0}function k(a,b){return(0|a[b+0]|a[b+1]<<8|a[b+2]<<16|a[b+3]<<24)>>>0}new i;var l=[0],m=[0],n=[],o=new i,p=function(a,b){var c={},d=0,e=!1,f=0,g=0;if(c.frames=[],!function(a,b,c,d){for(var e=0;e<4;e++)if(a[b+e]!=c.charCodeAt(e))return!0;return!1}(a,b,"RIFF",0)){for(k(a,b+=4),b+=8;b<a.length;){var h,i,l,m=function(a,b){for(var c="",d=0;d<4;d++)c+=String.fromCharCode(a[b++]);return c}(a,b),n=k(a,b+=4);b+=4;var o=n+(1&n);switch(m){case"VP8 ":case"VP8L":void 0===c.frames[d]&&(c.frames[d]={}),(l=c.frames[d]).src_off=e?g:b-8,l.src_size=f+n+8,d++,e&&(e=!1,f=0,g=0);break;case"VP8X":(l=c.header={}).feature_flags=a[b];var p=b+4;l.canvas_width=1+j(a,p),p+=3,l.canvas_height=1+j(a,p),p+=3;break;case"ALPH":e=!0,f=o+8,g=b-8;break;case"ANIM":(l=c.header).bgcolor=k(a,b),p=b+4,l.loop_count=0|a[(h=p)+0]|a[h+1]<<8,p+=2;break;case"ANMF":(l=c.frames[d]={}).offset_x=2*j(a,b),b+=3,l.offset_y=2*j(a,b),b+=3,l.width=1+j(a,b),b+=3,l.height=1+j(a,b),b+=3,l.duration=j(a,b),b+=3,i=a[b++],l.dispose=1&i,l.blend=i>>1&1}"ANMF"!=m&&(b+=o)}return c}}(a,0);p.response=a,p.rgbaoutput=!0,p.dataurl=!1;var q=p.header?p.header:null,r=p.frames?p.frames:null;if(q){q.loop_counter=q.loop_count,l=[q.canvas_height],m=[q.canvas_width];for(var s=0;s<r.length&&0!=r[s].blend;s++);}var t=r[0],u=o.WebPDecodeRGBA(a,t.src_off,t.src_size,m,l);t.rgba=u,t.imgwidth=m[0],t.imgheight=l[0];for(var v=0;v<m[0]*l[0]*4;v++)n[v]=u[v];return this.width=m,this.height=l,this.data=n,this}!function(a){var b=function(b,d,i,j){var k=4,l=f;switch(j){case a.image_compression.FAST:k=1,l=e;break;case a.image_compression.MEDIUM:k=6,l=g;break;case a.image_compression.SLOW:k=9,l=h}var m=$(b=c(b,d,i,l),{level:k});return a.__addimage__.arrayBufferToBinaryString(m)},c=function(a,b,c,d){for(var e,f,g,h=a.length/b,i=new Uint8Array(a.length+h),l=j(),m=0;m<h;m+=1){if(g=m*b,e=a.subarray(g,g+b),d)i.set(d(e,c,f),g+m);else{for(var n,o=l.length,p=[];n<o;n+=1)p[n]=l[n](e,c,f);var q=k(p.concat());i.set(p[q],g+m)}f=e}return i},d=function(a){var b=Array.apply([],a);return b.unshift(0),b},e=function(a,b){var c,d=[],e=a.length;d[0]=1;for(var f=0;f<e;f+=1)c=a[f-b]||0,d[f+1]=a[f]-c+256&255;return d},f=function(a,b,c){var d,e=[],f=a.length;e[0]=2;for(var g=0;g<f;g+=1)d=c&&c[g]||0,e[g+1]=a[g]-d+256&255;return e},g=function(a,b,c){var d,e,f=[],g=a.length;f[0]=3;for(var h=0;h<g;h+=1)d=a[h-b]||0,e=c&&c[h]||0,f[h+1]=a[h]+256-(d+e>>>1)&255;return f},h=function(a,b,c){var d,e,f=[],g=a.length;f[0]=4;for(var h=0;h<g;h+=1)d=a[h-b]||0,e=i(d,c&&c[h]||0,c&&c[h-b]||0),f[h+1]=a[h]-e+256&255;return f},i=function(a,b,c){if(a===b&&b===c)return a;var d=Math.abs(b-c),e=Math.abs(a-c),f=Math.abs(a+b-c-c);return d<=e&&d<=f?a:e<=f?b:c},j=function(){return[d,e,f,g,h]},k=function(a){var b=a.map(function(a){return a.reduce(function(a,b){return a+Math.abs(b)},0)});return b.indexOf(Math.min.apply(null,b))};a.processPNG=function(c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v=this.decode.FLATE_DECODE,w="";if(this.__addimage__.isArrayBuffer(c)&&(c=new Uint8Array(c)),this.__addimage__.isArrayBufferView(c)){if(c=(i=new bF(c)).imgData,h=i.bits,g=i.colorSpace,k=i.colors,-1!==[4,6].indexOf(i.colorType)){if(8===i.bits){o=(n=32==i.pixelBitlength?new Uint32Array(i.decodePixels().buffer):16==i.pixelBitlength?new Uint16Array(i.decodePixels().buffer):new Uint8Array(i.decodePixels().buffer)).length,q=new Uint8Array(o*i.colors),p=new Uint8Array(o);var x,y=i.pixelBitlength-i.bits;for(t=0,u=0;t<o;t++){for(s=n[t],x=0;x<y;)q[u++]=s>>>x&255,x+=i.bits;p[t]=s>>>x&255}}if(16===i.bits){q=new Uint8Array((o=(n=new Uint32Array(i.decodePixels().buffer)).length)*(32/i.pixelBitlength)*i.colors),p=new Uint8Array(o*(32/i.pixelBitlength)),r=i.colors>1,t=0,u=0;for(var z=0;t<o;)s=n[t++],q[u++]=s>>>0&255,r&&(q[u++]=s>>>16&255,s=n[t++],q[u++]=s>>>0&255),p[z++]=s>>>16&255;h=8}f!==a.image_compression.NONE?(c=b(q,i.width*i.colors,i.colors,f),m=b(p,i.width,1,f)):(c=q,m=p,v=void 0)}if(3===i.colorType&&(g=this.color_spaces.INDEXED,l=i.palette,i.transparency.indexed)){var A=i.transparency.indexed,B=0;for(t=0,o=A.length;t<o;++t)B+=A[t];if((B/=255)==o-1&&-1!==A.indexOf(0))j=[A.indexOf(0)];else if(B!==o){for(p=new Uint8Array((n=i.decodePixels()).length),t=0,o=n.length;t<o;t++)p[t]=A[n[t]];m=b(p,i.width,1)}}var C=function(b){var c;switch(b){case a.image_compression.FAST:c=11;break;case a.image_compression.MEDIUM:c=13;break;case a.image_compression.SLOW:c=14;break;default:c=12}return c}(f);return v===this.decode.FLATE_DECODE&&(w="/Predictor "+C+" "),w+="/Colors "+k+" /BitsPerComponent "+h+" /Columns "+i.width,(this.__addimage__.isArrayBuffer(c)||this.__addimage__.isArrayBufferView(c))&&(c=this.__addimage__.arrayBufferToBinaryString(c)),(m&&this.__addimage__.isArrayBuffer(m)||this.__addimage__.isArrayBufferView(m))&&(m=this.__addimage__.arrayBufferToBinaryString(m)),{alias:e,data:c,index:d,filter:v,decodeParameters:w,transparency:j,palette:l,sMask:m,predictor:C,width:i.width,height:i.height,bitsPerComponent:h,colorSpace:g}}}}(aF.API),function(a){a.processGIF89A=function(b,c,d,e){var f=new bG(b),g=f.width,h=f.height,i=[];f.decodeAndBlitFrameRGBA(0,i);var j=new bI(100).encode({data:i,width:g,height:h},100);return a.processJPEG.call(this,j,c,d,e)},a.processGIF87A=a.processGIF89A}(aF.API),bJ.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var a=0===this.colors?1<<this.bitPP:this.colors;this.palette=Array(a);for(var b=0;b<a;b++){var c=this.datav.getUint8(this.pos++,!0),d=this.datav.getUint8(this.pos++,!0),e=this.datav.getUint8(this.pos++,!0),f=this.datav.getUint8(this.pos++,!0);this.palette[b]={red:e,green:d,blue:c,quad:f}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},bJ.prototype.parseBGR=function(){this.pos=this.offset;try{var a="bit"+this.bitPP,b=this.width*this.height*4;this.data=new Uint8Array(b),this[a]()}catch(a){ac.log("bit decode error:"+a)}},bJ.prototype.bit1=function(){var a,b=Math.ceil(this.width/8),c=b%4;for(a=this.height-1;a>=0;a--){for(var d=this.bottom_up?a:this.height-1-a,e=0;e<b;e++)for(var f=this.datav.getUint8(this.pos++,!0),g=d*this.width*4+8*e*4,h=0;h<8&&8*e+h<this.width;h++){var i=this.palette[f>>7-h&1];this.data[g+4*h]=i.blue,this.data[g+4*h+1]=i.green,this.data[g+4*h+2]=i.red,this.data[g+4*h+3]=255}0!==c&&(this.pos+=4-c)}},bJ.prototype.bit4=function(){for(var a=Math.ceil(this.width/2),b=a%4,c=this.height-1;c>=0;c--){for(var d=this.bottom_up?c:this.height-1-c,e=0;e<a;e++){var f=this.datav.getUint8(this.pos++,!0),g=d*this.width*4+2*e*4,h=f>>4,i=15&f,j=this.palette[h];if(this.data[g]=j.blue,this.data[g+1]=j.green,this.data[g+2]=j.red,this.data[g+3]=255,2*e+1>=this.width)break;j=this.palette[i],this.data[g+4]=j.blue,this.data[g+4+1]=j.green,this.data[g+4+2]=j.red,this.data[g+4+3]=255}0!==b&&(this.pos+=4-b)}},bJ.prototype.bit8=function(){for(var a=this.width%4,b=this.height-1;b>=0;b--){for(var c=this.bottom_up?b:this.height-1-b,d=0;d<this.width;d++){var e=this.datav.getUint8(this.pos++,!0),f=c*this.width*4+4*d;if(e<this.palette.length){var g=this.palette[e];this.data[f]=g.red,this.data[f+1]=g.green,this.data[f+2]=g.blue,this.data[f+3]=255}else this.data[f]=255,this.data[f+1]=255,this.data[f+2]=255,this.data[f+3]=255}0!==a&&(this.pos+=4-a)}},bJ.prototype.bit15=function(){for(var a=this.width%3,b=parseInt("11111",2),c=this.height-1;c>=0;c--){for(var d=this.bottom_up?c:this.height-1-c,e=0;e<this.width;e++){var f=this.datav.getUint16(this.pos,!0);this.pos+=2;var g=(f&b)/b*255|0,h=(f>>5&b)/b*255|0,i=(f>>10&b)/b*255|0,j=f>>15?255:0,k=d*this.width*4+4*e;this.data[k]=i,this.data[k+1]=h,this.data[k+2]=g,this.data[k+3]=j}this.pos+=a}},bJ.prototype.bit16=function(){for(var a=this.width%3,b=parseInt("11111",2),c=parseInt("111111",2),d=this.height-1;d>=0;d--){for(var e=this.bottom_up?d:this.height-1-d,f=0;f<this.width;f++){var g=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(g&b)/b*255|0,i=(g>>5&c)/c*255|0,j=(g>>11)/b*255|0,k=e*this.width*4+4*f;this.data[k]=j,this.data[k+1]=i,this.data[k+2]=h,this.data[k+3]=255}this.pos+=a}},bJ.prototype.bit24=function(){for(var a=this.height-1;a>=0;a--){for(var b=this.bottom_up?a:this.height-1-a,c=0;c<this.width;c++){var d=this.datav.getUint8(this.pos++,!0),e=this.datav.getUint8(this.pos++,!0),f=this.datav.getUint8(this.pos++,!0),g=b*this.width*4+4*c;this.data[g]=f,this.data[g+1]=e,this.data[g+2]=d,this.data[g+3]=255}this.pos+=this.width%4}},bJ.prototype.bit32=function(){for(var a=this.height-1;a>=0;a--)for(var b=this.bottom_up?a:this.height-1-a,c=0;c<this.width;c++){var d=this.datav.getUint8(this.pos++,!0),e=this.datav.getUint8(this.pos++,!0),f=this.datav.getUint8(this.pos++,!0),g=this.datav.getUint8(this.pos++,!0),h=b*this.width*4+4*c;this.data[h]=f,this.data[h+1]=e,this.data[h+2]=d,this.data[h+3]=g}},bJ.prototype.getData=function(){return this.data},function(a){a.processBMP=function(b,c,d,e){var f=new bJ(b,!1),g=f.width,h=f.height,i={data:f.getData(),width:g,height:h},j=new bI(100).encode(i,100);return a.processJPEG.call(this,j,c,d,e)}}(aF.API),bK.prototype.getData=function(){return this.data},function(a){a.processWEBP=function(b,c,d,e){var f=new bK(b),g=f.width,h=f.height,i={data:f.getData(),width:g,height:h},j=new bI(100).encode(i,100);return a.processJPEG.call(this,j,c,d,e)}}(aF.API),aF.API.processRGBA=function(a,b,c){for(var d=a.data,e=d.length,f=new Uint8Array(e/4*3),g=new Uint8Array(e/4),h=0,i=0,j=0;j<e;j+=4){var k=d[j],l=d[j+1],m=d[j+2],n=d[j+3];f[h++]=k,f[h++]=l,f[h++]=m,g[i++]=n}var o=this.__addimage__.arrayBufferToBinaryString(f);return{alpha:this.__addimage__.arrayBufferToBinaryString(g),data:o,index:b,alias:c,colorSpace:"DeviceRGB",bitsPerComponent:8,width:a.width,height:a.height}},aF.API.setLanguage=function(a){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!==({af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"})[a]&&(this.internal.languageSettings.languageCode=a,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},bB=(bA=aF.API).getCharWidthsArray=function(a,b){var c,d,f=(b=b||{}).font||this.internal.getFont(),g=b.fontSize||this.internal.getFontSize(),h=b.charSpace||this.internal.getCharSpace(),i=b.widths?b.widths:f.metadata.Unicode.widths,j=i.fof?i.fof:1,k=b.kerning?b.kerning:f.metadata.Unicode.kerning,l=k.fof?k.fof:1,m=!1!==b.doKerning,n=0,o=a.length,p=0,q=i[0]||j,r=[];for(c=0;c<o;c++)d=a.charCodeAt(c),"function"==typeof f.metadata.widthOfString?r.push((f.metadata.widthOfGlyph(f.metadata.characterToGlyph(d))+1e3/g*h||0)/1e3):(n=m&&"object"===e()(k[d])&&!isNaN(parseInt(k[d][p],10))?k[d][p]/l:0,r.push((i[d]||q)/j+n)),p=d;return r},bC=bA.getStringUnitWidth=function(a,b){var c=(b=b||{}).fontSize||this.internal.getFontSize(),d=b.font||this.internal.getFont(),e=b.charSpace||this.internal.getCharSpace();return bA.processArabic&&(a=bA.processArabic(a)),"function"==typeof d.metadata.widthOfString?d.metadata.widthOfString(a,c,e)/c:bB.apply(this,arguments).reduce(function(a,b){return a+b},0)},bD=function(a,b,c,d){for(var e=[],f=0,g=a.length,h=0;f!==g&&h+b[f]<c;)h+=b[f],f++;e.push(a.slice(0,f));var i=f;for(h=0;f!==g;)h+b[f]>d&&(e.push(a.slice(i,f)),h=0,i=f),h+=b[f],f++;return i!==f&&e.push(a.slice(i,f)),e},bE=function(a,b,c){c||(c={});var d,e,f,g,h,i,j,k=[],l=[k],m=c.textIndent||0,n=0,o=0,p=a.split(" "),q=bB.apply(this,[" ",c])[0];if(i=-1===c.lineIndent?p[0].length+2:c.lineIndent||0){var r=Array(i).join(" "),s=[];p.map(function(a){(a=a.split(/\s*\n/)).length>1?s=s.concat(a.map(function(a,b){return(b&&a.length?"\n":"")+a})):s.push(a[0])}),p=s,i=bC.apply(this,[r,c])}for(f=0,g=p.length;f<g;f++){var t=0;if(d=p[f],i&&"\n"==d[0]&&(d=d.substr(1),t=1),m+n+(o=(e=bB.apply(this,[d,c])).reduce(function(a,b){return a+b},0))>b||t){if(o>b){for(h=bD.apply(this,[d,e,b-(m+n),b]),k.push(h.shift()),k=[h.pop()];h.length;)l.push([h.shift()]);o=e.slice(d.length-(k[0]?k[0].length:0)).reduce(function(a,b){return a+b},0)}else k=[d];l.push(k),m=o+i,n=q}else k.push(d),m+=n+o,n=q}return j=i?function(a,b){return(b?r:"")+a.join(" ")}:function(a){return a.join(" ")},l.map(j)},bA.splitTextToSize=function(a,b,c){var d,e=(c=c||{}).fontSize||this.internal.getFontSize(),f=(function(a){if(a.widths&&a.kerning)return{widths:a.widths,kerning:a.kerning};var b=this.internal.getFont(a.fontName,a.fontStyle);return b.metadata.Unicode?{widths:b.metadata.Unicode.widths||{0:1},kerning:b.metadata.Unicode.kerning||{}}:{font:b.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,c);d=Array.isArray(a)?a:String(a).split(/\r?\n/);var g=this.internal.scaleFactor*b/e;f.textIndent=c.textIndent?c.textIndent*this.internal.scaleFactor/e:0,f.lineIndent=c.lineIndent;var h,i,j=[];for(h=0,i=d.length;h<i;h++)j=j.concat(bE.apply(this,[d[h],g,f]));return j},function(a){a.__fontmetrics__=a.__fontmetrics__||{};for(var b="klmnopqrstuvwxyz",c={},d={},f=0;f<b.length;f++)c[b[f]]="0123456789abcdef"[f],d["0123456789abcdef"[f]]=b[f];var g=function(a){return"0x"+parseInt(a,10).toString(16)},h=a.__fontmetrics__.compress=function(a){var b,c,f,i,j=["{"];for(var k in a){if(b=a[k],c=isNaN(parseInt(k,10))?"'"+k+"'":(c=g(k=parseInt(k,10)).slice(2)).slice(0,-1)+d[c.slice(-1)],"number"==typeof b)b<0?(f=g(b).slice(3),i="-"):(f=g(b).slice(2),i=""),f=i+f.slice(0,-1)+d[f.slice(-1)];else{if("object"!==e()(b))throw Error("Don't know what to do with value type "+e()(b)+".");f=h(b)}j.push(c+f)}return j.push("}"),j.join("")},i=a.__fontmetrics__.uncompress=function(a){if("string"!=typeof a)throw Error("Invalid argument passed to uncompress.");for(var b,d,e,f,g={},h=1,i=g,j=[],k="",l="",m=a.length-1,n=1;n<m;n+=1)"'"==(f=a[n])?b?(e=b.join(""),b=void 0):b=[]:b?b.push(f):"{"==f?(j.push([i,e]),i={},e=void 0):"}"==f?((d=j.pop())[0][d[1]]=i,e=void 0,i=d[0]):"-"==f?h=-1:void 0===e?c.hasOwnProperty(f)?(k+=c[f],e=parseInt(k,16)*h,h=1,k=""):k+=f:c.hasOwnProperty(f)?(l+=c[f],i[e]=parseInt(l,16)*h,h=1,e=void 0,l=""):l+=f;return g},j={codePages:["WinAnsiEncoding"],WinAnsiEncoding:i("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},k={Courier:j,"Courier-Bold":j,"Courier-BoldOblique":j,"Courier-Oblique":j,Helvetica:j,"Helvetica-Bold":j,"Helvetica-BoldOblique":j,"Helvetica-Oblique":j,"Times-Roman":j,"Times-Bold":j,"Times-BoldItalic":j,"Times-Italic":j},l={Unicode:{"Courier-Oblique":i("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":i("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":i("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:i("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":i("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":i("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:i("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:i("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":i("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:i("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":i("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":i("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":i("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":i("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};a.events.push(["addFont",function(a){var b=a.font,c=l.Unicode[b.postScriptName];c&&(b.metadata.Unicode={},b.metadata.Unicode.widths=c.widths,b.metadata.Unicode.kerning=c.kerning);var d=k[b.postScriptName];d&&(b.metadata.Unicode.encoding=d,b.encoding=d.codePages[0])}])}(aF.API),function(a){var b=function(a){for(var b=a.length,c=new Uint8Array(b),d=0;d<b;d++)c[d]=a.charCodeAt(d);return c};a.API.events.push(["addFont",function(c){var d,e=void 0,f=c.font,g=c.instance;if(!f.isStandardFont){if(void 0===g)throw Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+f.postScriptName+"').");if("string"!=typeof(e=!1===g.existsFileInVFS(f.postScriptName)?g.loadFile(f.postScriptName):g.getFileFromVFS(f.postScriptName)))throw Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+f.postScriptName+"').");d=e,d=/^\x00\x01\x00\x00/.test(d)?b(d):b(ag(d)),f.metadata=a.API.TTFFont.open(d),f.metadata.Unicode=f.metadata.Unicode||{encoding:{},kerning:{},widths:[]},f.metadata.glyIdsUsed=[0]}}])}(aF),aF.API.addSvgAsImage=function(a,b,d,e,f,g,h,i){if(isNaN(b)||isNaN(d))throw ac.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(e)||isNaN(f))throw ac.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var j=document.createElement("canvas");j.width=e,j.height=f;var k=j.getContext("2d");k.fillStyle="#fff",k.fillRect(0,0,j.width,j.height);var l={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},m=this;return(aa.canvg?Promise.resolve(aa.canvg):c.e(876).then(c.bind(c,65876))).catch(function(a){return Promise.reject(Error("Could not load canvg: "+a))}).then(function(a){return a.default?a.default:a}).then(function(b){return b.fromString(k,a,l)},function(){return Promise.reject(Error("Could not load canvg."))}).then(function(a){return a.render(l)}).then(function(){m.addImage(j.toDataURL("image/jpeg",1),b,d,e,f,h,i)})},aF.API.putTotalPages=function(a){var b,c=0;15>parseInt(this.internal.getFont().id.substr(1),10)?(b=RegExp(a,"g"),c=this.internal.getNumberOfPages()):(b=RegExp(this.pdfEscape16(a,this.internal.getFont()),"g"),c=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var d=1;d<=this.internal.getNumberOfPages();d++)for(var e=0;e<this.internal.pages[d].length;e++)this.internal.pages[d][e]=this.internal.pages[d][e].replace(b,c);return this},aF.API.viewerPreferences=function(a,b){a=a||{},b=b||!1;var c,d,f,g,h={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},i=Object.keys(h),j=[],k=0,l=0,m=0;function n(a,b){var c,d=!1;for(c=0;c<a.length;c+=1)a[c]===b&&(d=!0);return d}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(h)),this.internal.viewerpreferences.isSubscribed=!1),c=this.internal.viewerpreferences.configuration,"reset"===a||!0===b){var o=i.length;for(m=0;m<o;m+=1)c[i[m]].value=c[i[m]].defaultValue,c[i[m]].explicitSet=!1}if("object"===e()(a)){for(f in a)if(g=a[f],n(i,f)&&void 0!==g){if("boolean"===c[f].type&&"boolean"==typeof g)c[f].value=g;else if("name"===c[f].type&&n(c[f].valueSet,g))c[f].value=g;else if("integer"===c[f].type&&Number.isInteger(g))c[f].value=g;else if("array"===c[f].type){for(k=0;k<g.length;k+=1)if(d=!0,1===g[k].length&&"number"==typeof g[k][0])j.push(String(g[k]-1));else if(g[k].length>1){for(l=0;l<g[k].length;l+=1)"number"!=typeof g[k][l]&&(d=!1);!0===d&&j.push([g[k][0]-1,g[k][1]-1].join(" "))}c[f].value="["+j.join(" ")+"]"}else c[f].value=c[f].defaultValue;c[f].explicitSet=!0}}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){var a,b=[];for(a in c)!0===c[a].explicitSet&&("name"===c[a].type?b.push("/"+a+" /"+c[a].value):b.push("/"+a+" "+c[a].value));0!==b.length&&this.internal.write("/ViewerPreferences\n<<\n"+b.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=c,this},function(a){var b=function(){var a='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',b=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),c=unescape(encodeURIComponent(a)),d=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),e=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),f=unescape(encodeURIComponent("</x:xmpmeta>")),g=c.length+d.length+e.length+b.length+f.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+g+" >>"),this.internal.write("stream"),this.internal.write(b+c+d+e+f),this.internal.write("endstream"),this.internal.write("endobj")},c=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};a.addMetadata=function(a,d){return void 0===this.internal.__metadata__&&(this.internal.__metadata__={metadata:a,namespaceuri:d||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",c),this.internal.events.subscribe("postPutResources",b)),this}}(aF.API),function(a){var b=a.API,c=b.pdfEscape16=function(a,b){for(var c,d=b.metadata.Unicode.widths,e=["","0","00","000","0000"],f=[""],g=0,h=a.length;g<h&&(c=b.metadata.characterToGlyph(a.charCodeAt(g)),b.metadata.glyIdsUsed.push(c),b.metadata.toUnicode[c]=a.charCodeAt(g),-1==d.indexOf(c)&&(d.push(c),d.push([parseInt(b.metadata.widthOfGlyph(c),10)])),"0"!=c);++g)c=c.toString(16),f.push(e[4-c.length],c);return f.join("")},d=function(a){var b,c,d,e,f,g,h;for(f="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",d=[],g=0,h=(c=Object.keys(a).sort(function(a,b){return a-b})).length;g<h;g++)b=c[g],d.length>=100&&(f+="\n"+d.length+" beginbfchar\n"+d.join("\n")+"\nendbfchar",d=[]),void 0!==a[b]&&null!==a[b]&&"function"==typeof a[b].toString&&(e=("0000"+a[b].toString(16)).slice(-4),b=("0000"+(+b).toString(16)).slice(-4),d.push("<"+b+"><"+e+">"));return d.length&&(f+="\n"+d.length+" beginbfchar\n"+d.join("\n")+"\nendbfchar\n"),f+="endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"};b.events.push(["putFont",function(b){!function(b){var c=b.font,e=b.out,f=b.newObject,g=b.putStream;if(c.metadata instanceof a.API.TTFFont&&"Identity-H"===c.encoding){for(var h=c.metadata.Unicode.widths,i=c.metadata.subset.encode(c.metadata.glyIdsUsed,1),j="",k=0;k<i.length;k++)j+=String.fromCharCode(i[k]);var l=f();g({data:j,addLength1:!0,objectId:l}),e("endobj");var m=f();g({data:d(c.metadata.toUnicode),addLength1:!0,objectId:m}),e("endobj");var n=f();e("<<"),e("/Type /FontDescriptor"),e("/FontName /"+az(c.fontName)),e("/FontFile2 "+l+" 0 R"),e("/FontBBox "+a.API.PDFObject.convert(c.metadata.bbox)),e("/Flags "+c.metadata.flags),e("/StemV "+c.metadata.stemV),e("/ItalicAngle "+c.metadata.italicAngle),e("/Ascent "+c.metadata.ascender),e("/Descent "+c.metadata.decender),e("/CapHeight "+c.metadata.capHeight),e(">>"),e("endobj");var o=f();e("<<"),e("/Type /Font"),e("/BaseFont /"+az(c.fontName)),e("/FontDescriptor "+n+" 0 R"),e("/W "+a.API.PDFObject.convert(h)),e("/CIDToGIDMap /Identity"),e("/DW 1000"),e("/Subtype /CIDFontType2"),e("/CIDSystemInfo"),e("<<"),e("/Supplement 0"),e("/Registry (Adobe)"),e("/Ordering ("+c.encoding+")"),e(">>"),e(">>"),e("endobj"),c.objectNumber=f(),e("<<"),e("/Type /Font"),e("/Subtype /Type0"),e("/ToUnicode "+m+" 0 R"),e("/BaseFont /"+az(c.fontName)),e("/Encoding /"+c.encoding),e("/DescendantFonts ["+o+" 0 R]"),e(">>"),e("endobj"),c.isAlreadyPutted=!0}}(b)}]),b.events.push(["putFont",function(b){!function(b){var c=b.font,e=b.out,f=b.newObject,g=b.putStream;if(c.metadata instanceof a.API.TTFFont&&"WinAnsiEncoding"===c.encoding){for(var h=c.metadata.rawData,i="",j=0;j<h.length;j++)i+=String.fromCharCode(h[j]);var k=f();g({data:i,addLength1:!0,objectId:k}),e("endobj");var l=f();g({data:d(c.metadata.toUnicode),addLength1:!0,objectId:l}),e("endobj");var m=f();e("<<"),e("/Descent "+c.metadata.decender),e("/CapHeight "+c.metadata.capHeight),e("/StemV "+c.metadata.stemV),e("/Type /FontDescriptor"),e("/FontFile2 "+k+" 0 R"),e("/Flags 96"),e("/FontBBox "+a.API.PDFObject.convert(c.metadata.bbox)),e("/FontName /"+az(c.fontName)),e("/ItalicAngle "+c.metadata.italicAngle),e("/Ascent "+c.metadata.ascender),e(">>"),e("endobj"),c.objectNumber=f();for(var n=0;n<c.metadata.hmtx.widths.length;n++)c.metadata.hmtx.widths[n]=parseInt(c.metadata.hmtx.widths[n]*(1e3/c.metadata.head.unitsPerEm));e("<</Subtype/TrueType/Type/Font/ToUnicode "+l+" 0 R/BaseFont/"+az(c.fontName)+"/FontDescriptor "+m+" 0 R/Encoding/"+c.encoding+" /FirstChar 29 /LastChar 255 /Widths "+a.API.PDFObject.convert(c.metadata.hmtx.widths)+">>"),e("endobj"),c.isAlreadyPutted=!0}}(b)}]);var e=function(a){var b,d=a.text||"",e=a.x,f=a.y,g=a.options||{},h=a.mutex||{},i=h.pdfEscape,j=h.activeFontKey,k=h.fonts,l=j,m="",n=0,o="",p=k[l].encoding;if("Identity-H"!==k[l].encoding)return{text:d,x:e,y:f,options:g,mutex:h};for(o=d,l=j,Array.isArray(d)&&(o=d[0]),n=0;n<o.length;n+=1)k[l].metadata.hasOwnProperty("cmap")&&(b=k[l].metadata.cmap.unicode.codeMap[o[n].charCodeAt(0)]),b||256>o[n].charCodeAt(0)&&k[l].metadata.hasOwnProperty("Unicode")?m+=o[n]:m+="";var q="";return 14>parseInt(l.slice(1))||"WinAnsiEncoding"===p?q=i(m,l).split("").map(function(a){return a.charCodeAt(0).toString(16)}).join(""):"Identity-H"===p&&(q=c(m,k[l])),h.isHex=!0,{text:q,x:e,y:f,options:g,mutex:h}};b.events.push(["postProcessText",function(a){var b=a.text||"",c=[],d={text:b,x:a.x,y:a.y,options:a.options,mutex:a.mutex};if(Array.isArray(b)){var f=0;for(f=0;f<b.length;f+=1)Array.isArray(b[f])&&3===b[f].length?c.push([e(Object.assign({},d,{text:b[f][0]})).text,b[f][1],b[f][2]]):c.push(e(Object.assign({},d,{text:b[f]})).text);a.text=c}else a.text=e(Object.assign({},d,{text:b})).text}])}(aF),function(a){var b=function(){return void 0===this.internal.vFS&&(this.internal.vFS={}),!0};a.existsFileInVFS=function(a){return b.call(this),void 0!==this.internal.vFS[a]},a.addFileToVFS=function(a,c){return b.call(this),this.internal.vFS[a]=c,this},a.getFileFromVFS=function(a){return b.call(this),void 0!==this.internal.vFS[a]?this.internal.vFS[a]:null}}(aF.API),function(a){a.__bidiEngine__=a.prototype.__bidiEngine__=function(a){var c,d,e,f,g,h,i,j=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],k=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],l={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},m={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},n=["(",")","(","<",">","<","[","]","[","{","}","{","\xab","\xbb","\xab","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],o=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),p=!1,q=0;this.__bidiEngine__={};var r=function(a){var c=a.charCodeAt(),d=c>>8,e=m[d];return void 0!==e?b[256*e+(255&c)]:252===d||253===d?"AL":o.test(d)?"L":8===d?"R":"N"},s=function(a){for(var b,c=0;c<a.length&&"L"!==(b=r(a.charAt(c)));c++)if("R"===b)return!0;return!1},t=function(a,b,g,h){var i,j,k,l,m=b[h];switch(m){case"L":case"R":case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":p=!1;break;case"N":case"AN":break;case"EN":p&&(m="AN");break;case"AL":p=!0,m="R";break;case"WS":case"BN":m="N";break;case"CS":h<1||h+1>=b.length||"EN"!==(i=g[h-1])&&"AN"!==i||"EN"!==(j=b[h+1])&&"AN"!==j?m="N":p&&(j="AN"),m=j===i?j:"N";break;case"ES":m="EN"===(i=h>0?g[h-1]:"B")&&h+1<b.length&&"EN"===b[h+1]?"EN":"N";break;case"ET":if(h>0&&"EN"===g[h-1]){m="EN";break}if(p){m="N";break}for(k=h+1,l=b.length;k<l&&"ET"===b[k];)k++;m=k<l&&"EN"===b[k]?"EN":"N";break;case"NSM":if(e&&!f){for(l=b.length,k=h+1;k<l&&"NSM"===b[k];)k++;if(k<l){var n=a[h];if(i=b[k],(n>=1425&&n<=2303||64286===n)&&("R"===i||"AL"===i)){m="R";break}}}m=h<1||"B"===(i=b[h-1])?"N":g[h-1];break;case"B":p=!1,c=!0,m=q;break;case"S":d=!0,m="N"}return m},u=function(a,b,c){var d=a.split("");return c&&v(d,c,{hiLevel:q}),d.reverse(),b&&b.reverse(),d.join("")},v=function(a,b,e){var f,g,h,i,m,n=-1,o=a.length,s=0,u=[],v=q?k:j,w=[];for(p=!1,c=!1,d=!1,g=0;g<o;g++)w[g]=r(a[g]);for(h=0;h<o;h++){if(m=s,u[h]=t(a,w,u,h),f=240&(s=v[m][l[u[h]]]),s&=15,b[h]=i=v[s][5],f>0)if(16===f){for(g=n;g<h;g++)b[g]=1;n=-1}else n=-1;if(v[s][6])-1===n&&(n=h);else if(n>-1){for(g=n;g<h;g++)b[g]=i;n=-1}"B"===w[h]&&(b[h]=0),e.hiLevel|=i}d&&function(a,b,c){for(var d=0;d<c;d++)if("S"===a[d]){b[d]=q;for(var e=d-1;e>=0&&"WS"===a[e];e--)b[e]=q}}(w,b,o)},w=function(a,b,d,e,f){if(!(f.hiLevel<a)){if(1===a&&1===q&&!c)return b.reverse(),void(d&&d.reverse());for(var g,h,i,j,k=b.length,l=0;l<k;){if(e[l]>=a){for(i=l+1;i<k&&e[i]>=a;)i++;for(j=l,h=i-1;j<h;j++,h--)g=b[j],b[j]=b[h],b[h]=g,d&&(g=d[j],d[j]=d[h],d[h]=g);l=i}l++}}},x=function(a,b,c){var d=a.split(""),e={hiLevel:q};return c||(c=[]),v(d,c,e),function(a,b,c){if(0!==c.hiLevel&&i)for(var d,e=0;e<a.length;e++)1===b[e]&&(d=n.indexOf(a[e]))>=0&&(a[e]=n[d+1])}(d,c,e),w(2,d,b,c,e),w(1,d,b,c,e),d.join("")};return this.__bidiEngine__.doBidiReorder=function(a,b,c){if(function(a,b){if(b)for(var c=0;c<a.length;c++)b[c]=c;void 0===f&&(f=s(a)),void 0===h&&(h=s(a))}(a,b),e||!g||h)if(e&&g&&f^h)q=+!!f,a=u(a,b,c);else if(!e&&g&&h)q=+!!f,a=u(a=x(a,b,c),b);else if(!e||f||g||h){if(e&&!g&&f^h)a=u(a,b),f?(q=0,a=x(a,b,c)):(q=1,a=u(a=x(a,b,c),b));else if(e&&f&&!g&&h)q=1,a=u(a=x(a,b,c),b);else if(!e&&!g&&f^h){var d=i;f?(q=1,a=x(a,b,c),q=0,i=!1,a=x(a,b,c),i=d):(q=0,a=u(a=x(a,b,c),b),q=1,i=!1,a=x(a,b,c),i=d,a=u(a,b))}}else q=0,a=x(a,b,c);else q=+!!f,a=x(a,b,c);return a},this.__bidiEngine__.setOptions=function(a){a&&(e=a.isInputVisual,g=a.isOutputVisual,f=a.isInputRtl,h=a.isOutputRtl,i=a.isSymmetricSwapping)},this.__bidiEngine__.setOptions(a),this.__bidiEngine__};var b=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],c=new a.__bidiEngine__({isInputVisual:!0});a.API.events.push(["postProcessText",function(a){var b=a.text;a.x,a.y;var d=a.options||{};a.mutex,d.lang;var e=[];if(d.isInputVisual="boolean"!=typeof d.isInputVisual||d.isInputVisual,c.setOptions(d),"[object Array]"===Object.prototype.toString.call(b)){var f=0;for(e=[],f=0;f<b.length;f+=1)"[object Array]"===Object.prototype.toString.call(b[f])?e.push([c.doBidiReorder(b[f][0]),b[f][1],b[f][2]]):e.push([c.doBidiReorder(b[f])]);a.text=e}else a.text=c.doBidiReorder(b);c.setOptions({isInputVisual:!0})}])}(aF),aF.API.TTFFont=function(){function a(a){var b;if(this.rawData=a,b=this.contents=new bM(a),this.contents.pos=4,"ttcf"===b.readString(4))throw Error("TTCF not supported.");b.pos=0,this.parse(),this.subset=new b3(this),this.registerTTF()}return a.open=function(b){return new a(b)},a.prototype.parse=function(){return this.directory=new bN(this.contents),this.head=new bQ(this),this.name=new bX(this),this.cmap=new bS(this),this.toUnicode={},this.hhea=new bT(this),this.maxp=new bY(this),this.hmtx=new bZ(this),this.post=new bV(this),this.os2=new bU(this),this.loca=new b2(this),this.glyf=new b_(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},a.prototype.registerTTF=function(){var a,b,c,d,e;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var b,c,d,e;for(e=[],b=0,c=(d=this.bbox).length;b<c;b++)a=d[b],e.push(Math.round(a*this.scaleFactor));return e}).call(this),this.stemV=0,this.post.exists?(c=255&(d=this.post.italic_angle),0!=(32768&(b=d>>16))&&(b=-(1+(65535^b))),this.italicAngle=+(b+"."+c)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(e=this.familyClass)||2===e||3===e||4===e||5===e||7===e,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw Error("No unicode cmap for font")},a.prototype.characterToGlyph=function(a){var b;return(null!=(b=this.cmap.unicode)?b.codeMap[a]:void 0)||0},a.prototype.widthOfGlyph=function(a){var b;return b=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(a).advance*b},a.prototype.widthOfString=function(a,b,c){var d,e,f,g;for(f=0,e=0,g=(a=""+a).length;0<=g?e<g:e>g;e=0<=g?++e:--e)d=a.charCodeAt(e),f+=this.widthOfGlyph(this.characterToGlyph(d))+1e3/b*c||0;return b/1e3*f},a.prototype.lineHeight=function(a,b){var c;return null==b&&(b=!1),c=b?this.lineGap:0,(this.ascender+c-this.decender)/1e3*a},a}();var bL,bM=function(){function a(a){this.data=null!=a?a:[],this.pos=0,this.length=this.data.length}return a.prototype.readByte=function(){return this.data[this.pos++]},a.prototype.writeByte=function(a){return this.data[this.pos++]=a},a.prototype.readUInt32=function(){return 0x1000000*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},a.prototype.writeUInt32=function(a){return this.writeByte(a>>>24&255),this.writeByte(a>>16&255),this.writeByte(a>>8&255),this.writeByte(255&a)},a.prototype.readInt32=function(){var a;return(a=this.readUInt32())>=0x80000000?a-0x100000000:a},a.prototype.writeInt32=function(a){return a<0&&(a+=0x100000000),this.writeUInt32(a)},a.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},a.prototype.writeUInt16=function(a){return this.writeByte(a>>8&255),this.writeByte(255&a)},a.prototype.readInt16=function(){var a;return(a=this.readUInt16())>=32768?a-65536:a},a.prototype.writeInt16=function(a){return a<0&&(a+=65536),this.writeUInt16(a)},a.prototype.readString=function(a){var b,c;for(c=[],b=0;0<=a?b<a:b>a;b=0<=a?++b:--b)c[b]=String.fromCharCode(this.readByte());return c.join("")},a.prototype.writeString=function(a){var b,c,d;for(d=[],b=0,c=a.length;0<=c?b<c:b>c;b=0<=c?++b:--b)d.push(this.writeByte(a.charCodeAt(b)));return d},a.prototype.readShort=function(){return this.readInt16()},a.prototype.writeShort=function(a){return this.writeInt16(a)},a.prototype.readLongLong=function(){var a,b,c,d,e,f,g,h;return a=this.readByte(),b=this.readByte(),c=this.readByte(),d=this.readByte(),e=this.readByte(),f=this.readByte(),g=this.readByte(),h=this.readByte(),128&a?-1*(0x100000000000000*(255^a)+0x1000000000000*(255^b)+0x10000000000*(255^c)+0x100000000*(255^d)+0x1000000*(255^e)+65536*(255^f)+256*(255^g)+(255^h)+1):0x100000000000000*a+0x1000000000000*b+0x10000000000*c+0x100000000*d+0x1000000*e+65536*f+256*g+h},a.prototype.writeLongLong=function(a){var b,c;return b=Math.floor(a/0x100000000),c=0|a,this.writeByte(b>>24&255),this.writeByte(b>>16&255),this.writeByte(b>>8&255),this.writeByte(255&b),this.writeByte(c>>24&255),this.writeByte(c>>16&255),this.writeByte(c>>8&255),this.writeByte(255&c)},a.prototype.readInt=function(){return this.readInt32()},a.prototype.writeInt=function(a){return this.writeInt32(a)},a.prototype.read=function(a){var b,c;for(b=[],c=0;0<=a?c<a:c>a;c=0<=a?++c:--c)b.push(this.readByte());return b},a.prototype.write=function(a){var b,c,d,e;for(e=[],c=0,d=a.length;c<d;c++)b=a[c],e.push(this.writeByte(b));return e},a}(),bN=function(){var a;function b(a){var b,c,d;for(this.scalarType=a.readInt(),this.tableCount=a.readShort(),this.searchRange=a.readShort(),this.entrySelector=a.readShort(),this.rangeShift=a.readShort(),this.tables={},c=0,d=this.tableCount;0<=d?c<d:c>d;c=0<=d?++c:--c)b={tag:a.readString(4),checksum:a.readInt(),offset:a.readInt(),length:a.readInt()},this.tables[b.tag]=b}return b.prototype.encode=function(b){var c,d,e,f,g,h,i,j,k,l,m,n,o;for(o in f=Math.floor((k=16*Math.floor(Math.log(m=Object.keys(b).length)/(h=Math.log(2))))/h),j=16*m-k,(d=new bM).writeInt(this.scalarType),d.writeShort(m),d.writeShort(k),d.writeShort(f),d.writeShort(j),e=16*m,i=d.pos+e,g=null,n=[],b)for(l=b[o],d.writeString(o),d.writeInt(a(l)),d.writeInt(i),d.writeInt(l.length),n=n.concat(l),"head"===o&&(g=i),i+=l.length;i%4;)n.push(0),i++;return d.write(n),c=0xb1b0afba-a(d.data),d.pos=g+8,d.writeUInt32(c),d.data},a=function(a){var b,c,d,e;for(a=b$.call(a);a.length%4;)a.push(0);for(d=new bM(a),c=0,b=0,e=a.length;b<e;b=b+=4)c+=d.readUInt32();return 0|c},b}(),bO={}.hasOwnProperty,bP=function(a,b){for(var c in b)bO.call(b,c)&&(a[c]=b[c]);function d(){this.constructor=a}return d.prototype=b.prototype,a.prototype=new d,a.__super__=b.prototype,a};bL=function(){function a(a){var b;this.file=a,b=this.file.directory.tables[this.tag],this.exists=!!b,b&&(this.offset=b.offset,this.length=b.length,this.parse(this.file.contents))}return a.prototype.parse=function(){},a.prototype.encode=function(){},a.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},a}();var bQ=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="head",b.prototype.parse=function(a){return a.pos=this.offset,this.version=a.readInt(),this.revision=a.readInt(),this.checkSumAdjustment=a.readInt(),this.magicNumber=a.readInt(),this.flags=a.readShort(),this.unitsPerEm=a.readShort(),this.created=a.readLongLong(),this.modified=a.readLongLong(),this.xMin=a.readShort(),this.yMin=a.readShort(),this.xMax=a.readShort(),this.yMax=a.readShort(),this.macStyle=a.readShort(),this.lowestRecPPEM=a.readShort(),this.fontDirectionHint=a.readShort(),this.indexToLocFormat=a.readShort(),this.glyphDataFormat=a.readShort()},b.prototype.encode=function(a){var b;return(b=new bM).writeInt(this.version),b.writeInt(this.revision),b.writeInt(this.checkSumAdjustment),b.writeInt(this.magicNumber),b.writeShort(this.flags),b.writeShort(this.unitsPerEm),b.writeLongLong(this.created),b.writeLongLong(this.modified),b.writeShort(this.xMin),b.writeShort(this.yMin),b.writeShort(this.xMax),b.writeShort(this.yMax),b.writeShort(this.macStyle),b.writeShort(this.lowestRecPPEM),b.writeShort(this.fontDirectionHint),b.writeShort(a),b.writeShort(this.glyphDataFormat),b.data},b}(),bR=function(){function a(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r;switch(this.platformID=a.readUInt16(),this.encodingID=a.readShort(),this.offset=b+a.readInt(),k=a.pos,a.pos=this.offset,this.format=a.readUInt16(),this.length=a.readUInt16(),this.language=a.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(h=0;h<256;++h)this.codeMap[h]=a.readByte();break;case 4:for(l=a.readUInt16()/2,a.pos+=6,e=function(){var b,c;for(c=[],h=b=0;0<=l?b<l:b>l;h=0<=l?++b:--b)c.push(a.readUInt16());return c}(),a.pos+=2,n=function(){var b,c;for(c=[],h=b=0;0<=l?b<l:b>l;h=0<=l?++b:--b)c.push(a.readUInt16());return c}(),i=function(){var b,c;for(c=[],h=b=0;0<=l?b<l:b>l;h=0<=l?++b:--b)c.push(a.readUInt16());return c}(),j=function(){var b,c;for(c=[],h=b=0;0<=l?b<l:b>l;h=0<=l?++b:--b)c.push(a.readUInt16());return c}(),d=(this.length-a.pos+this.offset)/2,g=function(){var b,c;for(c=[],h=b=0;0<=d?b<d:b>d;h=0<=d?++b:--b)c.push(a.readUInt16());return c}(),h=p=0,r=e.length;p<r;h=++p)for(o=e[h],c=q=m=n[h];m<=o?q<=o:q>=o;c=m<=o?++q:--q)0===j[h]?f=c+i[h]:0!==(f=g[j[h]/2+(c-m)-(l-h)]||0)&&(f+=i[h]),this.codeMap[c]=65535&f}a.pos=k}return a.encode=function(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V;switch(D=new bM,f=Object.keys(a).sort(function(a,b){return a-b}),b){case"macroman":for(o=0,p=function(){var a=[];for(n=0;n<256;++n)a.push(0);return a}(),r={0:0},e={},E=0,I=f.length;E<I;E++)null==r[S=a[d=f[E]]]&&(r[S]=++o),e[d]={old:a[d],new:r[a[d]]},p[d]=r[a[d]];return D.writeUInt16(1),D.writeUInt16(0),D.writeUInt32(12),D.writeUInt16(0),D.writeUInt16(262),D.writeUInt16(0),D.write(p),{charMap:e,subtable:D.data,maxGlyphID:o+1};case"unicode":for(B=[],k=[],s=0,r={},c={},q=i=null,F=0,J=f.length;F<J;F++)null==r[u=a[d=f[F]]]&&(r[u]=++s),c[d]={old:u,new:r[u]},g=r[u]-d,null!=q&&g===i||(q&&k.push(q),B.push(d),i=g),q=d;for(q&&k.push(q),k.push(65535),B.push(65535),z=2*(y=B.length),l=Math.log((x=2*Math.pow(Math.log(y)/Math.LN2,2))/2)/Math.LN2,w=2*y-x,h=[],v=[],m=[],n=G=0,K=B.length;G<K;n=++G){if(A=B[n],j=k[n],65535===A){h.push(0),v.push(0);break}if(A-(C=c[A].new)>=32768)for(h.push(0),v.push(2*(m.length+y-n)),d=H=A;A<=j?H<=j:H>=j;d=A<=j?++H:--H)m.push(c[d].new);else h.push(C-A),v.push(0)}for(D.writeUInt16(3),D.writeUInt16(1),D.writeUInt32(12),D.writeUInt16(4),D.writeUInt16(16+8*y+2*m.length),D.writeUInt16(0),D.writeUInt16(z),D.writeUInt16(x),D.writeUInt16(l),D.writeUInt16(w),Q=0,L=k.length;Q<L;Q++)d=k[Q],D.writeUInt16(d);for(D.writeUInt16(0),R=0,M=B.length;R<M;R++)d=B[R],D.writeUInt16(d);for(T=0,N=h.length;T<N;T++)g=h[T],D.writeUInt16(g);for(U=0,O=v.length;U<O;U++)t=v[U],D.writeUInt16(t);for(V=0,P=m.length;V<P;V++)o=m[V],D.writeUInt16(o);return{charMap:c,subtable:D.data,maxGlyphID:s+1}}},a}(),bS=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="cmap",b.prototype.parse=function(a){var b,c,d;for(a.pos=this.offset,this.version=a.readUInt16(),d=a.readUInt16(),this.tables=[],this.unicode=null,c=0;0<=d?c<d:c>d;c=0<=d?++c:--c)b=new bR(a,this.offset),this.tables.push(b),b.isUnicode&&null==this.unicode&&(this.unicode=b);return!0},b.encode=function(a,b){var c,d;return null==b&&(b="macroman"),c=bR.encode(a,b),(d=new bM).writeUInt16(0),d.writeUInt16(1),c.table=d.data.concat(c.subtable),c},b}(),bT=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="hhea",b.prototype.parse=function(a){return a.pos=this.offset,this.version=a.readInt(),this.ascender=a.readShort(),this.decender=a.readShort(),this.lineGap=a.readShort(),this.advanceWidthMax=a.readShort(),this.minLeftSideBearing=a.readShort(),this.minRightSideBearing=a.readShort(),this.xMaxExtent=a.readShort(),this.caretSlopeRise=a.readShort(),this.caretSlopeRun=a.readShort(),this.caretOffset=a.readShort(),a.pos+=8,this.metricDataFormat=a.readShort(),this.numberOfMetrics=a.readUInt16()},b}(),bU=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="OS/2",b.prototype.parse=function(a){if(a.pos=this.offset,this.version=a.readUInt16(),this.averageCharWidth=a.readShort(),this.weightClass=a.readUInt16(),this.widthClass=a.readUInt16(),this.type=a.readShort(),this.ySubscriptXSize=a.readShort(),this.ySubscriptYSize=a.readShort(),this.ySubscriptXOffset=a.readShort(),this.ySubscriptYOffset=a.readShort(),this.ySuperscriptXSize=a.readShort(),this.ySuperscriptYSize=a.readShort(),this.ySuperscriptXOffset=a.readShort(),this.ySuperscriptYOffset=a.readShort(),this.yStrikeoutSize=a.readShort(),this.yStrikeoutPosition=a.readShort(),this.familyClass=a.readShort(),this.panose=function(){var b,c;for(c=[],b=0;b<10;++b)c.push(a.readByte());return c}(),this.charRange=function(){var b,c;for(c=[],b=0;b<4;++b)c.push(a.readInt());return c}(),this.vendorID=a.readString(4),this.selection=a.readShort(),this.firstCharIndex=a.readShort(),this.lastCharIndex=a.readShort(),this.version>0&&(this.ascent=a.readShort(),this.descent=a.readShort(),this.lineGap=a.readShort(),this.winAscent=a.readShort(),this.winDescent=a.readShort(),this.codePageRange=function(){var b,c;for(c=[],b=0;b<2;b=++b)c.push(a.readInt());return c}(),this.version>1))return this.xHeight=a.readShort(),this.capHeight=a.readShort(),this.defaultChar=a.readShort(),this.breakChar=a.readShort(),this.maxContext=a.readShort()},b}(),bV=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="post",b.prototype.parse=function(a){var b,c,d,e;switch(a.pos=this.offset,this.format=a.readInt(),this.italicAngle=a.readInt(),this.underlinePosition=a.readShort(),this.underlineThickness=a.readShort(),this.isFixedPitch=a.readInt(),this.minMemType42=a.readInt(),this.maxMemType42=a.readInt(),this.minMemType1=a.readInt(),this.maxMemType1=a.readInt(),this.format){case 65536:case 196608:break;case 131072:for(c=a.readUInt16(),this.glyphNameIndex=[],e=0;0<=c?e<c:e>c;e=0<=c?++e:--e)this.glyphNameIndex.push(a.readUInt16());for(this.names=[],d=[];a.pos<this.offset+this.length;)b=a.readByte(),d.push(this.names.push(a.readString(b)));return d;case 151552:return c=a.readUInt16(),this.offsets=a.read(c);case 262144:return this.map=(function(){var b,c,d;for(d=[],e=b=0,c=this.file.maxp.numGlyphs;0<=c?b<c:b>c;e=0<=c?++b:--b)d.push(a.readUInt32());return d}).call(this)}},b}(),bW=function(a,b){this.raw=a,this.length=a.length,this.platformID=b.platformID,this.encodingID=b.encodingID,this.languageID=b.languageID},bX=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="name",b.prototype.parse=function(a){var b,c,d,e,f,g,h,i,j,k;for(a.pos=this.offset,a.readShort(),b=a.readShort(),g=a.readShort(),c=[],e=0;0<=b?e<b:e>b;e=0<=b?++e:--e)c.push({platformID:a.readShort(),encodingID:a.readShort(),languageID:a.readShort(),nameID:a.readShort(),length:a.readShort(),offset:this.offset+g+a.readShort()});for(h={},e=i=0,j=c.length;i<j;e=++i)a.pos=(d=c[e]).offset,f=new bW(a.readString(d.length),d),null==h[k=d.nameID]&&(h[k]=[]),h[d.nameID].push(f);this.strings=h,this.copyright=h[0],this.fontFamily=h[1],this.fontSubfamily=h[2],this.uniqueSubfamily=h[3],this.fontName=h[4],this.version=h[5];try{this.postscriptName=h[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(a){this.postscriptName=h[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=h[7],this.manufacturer=h[8],this.designer=h[9],this.description=h[10],this.vendorUrl=h[11],this.designerUrl=h[12],this.license=h[13],this.licenseUrl=h[14],this.preferredFamily=h[15],this.preferredSubfamily=h[17],this.compatibleFull=h[18],this.sampleText=h[19]},b}(),bY=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="maxp",b.prototype.parse=function(a){return a.pos=this.offset,this.version=a.readInt(),this.numGlyphs=a.readUInt16(),this.maxPoints=a.readUInt16(),this.maxContours=a.readUInt16(),this.maxCompositePoints=a.readUInt16(),this.maxComponentContours=a.readUInt16(),this.maxZones=a.readUInt16(),this.maxTwilightPoints=a.readUInt16(),this.maxStorage=a.readUInt16(),this.maxFunctionDefs=a.readUInt16(),this.maxInstructionDefs=a.readUInt16(),this.maxStackElements=a.readUInt16(),this.maxSizeOfInstructions=a.readUInt16(),this.maxComponentElements=a.readUInt16(),this.maxComponentDepth=a.readUInt16()},b}(),bZ=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="hmtx",b.prototype.parse=function(a){var b,c,d,e,f,g,h;for(a.pos=this.offset,this.metrics=[],b=0,g=this.file.hhea.numberOfMetrics;0<=g?b<g:b>g;b=0<=g?++b:--b)this.metrics.push({advance:a.readUInt16(),lsb:a.readInt16()});for(d=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var c,e;for(e=[],b=c=0;0<=d?c<d:c>d;b=0<=d?++c:--c)e.push(a.readInt16());return e}(),this.widths=(function(){var a,b,c,d;for(d=[],a=0,b=(c=this.metrics).length;a<b;a++)e=c[a],d.push(e.advance);return d}).call(this),c=this.widths[this.widths.length-1],h=[],b=f=0;0<=d?f<d:f>d;b=0<=d?++f:--f)h.push(this.widths.push(c));return h},b.prototype.forGlyph=function(a){return a in this.metrics?this.metrics[a]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[a-this.metrics.length]}},b}(),b$=[].slice,b_=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="glyf",b.prototype.parse=function(){return this.cache={}},b.prototype.glyphFor=function(a){var b,c,d,e,f,g,h,i,j,k;return a in this.cache?this.cache[a]:(e=this.file.loca,b=this.file.contents,c=e.indexOf(a),0===(d=e.lengthOf(a))?this.cache[a]=null:(b.pos=this.offset+c,f=(g=new bM(b.read(d))).readShort(),i=g.readShort(),k=g.readShort(),h=g.readShort(),j=g.readShort(),this.cache[a]=-1===f?new b1(g,i,k,h,j):new b0(g,f,i,k,h,j),this.cache[a]))},b.prototype.encode=function(a,b,c){var d,e,f,g,h;for(f=[],e=[],g=0,h=b.length;g<h;g++)d=a[b[g]],e.push(f.length),d&&(f=f.concat(d.encode(c)));return e.push(f.length),{table:f,offsets:e}},b}(),b0=function(){function a(a,b,c,d,e,f){this.raw=a,this.numberOfContours=b,this.xMin=c,this.yMin=d,this.xMax=e,this.yMax=f,this.compound=!1}return a.prototype.encode=function(){return this.raw.data},a}(),b1=function(){function a(a,b,c,d,e){var f,g;for(this.raw=a,this.xMin=b,this.yMin=c,this.xMax=d,this.yMax=e,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],f=this.raw;g=f.readShort(),this.glyphOffsets.push(f.pos),this.glyphIDs.push(f.readUInt16()),32&g;)f.pos+=1&g?4:2,128&g?f.pos+=8:64&g?f.pos+=4:8&g&&(f.pos+=2)}return a.prototype.encode=function(){var a,b,c;for(b=new bM(b$.call(this.raw.data)),a=0,c=this.glyphIDs.length;a<c;++a)b.pos=this.glyphOffsets[a];return b.data},a}(),b2=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return bP(b,bL),b.prototype.tag="loca",b.prototype.parse=function(a){var b,c;return a.pos=this.offset,b=this.file.head.indexToLocFormat,this.offsets=0===b?(function(){var b,d;for(d=[],c=0,b=this.length;c<b;c+=2)d.push(2*a.readUInt16());return d}).call(this):(function(){var b,d;for(d=[],c=0,b=this.length;c<b;c+=4)d.push(a.readUInt32());return d}).call(this)},b.prototype.indexOf=function(a){return this.offsets[a]},b.prototype.lengthOf=function(a){return this.offsets[a+1]-this.offsets[a]},b.prototype.encode=function(a,b){for(var c=new Uint32Array(this.offsets.length),d=0,e=0,f=0;f<c.length;++f)if(c[f]=d,e<b.length&&b[e]==f){++e,c[f]=d;var g=this.offsets[f],h=this.offsets[f+1]-g;h>0&&(d+=h)}for(var i=Array(4*c.length),j=0;j<c.length;++j)i[4*j+3]=255&c[j],i[4*j+2]=(65280&c[j])>>8,i[4*j+1]=(0xff0000&c[j])>>16,i[4*j]=(0xff000000&c[j])>>24;return i},b}(),b3=function(){function a(a){this.font=a,this.subset={},this.unicodes={},this.next=33}return a.prototype.generateCmap=function(){var a,b,c,d,e;for(b in d=this.font.cmap.tables[0].codeMap,a={},e=this.subset)c=e[b],a[b]=d[c];return a},a.prototype.glyphsFor=function(a){var b,c,d,e,f,g,h;for(d={},f=0,g=a.length;f<g;f++)d[e=a[f]]=this.font.glyf.glyphFor(e);for(e in b=[],d)(null!=(c=d[e])?c.compound:void 0)&&b.push.apply(b,c.glyphIDs);if(b.length>0)for(e in h=this.glyphsFor(b))c=h[e],d[e]=c;return d},a.prototype.encode=function(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q;for(d in c=bS.encode(this.generateCmap(),"unicode"),f=this.glyphsFor(a),m={0:0},q=c.charMap)m[(h=q[d]).old]=h.new;for(n in l=c.maxGlyphID,f)n in m||(m[n]=l++);return k=Object.keys(j=function(a){var b,c;for(b in c={},a)c[a[b]]=b;return c}(m)).sort(function(a,b){return a-b}),o=function(){var a,b,c;for(c=[],a=0,b=k.length;a<b;a++)g=k[a],c.push(j[g]);return c}(),e=this.font.glyf.encode(f,o,m),i=this.font.loca.encode(e.offsets,o),p={cmap:this.font.cmap.raw(),glyf:e.table,loca:i,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(b)},this.font.os2.exists&&(p["OS/2"]=this.font.os2.raw()),this.font.directory.encode(p)},a}();aF.API.PDFObject=function(){var a;function b(){}return a=function(a,b){return(Array(b+1).join("0")+a).slice(-b)},b.convert=function(c){var d,e,f,g;if(Array.isArray(c))return"["+(function(){var a,e,f;for(f=[],a=0,e=c.length;a<e;a++)d=c[a],f.push(b.convert(d));return f})().join(" ")+"]";if("string"==typeof c)return"/"+c;if(null!=c?c.isString:void 0)return"("+c+")";if(c instanceof Date)return"(D:"+a(c.getUTCFullYear(),4)+a(c.getUTCMonth(),2)+a(c.getUTCDate(),2)+a(c.getUTCHours(),2)+a(c.getUTCMinutes(),2)+a(c.getUTCSeconds(),2)+"Z)";if("[object Object]"===({}).toString.call(c)){for(e in f=["<<"],c)g=c[e],f.push("/"+e+" "+b.convert(g));return f.push(">>"),f.join("\n")}return""+c},b}()},21154:a=>{function b(c){return a.exports=b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},a.exports.__esModule=!0,a.exports.default=a.exports,b(c)}a.exports=b,a.exports.__esModule=!0,a.exports.default=a.exports}};