(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5009],{13717:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},19053:(e,s,a)=>{"use strict";a.d(s,{Ay:()=>o});var t=a(95155),l=a(12115),r=a(35695),i=a(40283),n=a(51154),c=a(1243),d=a(75525);function o(e){let{children:s,requiredPermissions:a=[],requiredRole:o,fallback:x}=e,{isAuthenticated:u,isLoading:h,user:g}=(0,i.As)(),{hasPermission:p,hasAnyPermission:b}=(0,i.Sk)(),f=(0,r.useRouter)();return((0,l.useEffect)(()=>{h||u||f.push("/login")},[u,h,f]),h)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(n.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"جاري التحقق من الصلاحيات..."})]})}):u?o&&(null==g?void 0:g.role)!==o?x||(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,t.jsx)(c.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"غير مصرح لك"}),(0,t.jsxs)("p",{className:"text-gray-600 mb-6",children:["هذه الصفحة مخصصة للمستخدمين من نوع: ",o]}),(0,t.jsx)("button",{onClick:()=>f.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة"})]})}):a.length>0&&!b(a)?x||(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,t.jsx)(d.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"صلاحيات غير كافية"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة"}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,t.jsx)("p",{className:"text-sm text-gray-700 font-medium mb-2",children:"الصلاحيات المطلوبة:"}),(0,t.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:a.map(e=>(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-2 h-2 bg-red-400 rounded-full"}),m(e)]},e))})]}),(0,t.jsx)("button",{onClick:()=>f.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة"})]})}):(0,t.jsx)(t.Fragment,{children:s}):null}let m=e=>({sales_view:"عرض المبيعات",sales_create:"إنشاء مبيعات",sales_edit:"تعديل المبيعات",sales_delete:"حذف المبيعات",sales_print:"طباعة المبيعات",sales_view_prices:"عرض الأسعار",purchases_view:"عرض المشتريات",purchases_create:"إنشاء مشتريات",purchases_edit:"تعديل المشتريات",purchases_delete:"حذف المشتريات",purchases_print:"طباعة المشتريات",inventory_view:"عرض المخزون",inventory_create:"إضافة للمخزون",inventory_edit:"تعديل المخزون",inventory_delete:"حذف من المخزون",inventory_print:"طباعة المخزون",customers_view:"عرض العملاء",customers_create:"إضافة عملاء",customers_edit:"تعديل العملاء",customers_delete:"حذف العملاء",suppliers_view:"عرض الموردين",suppliers_create:"إضافة موردين",suppliers_edit:"تعديل الموردين",suppliers_delete:"حذف الموردين",reports_view:"عرض التقارير",reports_financial:"التقارير المالية",reports_detailed:"التقارير المفصلة",reports_export:"تصدير التقارير",users_view:"عرض المستخدمين",users_create:"إضافة مستخدمين",users_edit:"تعديل المستخدمين",users_delete:"حذف المستخدمين",settings_view:"عرض الإعدادات",settings_edit:"تعديل الإعدادات",cashbox_view:"عرض الصندوق",cashbox_manage:"إدارة الصندوق",returns_view:"عرض المرتجعات",returns_create:"إنشاء مرتجعات",returns_edit:"تعديل المرتجعات",returns_delete:"حذف المرتجعات"})[e]||e},51154:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},58784:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>j});var t=a(95155),l=a(12115),r=a(61932),i=a(19053),n=a(40283),c=a(84616),d=a(47924),o=a(17580),m=a(75525),x=a(55670);let u=(0,a(19946).A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var h=a(69074),g=a(13717),p=a(78749),b=a(92657),f=a(62525),y=a(54416);function j(){let[e,s]=(0,l.useState)([]),[a,y]=(0,l.useState)(!0),[j,N]=(0,l.useState)(""),[_,k]=(0,l.useState)("all"),[A,S]=(0,l.useState)("all"),[C,E]=(0,l.useState)(!1),[D,M]=(0,l.useState)(null),[P,T]=(0,l.useState)(!1),{user:L}=(0,n.As)(),{canCreateUsers:O,canEditUsers:I,canDeleteUsers:z}=(0,n.Sk)(),{logUserActivity:R}=(0,n.Zy)();(0,l.useEffect)(()=>{U()},[]);let U=async()=>{try{y(!0);let e=[{id:"1",username:"admin",email:"<EMAIL>",full_name:"مدير النظام",role:"admin",is_active:!0,last_login:new Date().toISOString(),created_at:"2024-01-01T00:00:00Z"},{id:"2",username:"pharmacist1",email:"<EMAIL>",full_name:"أحمد الصيدلي",role:"pharmacist",is_active:!0,last_login:new Date(Date.now()-864e5).toISOString(),created_at:"2024-01-15T00:00:00Z"},{id:"3",username:"cashier1",email:"<EMAIL>",full_name:"فاطمة الكاشير",role:"cashier",is_active:!1,last_login:null,created_at:"2024-02-01T00:00:00Z"}];s(e)}catch(e){console.error("Error loading users:",e)}finally{y(!1)}},F=e.filter(e=>{let s=e.full_name.toLowerCase().includes(j.toLowerCase())||e.username.toLowerCase().includes(j.toLowerCase())||e.email.toLowerCase().includes(j.toLowerCase()),a="all"===_||e.role===_,t="all"===A||"active"===A&&e.is_active||"inactive"===A&&!e.is_active;return s&&a&&t}),Z=async(a,t)=>{try{s(e.map(e=>e.id===a?{...e,is_active:!t}:e));let l=e.find(e=>e.id===a);l&&await R(t?"DEACTIVATE_USER":"ACTIVATE_USER","".concat(t?"تعطيل":"تفعيل"," المستخدم: ").concat(l.username),{table_name:"users",record_id:a,old_values:{is_active:t},new_values:{is_active:!t}})}catch(e){console.error("Error toggling user status:",e)}},q=async a=>{try{let t=btoa(a.password),l={id:Date.now().toString(),username:a.username,full_name:a.full_name,email:a.email,phone:a.phone,role:a.role,password_hash:t,is_active:!0,created_at:new Date().toISOString(),last_login:null},r=[...e,l];s(r),localStorage.setItem("users",JSON.stringify(r)),await R("CREATE_USER","تم إنشاء مستخدم جديد: ".concat(l.full_name),{table_name:"users",record_id:l.id,new_values:{username:l.username,role:l.role}}),alert("تم إضافة المستخدم بنجاح!")}catch(e){console.error("Error adding user:",e),alert("حدث خطأ أثناء إضافة المستخدم")}},J=async a=>{try{let t=e.find(e=>e.id===a.id),l=e.map(e=>e.id===a.id?{...e,...a}:e);s(l),localStorage.setItem("users",JSON.stringify(l)),await R("UPDATE_USER","تم تحديث بيانات المستخدم: ".concat(a.full_name),{table_name:"users",record_id:a.id,old_values:{full_name:null==t?void 0:t.full_name,email:null==t?void 0:t.email,role:null==t?void 0:t.role},new_values:{full_name:a.full_name,email:a.email,role:a.role}}),alert("تم تحديث بيانات المستخدم بنجاح!")}catch(e){console.error("Error updating user:",e),alert("حدث خطأ أثناء تحديث المستخدم")}},G=async a=>{if(confirm("هل أنت متأكد من حذف هذا المستخدم؟"))try{let t=e.find(e=>e.id===a),l=e.filter(e=>e.id!==a);s(l),localStorage.setItem("users",JSON.stringify(l)),await R("DELETE_USER","تم حذف المستخدم: ".concat(null==t?void 0:t.full_name),{table_name:"users",record_id:a,old_values:{username:null==t?void 0:t.username,role:null==t?void 0:t.role}}),alert("تم حذف المستخدم بنجاح!")}catch(e){console.error("Error deleting user:",e),alert("حدث خطأ أثناء حذف المستخدم")}};return(0,t.jsx)(i.Ay,{requiredPermissions:["users_view"],children:(0,t.jsxs)(r.A,{children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-darker",children:"إدارة المستخدمين"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة حسابات المستخدمين وصلاحياتهم"})]}),O&&(0,t.jsxs)("button",{onClick:()=>E(!0),className:"flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors w-full md:w-auto min-h-[44px]",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-semibold",children:"إضافة مستخدم جديد"})]})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)("input",{type:"text",placeholder:"البحث في المستخدمين...",value:j,onChange:e=>N(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-dark font-medium min-h-[44px]"})]}),(0,t.jsx)("div",{children:(0,t.jsxs)("select",{value:_,onChange:e=>k(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-dark font-medium min-h-[44px]",children:[(0,t.jsx)("option",{value:"all",children:"جميع الأدوار"}),(0,t.jsx)("option",{value:"admin",children:"مدير النظام"}),(0,t.jsx)("option",{value:"manager",children:"مدير"}),(0,t.jsx)("option",{value:"pharmacist",children:"صيدلي"}),(0,t.jsx)("option",{value:"cashier",children:"كاشير"}),(0,t.jsx)("option",{value:"viewer",children:"مشاهد"})]})}),(0,t.jsx)("div",{children:(0,t.jsxs)("select",{value:A,onChange:e=>S(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-dark font-medium min-h-[44px]",children:[(0,t.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,t.jsx)("option",{value:"active",children:"نشط"}),(0,t.jsx)("option",{value:"inactive",children:"غير نشط"})]})})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:(0,t.jsx)("div",{className:"overflow-x-auto table-responsive",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 text-sm md:text-base",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المستخدم"}),(0,t.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الدور"}),(0,t.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الحالة"}),(0,t.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"آخر دخول"}),(0,t.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"تاريخ الإنشاء"}),(0,t.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a?(0,t.jsx)("tr",{children:(0,t.jsx)("td",{colSpan:6,className:"px-6 py-12 text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,t.jsx)("span",{className:"mr-3 text-gray-600",children:"جاري التحميل..."})]})})}):0===F.length?(0,t.jsx)("tr",{children:(0,t.jsxs)("td",{colSpan:6,className:"px-6 py-12 text-center",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"لا توجد مستخدمين"})]})}):F.map(e=>{var s;return(0,t.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-600"})})}),(0,t.jsxs)("div",{className:"mr-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.full_name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["@",e.username," • ",e.email]})]})]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat({admin:"bg-red-100 text-red-800",manager:"bg-purple-100 text-purple-800",pharmacist:"bg-blue-100 text-blue-800",cashier:"bg-green-100 text-green-800",viewer:"bg-gray-100 text-gray-800"}[e.role]||"bg-gray-100 text-gray-800"),children:[(0,t.jsx)(m.A,{className:"h-3 w-3 ml-1"}),{admin:"مدير النظام",manager:"مدير",pharmacist:"صيدلي",cashier:"كاشير",viewer:"مشاهد"}[s=e.role]||s]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(x.A,{className:"h-3 w-3 ml-1"}),"نشط"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u,{className:"h-3 w-3 ml-1"}),"غير نشط"]})})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.last_login?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 ml-1"}),new Date(e.last_login).toLocaleDateString("ar-EG")]}):"لم يسجل دخول"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("ar-EG")}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[I&&(0,t.jsx)("button",{onClick:()=>{M(e),T(!0)},className:"text-blue-600 hover:text-blue-900",title:"تعديل",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})}),I&&e.id!==(null==L?void 0:L.id)&&(0,t.jsx)("button",{onClick:()=>Z(e.id,e.is_active),className:"".concat(e.is_active?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"),title:e.is_active?"تعطيل":"تفعيل",children:e.is_active?(0,t.jsx)(p.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"})}),z&&e.id!==(null==L?void 0:L.id)&&(0,t.jsx)("button",{onClick:()=>G(e.id),className:"text-red-600 hover:text-red-900",title:"حذف",children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})]})})]},e.id)})})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})}),(0,t.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"إجمالي المستخدمين"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.length})]})})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(x.A,{className:"h-8 w-8 text-green-600"})}),(0,t.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"المستخدمين النشطين"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.filter(e=>e.is_active).length})]})})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(u,{className:"h-8 w-8 text-red-600"})}),(0,t.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"المستخدمين المعطلين"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.filter(e=>!e.is_active).length})]})})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})}),(0,t.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"المديرين"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:e.filter(e=>"admin"===e.role||"manager"===e.role).length})]})})]})})]})]}),C&&(0,t.jsx)(v,{onClose:()=>E(!1),onSave:q}),P&&D&&(0,t.jsx)(w,{user:D,onClose:()=>{T(!1),M(null)},onSave:J})]})})}function v(e){let{onClose:s,onSave:a}=e,[r,i]=(0,l.useState)({username:"",full_name:"",email:"",phone:"",role:"viewer",password:"",confirmPassword:""}),[n,c]=(0,l.useState)(!1),[d,o]=(0,l.useState)({}),m=async e=>{if(e.preventDefault(),(()=>{let e={};return r.username.trim()||(e.username="اسم المستخدم مطلوب"),r.full_name.trim()||(e.full_name="الاسم الكامل مطلوب"),r.email.trim()||(e.email="البريد الإلكتروني مطلوب"),r.password||(e.password="كلمة المرور مطلوبة"),r.password!==r.confirmPassword&&(e.confirmPassword="كلمات المرور غير متطابقة"),r.password&&r.password.length<6&&(e.password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"),o(e),0===Object.keys(e).length})()){c(!0);try{await a(r),s()}catch(e){console.error("Error adding user:",e)}finally{c(!1)}}};return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 md:p-4",children:(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto mobile-modal",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"إضافة مستخدم جديد"}),(0,t.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(y.A,{className:"h-6 w-6"})})]}),(0,t.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم المستخدم *"}),(0,t.jsx)("input",{type:"text",value:r.username,onChange:e=>i({...r,username:e.target.value}),className:"w-full px-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-dark font-medium min-h-[44px] ".concat(d.username?"border-red-500":"border-gray-300"),placeholder:"أدخل اسم المستخدم"}),d.username&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:d.username})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الاسم الكامل *"}),(0,t.jsx)("input",{type:"text",value:r.full_name,onChange:e=>i({...r,full_name:e.target.value}),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(d.full_name?"border-red-500":"border-gray-300"),placeholder:"أدخل الاسم الكامل"}),d.full_name&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:d.full_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البريد الإلكتروني *"}),(0,t.jsx)("input",{type:"email",value:r.email,onChange:e=>i({...r,email:e.target.value}),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(d.email?"border-red-500":"border-gray-300"),placeholder:"أدخل البريد الإلكتروني"}),d.email&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:d.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"رقم الهاتف"}),(0,t.jsx)("input",{type:"tel",value:r.phone,onChange:e=>i({...r,phone:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"أدخل رقم الهاتف"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الدور *"}),(0,t.jsxs)("select",{value:r.role,onChange:e=>i({...r,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"viewer",children:"مشاهد"}),(0,t.jsx)("option",{value:"cashier",children:"كاشير"}),(0,t.jsx)("option",{value:"pharmacist",children:"صيدلي"}),(0,t.jsx)("option",{value:"manager",children:"مدير"}),(0,t.jsx)("option",{value:"admin",children:"مدير النظام"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"كلمة المرور *"}),(0,t.jsx)("input",{type:"password",value:r.password,onChange:e=>i({...r,password:e.target.value}),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(d.password?"border-red-500":"border-gray-300"),placeholder:"أدخل كلمة المرور"}),d.password&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:d.password})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"تأكيد كلمة المرور *"}),(0,t.jsx)("input",{type:"password",value:r.confirmPassword,onChange:e=>i({...r,confirmPassword:e.target.value}),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(d.confirmPassword?"border-red-500":"border-gray-300"),placeholder:"أعد إدخال كلمة المرور"}),d.confirmPassword&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:d.confirmPassword})]}),(0,t.jsxs)("div",{className:"flex gap-3 pt-4 button-group-mobile",children:[(0,t.jsx)("button",{type:"button",onClick:s,className:"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors min-h-[44px]",children:"إلغاء"}),(0,t.jsx)("button",{type:"submit",disabled:n,className:"flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 min-h-[44px]",children:n?"جاري الحفظ...":"حفظ"})]})]})]})})})}function w(e){let{user:s,onClose:a,onSave:r}=e,[i,n]=(0,l.useState)({full_name:s.full_name,email:s.email,phone:s.phone||"",role:s.role,is_active:s.is_active}),[c,d]=(0,l.useState)(!1),o=async e=>{e.preventDefault(),d(!0);try{await r({...s,...i}),a()}catch(e){console.error("Error updating user:",e)}finally{d(!1)}};return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-2xl max-w-md w-full",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"تعديل المستخدم"}),(0,t.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(y.A,{className:"h-6 w-6"})})]}),(0,t.jsxs)("form",{onSubmit:o,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الاسم الكامل"}),(0,t.jsx)("input",{type:"text",value:i.full_name,onChange:e=>n({...i,full_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البريد الإلكتروني"}),(0,t.jsx)("input",{type:"email",value:i.email,onChange:e=>n({...i,email:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"رقم الهاتف"}),(0,t.jsx)("input",{type:"tel",value:i.phone,onChange:e=>n({...i,phone:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الدور"}),(0,t.jsxs)("select",{value:i.role,onChange:e=>n({...i,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"viewer",children:"مشاهد"}),(0,t.jsx)("option",{value:"cashier",children:"كاشير"}),(0,t.jsx)("option",{value:"pharmacist",children:"صيدلي"}),(0,t.jsx)("option",{value:"manager",children:"مدير"}),(0,t.jsx)("option",{value:"admin",children:"مدير النظام"})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",id:"is_active",checked:i.is_active,onChange:e=>n({...i,is_active:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"is_active",className:"mr-2 block text-sm text-gray-900",children:"حساب نشط"})]}),(0,t.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,t.jsx)("button",{type:"button",onClick:a,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"إلغاء"}),(0,t.jsx)("button",{type:"submit",disabled:c,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:c?"جاري الحفظ...":"حفظ التغييرات"})]})]})]})})})}},69074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69469:(e,s,a)=>{Promise.resolve().then(a.bind(a,58784))},78749:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{e.O(0,[6874,6543,8080,1932,8441,5964,7358],()=>e(e.s=69469)),_N_E=e.O()}]);