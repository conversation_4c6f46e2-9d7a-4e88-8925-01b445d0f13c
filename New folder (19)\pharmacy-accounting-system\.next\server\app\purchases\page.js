(()=>{var a={};a.id=313,a.ids=[313],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26611:(a,b,c)=>{Promise.resolve().then(c.bind(c,31987))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31987:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\purchases\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\purchases\\page.tsx","default")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},37827:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["purchases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,31987)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\purchases\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\purchases\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/purchases/page",pathname:"/purchases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/purchases/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55513:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>q});var d=c(60687),e=c(43210),f=c(21979),g=c(79410),h=c(11860),i=c(96474),j=c(19080),k=c(8819),l=c(71444),m=c(84997),n=c(97711),o=c(31836),p=c(56278);function q(){let[a,b]=(0,e.useState)([]),[c,q]=(0,e.useState)(null),[r,s]=(0,e.useState)(0),[t,u]=(0,e.useState)(""),[v,w]=(0,e.useState)(!1),[x,y]=(0,e.useState)(!1),[z,A]=(0,e.useState)({name:"",category:"",manufacturer:"",activeIngredient:"",strength:"",form:"",batchCode:"",quantity:1,unitCost:0,sellingPrice:0,expiryDate:"",receivedDate:""}),[B,C]=(0,e.useState)([]),[D,E]=(0,e.useState)([]),[F,G]=(0,e.useState)(!1),[H,I]=(0,e.useState)("cash"),[J,K]=(0,e.useState)(""),[L,M]=(0,e.useState)(""),[N,O]=(0,e.useState)(!1),[P,Q]=(0,e.useState)(null),[R,S]=(0,e.useState)([]),[T,U]=(0,e.useState)(""),[V,W]=(0,e.useState)(!1),[X,Y]=(0,e.useState)([]),{settings:Z}=(0,n.usePrintSettings)(),{mounted:$,currentDate:_,generateInvoiceNumber:aa,getCurrentDateISO:ab,formatNumber:ac}=(0,p.F)(),ad=async()=>{try{let a=await (0,o.getMedicines)();a.success&&a.data?C(a.data):(console.error("Failed to load medicines:",a.error),C([{id:"1",name:"باراسيتامول 500mg",category:"مسكنات"},{id:"2",name:"أموكسيسيلين 250mg",category:"مضادات حيوية"}]))}catch(a){console.error("Error loading medicines:",a),C([{id:"1",name:"باراسيتامول 500mg",category:"مسكنات"},{id:"2",name:"أموكسيسيلين 250mg",category:"مضادات حيوية"}])}},ae=async()=>{try{let a=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),b=new Map;a.forEach(a=>{let c=`${a.medicineName}_${a.manufacturer}_${a.activeIngredient}`;if(b.has(c)){let d=b.get(c);d.purchaseCount+=1,new Date(a.created_at)>new Date(d.lastPurchaseDate)&&(d.lastUnitCost=a.unitCost,d.lastSellingPrice=a.sellingPrice,d.lastPurchaseDate=a.created_at)}else b.set(c,{medicineName:a.medicineName,category:a.category,manufacturer:a.manufacturer,activeIngredient:a.activeIngredient,strength:a.strength,form:a.form,lastUnitCost:a.unitCost,lastSellingPrice:a.sellingPrice,lastPurchaseDate:a.created_at,purchaseCount:1})}),S(Array.from(b.values()))}catch(a){console.error("Error loading previous purchases:",a)}},af=()=>{A({name:"",category:"",manufacturer:"",activeIngredient:"",strength:"",form:"",batchCode:"",quantity:1,unitCost:0,sellingPrice:0,expiryDate:"",receivedDate:ab()}),U(""),W(!1)},ag=(a,c)=>{let d=Math.max(0,c||0);b(b=>b.map(b=>b.id===a?{...b,quantity:d,totalCost:d*b.unitCost}:b))},ah=()=>a.reduce((a,b)=>a+b.totalCost,0),ai=()=>ah()-r,aj=async()=>{if(!c)return void alert("يرجى اختيار المورد");if(0===a.length)return void alert("يرجى إضافة عناصر للفاتورة");G(!0);try{let d=`PUR-${Date.now()}`,e={invoice_number:d,supplier_id:c.id,total_amount:ah(),discount_amount:r,final_amount:ai(),payment_method:H,payment_status:"cash"===H?"paid":"pending",notes:J,private_notes:L},f=a.map(a=>({medicineId:a.medicineId||null,medicineName:a.medicineName,category:a.category,manufacturer:a.manufacturer,activeIngredient:a.activeIngredient,strength:a.strength,form:a.form,batchCode:a.batchCode,quantity:a.quantity,unitCost:a.unitCost,totalCost:a.totalCost,expiryDate:a.expiryDate,receivedDate:a.receivedDate,sellingPrice:a.sellingPrice})),g=await (0,o.completePurchaseTransaction)(e,f);if(g.success){let g={...e,invoiceNumber:d,date:ab(),supplierName:c?.name,supplierPhone:c?.phone,supplierAddress:c?.address,items:a,subtotal:ah(),discount:r,finalAmount:ai(),purchase_invoice_items:f.map(a=>({...a,unit_cost:a.unitCost,total_cost:a.totalCost,batch_code:a.batchCode,expiry_date:a.expiryDate,medicine_name:a.medicineName,medicines:{name:a.medicineName||"غير محدد",category:a.category||"",manufacturer:a.manufacturer||"",strength:a.strength||"",form:a.form||""}}))};Q(g),alert("تم حفظ فاتورة الشراء بنجاح!"),setTimeout(()=>{(0,n.printInvoice)(g,"purchase",Z)},500);let h=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),i=f.map(a=>({...a,created_at:new Date().toISOString()}));h.push(...i),localStorage.setItem("purchase_invoice_items",JSON.stringify(h)),await ae(),b([]),q(null),s(0),I("cash"),K(""),M(""),await ad()}else alert("حدث خطأ أثناء حفظ الفاتورة: "+(g.error?.message||"خطأ غير معروف"))}catch(a){console.error("Error saving purchase invoice:",a),alert("حدث خطأ أثناء حفظ الفاتورة")}finally{G(!1)}};return(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"فواتير المشتريات"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"إنشاء وإدارة فواتير شراء الأدوية من الموردين"})]}),(0,d.jsx)("div",{className:"flex items-center gap-3",children:(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg px-4 py-2",children:(0,d.jsx)("p",{className:"text-green-800 text-sm font-medium",children:"\uD83D\uDCA1 الطريقة الوحيدة لإضافة أدوية للمخزون"})})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"اختيار المورد"}),c?(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-blue-900",children:c.name}),(0,d.jsxs)("p",{className:"text-sm text-blue-700",children:[c.contact_person," • ",c.phone]})]})]}),(0,d.jsx)("button",{onClick:()=>q(null),className:"text-blue-600 hover:text-blue-800",children:(0,d.jsx)(h.A,{className:"h-4 w-4"})})]}):(0,d.jsxs)("button",{onClick:()=>w(!0),className:"w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-colors",children:[(0,d.jsx)(g.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-gray-600",children:"انقر لاختيار مورد"})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"إضافة الأدوية"}),(0,d.jsxs)("button",{onClick:()=>y(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),"إضافة دواء"]})]}),0===a.length?(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)(j.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,d.jsx)("p",{children:"لم يتم إضافة أي أدوية بعد"}),(0,d.jsx)("p",{className:"text-sm",children:'انقر على "إضافة دواء" لبدء إنشاء الفاتورة'})]}):(0,d.jsx)("div",{className:"space-y-4",children:a.map(a=>(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900 text-lg",children:a.medicineName}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"الفئة:"})," ",a.category]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"الشركة المصنعة:"})," ",a.manufacturer]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"المادة الفعالة:"})," ",a.activeIngredient]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"التركيز:"})," ",a.strength]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"الشكل:"})," ",a.form]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"رقم الوجبة:"})," ",a.batchCode]})]})]}),(0,d.jsx)("button",{onClick:()=>{var c;return c=a.id,void b(a=>a.filter(a=>a.id!==c))},className:"p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200",children:(0,d.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"الكمية"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 justify-center",children:[(0,d.jsx)("button",{onClick:()=>ag(a.id,a.quantity-1),className:"w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300",children:"-"}),(0,d.jsx)("input",{type:"number",value:a.quantity||"",onChange:b=>{let c=b.target.value;if(""===c)ag(a.id,0);else{let b=parseInt(c);isNaN(b)||ag(a.id,b)}},onBlur:b=>{(""===b.target.value||0===parseInt(b.target.value))&&ag(a.id,1)},className:"w-16 text-center font-medium border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500",min:"0",placeholder:"1"}),(0,d.jsx)("button",{onClick:()=>ag(a.id,a.quantity+1),className:"w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300",children:"+"})]})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"سعر الشراء"}),(0,d.jsx)("input",{type:"number",value:a.unitCost,onChange:c=>{var d,e;return d=a.id,e=Number(c.target.value),void b(a=>a.map(a=>a.id===d?{...a,unitCost:e,totalCost:a.quantity*e}:a))},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded text-center",placeholder:"0"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"سعر البيع"}),(0,d.jsx)("input",{type:"number",value:a.sellingPrice,onChange:c=>{var d,e;return d=a.id,e=Number(c.target.value),void b(a=>a.map(a=>a.id===d?{...a,sellingPrice:e}:a))},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded text-center",placeholder:"0"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"الربح"}),(0,d.jsxs)("p",{className:"font-medium text-green-600",children:[ac((a.sellingPrice-a.unitCost)*a.quantity)," د.ع"]})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"المجموع"}),(0,d.jsxs)("p",{className:"font-medium text-blue-600",children:[ac(a.totalCost)," د.ع"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-3 text-sm text-gray-600",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"تاريخ الانتهاء:"})," ",a.expiryDate]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"تاريخ الاستلام:"})," ",a.receivedDate]})]})]},a.id))})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"ملخص الفاتورة"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"المجموع الفرعي:"}),(0,d.jsxs)("span",{className:"font-medium",children:[ac(ah())," د.ع"]})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"الخصم:"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"number",value:r,onChange:a=>s(Number(a.target.value)),className:"w-20 px-2 py-1 text-sm border border-gray-300 rounded text-center",placeholder:"0",min:"0"}),(0,d.jsx)("span",{className:"text-sm text-gray-500",children:"د.ع"})]})]}),(0,d.jsx)("div",{className:"border-t pt-3",children:(0,d.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,d.jsx)("span",{children:"المجموع النهائي:"}),(0,d.jsxs)("span",{className:"text-blue-600",children:[ac(ai())," د.ع"]})]})})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"طريقة الدفع"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)("button",{onClick:()=>I("cash"),className:`p-3 border-2 rounded-lg text-center transition-colors ${"cash"===H?"border-green-500 bg-green-50 text-green-700":"border-gray-300 text-gray-600 hover:border-green-300"}`,children:[(0,d.jsx)("div",{className:"font-medium",children:"نقداً"}),(0,d.jsx)("div",{className:"text-sm",children:"دفع فوري"})]}),(0,d.jsxs)("button",{onClick:()=>I("credit"),className:`p-3 border-2 rounded-lg text-center transition-colors ${"credit"===H?"border-orange-500 bg-orange-50 text-orange-700":"border-gray-300 text-gray-600 hover:border-orange-300"}`,children:[(0,d.jsx)("div",{className:"font-medium",children:"آجل"}),(0,d.jsx)("div",{className:"text-sm",children:"دفع لاحق"})]})]}),"credit"===H&&(0,d.jsx)("div",{className:"mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,d.jsx)("p",{className:"text-orange-800 text-sm",children:"⚠️ سيتم إضافة هذا المبلغ لحساب المورد كدين"})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"الملاحظات"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات عامة (تظهر في الطباعة)"}),(0,d.jsx)("textarea",{value:J,onChange:a=>K(a.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ملاحظات للمورد..."})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات خاصة (للنظام فقط)"}),(0,d.jsx)("textarea",{value:L,onChange:a=>M(a.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50",placeholder:"ملاحظات داخلية..."})]})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("button",{onClick:aj,disabled:0===a.length||!c||F,className:"w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),F?"جاري الحفظ...":"حفظ فاتورة الشراء"]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,d.jsxs)("button",{onClick:()=>{!P&&a.length>0&&aj(),O(!0)},disabled:0===a.length,className:"bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),"معاينة وطباعة"]}),(0,d.jsxs)("button",{onClick:()=>{P?(0,n.printInvoice)(P,"purchase",Z):alert("لا توجد فاتورة للطباعة")},disabled:!P,className:"bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),"طباعة مباشرة"]})]})]})]})]}),v&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"اختيار المورد"}),(0,d.jsx)("button",{onClick:()=>w(!1),className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(h.A,{className:"h-5 w-5"})})]}),(0,d.jsx)("div",{className:"space-y-3",children:D.map(a=>(0,d.jsx)("button",{onClick:()=>{q(a),w(!1)},className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 text-gray-400"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:a.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:[a.contact_person," • ",a.phone]})]})]})},a.id))})]})}),x&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-screen overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"إضافة دواء للفاتورة"}),(0,d.jsxs)("p",{className:"text-sm text-blue-600 mt-1",children:["\uD83D\uDCA1 ابدأ بكتابة اسم الدواء للبحث في ",R.length," دواء من المشتريات السابقة"]})]}),(0,d.jsx)("button",{onClick:()=>{y(!1),af()},className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(h.A,{className:"h-5 w-5"})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-md font-semibold text-gray-800 border-b pb-2",children:"المعلومات الأساسية"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم الدواء *"}),(0,d.jsx)("input",{type:"text",value:T||z.name,onChange:a=>{let b=a.target.value;U(b),A({...z,name:b})},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ابدأ بكتابة اسم الدواء للبحث في المشتريات السابقة..."}),V&&X.length>0&&(0,d.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:X.map((a,b)=>(0,d.jsxs)("div",{onClick:()=>{A({...z,name:a.medicineName,category:a.category,manufacturer:a.manufacturer,activeIngredient:a.activeIngredient,strength:a.strength,form:a.form,unitCost:a.lastUnitCost||0,sellingPrice:a.lastSellingPrice||0}),U(a.medicineName),W(!1)},className:"p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0",children:[(0,d.jsx)("div",{className:"font-medium text-gray-900",children:a.medicineName}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[a.manufacturer," - ",a.category]}),(0,d.jsxs)("div",{className:"text-xs text-blue-600 mt-1",children:["آخر سعر شراء: ",ac(a.lastUnitCost||0)," د.ع | آخر سعر بيع: ",ac(a.lastSellingPrice||0)," د.ع | تم شراؤه ",a.purchaseCount," مرة"]})]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الفئة *"}),(0,d.jsxs)("select",{value:z.category,onChange:a=>A({...z,category:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"",children:"اختر الفئة"}),(0,d.jsx)("option",{value:"مسكنات",children:"مسكنات"}),(0,d.jsx)("option",{value:"مضادات حيوية",children:"مضادات حيوية"}),(0,d.jsx)("option",{value:"فيتامينات",children:"فيتامينات"}),(0,d.jsx)("option",{value:"أدوية القلب",children:"أدوية القلب"}),(0,d.jsx)("option",{value:"أدوية الجهاز الهضمي",children:"أدوية الجهاز الهضمي"}),(0,d.jsx)("option",{value:"أدوية الجهاز التنفسي",children:"أدوية الجهاز التنفسي"}),(0,d.jsx)("option",{value:"أخرى",children:"أخرى"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الشركة المصنعة"}),(0,d.jsx)("input",{type:"text",value:z.manufacturer,onChange:a=>A({...z,manufacturer:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"اسم الشركة المصنعة"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"المادة الفعالة"}),(0,d.jsx)("input",{type:"text",value:z.activeIngredient,onChange:a=>A({...z,activeIngredient:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"المادة الفعالة"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"التركيز"}),(0,d.jsx)("input",{type:"text",value:z.strength,onChange:a=>A({...z,strength:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"500mg"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الشكل *"}),(0,d.jsxs)("select",{value:z.form,onChange:a=>A({...z,form:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"",children:"اختر الشكل"}),(0,d.jsx)("option",{value:"tablet",children:"قرص"}),(0,d.jsx)("option",{value:"capsule",children:"كبسولة"}),(0,d.jsx)("option",{value:"syrup",children:"شراب"}),(0,d.jsx)("option",{value:"injection",children:"حقنة"}),(0,d.jsx)("option",{value:"cream",children:"كريم"}),(0,d.jsx)("option",{value:"drops",children:"قطرة"}),(0,d.jsx)("option",{value:"powder",children:"بودرة"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-md font-semibold text-gray-800 border-b pb-2",children:"تفاصيل الشراء"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"كود الوجبة *"}),(0,d.jsx)("input",{type:"text",value:z.batchCode,onChange:a=>A({...z,batchCode:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"B001"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الكمية *"}),(0,d.jsx)("input",{type:"number",value:z.quantity,onChange:a=>A({...z,quantity:Number(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"1"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"تاريخ الاستلام"}),(0,d.jsx)("input",{type:"date",value:z.receivedDate,onChange:a=>A({...z,receivedDate:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"تاريخ الانتهاء *"}),(0,d.jsx)("input",{type:"date",value:z.expiryDate,onChange:a=>A({...z,expiryDate:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"سعر الشراء *"}),(0,d.jsx)("input",{type:"number",value:z.unitCost,onChange:a=>A({...z,unitCost:Number(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0",min:"0",step:"0.01"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"سعر البيع *"}),(0,d.jsx)("input",{type:"number",value:z.sellingPrice,onChange:a=>A({...z,sellingPrice:Number(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0",min:"0",step:"0.01"})]})]}),(0,d.jsx)("div",{className:"p-3 bg-green-50 rounded-lg",children:(0,d.jsxs)("div",{className:"text-sm text-green-800",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"إجمالي التكلفة:"}),(0,d.jsxs)("span",{className:"font-medium",children:[ac(z.quantity*z.unitCost)," د.ع"]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"الربح المتوقع:"}),(0,d.jsxs)("span",{className:"font-medium",children:[ac((z.sellingPrice-z.unitCost)*z.quantity)," د.ع"]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"نسبة الربح:"}),(0,d.jsx)("span",{className:"font-medium",children:z.unitCost>0?`${((z.sellingPrice-z.unitCost)/z.unitCost*100).toFixed(1)}%`:"0%"})]})]})})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,d.jsx)("button",{type:"button",onClick:()=>{y(!1),af()},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"إلغاء"}),(0,d.jsx)("button",{type:"button",onClick:()=>{if(!z.name||!z.category||!z.form||!z.batchCode||!z.expiryDate||z.unitCost<=0||z.sellingPrice<=0)return void alert("يرجى ملء جميع الحقول المطلوبة");let c=B.find(a=>a.name.toLowerCase()===z.name.toLowerCase());b([...a,{id:`${Date.now()}-${Math.random().toString(36).substr(2,9)}`,medicineId:c?.id||null,medicineName:z.name,category:z.category,manufacturer:z.manufacturer,activeIngredient:z.activeIngredient,strength:z.strength,form:z.form,batchCode:z.batchCode,quantity:z.quantity,unitCost:z.unitCost,sellingPrice:z.sellingPrice,totalCost:z.quantity*z.unitCost,expiryDate:z.expiryDate,receivedDate:z.receivedDate}]),y(!1),af()},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"إضافة للفاتورة"})]})]})}),N&&P&&(0,d.jsx)(m.Ay,{title:"فاتورة مشتريات",data:P,type:"invoice",settings:Z,onClose:()=>O(!1),children:(0,d.jsx)(m.dt,{invoice:P,type:"purchase",settings:Z})})]})})}},55591:a=>{"use strict";a.exports=require("https")},56278:(a,b,c)=>{"use strict";c.d(b,{F:()=>e});var d=c(43210);function e(){let[a,b]=(0,d.useState)(!1),[c,e]=(0,d.useState)("");return{mounted:a,currentDate:c,generateInvoiceNumber:()=>`INV-${Date.now()}`,getCurrentDateISO:()=>new Date().toISOString().split("T")[0],formatNumber:b=>a?b.toLocaleString():b.toString()}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63563:(a,b,c)=>{Promise.resolve().then(c.bind(c,55513))},74075:a=>{"use strict";a.exports=require("zlib")},79410:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,463,314,979,31,711,997],()=>b(b.s=37827));module.exports=c})();