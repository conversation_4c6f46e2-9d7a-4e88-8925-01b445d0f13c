'use client'

import AppLayout from '@/components/AppLayout'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import {
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Calendar,
  Pill,
  Shield
} from 'lucide-react'
import { formatCurrency, formatNumber } from '@/utils/formatters'

const stats = [
  {
    title: 'إجمالي المبيعات اليوم',
    value: formatNumber(2450000),
    unit: 'د.ع',
    change: '+12%',
    changeType: 'positive' as const,
    icon: DollarSign
  },
  {
    title: 'عدد الفواتير',
    value: formatNumber(156),
    unit: 'فاتورة',
    change: '+8%',
    changeType: 'positive' as const,
    icon: ShoppingCart
  },
  {
    title: 'الأدوية المتوفرة',
    value: formatNumber(1247),
    unit: 'صنف',
    change: '-2%',
    changeType: 'negative' as const,
    icon: Package
  },
  {
    title: 'العملاء النشطين',
    value: formatNumber(89),
    unit: 'عميل',
    change: '+5%',
    changeType: 'positive' as const,
    icon: Users
  }
]

const recentSales = [
  { id: '001', customer: 'أحمد محمد', amount: 125000, time: '10:30 ص' },
  { id: '002', customer: 'فاطمة علي', amount: 89000, time: '10:15 ص' },
  { id: '003', customer: 'محمد حسن', amount: 156000, time: '09:45 ص' },
  { id: '004', customer: 'سارة أحمد', amount: 67000, time: '09:30 ص' },
  { id: '005', customer: 'علي محمود', amount: 234000, time: '09:15 ص' }
]

const expiringMedicines = [
  { name: 'باراسيتامول 500mg', batch: 'B001', expiry: '2024-08-15', quantity: 50 },
  { name: 'أموكسيسيلين 250mg', batch: 'B002', expiry: '2024-08-20', quantity: 30 },
  { name: 'إيبوبروفين 400mg', batch: 'B003', expiry: '2024-08-25', quantity: 25 }
]

export default function Home() {
  const { user } = useAuth()

  return (
    <ProtectedRoute>
      <AppLayout>
      <div className="space-y-8">
        {/* Enhanced Welcome Header */}
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-3xl p-8 text-white shadow-2xl animate-fadeInDown">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white opacity-5 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white opacity-5 rounded-full translate-y-24 -translate-x-24"></div>

          <div className="relative z-10 flex items-center justify-between">
            <div className="animate-fadeInLeft">
              <h1 className="text-5xl font-bold mb-3 text-shadow">مرحباً بك، {user?.full_name || 'المستخدم'}</h1>
              <p className="text-blue-100 text-xl mb-6 font-medium">نظام إدارة الصيدلية - مكتب لارين العلمي</p>
              <div className="flex flex-wrap items-center gap-6">
                <div className="flex items-center gap-3 bg-white/20 backdrop-blur-sm rounded-xl px-4 py-2">
                  <Shield className="h-6 w-6 text-blue-200" />
                  <span className="text-blue-100 font-medium">{user?.role === 'admin' ? 'مدير النظام' : user?.role}</span>
                </div>
                <div className="flex items-center gap-3 bg-white/20 backdrop-blur-sm rounded-xl px-4 py-2">
                  <Calendar className="h-6 w-6 text-blue-200" />
                  <span className="text-blue-100 font-medium">{new Date().toLocaleDateString('ar-EG', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</span>
                </div>
              </div>
            </div>
            <div className="hidden lg:block animate-float">
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 animate-glow">
                <Pill className="h-20 w-20 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            const gradients = [
              'from-blue-500 to-blue-600',
              'from-green-500 to-green-600',
              'from-purple-500 to-purple-600',
              'from-orange-500 to-orange-600'
            ]
            const bgColors = [
              'bg-blue-50',
              'bg-green-50',
              'bg-purple-50',
              'bg-orange-50'
            ]
            const animationDelays = ['animate-fadeInUp', 'animate-fadeInUp animation-delay-150', 'animate-fadeInUp animation-delay-300', 'animate-fadeInUp animation-delay-500']

            return (
              <div key={index} className={`card-enhanced ${bgColors[index]} p-6 hover-lift ${animationDelays[index]} group`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-semibold text-gray-600 mb-3">{stat.title}</p>
                    <div className="flex items-baseline gap-2 mb-4">
                      <p className="text-4xl font-bold text-gray-900">{stat.value}</p>
                      <span className="text-sm text-gray-500 font-medium">{stat.unit}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${
                        stat.changeType === 'positive' ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        <TrendingUp className={`h-3 w-3 ${
                          stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600 rotate-180'
                        }`} />
                        <span className={`text-xs font-bold ${
                          stat.changeType === 'positive' ? 'text-green-700' : 'text-red-700'
                        }`}>
                          {stat.change}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">من الأمس</span>
                    </div>
                  </div>
                  <div className={`bg-gradient-to-br ${gradients[index]} p-4 rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Enhanced Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Sales - Enhanced */}
          <div className="card-enhanced bg-gradient-to-br from-green-50 to-emerald-50 animate-fadeInLeft">
            <div className="p-6 border-b border-green-100">
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-3 rounded-xl">
                  <ShoppingCart className="h-6 w-6 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">آخر المبيعات</h2>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentSales.map((sale, index) => (
                  <div key={sale.id} className={`flex items-center justify-between p-4 bg-white/80 backdrop-blur-sm rounded-xl hover-lift group animate-fadeInUp`} style={{animationDelay: `${index * 100}ms`}}>
                    <div className="flex items-center gap-4">
                      <div className="bg-gradient-to-br from-green-100 to-emerald-100 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                        <ShoppingCart className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="font-bold text-gray-900">فاتورة #{sale.id}</p>
                        <p className="text-sm text-gray-600 font-medium">{sale.customer}</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-green-600 text-lg">{formatCurrency(sale.amount)}</p>
                      <p className="text-sm text-gray-500 font-medium">{sale.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Expiring Medicines - Enhanced */}
          <div className="card-enhanced bg-gradient-to-br from-orange-50 to-amber-50 animate-fadeInRight">
            <div className="p-6 border-b border-orange-100">
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-br from-orange-500 to-amber-600 p-3 rounded-xl animate-pulse">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">أدوية قاربت على الانتهاء</h2>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {expiringMedicines.map((medicine, index) => (
                  <div key={index} className={`flex items-center justify-between p-4 bg-white/80 backdrop-blur-sm rounded-xl hover-lift group animate-fadeInUp`} style={{animationDelay: `${index * 100}ms`}}>
                    <div className="flex items-center gap-4">
                      <div className="bg-gradient-to-br from-orange-100 to-amber-100 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                        <Pill className="h-5 w-5 text-orange-600" />
                      </div>
                      <div>
                        <p className="font-bold text-gray-900">{medicine.name}</p>
                        <p className="text-sm text-gray-600 font-medium">وجبة: {medicine.batch}</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-orange-600 text-lg">{medicine.expiry}</p>
                      <p className="text-sm text-gray-500 font-medium">{medicine.quantity} قطعة</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
    </ProtectedRoute>
  )
}
