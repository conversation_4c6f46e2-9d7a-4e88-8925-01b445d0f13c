"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[988],{10988:(e,t,a)=>{a.r(t),a.d(t,{addCashTransaction:()=>R,addCustomer:()=>v,addInventoryMovement:()=>S,addMedicine:()=>n,addMedicineBatch:()=>l,addPurchaseInvoiceItems:()=>f,addReturnItems:()=>E,addSalesInvoiceItems:()=>m,addSupplier:()=>w,completePurchaseTransaction:()=>P,completeSalesTransaction:()=>I,createPurchaseInvoice:()=>h,createPurchaseReturn:()=>x,createSalesInvoice:()=>u,createSalesReturn:()=>q,fixLocalStorageInvoiceItems:()=>g,getCashBalance:()=>F,getCashTransactions:()=>T,getCustomerDebts:()=>L,getCustomerStatement:()=>Z,getCustomers:()=>y,getInventoryReport:()=>G,getMedicineMovementReport:()=>Y,getMedicineNameFromBatch:()=>p,getMedicines:()=>c,getPurchaseInvoiceForPrint:()=>J,getPurchaseInvoices:()=>D,getPurchasesReport:()=>U,getReturnById:()=>k,getReturnForPrint:()=>V,getReturns:()=>M,getSalesInvoiceForPrint:()=>O,getSalesInvoices:()=>N,getSalesReport:()=>A,getSupplierDebts:()=>B,getSupplierStatement:()=>j,getSuppliers:()=>b,initializeSystemData:()=>o,processReturn:()=>C,updateBatchQuantity:()=>d,updatePaymentStatus:()=>z,updateReturn:()=>X,updateReturnStatus:()=>Q});var r=a(42099);let n=async e=>{try{let{data:t,error:a}=await r.N.from("medicines").insert([{name:e.name,category:e.category,manufacturer:e.manufacturer||"",active_ingredient:e.active_ingredient||"",strength:e.strength||"",form:e.form,unit_price:e.unit_price,selling_price:e.selling_price}]).select().single();if(a)throw a;return{success:!0,data:t}}catch(e){return console.error("Error adding medicine:",e),{success:!1,error:e}}},c=async()=>{try{let{data:e,error:t}=await r.N.from("medicines").select("\n        *,\n        medicine_batches (\n          id,\n          batch_code,\n          expiry_date,\n          quantity,\n          cost_price,\n          selling_price,\n          supplier_id,\n          received_date\n        )\n      ").order("name");if(t)return console.warn("Supabase error fetching medicines, using localStorage:",t),s();return{success:!0,data:e}}catch(e){return console.error("Error fetching medicines:",e),s()}},s=()=>{try{let e=JSON.parse(localStorage.getItem("medicines")||"[]"),t=JSON.parse(localStorage.getItem("medicine_batches")||"[]");if(0===e.length)return console.log("\uD83D\uDD04 لا توجد أدوية في localStorage، إنشاء بيانات تجريبية..."),i();let a=e.map(e=>({...e,medicine_batches:t.filter(t=>t.medicine_id===e.id),batches:t.filter(t=>t.medicine_id===e.id)}));return console.log("✅ تم تحميل ".concat(a.length," دواء من localStorage")),{success:!0,data:a}}catch(e){return console.error("Error loading medicines from localStorage:",e),{success:!1,error:e}}},i=()=>{try{let e=[{id:"med_1",name:"باراسيتامول 500 مجم",category:"مسكنات",manufacturer:"شركة الأدوية العراقية",strength:"500mg",form:"أقراص",created_at:new Date().toISOString()},{id:"med_2",name:"أموكسيسيلين 250 مجم",category:"مضادات حيوية",manufacturer:"شركة بغداد للأدوية",strength:"250mg",form:"كبسولات",created_at:new Date().toISOString()},{id:"med_3",name:"أسبرين 100 مجم",category:"مسكنات",manufacturer:"شركة النهرين",strength:"100mg",form:"أقراص",created_at:new Date().toISOString()},{id:"med_4",name:"إيبوبروفين 400 مجم",category:"مسكنات",manufacturer:"شركة الرافدين",strength:"400mg",form:"أقراص",created_at:new Date().toISOString()},{id:"med_5",name:"أوميبرازول 20 مجم",category:"أدوية المعدة",manufacturer:"شركة دجلة",strength:"20mg",form:"كبسولات",created_at:new Date().toISOString()}],t=[{id:"batch_1",medicine_id:"med_1",batch_code:"PAR001",expiry_date:"2025-12-31",quantity:100,cost_price:500,selling_price:750,received_date:"2024-01-01",created_at:new Date().toISOString()},{id:"batch_2",medicine_id:"med_2",batch_code:"AMX001",expiry_date:"2025-06-30",quantity:50,cost_price:1e3,selling_price:1500,received_date:"2024-01-01",created_at:new Date().toISOString()},{id:"batch_3",medicine_id:"med_3",batch_code:"ASP001",expiry_date:"2026-03-31",quantity:200,cost_price:300,selling_price:500,received_date:"2024-01-01",created_at:new Date().toISOString()},{id:"batch_4",medicine_id:"med_4",batch_code:"IBU001",expiry_date:"2025-09-30",quantity:75,cost_price:800,selling_price:1200,received_date:"2024-01-01",created_at:new Date().toISOString()},{id:"batch_5",medicine_id:"med_5",batch_code:"OME001",expiry_date:"2025-11-30",quantity:30,cost_price:1500,selling_price:2e3,received_date:"2024-01-01",created_at:new Date().toISOString()}],a=[{id:"cust_1",name:"أحمد محمد علي",phone:"07701234567",address:"بغداد - الكرادة",created_at:new Date().toISOString()},{id:"cust_2",name:"فاطمة حسن",phone:"07809876543",address:"بغداد - الجادرية",created_at:new Date().toISOString()}];localStorage.setItem("medicines",JSON.stringify(e)),localStorage.setItem("medicine_batches",JSON.stringify(t)),localStorage.setItem("customers",JSON.stringify(a)),localStorage.setItem("sales_invoices",JSON.stringify([])),localStorage.setItem("sales_invoice_items",JSON.stringify([]));let r=e.map(e=>({...e,medicine_batches:t.filter(t=>t.medicine_id===e.id),batches:t.filter(t=>t.medicine_id===e.id)}));return console.log("✅ تم إنشاء ".concat(r.length," دواء تجريبي")),console.log("✅ تم إنشاء ".concat(t.length," دفعة تجريبية")),console.log("✅ تم إنشاء ".concat(a.length," عميل تجريبي")),{success:!0,data:r}}catch(e){return console.error("Error creating sample medicines:",e),{success:!1,error:e}}},o=async()=>{try{console.log("\uD83D\uDD04 تهيئة بيانات النظام...");let e=JSON.parse(localStorage.getItem("medicines")||"[]"),t=JSON.parse(localStorage.getItem("medicine_batches")||"[]");if(0===e.length||0===t.length)return console.log("\uD83D\uDCE6 إنشاء البيانات الأساسية..."),i();return console.log("✅ البيانات الأساسية موجودة: ".concat(e.length," دواء، ").concat(t.length," دفعة")),{success:!0,data:e}}catch(e){return console.error("Error initializing system data:",e),{success:!1,error:e}}},l=async e=>{try{let{data:t,error:a}=await r.N.from("medicine_batches").insert([e]).select().single();if(a)throw a;return{success:!0,data:t}}catch(e){return console.error("Error adding medicine batch:",e),{success:!1,error:e}}},d=async(e,t)=>{try{let{data:a,error:n}=await r.N.from("medicine_batches").update({quantity:t}).eq("id",e).select().single();if(n){console.warn("Supabase error updating batch quantity, using localStorage:",n);let a=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),r=a.findIndex(t=>t.id===e);if(-1!==r)return a[r].quantity=t,localStorage.setItem("medicine_batches",JSON.stringify(a)),{success:!0,data:a[r]};return{success:!1,error:"Batch not found in localStorage"}}return{success:!0,data:a}}catch(a){console.error("Error updating batch quantity:",a);try{let a=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),r=a.findIndex(t=>t.id===e);if(-1!==r)return a[r].quantity=t,localStorage.setItem("medicine_batches",JSON.stringify(a)),{success:!0,data:a[r]};return{success:!1,error:"Batch not found"}}catch(e){return console.error("LocalStorage fallback failed for batch update:",e),{success:!1,error:e}}}},u=async e=>{try{let{data:t,error:a}=await r.N.from("sales_invoices").insert([e]).select().single();if(a){console.warn("Supabase error, using localStorage:",a);let t={id:"invoice_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,created_at:new Date().toISOString()},r=JSON.parse(localStorage.getItem("sales_invoices")||"[]");return r.push(t),localStorage.setItem("sales_invoices",JSON.stringify(r)),{success:!0,data:t}}return{success:!0,data:t}}catch(t){console.error("Error creating sales invoice:",t);try{let t={id:"invoice_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,created_at:new Date().toISOString()},a=JSON.parse(localStorage.getItem("sales_invoices")||"[]");return a.push(t),localStorage.setItem("sales_invoices",JSON.stringify(a)),{success:!0,data:t}}catch(e){return console.error("LocalStorage fallback failed:",e),{success:!1,error:e}}}},m=async e=>{try{let{data:t,error:a}=await r.N.from("sales_invoice_items").insert(e).select();if(a){console.warn("Supabase error for invoice items, using localStorage:",a),console.log("\uD83D\uDCE6 العناصر الواردة للحفظ:",e);let t=e.map(e=>{let t=e.medicine_name||e.medicineName;return t&&"غير محدد"!==t?(console.log("✅ استخدام اسم الدواء الموجود: ".concat(t)),{id:"item_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,medicine_name:t,medicineName:t,medicine_batches:{batch_code:"",expiry_date:"",medicines:{name:t,category:"",manufacturer:"",strength:"",form:""}},created_at:new Date().toISOString()}):(console.log("⚠️ لا يوجد اسم دواء، سيتم البحث عنه..."),e)}),r=t.filter(e=>!e.medicine_name||"غير محدد"===e.medicine_name),n=t;if(r.length>0){console.log("\uD83D\uDD0D تحسين ".concat(r.length," عنصر يحتاج أسماء أدوية"));let e=await _(r);n=t.map(t=>(!t.medicine_name||"غير محدد"===t.medicine_name)&&e.find(e=>e.medicine_batch_id===t.medicine_batch_id)||t)}let c=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]");return c.push(...n),localStorage.setItem("sales_invoice_items",JSON.stringify(c)),console.log("✅ تم حفظ العناصر في localStorage:",n),{success:!0,data:n}}return{success:!0,data:t}}catch(t){console.error("Error adding sales invoice items:",t);try{console.log("\uD83D\uDD04 Final fallback - حفظ العناصر مع الأسماء الموجودة");let t=e.map(e=>{let t=e.medicine_name||e.medicineName||"غير محدد";return{id:"item_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,medicine_name:t,medicineName:t,medicine_batches:{batch_code:"",expiry_date:"",medicines:{name:t,category:"",manufacturer:"",strength:"",form:""}},created_at:new Date().toISOString()}}),a=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]");return a.push(...t),localStorage.setItem("sales_invoice_items",JSON.stringify(a)),console.log("✅ Final fallback - تم حفظ العناصر:",t),{success:!0,data:t}}catch(e){return console.error("LocalStorage fallback failed for items:",e),{success:!1,error:e}}}},_=async e=>{try{let t=JSON.parse(localStorage.getItem("medicines")||"[]"),a=JSON.parse(localStorage.getItem("medicine_batches")||"[]");return e.map(e=>{let r=e.medicine_name||e.medicineName;if(!r||"غير محدد"===r){let n=a.find(t=>t.id===e.medicine_batch_id),c=t.find(e=>e.id===(null==n?void 0:n.medicine_id));r=(null==c?void 0:c.name)||"غير محدد"}let n=a.find(t=>t.id===e.medicine_batch_id),c=t.find(e=>e.id===(null==n?void 0:n.medicine_id));return{id:"item_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,medicine_name:r,medicineName:r,medicine_batches:{batch_code:(null==n?void 0:n.batch_code)||"",expiry_date:(null==n?void 0:n.expiry_date)||"",medicines:{name:r,category:(null==c?void 0:c.category)||"",manufacturer:(null==c?void 0:c.manufacturer)||"",strength:(null==c?void 0:c.strength)||"",form:(null==c?void 0:c.form)||""}},created_at:new Date().toISOString()}})}catch(t){return console.error("خطأ في تحسين العناصر بأسماء الأدوية:",t),e.map(e=>({id:"item_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,medicine_name:"غير محدد",created_at:new Date().toISOString()}))}},g=()=>{try{console.log("\uD83D\uDD27 بدء إصلاح بيانات الفواتير في localStorage...");let e=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),t=JSON.parse(localStorage.getItem("medicines")||"[]"),a=JSON.parse(localStorage.getItem("medicine_batches")||"[]");console.log("\uD83D\uDCE6 عدد عناصر الفواتير: ".concat(e.length)),console.log("\uD83D\uDC8A عدد الأدوية: ".concat(t.length)),console.log("\uD83D\uDCCB عدد الدفعات: ".concat(a.length));let r=0,n=0,c=e.map(e=>{var c,s;if((null==(s=e.medicine_batches)||null==(c=s.medicines)?void 0:c.name)&&"غير محدد"!==e.medicine_batches.medicines.name)return e;let i=a.find(t=>t.id===e.medicine_batch_id),o=t.find(e=>e.id===(null==i?void 0:i.medicine_id));if(null==o?void 0:o.name)return r++,console.log("✅ إصلاح العنصر: ".concat(o.name," (Batch: ").concat(null==i?void 0:i.batch_code,")")),{...e,medicine_name:o.name,medicineName:o.name,medicine_batches:{batch_code:(null==i?void 0:i.batch_code)||e.batch_code||"",expiry_date:(null==i?void 0:i.expiry_date)||e.expiry_date||"",medicines:{name:o.name,category:o.category||"",manufacturer:o.manufacturer||"",strength:o.strength||"",form:o.form||""}}};{n++,console.log("⚠️ لم يتم العثور على الدواء للعنصر: ".concat(e.medicine_batch_id));let t=e.medicine_name||e.medicineName||"غير محدد";return{...e,medicine_name:t,medicineName:t,medicine_batches:{batch_code:(null==i?void 0:i.batch_code)||e.batch_code||"",expiry_date:(null==i?void 0:i.expiry_date)||e.expiry_date||"",medicines:{name:t,category:"",manufacturer:"",strength:"",form:""}}}}});return localStorage.setItem("sales_invoice_items",JSON.stringify(c)),console.log("✅ تم إصلاح ".concat(r," عنصر من أصل ").concat(e.length)),console.log("⚠️ لم يتم العثور على ".concat(n," عنصر")),{success:!0,fixedCount:r,notFoundCount:n,totalCount:e.length}}catch(e){return console.error("❌ خطأ في إصلاح بيانات localStorage:",e),{success:!1,error:e}}},p=e=>{try{let t=JSON.parse(localStorage.getItem("medicines")||"[]"),a=JSON.parse(localStorage.getItem("medicine_batches")||"[]").find(t=>t.id===e),r=t.find(e=>e.id===(null==a?void 0:a.medicine_id));return(null==r?void 0:r.name)||"غير محدد"}catch(e){return console.error("خطأ في الحصول على اسم الدواء:",e),"غير محدد"}},h=async e=>{try{let{data:t,error:a}=await r.N.from("purchase_invoices").insert([e]).select().single();if(a){console.warn("Supabase error for purchase invoice, using localStorage:",a);let t={id:"purchase_invoice_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,created_at:new Date().toISOString()},r=JSON.parse(localStorage.getItem("purchase_invoices")||"[]");return r.push(t),localStorage.setItem("purchase_invoices",JSON.stringify(r)),{success:!0,data:t}}return{success:!0,data:t}}catch(t){console.error("Error creating purchase invoice:",t);try{let t={id:"purchase_invoice_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,created_at:new Date().toISOString()},a=JSON.parse(localStorage.getItem("purchase_invoices")||"[]");return a.push(t),localStorage.setItem("purchase_invoices",JSON.stringify(a)),{success:!0,data:t}}catch(e){return console.error("LocalStorage fallback failed for purchase invoice:",e),{success:!1,error:e}}}},f=async e=>{try{let{data:t,error:a}=await r.N.from("purchase_invoice_items").insert(e).select();if(a){console.warn("Supabase error for purchase invoice items, using localStorage:",a);let t=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),r=e.map(e=>({id:"purchase_item_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,medicine_name:e.medicine_name||"غير محدد",medicineName:e.medicine_name||"غير محدد",created_at:new Date().toISOString()}));return t.push(...r),localStorage.setItem("purchase_invoice_items",JSON.stringify(t)),{success:!0,data:r}}return{success:!0,data:t}}catch(t){console.error("Error adding purchase invoice items:",t);try{let t=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),a=e.map(e=>({id:"purchase_item_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,medicine_name:e.medicine_name||"غير محدد",medicineName:e.medicine_name||"غير محدد",created_at:new Date().toISOString()}));return t.push(...a),localStorage.setItem("purchase_invoice_items",JSON.stringify(t)),{success:!0,data:a}}catch(e){return console.error("LocalStorage fallback failed for purchase items:",e),{success:!1,error:e}}}},S=async e=>{try{let{data:t,error:a}=await r.N.from("inventory_movements").insert([e]).select().single();if(a){console.warn("Supabase error for inventory movement, using localStorage:",a);let t={id:"movement_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,created_at:new Date().toISOString()},r=JSON.parse(localStorage.getItem("inventory_movements")||"[]");return r.push(t),localStorage.setItem("inventory_movements",JSON.stringify(r)),{success:!0,data:t}}return{success:!0,data:t}}catch(t){console.error("Error adding inventory movement:",t);try{let t={id:"movement_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,created_at:new Date().toISOString()},a=JSON.parse(localStorage.getItem("inventory_movements")||"[]");return a.push(t),localStorage.setItem("inventory_movements",JSON.stringify(a)),{success:!0,data:t}}catch(e){return console.error("LocalStorage fallback failed for inventory movement:",e),{success:!1,error:e}}}},y=async()=>{try{let{data:e,error:t}=await r.N.from("customers").select("*").order("name");if(t)throw t;return{success:!0,data:e}}catch(e){return console.error("Error fetching customers:",e),{success:!1,error:e}}},v=async e=>{try{let{data:t,error:a}=await r.N.from("customers").insert([e]).select().single();if(a)throw a;return{success:!0,data:t}}catch(e){return console.error("Error adding customer:",e),{success:!1,error:e}}},b=async()=>{try{let{data:e,error:t}=await r.N.from("suppliers").select("*").order("name");if(t)throw t;return{success:!0,data:e}}catch(e){return console.error("Error fetching suppliers:",e),{success:!1,error:e}}},w=async e=>{try{let{data:t,error:a}=await r.N.from("suppliers").insert([e]).select().single();if(a)throw a;return{success:!0,data:t}}catch(e){return console.error("Error adding supplier:",e),{success:!1,error:e}}},I=async(e,t)=>{try{console.log("\uD83D\uDD04 بدء معاملة المبيعات الكاملة..."),console.log("\uD83D\uDCC4 بيانات الفاتورة:",e),console.log("\uD83D\uDCE6 العناصر:",t),console.log("\uD83D\uDCDD إنشاء الفاتورة...");let n=await u(e);if(console.log("\uD83D\uDCDD نتيجة إنشاء الفاتورة:",n),!n.success){var a;throw console.error("❌ فشل في إنشاء الفاتورة:",n.error),Error("فشل في إنشاء الفاتورة: ".concat((null==(a=n.error)?void 0:a.message)||"خطأ غير معروف"))}let c=n.data.id;console.log("✅ تم إنشاء الفاتورة بنجاح، ID:",c),console.log("\uD83D\uDCE6 معالجة عناصر الفاتورة...");let s=[];for(let e of t){console.log("\uD83D\uDCE6 معالجة العنصر:",e);let t=e.medicine_batch_id||e.batchId;if(s.push({invoice_id:c,medicine_batch_id:t,quantity:e.quantity,unit_price:e.unit_price||e.unitPrice,total_price:e.total_price||e.totalPrice,is_gift:e.is_gift||e.isGift||!1,medicine_name:e.medicine_name||e.medicineName||"غير محدد"}),!(e.is_gift||e.isGift))try{let a=await r.N.from("medicine_batches").select("quantity").eq("id",t).single();if(a.data){let r=Math.max(0,a.data.quantity-e.quantity);await d(t,r),console.log("✅ تم تحديث كمية الدفعة ".concat(t," إلى ").concat(r))}}catch(e){console.warn("تحذير: فشل في تحديث كمية الدفعة:",e)}try{await S({medicine_batch_id:t,movement_type:"out",quantity:e.quantity,reference_type:"sale",reference_id:c,notes:e.is_gift||e.isGift?"هدية":void 0}),console.log("✅ تم إضافة حركة المخزون للدفعة ".concat(t))}catch(e){console.warn("تحذير: فشل في إضافة حركة المخزون:",e)}}console.log("\uD83D\uDCDD إضافة عناصر الفاتورة...");let i=await m(s);if(i.success?console.log("✅ تم إضافة جميع عناصر الفاتورة بنجاح"):console.warn("تحذير: فشل في إضافة عناصر الفاتورة:",i.error),"cash"===e.payment_method&&"paid"===e.payment_status)try{await R({transaction_type:"income",category:"مبيعات",amount:e.final_amount,description:"فاتورة مبيعات رقم ".concat(e.invoice_number),reference_type:"sale",reference_id:c,payment_method:"cash",notes:e.notes}),console.log("✅ تم إضافة معاملة الصندوق")}catch(e){console.warn("تحذير: فشل في إضافة معاملة الصندوق:",e)}return console.log("\uD83C\uDF89 تمت معاملة المبيعات بنجاح!"),{success:!0,data:{invoiceId:c}}}catch(e){return console.error("❌ خطأ في إتمام معاملة المبيعات:",e),{success:!1,error:e}}},N=async()=>{try{let{data:e,error:t}=await r.N.from("sales_invoices").select("\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      ").order("created_at",{ascending:!1});if(t){console.warn("Supabase error for sales invoices, using localStorage fallback:",t);let e=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),a=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),r=JSON.parse(localStorage.getItem("medicines")||"[]"),n=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),c=e.map(e=>{let t=a.filter(t=>t.invoice_id===e.id).map(e=>{var t,a;if(null==(a=e.medicine_batches)||null==(t=a.medicines)?void 0:t.name)return e;let c=n.find(t=>t.id===e.medicine_batch_id),s=r.find(e=>e.id===(null==c?void 0:c.medicine_id));return{...e,medicine_name:(null==s?void 0:s.name)||e.medicine_name||"غير محدد",medicine_batches:{batch_code:(null==c?void 0:c.batch_code)||"",expiry_date:(null==c?void 0:c.expiry_date)||"",medicines:{name:(null==s?void 0:s.name)||e.medicine_name||"غير محدد",category:(null==s?void 0:s.category)||"",manufacturer:(null==s?void 0:s.manufacturer)||"",strength:(null==s?void 0:s.strength)||"",form:(null==s?void 0:s.form)||""}}}});return{...e,sales_invoice_items:t}});return{success:!0,data:c}}return{success:!0,data:e}}catch(e){console.error("Error fetching sales invoices:",e);try{let e=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),t=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),a=JSON.parse(localStorage.getItem("medicines")||"[]"),r=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),n=e.map(e=>{let n=t.filter(t=>t.invoice_id===e.id).map(e=>{var t,n;if(null==(n=e.medicine_batches)||null==(t=n.medicines)?void 0:t.name)return e;let c=r.find(t=>t.id===e.medicine_batch_id),s=a.find(e=>e.id===(null==c?void 0:c.medicine_id));return{...e,medicine_name:(null==s?void 0:s.name)||e.medicine_name||"غير محدد",medicine_batches:{batch_code:(null==c?void 0:c.batch_code)||"",expiry_date:(null==c?void 0:c.expiry_date)||"",medicines:{name:(null==s?void 0:s.name)||e.medicine_name||"غير محدد",category:(null==s?void 0:s.category)||"",manufacturer:(null==s?void 0:s.manufacturer)||"",strength:(null==s?void 0:s.strength)||"",form:(null==s?void 0:s.form)||""}}}});return{...e,sales_invoice_items:n}});return{success:!0,data:n}}catch(t){return console.error("LocalStorage fallback failed:",t),{success:!1,error:e}}}},O=async e=>{try{let{data:t,error:a}=await r.N.from("sales_invoices").select("\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      ").eq("id",e).single();if(a){console.warn("Supabase error for single invoice, using localStorage fallback:",a);let t=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),r=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),n=JSON.parse(localStorage.getItem("medicines")||"[]"),c=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),s=t.find(t=>t.id===e);if(s){let t=r.filter(t=>t.invoice_id===e);console.log("\uD83D\uDD27 بدء تحسين عناصر الفاتورة للطباعة..."),console.log("\uD83D\uDCE6 عدد العناصر:",t.length),console.log("\uD83D\uDC8A عدد الأدوية المتاحة:",n.length),console.log("\uD83D\uDCCB عدد الدفعات المتاحة:",c.length);let a=t.map((e,t)=>{console.log("\n--- العنصر ".concat(t+1," ---")),console.log("البيانات الأصلية:",e);let a=c.find(t=>t.id===e.medicine_batch_id);console.log("الدفعة الموجودة:",a);let r=n.find(e=>e.id===(null==a?void 0:a.medicine_id));console.log("الدواء الموجود:",r);let s=(null==r?void 0:r.name)||"غير محدد";console.log("اسم الدواء المحسوب:",s);let i={...e,medicine_name:s,medicineName:s,medicine_batches:{id:null==a?void 0:a.id,batch_code:(null==a?void 0:a.batch_code)||e.batch_code||"",expiry_date:(null==a?void 0:a.expiry_date)||e.expiry_date||"",medicine_id:null==a?void 0:a.medicine_id,medicines:{id:null==r?void 0:r.id,name:s,category:(null==r?void 0:r.category)||"",manufacturer:(null==r?void 0:r.manufacturer)||"",strength:(null==r?void 0:r.strength)||"",form:(null==r?void 0:r.form)||""}}};return console.log("العنصر المحسن:",i),i});return console.log("✅ تم تحسين جميع العناصر"),console.log("النتيجة النهائية:",a),{success:!0,data:{...s,sales_invoice_items:a}}}}return{success:!0,data:t}}catch(e){return console.error("Error fetching sales invoice for print:",e),{success:!1,error:e}}},J=async e=>{try{let{data:t,error:a}=await r.N.from("purchase_invoices").select("\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      ").eq("id",e).single();if(a){console.warn("Supabase error for single purchase invoice, using localStorage fallback:",a);let t=JSON.parse(localStorage.getItem("purchase_invoices")||"[]"),r=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),n=t.find(t=>t.id===e);if(n){let t=r.filter(t=>t.invoice_id===e);console.log("\uD83D\uDD27 بدء تحسين عناصر فاتورة المشتريات للطباعة..."),console.log("\uD83D\uDCE6 عدد العناصر:",t.length);let a=t.map((e,t)=>{console.log("\n--- العنصر ".concat(t+1," ---")),console.log("البيانات الأصلية:",e);let a=e.medicine_name||e.medicineName||"غير محدد";console.log("اسم الدواء:",a);let r={...e,medicine_name:a,medicineName:a,medicines:{name:a,category:e.category||"",manufacturer:e.manufacturer||"",strength:e.strength||"",form:e.form||""}};return console.log("العنصر المحسن:",r),r});return console.log("✅ تم تحسين جميع عناصر المشتريات"),{success:!0,data:{...n,purchase_invoice_items:a}}}}return{success:!0,data:t}}catch(e){return console.error("Error fetching purchase invoice for print:",e),{success:!1,error:e}}},D=async()=>{try{let{data:e,error:t}=await r.N.from("purchase_invoices").select("\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      ").order("created_at",{ascending:!1});if(t){console.warn("Supabase error for purchase invoices, using localStorage fallback:",t);let e=JSON.parse(localStorage.getItem("purchase_invoices")||"[]"),a=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),r=e.map(e=>({...e,purchase_invoice_items:a.filter(t=>t.invoice_id===e.id)}));return{success:!0,data:r}}return{success:!0,data:e}}catch(e){console.error("Error fetching purchase invoices:",e);try{let e=JSON.parse(localStorage.getItem("purchase_invoices")||"[]"),t=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),a=e.map(e=>({...e,purchase_invoice_items:t.filter(t=>t.invoice_id===e.id)}));return{success:!0,data:a}}catch(t){return console.error("LocalStorage fallback failed:",t),{success:!1,error:e}}}},q=async e=>{try{let{data:t,error:a}=await r.N.from("sales_returns").insert([e]).select().single();if(a)throw a;return{success:!0,data:t}}catch(t){console.warn("Supabase sales return failed, using localStorage fallback:",t);try{let t={...e,id:"sr_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),created_at:new Date().toISOString()},a=JSON.parse(localStorage.getItem("sales_returns")||"[]");return a.push(t),localStorage.setItem("sales_returns",JSON.stringify(a)),console.log("Sales return saved to localStorage:",t),console.log("Total sales returns in localStorage:",a.length),{success:!0,data:t}}catch(e){return console.error("Error creating sales return (fallback):",e),{success:!1,error:e}}}},x=async e=>{try{let{data:t,error:a}=await r.N.from("purchase_returns").insert([e]).select().single();if(a)throw a;return{success:!0,data:t}}catch(t){console.warn("Supabase purchase return failed, using localStorage fallback:",t);try{let t={...e,id:"pr_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),created_at:new Date().toISOString()},a=JSON.parse(localStorage.getItem("purchase_returns")||"[]");return a.push(t),localStorage.setItem("purchase_returns",JSON.stringify(a)),console.log("Purchase return saved to localStorage:",t),console.log("Total purchase returns in localStorage:",a.length),{success:!0,data:t}}catch(e){return console.error("Error creating purchase return (fallback):",e),{success:!1,error:e}}}},E=async e=>{var t,a;try{let a=(null==(t=e[0])?void 0:t.return_type)==="sales"?"sales_return_items":"purchase_return_items",{data:n,error:c}=await r.N.from(a).insert(e.map(e=>({return_id:e.return_id,medicine_batch_id:e.medicine_batch_id,medicine_id:e.medicine_id,quantity:e.quantity,unit_price:e.unit_price,total_price:e.total_price}))).select();if(c)throw c;return{success:!0,data:n}}catch(t){console.warn("Supabase return items failed, using localStorage fallback:",t);try{let t=(null==(a=e[0])?void 0:a.return_type)==="sales"?"sales_return_items":"purchase_return_items",r=e.map(e=>({...e,id:"ri_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),created_at:new Date().toISOString()})),n=JSON.parse(localStorage.getItem(t)||"[]");return n.push(...r),localStorage.setItem(t,JSON.stringify(n)),{success:!0,data:r}}catch(e){return console.error("Error adding return items (fallback):",e),{success:!1,error:e}}}},C=async(e,t,a)=>{try{let n="sales"===e?await q(t):await x(t);if(!n.success)throw Error("Failed to create return");let c=n.data.id,s=a.map(t=>({return_id:c,return_type:e,medicine_batch_id:t.batchId,medicine_id:t.medicineId,quantity:t.quantity,unit_price:t.unitPrice,total_price:t.totalPrice}));await E(s);try{if("sales"===e){for(let e of a)if(e.batchId)try{let{data:a}=await r.N.from("medicine_batches").select("quantity").eq("id",e.batchId).single();a&&await d(e.batchId,a.quantity+e.quantity),await S({medicine_batch_id:e.batchId,movement_type:"in",quantity:e.quantity,reference_type:"return",reference_id:c,notes:"مرتجع مبيعات - ".concat(t.reason)})}catch(t){console.warn("Failed to update inventory for item:",e.batchId,t)}}if("purchase"===e){for(let e of a)if(e.batchId)try{let{data:a}=await r.N.from("medicine_batches").select("quantity").eq("id",e.batchId).single();if(a){let t=Math.max(0,a.quantity-e.quantity);await d(e.batchId,t)}await S({medicine_batch_id:e.batchId,movement_type:"out",quantity:e.quantity,reference_type:"return",reference_id:c,notes:"مرتجع مشتريات - ".concat(t.reason)})}catch(t){console.warn("Failed to update inventory for item:",e.batchId,t)}}}catch(e){console.warn("Inventory update failed, but return was created successfully:",e)}return{success:!0,data:{returnId:c}}}catch(e){return console.error("Error processing return:",e),{success:!1,error:e}}},M=async()=>{try{let e=JSON.parse(localStorage.getItem("sales_returns")||"[]"),t=JSON.parse(localStorage.getItem("purchase_returns")||"[]"),a=JSON.parse(localStorage.getItem("customers")||"[]"),r=JSON.parse(localStorage.getItem("suppliers")||"[]");console.log("Loading returns from localStorage:",{salesReturns:e.length,purchaseReturns:t.length,customers:a.length,suppliers:r.length});let n=e.map(e=>{var t;let r=a.find(t=>t.id===e.customer_id);return console.log("Enriching sales return ".concat(e.id,":"),{original_items:e.return_items,items_count:(null==(t=e.return_items)?void 0:t.length)||0}),{...e,return_type:"sales",customers:r?{name:r.name,phone:r.phone,address:r.address}:null,customer_name:(null==r?void 0:r.name)||e.customer_name||"عميل غير محدد",return_items:e.return_items||[]}}),c=t.map(e=>{var t;let a=r.find(t=>t.id===e.supplier_id);return console.log("Enriching purchase return ".concat(e.id,":"),{original_items:e.return_items,items_count:(null==(t=e.return_items)?void 0:t.length)||0}),{...e,return_type:"purchase",suppliers:a?{name:a.name,phone:a.phone,address:a.address}:null,supplier_name:(null==a?void 0:a.name)||e.supplier_name||"مورد غير محدد",return_items:e.return_items||[]}});if(n.length>0||c.length>0){let e=[...n,...c].sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime());return console.log("Returning enriched returns from localStorage:",e.slice(0,2)),{success:!0,data:e}}}catch(e){console.warn("Error reading from localStorage:",e)}try{console.log("Trying Supabase for returns...");let[e,t]=await Promise.all([r.N.from("sales_returns").select("\n          *,\n          customers (name, phone),\n          sales_return_items (\n            *,\n            medicine_batches (\n              batch_code,\n              medicines (name)\n            )\n          )\n        ").order("created_at",{ascending:!1}),r.N.from("purchase_returns").select("\n          *,\n          suppliers (name, contact_person),\n          purchase_return_items (\n            *,\n            medicines (name)\n          )\n        ").order("created_at",{ascending:!1})]),a=[...(e.data||[]).map(e=>{var t;return{...e,return_type:"sales",customer_name:(null==(t=e.customers)?void 0:t.name)||"عميل غير محدد"}}),...(t.data||[]).map(e=>{var t;return{...e,return_type:"purchase",supplier_name:(null==(t=e.suppliers)?void 0:t.name)||"مورد غير محدد"}})].sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime());return console.log("Returning returns from Supabase:",a.slice(0,2)),{success:!0,data:a}}catch(e){return console.warn("Supabase returns failed, returning empty array:",e),{success:!0,data:[]}}},k=async e=>{try{let t=JSON.parse(localStorage.getItem("sales_returns")||"[]"),a=JSON.parse(localStorage.getItem("purchase_returns")||"[]"),n=JSON.parse(localStorage.getItem("customers")||"[]"),c=JSON.parse(localStorage.getItem("suppliers")||"[]"),s=t.find(t=>t.id===e),i="sales";if(s||(s=a.find(t=>t.id===e),i="purchase"),s){if("sales"===i){let e=n.find(e=>e.id===s.customer_id);s={...s,return_type:"sales",customers:e?{name:e.name,phone:e.phone,address:e.address}:null,customer_name:(null==e?void 0:e.name)||s.customer_name||"عميل غير محدد"}}else{let e=c.find(e=>e.id===s.supplier_id);s={...s,return_type:"purchase",suppliers:e?{name:e.name,phone:e.phone,address:e.address}:null,supplier_name:(null==e?void 0:e.name)||s.supplier_name||"مورد غير محدد"}}return console.log("Found enriched return in localStorage:",s),{success:!0,data:s}}if(!r.N)return console.warn("Supabase not available, return not found"),{success:!1,error:"Return not found"};let{data:o,error:l}=await r.N.from("sales_returns").select("\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            *,\n            medicines (name)\n          )\n        )\n      ").eq("id",e).single();if(o&&!l){let e={...o,return_type:"sales",return_items:o.sales_return_items||[]};return console.log("Found sales return in Supabase:",e),{success:!0,data:e}}let{data:d,error:u}=await r.N.from("purchase_returns").select("\n        *,\n        suppliers (name, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name)\n        )\n      ").eq("id",e).single();if(d&&!u){let e={...d,return_type:"purchase",return_items:d.purchase_return_items||[]};return console.log("Found purchase return in Supabase:",e),{success:!0,data:e}}return console.warn("Return not found:",e),{success:!1,error:"Return not found"}}catch(e){return console.error("Error getting return by ID:",e),{success:!1,error:e}}},P=async(e,t)=>{try{let a=await h(e);if(!a.success)throw Error("Failed to create purchase invoice");let r=a.data.id;for(let a of t){let t=a.medicineId;if(!t){let e=await n({name:a.medicineName,category:a.category||"أخرى",manufacturer:a.manufacturer||"",active_ingredient:a.activeIngredient||"",strength:a.strength||"",form:a.form||"tablet",unit_price:a.unitCost,selling_price:a.sellingPrice||1.5*a.unitCost});if(e.success)t=e.data.id;else{console.error("Failed to create medicine:",e.error);continue}}await f([{invoice_id:r,medicine_id:t,batch_code:a.batchCode,quantity:a.quantity,unit_cost:a.unitCost,total_cost:a.totalCost,expiry_date:a.expiryDate,medicine_name:a.medicineName||"غير محدد"}]);let c=await l({medicine_id:t,batch_code:a.batchCode,expiry_date:a.expiryDate,quantity:a.quantity,cost_price:a.unitCost,selling_price:a.sellingPrice||1.5*a.unitCost,supplier_id:e.supplier_id});c.success&&await S({medicine_batch_id:c.data.id,movement_type:"in",quantity:a.quantity,reference_type:"purchase",reference_id:r})}return"cash"===e.payment_method&&"paid"===e.payment_status&&await R({transaction_type:"expense",category:"مشتريات",amount:e.final_amount,description:"فاتورة مشتريات رقم ".concat(e.invoice_number),reference_type:"purchase",reference_id:r,payment_method:"cash",notes:e.notes}),{success:!0,data:{invoiceId:r}}}catch(e){return console.error("Error completing purchase transaction:",e),{success:!1,error:e}}},R=async e=>{try{let{data:t,error:a}=await r.N.from("cash_transactions").insert([e]).select().single();if(a)throw a;return{success:!0,data:t}}catch(t){console.warn("Supabase cash transaction failed, using localStorage fallback:",t);try{let t={...e,id:"ct_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),created_at:new Date().toISOString()},a=JSON.parse(localStorage.getItem("cash_transactions")||"[]");return a.push(t),localStorage.setItem("cash_transactions",JSON.stringify(a)),console.log("Cash transaction saved to localStorage:",t),console.log("Total cash transactions in localStorage:",a.length),{success:!0,data:t}}catch(e){return console.error("Error adding cash transaction (fallback):",e),{success:!1,error:e}}}},T=async e=>{try{let t=JSON.parse(localStorage.getItem("cash_transactions")||"[]");if(t.length>0){console.log("Loading cash transactions from localStorage:",t.length);let a=t;return(null==e?void 0:e.start_date)&&(a=a.filter(t=>t.created_at>=e.start_date)),(null==e?void 0:e.end_date)&&(a=a.filter(t=>t.created_at<=e.end_date)),(null==e?void 0:e.transaction_type)&&(a=a.filter(t=>t.transaction_type===e.transaction_type)),(null==e?void 0:e.category)&&(a=a.filter(t=>t.category===e.category)),a.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()),{success:!0,data:a}}}catch(e){console.warn("Error reading cash transactions from localStorage:",e)}try{console.log("Trying Supabase for cash transactions...");let t=r.N.from("cash_transactions").select("*").order("created_at",{ascending:!1});(null==e?void 0:e.start_date)&&(t=t.gte("created_at",e.start_date)),(null==e?void 0:e.end_date)&&(t=t.lte("created_at",e.end_date)),(null==e?void 0:e.transaction_type)&&(t=t.eq("transaction_type",e.transaction_type)),(null==e?void 0:e.category)&&(t=t.eq("category",e.category));let{data:a,error:n}=await t;if(n)throw n;return{success:!0,data:a}}catch(e){return console.warn("Supabase cash transactions failed, returning empty array:",e),{success:!0,data:[]}}},F=async()=>{try{let e=JSON.parse(localStorage.getItem("cash_transactions")||"[]");if(e.length>0){console.log("Calculating cash balance from localStorage:",e.length,"transactions");let t=e.reduce((e,t)=>"income"===t.transaction_type?e+t.amount:e-t.amount,0);return{success:!0,data:t}}}catch(e){console.warn("Error calculating balance from localStorage:",e)}try{console.log("Trying Supabase for cash balance...");let{data:e,error:t}=await r.N.from("cash_transactions").select("transaction_type, amount");if(t)throw t;let a=e.reduce((e,t)=>"income"===t.transaction_type?e+t.amount:e-t.amount,0);return{success:!0,data:a}}catch(e){return console.warn("Supabase cash balance failed, returning 0:",e),{success:!0,data:0}}},L=async()=>{try{let e=JSON.parse(localStorage.getItem("sales_invoices")||"[]");if(e.length>0){console.log("Loading customer debts from localStorage:",e.length,"invoices");let t=e.filter(e=>"pending"===e.payment_status||"partial"===e.payment_status);return console.log("Found customer debts:",t.length),{success:!0,data:t}}{console.log("No sales invoices found, creating sample customer debts");let e=[{id:"debt_1",invoice_number:"INV-001",customer_id:"cust_1",customer_name:"أحمد محمد علي",final_amount:15e4,payment_status:"pending",created_at:new Date(Date.now()-1728e5).toISOString(),customers:{name:"أحمد محمد علي",phone:"07901111111"}},{id:"debt_2",invoice_number:"INV-003",customer_id:"cust_2",customer_name:"فاطمة حسن محمد",final_amount:85e3,payment_status:"partial",created_at:new Date(Date.now()-432e6).toISOString(),customers:{name:"فاطمة حسن محمد",phone:"07802222222"}}];return{success:!0,data:e}}}catch(e){console.warn("Error reading customer debts from localStorage:",e)}try{console.log("Trying Supabase for customer debts...");let{data:e,error:t}=await r.N.from("sales_invoices").select("\n        id,\n        invoice_number,\n        customer_id,\n        customer_name,\n        final_amount,\n        payment_status,\n        created_at,\n        customers (name, phone)\n      ").eq("payment_status","pending").order("created_at",{ascending:!1});if(t)throw t;return{success:!0,data:e}}catch(e){return console.warn("Supabase customer debts failed, returning empty array:",e),{success:!0,data:[]}}},B=async()=>{try{let e=JSON.parse(localStorage.getItem("purchase_invoices")||"[]");if(e.length>0){console.log("Loading supplier debts from localStorage:",e.length,"invoices");let t=e.filter(e=>"pending"===e.payment_status||"partial"===e.payment_status);return console.log("Found supplier debts:",t.length),{success:!0,data:t}}{console.log("No purchase invoices found, creating sample supplier debts");let e=[{id:"debt_3",invoice_number:"PUR-001",supplier_id:"sup_1",final_amount:25e5,payment_status:"pending",created_at:new Date(Date.now()-2592e5).toISOString(),suppliers:{name:"شركة الأدوية العراقية",contact_person:"أحمد محمد",phone:"07901234567"}},{id:"debt_4",invoice_number:"PUR-004",supplier_id:"sup_2",final_amount:18e5,payment_status:"partial",created_at:new Date(Date.now()-6048e5).toISOString(),suppliers:{name:"شركة بغداد للأدوية",contact_person:"فاطمة علي",phone:"07801234567"}}];return{success:!0,data:e}}}catch(e){console.warn("Error reading supplier debts from localStorage:",e)}try{console.log("Trying Supabase for supplier debts...");let{data:e,error:t}=await r.N.from("purchase_invoices").select("\n        id,\n        invoice_number,\n        supplier_id,\n        final_amount,\n        payment_status,\n        created_at,\n        suppliers (name, contact_person, phone)\n      ").eq("payment_status","pending").order("created_at",{ascending:!1});if(t)throw t;return{success:!0,data:e}}catch(e){return console.warn("Supabase supplier debts failed, returning empty array:",e),{success:!0,data:[]}}},z=async(e,t,a,n)=>{try{let{data:c,error:s}=await r.N.from("sales"===e?"sales_invoices":"purchase_invoices").update({payment_status:a,...n&&{paid_amount:n}}).eq("id",t).select().single();if(s)throw s;return"paid"===a&&n&&await R({transaction_type:"sales"===e?"income":"expense",category:"sales"===e?"مبيعات":"مشتريات",amount:n,description:"دفع فاتورة ".concat("sales"===e?"مبيعات":"مشتريات"," رقم ").concat(c.invoice_number),reference_type:e,reference_id:t,payment_method:"cash"}),{success:!0,data:c}}catch(r){console.warn("Supabase payment update failed, using localStorage fallback:",r);try{let r="sales"===e?"sales_invoices":"purchase_invoices",c=JSON.parse(localStorage.getItem(r)||"[]"),s=c.findIndex(e=>e.id===t);if(-1!==s)return c[s].payment_status=a,n&&(c[s].paid_amount=n),localStorage.setItem(r,JSON.stringify(c)),"paid"===a&&n&&await R({transaction_type:"sales"===e?"income":"expense",category:"sales"===e?"مبيعات":"مشتريات",amount:n,description:"دفع فاتورة ".concat("sales"===e?"مبيعات":"مشتريات"," رقم ").concat(c[s].invoice_number),reference_type:e,reference_id:t,payment_method:"cash"}),console.log("Payment status updated in localStorage:",c[s]),{success:!0,data:c[s]};throw Error("Invoice not found in localStorage")}catch(e){return console.error("Error updating payment status (fallback):",e),{success:!1,error:e}}}},A=async e=>{try{let t=r.N.from("sales_invoices").select("\n        *,\n        customers (name, phone),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            medicines (name, category)\n          )\n        )\n      ").order("created_at",{ascending:!1});e.start_date&&(t=t.gte("created_at",e.start_date)),e.end_date&&(t=t.lte("created_at",e.end_date)),e.customer_id&&(t=t.eq("customer_id",e.customer_id));let{data:a,error:n}=await t;if(n)throw n;let c=a;return e.medicine_id&&(c=a.filter(t=>t.sales_invoice_items.some(t=>{var a,r;return(null==(r=t.medicine_batches)||null==(a=r.medicines)?void 0:a.id)===e.medicine_id}))),{success:!0,data:c}}catch(e){return console.error("Error fetching sales report:",e),{success:!1,error:e}}},U=async e=>{try{let t=r.N.from("purchase_invoices").select("\n        *,\n        suppliers (name, contact_person, phone),\n        purchase_invoice_items (\n          *,\n          medicines (name, category)\n        )\n      ").order("created_at",{ascending:!1});e.start_date&&(t=t.gte("created_at",e.start_date)),e.end_date&&(t=t.lte("created_at",e.end_date)),e.supplier_id&&(t=t.eq("supplier_id",e.supplier_id));let{data:a,error:n}=await t;if(n)throw n;let c=a;return e.medicine_id&&(c=a.filter(t=>t.purchase_invoice_items.some(t=>{var a;return(null==(a=t.medicines)?void 0:a.id)===e.medicine_id}))),{success:!0,data:c}}catch(e){return console.error("Error fetching purchases report:",e),{success:!1,error:e}}},Z=async(e,t)=>{try{let a=r.N.from("sales_invoices").select("\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      ").eq("customer_id",e).order("created_at",{ascending:!1}),n=r.N.from("sales_returns").select("\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      ").eq("customer_id",e).order("created_at",{ascending:!1});t.start_date&&(a=a.gte("created_at",t.start_date),n=n.gte("created_at",t.start_date)),t.end_date&&(a=a.lte("created_at",t.end_date),n=n.lte("created_at",t.end_date));let[c,s]=await Promise.all([a,n]);if(c.error)throw c.error;if(s.error)throw s.error;return{success:!0,data:{sales:c.data,returns:s.data}}}catch(e){return console.error("Error fetching customer statement:",e),{success:!1,error:e}}},j=async(e,t)=>{try{let a=r.N.from("purchase_invoices").select("\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      ").eq("supplier_id",e).order("created_at",{ascending:!1}),n=r.N.from("purchase_returns").select("\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      ").eq("supplier_id",e).order("created_at",{ascending:!1});t.start_date&&(a=a.gte("created_at",t.start_date),n=n.gte("created_at",t.start_date)),t.end_date&&(a=a.lte("created_at",t.end_date),n=n.lte("created_at",t.end_date));let[c,s]=await Promise.all([a,n]);if(c.error)throw c.error;if(s.error)throw s.error;return{success:!0,data:{purchases:c.data,returns:s.data}}}catch(e){return console.error("Error fetching supplier statement:",e),{success:!1,error:e}}},Y=async(e,t)=>{try{let a=r.N.from("inventory_movements").select("\n        *,\n        medicine_batches (\n          batch_code,\n          expiry_date,\n          medicines (name, category)\n        )\n      ").order("created_at",{ascending:!1});t.start_date&&(a=a.gte("created_at",t.start_date)),t.end_date&&(a=a.lte("created_at",t.end_date));let{data:n,error:c}=await a;if(c)throw c;let s=n.filter(t=>{var a,r;return(null==(r=t.medicine_batches)||null==(a=r.medicines)?void 0:a.id)===e});return{success:!0,data:s}}catch(e){return console.error("Error fetching medicine movement report:",e),{success:!1,error:e}}},G=async e=>{try{let t=r.N.from("medicine_batches").select("\n        *,\n        medicines (name, category, manufacturer)\n      ").order("expiry_date",{ascending:!0}),{data:a,error:n}=await t;if(n)throw n;let c=a;if(e.category&&(c=c.filter(t=>{var a;return(null==(a=t.medicines)?void 0:a.category)===e.category})),e.low_stock&&(c=c.filter(e=>e.quantity<10)),e.expired){let e=new Date().toISOString().split("T")[0];c=c.filter(t=>t.expiry_date<e)}if(e.expiring_soon){let e=new Date;e.setDate(e.getDate()+30);let t=e.toISOString().split("T")[0],a=new Date().toISOString().split("T")[0];c=c.filter(e=>e.expiry_date>=a&&e.expiry_date<=t)}return{success:!0,data:c}}catch(e){return console.error("Error fetching inventory report:",e),{success:!1,error:e}}},Q=async(e,t,a)=>{let r={status:t};return a&&(r.rejection_reason=a),X(e,r)},V=async e=>{try{console.log("\uD83D\uDD0D البحث عن المرتجع للطباعة:",e);let t=JSON.parse(localStorage.getItem("sales_returns")||"[]"),a=JSON.parse(localStorage.getItem("purchase_returns")||"[]"),n=JSON.parse(localStorage.getItem("sales_return_items")||"[]"),c=JSON.parse(localStorage.getItem("purchase_return_items")||"[]"),s=t.find(t=>t.id===e);if(s){let t=n.filter(t=>t.return_id===e),a=JSON.parse(localStorage.getItem("medicines")||"[]"),r=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),c=t.map(e=>{let t=e.medicine_name||e.medicineName||"غير محدد",n=e.batch_code||e.batchCode||"",c=e.expiry_date||e.expiryDate||"";if("غير محدد"===t&&e.medicine_batch_id){let s=r.find(t=>t.id===e.medicine_batch_id);if(s){n=s.batch_number||n,c=s.expiry_date||c;let e=a.find(e=>e.id===s.medicine_id);e&&(t=e.name||t)}}if("غير محدد"===t&&e.medicine_id){let r=a.find(t=>t.id===e.medicine_id);r&&(t=r.name||t)}return{...e,medicine_name:t,batch_code:n,expiry_date:c,unit_price:e.unit_price||e.unitPrice||0,total_price:e.total_price||e.totalPrice||0}}),i={...s,type:"sales",return_type:"sales",return_invoice_items:c};return console.log("✅ تم العثور على مرتجع مبيعات:",i),{success:!0,data:i}}if(s=a.find(t=>t.id===e)){let t=c.filter(t=>t.return_id===e),a=JSON.parse(localStorage.getItem("medicines")||"[]"),r=t.map(e=>{let t=e.medicine_name||e.medicineName||"غير محدد";if("غير محدد"===t&&e.medicine_id){let r=a.find(t=>t.id===e.medicine_id);r&&(t=r.name||t)}return{...e,medicine_name:t,batch_code:e.batch_code||e.batchCode||"",expiry_date:e.expiry_date||e.expiryDate||"",unit_cost:e.unit_cost||e.unitCost||0,total_cost:e.total_cost||e.totalCost||0}}),n={...s,type:"purchase",return_type:"purchase",return_invoice_items:r};return console.log("✅ تم العثور على مرتجع مشتريات:",n),{success:!0,data:n}}console.log("⚠️ لم يتم العثور على المرتجع في localStorage، محاولة Supabase...");let{data:i,error:o}=await r.N.from("sales_returns").select("\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      ").eq("id",e).single();if(i&&!o){let e={...i,type:"sales",return_type:"sales",return_invoice_items:(i.sales_return_items||[]).map(e=>{var t,a;return{...e,medicine_name:(null==(a=e.medicine_batches)||null==(t=a.medicines)?void 0:t.name)||e.medicine_name||"غير محدد",unit_price:e.unit_price||0,total_price:e.total_price||0}})};return console.log("✅ تم العثور على مرتجع مبيعات في Supabase:",e),{success:!0,data:e}}let{data:l,error:d}=await r.N.from("purchase_returns").select("\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      ").eq("id",e).single();if(l&&!d){let e={...l,type:"purchase",return_type:"purchase",return_invoice_items:(l.purchase_return_items||[]).map(e=>{var t;return{...e,medicine_name:(null==(t=e.medicines)?void 0:t.name)||e.medicine_name||"غير محدد",unit_cost:e.unit_cost||0,total_cost:e.total_cost||0}})};return console.log("✅ تم العثور على مرتجع مشتريات في Supabase:",e),{success:!0,data:e}}return console.log("❌ لم يتم العثور على المرتجع"),{success:!1,error:"Return not found"}}catch(e){return console.error("Error fetching return for print:",e),{success:!1,error:e}}},X=async(e,t)=>{try{let a=JSON.parse(localStorage.getItem("sales_returns")||"[]"),n=JSON.parse(localStorage.getItem("purchase_returns")||"[]"),c=a.findIndex(t=>t.id===e);if(-1!==c){a[c]={...a[c],...t,updated_at:new Date().toISOString()},localStorage.setItem("sales_returns",JSON.stringify(a));try{let{error:a}=await r.N.from("sales_returns").update(t).eq("id",e);a&&console.warn("Failed to update return in Supabase:",a)}catch(e){console.warn("Supabase update failed, continuing with localStorage:",e)}return{success:!0,data:a[c]}}let s=n.findIndex(t=>t.id===e);if(-1!==s){n[s]={...n[s],...t,updated_at:new Date().toISOString()},localStorage.setItem("purchase_returns",JSON.stringify(n));try{let{error:a}=await r.N.from("purchase_returns").update(t).eq("id",e);a&&console.warn("Failed to update return in Supabase:",a)}catch(e){console.warn("Supabase update failed, continuing with localStorage:",e)}return{success:!0,data:n[s]}}return{success:!1,error:"Return not found"}}catch(e){return console.error("Error updating return:",e),{success:!1,error:e}}}},42099:(e,t,a)=>{a.d(t,{N:()=>r});let r=(0,a(55647).UU)("https://tazhdabhidycvsvjxqcb.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRhemhkYWJoaWR5Y3Zzdmp4cWNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMzU0NzYsImV4cCI6MjA2ODgxMTQ3Nn0.0nxvya6ZMm-22SgiqCrxxjfJyNK-Zs4vkAPAaPlAQEE")}}]);