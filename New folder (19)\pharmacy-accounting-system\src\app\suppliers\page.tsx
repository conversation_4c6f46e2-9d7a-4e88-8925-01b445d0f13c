'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Building,
  Phone,
  MapPin,
  Mail,
  FileText,
  X,
  UserCheck,
  Eye,
  Printer,
  Calendar,
  DollarSign,
  Download
} from 'lucide-react'

interface Supplier {
  id: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  notes?: string
  created_at: string
}

interface PurchaseInvoice {
  id: string
  invoice_number: string
  supplier_name: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: string
  payment_status: string
  notes?: string
  created_at: string
}

export default function SuppliersPage() {
  const [suppliers, setSuppliers] = useState<Supplier[]>([
    {
      id: '1',
      name: 'شركة الأدوية العراقية',
      contact_person: 'أحمد محمد',
      phone: '07901234567',
      email: '<EMAIL>',
      address: 'بغداد - الكرادة',
      notes: 'مورد رئيسي للمسكنات',
      created_at: '2024-01-10'
    },
    {
      id: '2',
      name: 'شركة بغداد للأدوية',
      contact_person: 'فاطمة علي',
      phone: '07801234567',
      email: '<EMAIL>',
      address: 'بغداد - الجادرية',
      notes: 'متخصص في المضادات الحيوية',
      created_at: '2024-01-15'
    },
    {
      id: '3',
      name: 'شركة النهرين للأدوية',
      contact_person: 'محمد حسن',
      phone: '07701234567',
      email: '<EMAIL>',
      address: 'بغداد - الأعظمية',
      created_at: '2024-02-01'
    },
    {
      id: '4',
      name: 'شركة دجلة للأدوية',
      contact_person: 'سارة أحمد',
      phone: '***********',
      email: '<EMAIL>',
      address: 'بغداد - الكاظمية',
      notes: 'فيتامينات ومكملات غذائية',
      created_at: '2024-02-10'
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null)
  const [showAccountStatement, setShowAccountStatement] = useState(false)
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null)
  const [supplierInvoices, setSupplierInvoices] = useState<PurchaseInvoice[]>([])
  const [formData, setFormData] = useState({
    name: '',
    contact_person: '',
    phone: '',
    email: '',
    address: '',
    notes: ''
  })

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.contact_person?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.phone?.includes(searchTerm) ||
    supplier.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddSupplier = () => {
    setEditingSupplier(null)
    setFormData({ name: '', contact_person: '', phone: '', email: '', address: '', notes: '' })
    setShowModal(true)
  }

  const handleEditSupplier = (supplier: Supplier) => {
    setEditingSupplier(supplier)
    setFormData({
      name: supplier.name,
      contact_person: supplier.contact_person || '',
      phone: supplier.phone || '',
      email: supplier.email || '',
      address: supplier.address || '',
      notes: supplier.notes || ''
    })
    setShowModal(true)
  }

  const handleDeleteSupplier = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
      setSuppliers(suppliers.filter(supplier => supplier.id !== id))
      alert('تم حذف المورد بنجاح!')
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      alert('يرجى إدخال اسم المورد')
      return
    }

    if (editingSupplier) {
      // تحديث المورد
      setSuppliers(suppliers.map(supplier =>
        supplier.id === editingSupplier.id
          ? { ...supplier, ...formData }
          : supplier
      ))
      alert('تم تحديث بيانات المورد بنجاح!')
    } else {
      // إضافة مورد جديد
      const newSupplier: Supplier = {
        id: Date.now().toString(),
        ...formData,
        created_at: new Date().toISOString().split('T')[0]
      }
      setSuppliers([...suppliers, newSupplier])
      alert('تم إضافة المورد بنجاح!')
    }

    setShowModal(false)
    setEditingSupplier(null)
    setFormData({ name: '', contact_person: '', phone: '', email: '', address: '', notes: '' })
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingSupplier(null)
    setFormData({ name: '', contact_person: '', phone: '', email: '', address: '', notes: '' })
  }

  const handleViewAccountStatement = (supplier: Supplier) => {
    setSelectedSupplier(supplier)
    loadSupplierInvoices(supplier.name)
    setShowAccountStatement(true)
  }

  const loadSupplierInvoices = (supplierName: string) => {
    try {
      // Load purchase invoices from localStorage
      const purchaseData = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')
      const supplierInvoicesData = purchaseData.filter((invoice: PurchaseInvoice) =>
        invoice.supplier_name === supplierName
      )
      setSupplierInvoices(supplierInvoicesData)
    } catch (error) {
      console.error('Error loading supplier invoices:', error)
      setSupplierInvoices([])
    }
  }

  const calculateSupplierTotalDebt = (supplierName: string) => {
    const invoices = supplierInvoices.filter(invoice =>
      invoice.supplier_name === supplierName && invoice.payment_status === 'pending'
    )
    return invoices.reduce((total, invoice) => total + invoice.final_amount, 0)
  }

  const calculateSupplierTotalPaid = (supplierName: string) => {
    const invoices = supplierInvoices.filter(invoice =>
      invoice.supplier_name === supplierName && invoice.payment_status === 'paid'
    )
    return invoices.reduce((total, invoice) => total + invoice.final_amount, 0)
  }

  const printAccountStatement = () => {
    if (!selectedSupplier) return

    const printContent = `
      <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1>صيدلية الشفاء</h1>
          <h2>كشف حساب المورد</h2>
        </div>

        <div style="margin-bottom: 20px;">
          <h3>معلومات المورد:</h3>
          <p><strong>اسم الشركة:</strong> ${selectedSupplier.name}</p>
          <p><strong>الشخص المسؤول:</strong> ${selectedSupplier.contact_person || 'غير محدد'}</p>
          <p><strong>الهاتف:</strong> ${selectedSupplier.phone || 'غير محدد'}</p>
          <p><strong>العنوان:</strong> ${selectedSupplier.address || 'غير محدد'}</p>
          <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-EG')}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 8px;">رقم الفاتورة</th>
              <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>
              <th style="border: 1px solid #ddd; padding: 8px;">المبلغ</th>
              <th style="border: 1px solid #ddd; padding: 8px;">حالة الدفع</th>
            </tr>
          </thead>
          <tbody>
            ${supplierInvoices.map(invoice => `
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${invoice.invoice_number}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${new Date(invoice.created_at).toLocaleDateString('ar-EG')}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${invoice.final_amount.toLocaleString()} د.ع</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div style="margin-top: 20px;">
          <p><strong>إجمالي المبالغ المدفوعة:</strong> ${calculateSupplierTotalPaid(selectedSupplier.name).toLocaleString()} د.ع</p>
          <p><strong>إجمالي المبالغ المعلقة:</strong> ${calculateSupplierTotalDebt(selectedSupplier.name).toLocaleString()} د.ع</p>
          <p><strong>إجمالي المعاملات:</strong> ${(calculateSupplierTotalPaid(selectedSupplier.name) + calculateSupplierTotalDebt(selectedSupplier.name)).toLocaleString()} د.ع</p>
        </div>
      </div>
    `

    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(printContent)
      printWindow.document.close()
      printWindow.print()
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الموردين</h1>
            <p className="text-gray-600 mt-1">إدارة بيانات الموردين وشركات الأدوية</p>
          </div>
          <button
            onClick={handleAddSupplier}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            إضافة مورد جديد
          </button>
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="البحث عن مورد..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Suppliers List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              قائمة الموردين ({filteredSuppliers.length})
            </h2>
          </div>
          
          {filteredSuppliers.length === 0 ? (
            <div className="text-center py-12">
              <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد موردين</h3>
              <p className="text-gray-500">لم يتم العثور على موردين تطابق معايير البحث</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredSuppliers.map((supplier) => (
                <div key={supplier.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="bg-green-50 p-3 rounded-lg">
                        <Building className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{supplier.name}</h3>
                        {supplier.contact_person && (
                          <div className="flex items-center gap-1 mt-1 text-sm text-gray-600">
                            <UserCheck className="h-3 w-3" />
                            <span>جهة الاتصال: {supplier.contact_person}</span>
                          </div>
                        )}
                        <div className="flex flex-wrap gap-4 mt-2 text-sm text-gray-600">
                          {supplier.phone && (
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              <span>{supplier.phone}</span>
                            </div>
                          )}
                          {supplier.email && (
                            <div className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              <span>{supplier.email}</span>
                            </div>
                          )}
                          {supplier.address && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              <span>{supplier.address}</span>
                            </div>
                          )}
                        </div>
                        {supplier.notes && (
                          <div className="flex items-center gap-1 mt-1 text-sm text-gray-500">
                            <FileText className="h-3 w-3" />
                            <span>{supplier.notes}</span>
                          </div>
                        )}
                        <p className="text-xs text-gray-400 mt-2">
                          تاريخ الإضافة: {supplier.created_at}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleViewAccountStatement(supplier)}
                        className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg"
                        title="كشف الحساب"
                      >
                        <FileText className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEditSupplier(supplier)}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                        title="تعديل"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteSupplier(supplier.id)}
                        className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg"
                        title="حذف"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Add/Edit Supplier Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  {editingSupplier ? 'تعديل المورد' : 'إضافة مورد جديد'}
                </h2>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم المورد *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="اسم الشركة أو المورد"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    جهة الاتصال
                  </label>
                  <input
                    type="text"
                    value={formData.contact_person}
                    onChange={(e) => setFormData({ ...formData, contact_person: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="اسم الشخص المسؤول"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم الهاتف
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="07901234567"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    العنوان
                  </label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="عنوان الشركة"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="ملاحظات حول المورد أو التخصص"
                    rows={3}
                  />
                </div>
                
                <div className="flex justify-end gap-3 mt-6">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {editingSupplier ? 'تحديث' : 'إضافة'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Account Statement Modal */}
        {showAccountStatement && selectedSupplier && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  كشف حساب المورد - {selectedSupplier.name}
                </h2>
                <div className="flex items-center gap-2">
                  <button
                    onClick={printAccountStatement}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
                  >
                    <Printer className="h-4 w-4" />
                    طباعة
                  </button>
                  <button
                    onClick={() => {
                      setShowAccountStatement(false)
                      setSelectedSupplier(null)
                      setSupplierInvoices([])
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Supplier Info */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">معلومات المورد</h3>
                    <p><span className="font-medium">اسم الشركة:</span> {selectedSupplier.name}</p>
                    <p><span className="font-medium">الشخص المسؤول:</span> {selectedSupplier.contact_person || 'غير محدد'}</p>
                    <p><span className="font-medium">الهاتف:</span> {selectedSupplier.phone || 'غير محدد'}</p>
                    <p><span className="font-medium">العنوان:</span> {selectedSupplier.address || 'غير محدد'}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">ملخص الحساب</h3>
                    <p><span className="font-medium">إجمالي المبالغ المدفوعة:</span> <span className="text-green-600">{calculateSupplierTotalPaid(selectedSupplier.name).toLocaleString()} د.ع</span></p>
                    <p><span className="font-medium">إجمالي المبالغ المعلقة:</span> <span className="text-red-600">{calculateSupplierTotalDebt(selectedSupplier.name).toLocaleString()} د.ع</span></p>
                    <p><span className="font-medium">إجمالي المعاملات:</span> <span className="text-blue-600">{(calculateSupplierTotalPaid(selectedSupplier.name) + calculateSupplierTotalDebt(selectedSupplier.name)).toLocaleString()} د.ع</span></p>
                  </div>
                </div>
              </div>

              {/* Invoices Table */}
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">فواتير المورد ({supplierInvoices.length})</h3>
                </div>
                <div className="overflow-x-auto">
                  {supplierInvoices.length === 0 ? (
                    <div className="text-center py-12">
                      <FileText className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد فواتير</h3>
                      <p className="mt-1 text-sm text-gray-500">لم يتم العثور على فواتير لهذا المورد.</p>
                    </div>
                  ) : (
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            رقم الفاتورة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            التاريخ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المبلغ الإجمالي
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الخصم
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المبلغ النهائي
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            طريقة الدفع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            حالة الدفع
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {supplierInvoices.map((invoice) => (
                          <tr key={invoice.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {invoice.invoice_number}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(invoice.created_at).toLocaleDateString('ar-EG')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {invoice.total_amount.toLocaleString()} د.ع
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {invoice.discount_amount.toLocaleString()} د.ع
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {invoice.final_amount.toLocaleString()} د.ع
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex items-center">
                                {invoice.payment_method === 'cash' ? (
                                  <DollarSign className="h-4 w-4 text-green-500 mr-1" />
                                ) : (
                                  <Calendar className="h-4 w-4 text-blue-500 mr-1" />
                                )}
                                {invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                invoice.payment_status === 'paid'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  )
}
