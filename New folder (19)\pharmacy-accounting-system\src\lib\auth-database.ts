import { supabase } from './supabase'

// دالة تشفير بسيطة مؤقتة (يجب استبدالها بـ bcrypt في الإنتاج)
const hashPassword = async (password: string): Promise<string> => {
  // تشفير بسيط مؤقت - يجب استخدام bcrypt في الإنتاج
  return btoa(password + 'salt_key_laren_2024')
}

const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  // مقارنة بسيطة مؤقتة - يجب استخدام bcrypt في الإنتاج
  return btoa(password + 'salt_key_laren_2024') === hash
}

// أنواع البيانات
export interface User {
  id: string
  username: string
  email: string
  full_name: string
  role: 'admin' | 'manager' | 'pharmacist' | 'cashier' | 'viewer'
  permissions: UserPermissions
  is_active: boolean
  last_login: string | null
  created_at: string
  updated_at: string
  created_by: string | null
}

export interface UserPermissions {
  // صلاحيات المبيعات
  sales_view: boolean
  sales_create: boolean
  sales_edit: boolean
  sales_delete: boolean
  sales_print: boolean
  sales_view_prices: boolean
  
  // صلاحيات المشتريات
  purchases_view: boolean
  purchases_create: boolean
  purchases_edit: boolean
  purchases_delete: boolean
  purchases_print: boolean
  
  // صلاحيات المخزون
  inventory_view: boolean
  inventory_create: boolean
  inventory_edit: boolean
  inventory_delete: boolean
  inventory_print: boolean
  
  // صلاحيات العملاء والموردين
  customers_view: boolean
  customers_create: boolean
  customers_edit: boolean
  customers_delete: boolean
  
  suppliers_view: boolean
  suppliers_create: boolean
  suppliers_edit: boolean
  suppliers_delete: boolean
  
  // صلاحيات التقارير
  reports_view: boolean
  reports_financial: boolean
  reports_detailed: boolean
  reports_export: boolean
  
  // صلاحيات النظام
  users_view: boolean
  users_create: boolean
  users_edit: boolean
  users_delete: boolean
  
  settings_view: boolean
  settings_edit: boolean
  
  // صلاحيات الصندوق
  cashbox_view: boolean
  cashbox_manage: boolean
  
  // صلاحيات المرتجعات
  returns_view: boolean
  returns_create: boolean
  returns_edit: boolean
  returns_delete: boolean
}

export interface ActivityLog {
  id: string
  user_id: string
  action: string
  description: string
  table_name: string | null
  record_id: string | null
  old_values: any | null
  new_values: any | null
  ip_address: string | null
  user_agent: string | null
  created_at: string
}

export interface LoginSession {
  id: string
  user_id: string
  token: string
  expires_at: string
  remember_me: boolean
  ip_address: string | null
  user_agent: string | null
  is_active: boolean
  created_at: string
}

// الأدوار الافتراضية
export const DEFAULT_ROLES: Record<string, UserPermissions> = {
  admin: {
    // صلاحيات كاملة للمدير
    sales_view: true,
    sales_create: true,
    sales_edit: true,
    sales_delete: true,
    sales_print: true,
    sales_view_prices: true,
    
    purchases_view: true,
    purchases_create: true,
    purchases_edit: true,
    purchases_delete: true,
    purchases_print: true,
    
    inventory_view: true,
    inventory_create: true,
    inventory_edit: true,
    inventory_delete: true,
    inventory_print: true,
    
    customers_view: true,
    customers_create: true,
    customers_edit: true,
    customers_delete: true,
    
    suppliers_view: true,
    suppliers_create: true,
    suppliers_edit: true,
    suppliers_delete: true,
    
    reports_view: true,
    reports_financial: true,
    reports_detailed: true,
    reports_export: true,
    
    users_view: true,
    users_create: true,
    users_edit: true,
    users_delete: true,
    
    settings_view: true,
    settings_edit: true,
    
    cashbox_view: true,
    cashbox_manage: true,
    
    returns_view: true,
    returns_create: true,
    returns_edit: true,
    returns_delete: true,
  },
  
  manager: {
    // صلاحيات المدير (بدون إدارة المستخدمين)
    sales_view: true,
    sales_create: true,
    sales_edit: true,
    sales_delete: true,
    sales_print: true,
    sales_view_prices: true,
    
    purchases_view: true,
    purchases_create: true,
    purchases_edit: true,
    purchases_delete: true,
    purchases_print: true,
    
    inventory_view: true,
    inventory_create: true,
    inventory_edit: true,
    inventory_delete: false,
    inventory_print: true,
    
    customers_view: true,
    customers_create: true,
    customers_edit: true,
    customers_delete: false,
    
    suppliers_view: true,
    suppliers_create: true,
    suppliers_edit: true,
    suppliers_delete: false,
    
    reports_view: true,
    reports_financial: true,
    reports_detailed: true,
    reports_export: true,
    
    users_view: true,
    users_create: false,
    users_edit: false,
    users_delete: false,
    
    settings_view: true,
    settings_edit: false,
    
    cashbox_view: true,
    cashbox_manage: true,
    
    returns_view: true,
    returns_create: true,
    returns_edit: true,
    returns_delete: false,
  },
  
  pharmacist: {
    // صلاحيات الصيدلي
    sales_view: true,
    sales_create: true,
    sales_edit: true,
    sales_delete: false,
    sales_print: true,
    sales_view_prices: true,
    
    purchases_view: true,
    purchases_create: false,
    purchases_edit: false,
    purchases_delete: false,
    purchases_print: true,
    
    inventory_view: true,
    inventory_create: false,
    inventory_edit: true,
    inventory_delete: false,
    inventory_print: true,
    
    customers_view: true,
    customers_create: true,
    customers_edit: true,
    customers_delete: false,
    
    suppliers_view: true,
    suppliers_create: false,
    suppliers_edit: false,
    suppliers_delete: false,
    
    reports_view: true,
    reports_financial: false,
    reports_detailed: false,
    reports_export: false,
    
    users_view: false,
    users_create: false,
    users_edit: false,
    users_delete: false,
    
    settings_view: false,
    settings_edit: false,
    
    cashbox_view: true,
    cashbox_manage: false,
    
    returns_view: true,
    returns_create: true,
    returns_edit: false,
    returns_delete: false,
  },
  
  cashier: {
    // صلاحيات الكاشير
    sales_view: true,
    sales_create: true,
    sales_edit: false,
    sales_delete: false,
    sales_print: true,
    sales_view_prices: false,
    
    purchases_view: false,
    purchases_create: false,
    purchases_edit: false,
    purchases_delete: false,
    purchases_print: false,
    
    inventory_view: true,
    inventory_create: false,
    inventory_edit: false,
    inventory_delete: false,
    inventory_print: false,
    
    customers_view: true,
    customers_create: true,
    customers_edit: false,
    customers_delete: false,
    
    suppliers_view: false,
    suppliers_create: false,
    suppliers_edit: false,
    suppliers_delete: false,
    
    reports_view: false,
    reports_financial: false,
    reports_detailed: false,
    reports_export: false,
    
    users_view: false,
    users_create: false,
    users_edit: false,
    users_delete: false,
    
    settings_view: false,
    settings_edit: false,
    
    cashbox_view: true,
    cashbox_manage: false,
    
    returns_view: true,
    returns_create: false,
    returns_edit: false,
    returns_delete: false,
  },
  
  viewer: {
    // صلاحيات المشاهد فقط
    sales_view: true,
    sales_create: false,
    sales_edit: false,
    sales_delete: false,
    sales_print: false,
    sales_view_prices: false,
    
    purchases_view: true,
    purchases_create: false,
    purchases_edit: false,
    purchases_delete: false,
    purchases_print: false,
    
    inventory_view: true,
    inventory_create: false,
    inventory_edit: false,
    inventory_delete: false,
    inventory_print: false,
    
    customers_view: true,
    customers_create: false,
    customers_edit: false,
    customers_delete: false,
    
    suppliers_view: true,
    suppliers_create: false,
    suppliers_edit: false,
    suppliers_delete: false,
    
    reports_view: true,
    reports_financial: false,
    reports_detailed: false,
    reports_export: false,
    
    users_view: false,
    users_create: false,
    users_edit: false,
    users_delete: false,
    
    settings_view: false,
    settings_edit: false,
    
    cashbox_view: false,
    cashbox_manage: false,
    
    returns_view: true,
    returns_create: false,
    returns_edit: false,
    returns_delete: false,
  }
}

// دوال إدارة المستخدمين
export const createUser = async (userData: {
  username: string
  email: string
  password: string
  full_name: string
  role: string
  custom_permissions?: Partial<UserPermissions>
  created_by: string
}) => {
  try {
    // تشفير كلمة المرور
    const hashedPassword = await hashPassword(userData.password)

    // الحصول على الصلاحيات الافتراضية للدور
    const defaultPermissions = DEFAULT_ROLES[userData.role as keyof typeof DEFAULT_ROLES]

    // دمج الصلاحيات المخصصة مع الافتراضية
    const permissions = { ...defaultPermissions, ...userData.custom_permissions }

    const { data, error } = await supabase
      .from('users')
      .insert([
        {
          username: userData.username,
          email: userData.email,
          password_hash: hashedPassword,
          full_name: userData.full_name,
          role: userData.role,
          permissions: permissions,
          is_active: true,
          created_by: userData.created_by
        }
      ])
      .select()

    if (error) throw error

    // تسجيل النشاط
    await logActivity({
      user_id: userData.created_by,
      action: 'CREATE_USER',
      description: `تم إنشاء مستخدم جديد: ${userData.username}`,
      table_name: 'users',
      record_id: data[0]?.id,
      new_values: { username: userData.username, role: userData.role }
    })

    return { success: true, data: data[0] }
  } catch (error: any) {
    return { success: false, error: error.message }
  }
}

export const authenticateUser = async (username: string, password: string, rememberMe: boolean = false) => {
  try {
    // البحث عن المستخدم
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single()

    if (error || !user) {
      return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' }
    }

    // التحقق من كلمة المرور
    const isValidPassword = await comparePassword(password, user.password_hash)

    if (!isValidPassword) {
      return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' }
    }

    // إنشاء جلسة جديدة
    const sessionToken = generateSessionToken()
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + (rememberMe ? 24 * 30 : 24)) // 30 يوم أو 24 ساعة

    const { data: session, error: sessionError } = await supabase
      .from('login_sessions')
      .insert([
        {
          user_id: user.id,
          token: sessionToken,
          expires_at: expiresAt.toISOString(),
          remember_me: rememberMe,
          is_active: true
        }
      ])
      .select()

    if (sessionError) throw sessionError

    // تحديث آخر تسجيل دخول
    await supabase
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id)

    // تسجيل النشاط
    await logActivity({
      user_id: user.id,
      action: 'LOGIN',
      description: `تسجيل دخول المستخدم: ${user.username}`,
      table_name: 'users',
      record_id: user.id
    })

    return {
      success: true,
      user: { ...user, password_hash: undefined },
      session: session[0]
    }
  } catch (error: any) {
    return { success: false, error: error.message }
  }
}

export const logoutUser = async (sessionToken: string) => {
  try {
    // الحصول على معلومات الجلسة
    const { data: session } = await supabase
      .from('login_sessions')
      .select('user_id, users(username)')
      .eq('token', sessionToken)
      .eq('is_active', true)
      .single()

    // إلغاء تفعيل الجلسة
    const { error } = await supabase
      .from('login_sessions')
      .update({ is_active: false })
      .eq('token', sessionToken)

    if (error) throw error

    // تسجيل النشاط
    if (session) {
      await logActivity({
        user_id: session.user_id,
        action: 'LOGOUT',
        description: `تسجيل خروج المستخدم: ${(session as any).users.username}`,
        table_name: 'users',
        record_id: session.user_id
      })
    }

    return { success: true }
  } catch (error: any) {
    return { success: false, error: error.message }
  }
}

export const validateSession = async (sessionToken: string) => {
  try {
    const { data: session, error } = await supabase
      .from('login_sessions')
      .select(`
        *,
        users (
          id,
          username,
          email,
          full_name,
          role,
          permissions,
          is_active
        )
      `)
      .eq('token', sessionToken)
      .eq('is_active', true)
      .gt('expires_at', new Date().toISOString())
      .single()

    if (error || !session) {
      return { success: false, error: 'جلسة غير صالحة' }
    }

    return { success: true, user: session.users, session }
  } catch (error: any) {
    return { success: false, error: error.message }
  }
}

// دالة توليد رمز الجلسة
const generateSessionToken = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

// دالة تسجيل النشاطات
export const logActivity = async (activityData: {
  user_id: string
  action: string
  description: string
  table_name?: string
  record_id?: string
  old_values?: any
  new_values?: any
  ip_address?: string
  user_agent?: string
}) => {
  try {
    const { error } = await supabase
      .from('activity_logs')
      .insert([
        {
          ...activityData,
          created_at: new Date().toISOString()
        }
      ])

    if (error) throw error
    return { success: true }
  } catch (error: any) {
    console.error('Error logging activity:', error)
    return { success: false, error: error.message }
  }
}
