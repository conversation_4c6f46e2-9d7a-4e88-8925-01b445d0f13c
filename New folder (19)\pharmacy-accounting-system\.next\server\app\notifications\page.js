(()=>{var a={};a.id=554,a.ids=[554],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},711:(a,b,c)=>{Promise.resolve().then(c.bind(c,35037))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>j});var d=c(60687);c(43210);var e=c(16189),f=c(63213),g=c(41862),h=c(43649),i=c(99891);function j({children:a,requiredPermissions:b=[],requiredRole:c,fallback:j}){let{isAuthenticated:l,isLoading:m,user:n}=(0,f.As)(),{hasPermission:o,hasAnyPermission:p}=(0,f.Sk)(),q=(0,e.useRouter)();return m?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(g.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"جاري التحقق من الصلاحيات..."})]})}):l?c&&n?.role!==c?j||(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,d.jsx)(h.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"غير مصرح لك"}),(0,d.jsxs)("p",{className:"text-gray-600 mb-6",children:["هذه الصفحة مخصصة للمستخدمين من نوع: ",c]}),(0,d.jsx)("button",{onClick:()=>q.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة"})]})}):b.length>0&&!p(b)?j||(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,d.jsx)(i.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"صلاحيات غير كافية"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة"}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,d.jsx)("p",{className:"text-sm text-gray-700 font-medium mb-2",children:"الصلاحيات المطلوبة:"}),(0,d.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:b.map(a=>(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"w-2 h-2 bg-red-400 rounded-full"}),k(a)]},a))})]}),(0,d.jsx)("button",{onClick:()=>q.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة"})]})}):(0,d.jsx)(d.Fragment,{children:a}):null}let k=a=>({sales_view:"عرض المبيعات",sales_create:"إنشاء مبيعات",sales_edit:"تعديل المبيعات",sales_delete:"حذف المبيعات",sales_print:"طباعة المبيعات",sales_view_prices:"عرض الأسعار",purchases_view:"عرض المشتريات",purchases_create:"إنشاء مشتريات",purchases_edit:"تعديل المشتريات",purchases_delete:"حذف المشتريات",purchases_print:"طباعة المشتريات",inventory_view:"عرض المخزون",inventory_create:"إضافة للمخزون",inventory_edit:"تعديل المخزون",inventory_delete:"حذف من المخزون",inventory_print:"طباعة المخزون",customers_view:"عرض العملاء",customers_create:"إضافة عملاء",customers_edit:"تعديل العملاء",customers_delete:"حذف العملاء",suppliers_view:"عرض الموردين",suppliers_create:"إضافة موردين",suppliers_edit:"تعديل الموردين",suppliers_delete:"حذف الموردين",reports_view:"عرض التقارير",reports_financial:"التقارير المالية",reports_detailed:"التقارير المفصلة",reports_export:"تصدير التقارير",users_view:"عرض المستخدمين",users_create:"إضافة مستخدمين",users_edit:"تعديل المستخدمين",users_delete:"حذف المستخدمين",settings_view:"عرض الإعدادات",settings_edit:"تعديل الإعدادات",cashbox_view:"عرض الصندوق",cashbox_manage:"إدارة الصندوق",returns_view:"عرض المرتجعات",returns_create:"إنشاء مرتجعات",returns_edit:"تعديل المرتجعات",returns_delete:"حذف المرتجعات"})[a]||a},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28971:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,63555)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\notifications\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\notifications\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/notifications/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35037:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>A});var d=c(60687),e=c(43210),f=c(16189),g=c(21979),h=c(20769),i=c(46952),j=c(35071),k=c(43649),l=c(5336),m=c(96882),n=c(19080),o=c(28561),p=c(23928),q=c(41312),r=c(84027),s=c(97051),t=c(78122),u=c(40945),v=c(99270),w=c(48730),x=c(25334),y=c(13964),z=c(88233);function A(){let[a,b]=(0,e.useState)(""),[c,A]=(0,e.useState)("all"),[B,C]=(0,e.useState)("all"),[D,E]=(0,e.useState)("all"),F=(0,f.useRouter)(),{notifications:G,unreadCount:H,markAsRead:I,markAllAsRead:J,removeNotification:K,clearAll:L,refreshNotifications:M}=(0,i.E)(),N=G.filter(b=>{let d=b.title.toLowerCase().includes(a.toLowerCase())||b.message.toLowerCase().includes(a.toLowerCase()),e="all"===c||b.category===c,f="all"===B||b.priority===B,g="all"===D||"read"===D&&b.isRead||"unread"===D&&!b.isRead;return d&&e&&f&&g});return(0,d.jsx)(h.Ay,{children:(0,d.jsx)(g.A,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"التنبيهات"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة ومتابعة جميع تنبيهات النظام"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:M,className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,d.jsx)(t.A,{className:"h-4 w-4"}),"تحديث"]}),H>0&&(0,d.jsxs)("button",{onClick:J,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(u.A,{className:"h-4 w-4"}),"تحديد الكل كمقروء"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(s.A,{className:"h-8 w-8 text-blue-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"إجمالي التنبيهات"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:G.length})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(k.A,{className:"h-8 w-8 text-red-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"غير مقروءة"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:H})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(j.A,{className:"h-8 w-8 text-red-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"حرجة"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:G.filter(a=>"critical"===a.priority).length})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(n.A,{className:"h-8 w-8 text-purple-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"تنبيهات المخزون"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:G.filter(a=>"inventory"===a.category).length})]})})]})})]}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(v.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",placeholder:"البحث في التنبيهات...",value:a,onChange:a=>b(a.target.value),className:"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsx)("div",{children:(0,d.jsxs)("select",{value:c,onChange:a=>A(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الفئات"}),(0,d.jsx)("option",{value:"inventory",children:"مخزون"}),(0,d.jsx)("option",{value:"sales",children:"مبيعات"}),(0,d.jsx)("option",{value:"financial",children:"مالي"}),(0,d.jsx)("option",{value:"user",children:"مستخدمين"}),(0,d.jsx)("option",{value:"system",children:"نظام"})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("select",{value:B,onChange:a=>C(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الأولويات"}),(0,d.jsx)("option",{value:"critical",children:"حرج"}),(0,d.jsx)("option",{value:"high",children:"عالي"}),(0,d.jsx)("option",{value:"medium",children:"متوسط"}),(0,d.jsx)("option",{value:"low",children:"منخفض"})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("select",{value:D,onChange:a=>E(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,d.jsx)("option",{value:"unread",children:"غير مقروءة"}),(0,d.jsx)("option",{value:"read",children:"مقروءة"})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:0===N.length?(0,d.jsxs)("div",{className:"p-12 text-center",children:[(0,d.jsx)(s.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد تنبيهات"}),(0,d.jsx)("p",{className:"text-gray-500",children:"لا توجد تنبيهات تطابق المعايير المحددة"})]}):(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:N.map(a=>{var b,c,e,f;return(0,d.jsx)("div",{className:`p-6 hover:bg-gray-50 transition-colors ${!a.isRead?"bg-blue-50 border-r-4 border-blue-500":""}`,children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0 mt-1",children:(b=a.type,c=a.category,"error"===b?(0,d.jsx)(j.A,{className:"h-5 w-5 text-red-500"}):"warning"===b?(0,d.jsx)(k.A,{className:"h-5 w-5 text-yellow-500"}):"success"===b?(0,d.jsx)(l.A,{className:"h-5 w-5 text-green-500"}):"info"===b?(0,d.jsx)(m.A,{className:"h-5 w-5 text-blue-500"}):"inventory"===c?(0,d.jsx)(n.A,{className:"h-5 w-5 text-purple-500"}):"sales"===c?(0,d.jsx)(o.A,{className:"h-5 w-5 text-green-500"}):"financial"===c?(0,d.jsx)(p.A,{className:"h-5 w-5 text-yellow-500"}):"user"===c?(0,d.jsx)(q.A,{className:"h-5 w-5 text-blue-500"}):"system"===c?(0,d.jsx)(r.A,{className:"h-5 w-5 text-gray-500"}):(0,d.jsx)(s.A,{className:"h-5 w-5 text-gray-500"}))}),(0,d.jsx)("div",{className:"flex-1 min-w-0",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)("h3",{className:`text-lg font-medium ${!a.isRead?"text-gray-900":"text-gray-700"}`,children:a.title}),!a.isRead&&(0,d.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),(0,d.jsx)("p",{className:"text-gray-600 mb-3",children:a.message}),(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(e=a.category,(0,d.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${{inventory:"bg-purple-100 text-purple-800",sales:"bg-green-100 text-green-800",financial:"bg-yellow-100 text-yellow-800",user:"bg-blue-100 text-blue-800",system:"bg-gray-100 text-gray-800"}[e]}`,children:{inventory:"مخزون",sales:"مبيعات",financial:"مالي",user:"مستخدمين",system:"نظام"}[e]})),(f=a.priority,(0,d.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${{critical:"bg-red-100 text-red-800 border-red-200",high:"bg-orange-100 text-orange-800 border-orange-200",medium:"bg-yellow-100 text-yellow-800 border-yellow-200",low:"bg-blue-100 text-blue-800 border-blue-200"}[f]}`,children:{critical:"حرج",high:"عالي",medium:"متوسط",low:"منخفض"}[f]}))]}),(0,d.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-500",children:[(0,d.jsx)(w.A,{className:"h-4 w-4"}),(a=>{let b=new Date,c=new Date(a),d=Math.floor((b.getTime()-c.getTime())/6e4);if(d<1)return"الآن";if(d<60)return`منذ ${d} دقيقة`;let e=Math.floor(d/60);if(e<24)return`منذ ${e} ساعة`;let f=Math.floor(e/24);return`منذ ${f} يوم`})(a.createdAt)]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mr-4",children:[a.actionUrl&&(0,d.jsxs)("button",{onClick:()=>{a.isRead||I(a.id),a.actionUrl&&F.push(a.actionUrl)},className:"flex items-center gap-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-800 border border-blue-200 rounded-lg hover:bg-blue-50",children:[(0,d.jsx)(x.A,{className:"h-3 w-3"}),a.actionLabel||"عرض"]}),!a.isRead&&(0,d.jsx)("button",{onClick:()=>I(a.id),className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg",title:"تحديد كمقروء",children:(0,d.jsx)(y.A,{className:"h-4 w-4"})}),(0,d.jsx)("button",{onClick:()=>K(a.id),className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg",title:"حذف",children:(0,d.jsx)(z.A,{className:"h-4 w-4"})})]})]})})]})},a.id)})})}),G.length>0&&(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["عرض ",N.length," من ",G.length," تنبيه"]}),(0,d.jsx)("div",{className:"flex items-center gap-3",children:(0,d.jsxs)("button",{onClick:L,className:"flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors",children:[(0,d.jsx)(z.A,{className:"h-4 w-4"}),"مسح جميع التنبيهات"]})})]})})]})})})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63555:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\notifications\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92463:(a,b,c)=>{Promise.resolve().then(c.bind(c,63555))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,314,979],()=>b(b.s=28971));module.exports=c})();