'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Plus,
  Search,
  ShoppingCart,
  User,
  Calculator,
  Gift,
  Percent,
  Printer,
  Save,
  X,
  Calendar,
  Download
} from 'lucide-react'
import PrintableInvoice from '@/components/PrintableInvoice'
import PrintTemplate, { InvoicePrint } from '@/components/PrintTemplate'
import { usePrintSettings, printInvoice } from '@/hooks/usePrintSettings'
import { exportSalesData } from '@/lib/exportUtils'
import { getMedicines, getCustomers, completeSalesTransaction, initializeSystemData } from '@/lib/database'
import { useEffect } from 'react'
import { useClientDate } from '@/hooks/useClientDate'

interface InvoiceItem {
  id: string
  batchId: string
  medicineName: string
  batchCode: string
  quantity: number
  giftQuantity: number
  unitPrice: number
  totalPrice: number
  expiryDate: string
  availableQuantity: number
}

export default function SalesPage() {
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([])
  const [customerName, setCustomerName] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null)
  const [discountAmount, setDiscountAmount] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCustomerModal, setShowCustomerModal] = useState(false)
  const [showPrintPreview, setShowPrintPreview] = useState(false)
  const [currentInvoice, setCurrentInvoice] = useState<any>(null)
  const [availableMedicines, setAvailableMedicines] = useState<any[]>([])
  const [customers, setCustomers] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'credit'>('cash')
  const [notes, setNotes] = useState('')
  const [privateNotes, setPrivateNotes] = useState('')
  const { settings: printSettings } = usePrintSettings()
  const { mounted, currentDate, generateInvoiceNumber, getCurrentDateISO, formatNumber } = useClientDate()

  // Load data on component mount
  useEffect(() => {
    loadMedicines()
    loadCustomers()
  }, [])

  const loadMedicines = async () => {
    try {
      // Initialize system data first
      await initializeSystemData()

      const result = await getMedicines()
      if (result.success && result.data) {
        // Transform data to include batches
        const medicinesWithBatches: any[] = []
        result.data.forEach((medicine: any) => {
          if (medicine.medicine_batches && Array.isArray(medicine.medicine_batches)) {
            medicine.medicine_batches.forEach((batch: any) => {
              if (batch.quantity > 0) { // Only show batches with available quantity
                medicinesWithBatches.push({
                  id: medicine.id,
                  batchId: batch.id,
                  name: medicine.name,
                  batchCode: batch.batch_code,
                  price: batch.selling_price,
                  quantity: batch.quantity,
                  expiry: batch.expiry_date,
                  category: medicine.category,
                  manufacturer: medicine.manufacturer
                })
              }
            })
          }
        })
        setAvailableMedicines(medicinesWithBatches)
      } else {
        console.error('Failed to load medicines:', result.error)
        // Use sample data as fallback
        setAvailableMedicines([
          { id: '1', batchId: 'b1', name: 'باراسيتامول 500mg', batchCode: 'B001', price: 750, quantity: 150, expiry: '2024-12-15' },
          { id: '2', batchId: 'b2', name: 'أموكسيسيلين 250mg', batchCode: 'B003', price: 1800, quantity: 200, expiry: '2025-03-10' }
        ])
      }
    } catch (error) {
      console.error('Error loading medicines:', error)
      // Use sample data as fallback
      setAvailableMedicines([
        { id: '1', batchId: 'b1', name: 'باراسيتامول 500mg', batchCode: 'B001', price: 750, quantity: 150, expiry: '2024-12-15' },
        { id: '2', batchId: 'b2', name: 'أموكسيسيلين 250mg', batchCode: 'B003', price: 1800, quantity: 200, expiry: '2025-03-10' }
      ])
    }
  }

  const loadCustomers = async () => {
    try {
      const result = await getCustomers()
      if (result.success && result.data) {
        setCustomers(result.data)
      } else {
        console.error('Failed to load customers:', result.error)
        // Use sample data as fallback
        setCustomers([
          { id: '1', name: 'أحمد محمد علي', phone: '07901111111', address: 'بغداد - الكرادة' },
          { id: '2', name: 'فاطمة حسن محمد', phone: '07802222222', address: 'بغداد - الجادرية' }
        ])
      }
    } catch (error) {
      console.error('Error loading customers:', error)
      // Use sample data as fallback
      setCustomers([
        { id: '1', name: 'أحمد محمد علي', phone: '07901111111', address: 'بغداد - الكرادة' },
        { id: '2', name: 'فاطمة حسن محمد', phone: '07802222222', address: 'بغداد - الجادرية' }
      ])
    }
  }

  const filteredMedicines = availableMedicines.filter(medicine =>
    medicine.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const addToInvoice = (medicine: any) => {
    // التحقق من توفر الكمية
    if (medicine.quantity <= 0) {
      alert('هذا الدواء غير متوفر في المخزون')
      return
    }

    const existingItem = invoiceItems.find(item =>
      item.batchId === medicine.batchId
    )

    if (existingItem) {
      // التحقق من عدم تجاوز الكمية المتوفرة
      const totalRequested = existingItem.quantity + existingItem.giftQuantity + 1
      if (totalRequested > medicine.quantity) {
        alert('الكمية المطلوبة تتجاوز المتوفر في المخزون')
        return
      }

      setInvoiceItems(items =>
        items.map(item =>
          item.batchId === medicine.batchId
            ? {
                ...item,
                quantity: item.quantity + 1,
                totalPrice: (item.quantity + 1) * item.unitPrice
              }
            : item
        )
      )
    } else {
      const newItem: InvoiceItem = {
        id: medicine.id,
        batchId: medicine.batchId,
        medicineName: medicine.name,
        batchCode: medicine.batchCode,
        quantity: 1,
        giftQuantity: 0,
        unitPrice: medicine.price,
        totalPrice: medicine.price,
        expiryDate: medicine.expiry,
        availableQuantity: medicine.quantity
      }
      setInvoiceItems([...invoiceItems, newItem])
    }
    setSearchTerm('')
  }

  const updateQuantity = (batchId: string, newQuantity: number) => {
    // السماح بالقيم الفارغة أو الصفر دون حذف العنصر
    const quantity = Math.max(0, newQuantity || 0)

    setInvoiceItems(items =>
      items.map(item => {
        if (item.batchId === batchId) {
          const totalRequested = quantity + item.giftQuantity
          if (totalRequested > item.availableQuantity && quantity > 0) {
            alert('الكمية المطلوبة تتجاوز المتوفر في المخزون')
            return item
          }
          return {
            ...item,
            quantity: quantity,
            totalPrice: quantity * item.unitPrice
          }
        }
        return item
      })
    )
  }

  const updateGiftQuantity = (batchId: string, newGiftQuantity: number) => {
    if (newGiftQuantity < 0) return

    setInvoiceItems(items =>
      items.map(item => {
        if (item.batchId === batchId) {
          const totalRequested = item.quantity + newGiftQuantity
          if (totalRequested > item.availableQuantity) {
            alert('الكمية المطلوبة تتجاوز المتوفر في المخزون')
            return item
          }
          return { ...item, giftQuantity: newGiftQuantity }
        }
        return item
      })
    )
  }

  const removeItem = (batchId: string) => {
    setInvoiceItems(items =>
      items.filter(item => item.batchId !== batchId)
    )
  }

  const calculateSubtotal = () => {
    return invoiceItems.reduce((total, item) =>
      total + item.totalPrice, 0
    )
  }

  const calculateFinalAmount = () => {
    return calculateSubtotal() - discountAmount
  }

  const handleSaveInvoice = async () => {
    console.log('🔄 بدء عملية حفظ الفاتورة...')
    console.log('📦 عدد العناصر:', invoiceItems.length)
    console.log('📋 العناصر:', invoiceItems)

    if (invoiceItems.length === 0) {
      alert('يرجى إضافة عناصر للفاتورة')
      return
    }

    setLoading(true)
    try {
      const invoiceNumber = generateInvoiceNumber()

      // Prepare invoice data
      const invoiceData = {
        invoice_number: invoiceNumber,
        customer_id: selectedCustomer?.id,
        customer_name: selectedCustomer?.name || customerName,
        total_amount: calculateSubtotal(),
        discount_amount: discountAmount,
        final_amount: calculateFinalAmount(),
        payment_method: paymentMethod,
        payment_status: paymentMethod === 'cash' ? 'paid' : 'pending',
        notes: notes,
        private_notes: privateNotes
      }

      // Prepare items for database
      const dbItems = []
      for (const item of invoiceItems) {
        // Find medicine name for this item - availableMedicines has flat structure
        const medicine = availableMedicines.find(m => m.batchId === item.batchId)
        const medicineName = medicine?.name || 'غير محدد'

        console.log(`🔍 البحث عن الدواء للدفعة ${item.batchId}:`)
        console.log(`📋 الدواء الموجود:`, medicine)
        console.log(`💊 اسم الدواء: ${medicineName}`)

        // Add regular sale item
        if (item.quantity > 0) {
          dbItems.push({
            medicine_batch_id: item.batchId,
            quantity: item.quantity,
            unit_price: item.unitPrice,
            total_price: item.totalPrice,
            is_gift: false,
            medicine_name: medicineName,
            medicineName: medicineName
          })
        }

        // Add gift item if any
        if (item.giftQuantity > 0) {
          dbItems.push({
            medicine_batch_id: item.batchId,
            quantity: item.giftQuantity,
            unit_price: 0,
            total_price: 0,
            is_gift: true,
            medicine_name: medicineName,
            medicineName: medicineName
          })
        }
      }

      // Save to database
      console.log('💾 حفظ البيانات في قاعدة البيانات...')
      console.log('📄 بيانات الفاتورة:', invoiceData)
      console.log('📦 عناصر الفاتورة:', dbItems)

      const result = await completeSalesTransaction(invoiceData, dbItems)
      console.log('✅ نتيجة الحفظ:', result)

      if (result.success) {
        // Create enhanced items for printing with medicine names
        const enhancedItems = dbItems.map(item => {
          const medicine = availableMedicines.find(m => m.batchId === item.medicine_batch_id)
          const medicineName = medicine?.name || item.medicine_name || item.medicineName || 'غير محدد'

          console.log(`🖨️ تحضير عنصر للطباعة:`)
          console.log(`📋 الدواء الموجود:`, medicine)
          console.log(`💊 اسم الدواء للطباعة: ${medicineName}`)

          return {
            id: item.id || `item_${Date.now()}_${Math.random()}`,
            batchId: item.medicine_batch_id || item.batchId,
            name: medicineName,
            quantity: item.quantity,
            unitPrice: item.unit_price || item.unitPrice,
            totalPrice: item.total_price || item.totalPrice,
            isGift: item.is_gift || item.isGift || false,
            medicine_name: medicineName,
            medicineName: medicineName,
            medicine_batches: {
              batch_code: medicine?.batchCode || '',
              expiry_date: medicine?.expiry || '',
              medicines: {
                name: medicineName,
                category: medicine?.category || '',
                manufacturer: medicine?.manufacturer || '',
                strength: medicine?.strength || '',
                form: medicine?.form || ''
              }
            }
          }
        })

        const completeInvoiceData = {
          ...invoiceData,
          invoiceNumber,
          date: getCurrentDateISO(),
          customerName: selectedCustomer?.name || customerName,
          customerPhone: selectedCustomer?.phone,
          customerAddress: selectedCustomer?.address,
          items: enhancedItems, // Use enhanced items instead of original invoiceItems
          subtotal: calculateSubtotal(),
          discount: discountAmount,
          finalAmount: calculateFinalAmount(),
          sales_invoice_items: enhancedItems // Use enhanced items for sales_invoice_items too
        }

        setCurrentInvoice(completeInvoiceData)
        alert('تم حفظ الفاتورة بنجاح!')

        // Auto print invoice
        setTimeout(() => {
          printInvoice(completeInvoiceData, 'sales', printSettings)
        }, 500)

        // Reload medicines to update quantities
        await loadMedicines()

        // Clear form
        setInvoiceItems([])
        setSelectedCustomer(null)
        setCustomerName('')
        setDiscountAmount(0)
        setPaymentMethod('cash')
        setNotes('')
        setPrivateNotes('')
      } else {
        console.error('❌ فشل في حفظ الفاتورة:', result.error)
        const errorMessage = result.error?.message || 'خطأ غير معروف'
        alert(`حدث خطأ أثناء حفظ الفاتورة:\n${errorMessage}\n\nيرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.`)
      }
    } catch (error) {
      console.error('💥 خطأ غير متوقع في حفظ الفاتورة:', error)
      alert(`حدث خطأ غير متوقع أثناء حفظ الفاتورة:\n${error}\n\nيرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.`)
    } finally {
      setLoading(false)
    }
  }

  const handlePrintInvoice = () => {
    if (!currentInvoice && invoiceItems.length > 0) {
      handleSaveInvoice()
    }
    setShowPrintPreview(true)
  }

  const handleDirectPrint = () => {
    if (currentInvoice) {
      printInvoice(currentInvoice, 'sales', printSettings)
    } else {
      alert('لا توجد فاتورة للطباعة')
    }
  }

  const handleExportInvoice = async () => {
    if (invoiceItems.length === 0) {
      alert('لا توجد بيانات للتصدير')
      return
    }

    const invoiceData = currentInvoice || {
      invoiceNumber: generateInvoiceNumber(),
      date: getCurrentDateISO(),
      customerName: selectedCustomer?.name || customerName,
      subtotal: calculateSubtotal(),
      discount: discountAmount,
      finalAmount: calculateFinalAmount()
    }

    const success = await exportSalesData([invoiceData])
    if (success) {
      alert('تم تصدير الفاتورة بنجاح!')
    } else {
      alert('حدث خطأ أثناء التصدير')
    }
  }

  const selectCustomer = (customer: any) => {
    setSelectedCustomer(customer)
    setCustomerName(customer.name)
    setShowCustomerModal(false)
  }

  const clearCustomer = () => {
    setSelectedCustomer(null)
    setCustomerName('')
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المبيعات</h1>
            <p className="text-gray-600 mt-1">إنشاء فاتورة مبيعات جديدة</p>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Calendar className="h-4 w-4" />
            <span>{mounted ? currentDate : ''}</span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Medicine Search */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">البحث عن الأدوية</h2>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="ابحث عن دواء..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {searchTerm && (
                <div className="mt-4 max-h-60 overflow-y-auto">
                  {filteredMedicines.map((medicine) => (
                    <div
                      key={`${medicine.id}-${medicine.batchCode}`}
                      onClick={() => addToInvoice(medicine)}
                      className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
                    >
                      <div>
                        <p className="font-medium text-gray-900">{medicine.name}</p>
                        <p className="text-sm text-gray-500">
                          وجبة: {medicine.batchCode} • متوفر: {medicine.quantity} • انتهاء: {medicine.expiry}
                        </p>
                      </div>
                      <div className="text-left">
                        <p className="font-medium text-gray-900">{medicine.price} د.ع</p>
                        <button className="text-blue-600 text-sm hover:text-blue-800">
                          إضافة
                        </button>
                      </div>
                    </div>
                  ))}
                  {filteredMedicines.length === 0 && (
                    <p className="text-gray-500 text-center py-4">لا توجد نتائج</p>
                  )}
                </div>
              )}
            </div>

            {/* Invoice Items */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">عناصر الفاتورة</h2>
              
              {invoiceItems.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">لا توجد عناصر في الفاتورة</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {invoiceItems.map((item) => (
                    <div key={item.batchId} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{item.medicineName}</p>
                          <p className="text-sm text-gray-500">
                            وجبة: {item.batchCode} • انتهاء: {item.expiryDate} • متوفر: {item.availableQuantity}
                          </p>
                        </div>
                        <button
                          onClick={() => removeItem(item.batchId)}
                          className="p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        {/* Regular Sale */}
                        <div className="bg-blue-50 p-3 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-blue-800">البيع</span>
                            <span className="text-sm text-blue-600">{item.unitPrice} د.ع/قطعة</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => updateQuantity(item.batchId, item.quantity - 1)}
                              className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200"
                            >
                              -
                            </button>
                            <input
                              key={`quantity-${item.batchId}`}
                              type="number"
                              value={item.quantity || ''}
                              onChange={(e) => {
                                const value = e.target.value
                                if (value === '') {
                                  updateQuantity(item.batchId, 0)
                                } else {
                                  const numValue = parseInt(value)
                                  if (!isNaN(numValue)) {
                                    updateQuantity(item.batchId, numValue)
                                  }
                                }
                              }}
                              onBlur={(e) => {
                                if (e.target.value === '' || parseInt(e.target.value) === 0) {
                                  updateQuantity(item.batchId, 1)
                                }
                              }}
                              className="w-16 text-center font-medium border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                              min="0"
                              max={item.availableQuantity}
                              placeholder="1"
                            />
                            <button
                              onClick={() => updateQuantity(item.batchId, item.quantity + 1)}
                              className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200"
                            >
                              +
                            </button>
                            <span className="mr-2 text-sm font-medium text-blue-800">
                              = {item.totalPrice} د.ع
                            </span>
                          </div>
                        </div>

                        {/* Gift */}
                        <div className="bg-green-50 p-3 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-green-800">هدية</span>
                            <span className="text-sm text-green-600">مجاناً</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => updateGiftQuantity(item.batchId, item.giftQuantity - 1)}
                              className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 hover:bg-green-200"
                            >
                              -
                            </button>
                            <input
                              type="number"
                              value={item.giftQuantity || ''}
                              onChange={(e) => {
                                const value = e.target.value
                                if (value === '') {
                                  updateGiftQuantity(item.batchId, 0)
                                } else {
                                  const numValue = parseInt(value)
                                  if (!isNaN(numValue)) {
                                    updateGiftQuantity(item.batchId, numValue)
                                  }
                                }
                              }}
                              className="w-16 text-center font-medium border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-green-500"
                              min="0"
                              max={item.availableQuantity}
                              placeholder="0"
                            />
                            <button
                              onClick={() => updateGiftQuantity(item.batchId, item.giftQuantity + 1)}
                              className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 hover:bg-green-200"
                            >
                              +
                            </button>
                            <Gift className="h-4 w-4 text-green-600 mr-2" />
                          </div>
                        </div>
                      </div>

                      <div className="mt-3 text-right">
                        <span className="text-sm text-gray-500">
                          إجمالي الكمية: {item.quantity + item.giftQuantity} •
                          المتبقي: {item.availableQuantity - (item.quantity + item.giftQuantity)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Invoice Summary */}
          <div className="space-y-6">
            {/* Customer Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">معلومات العميل</h2>
              <div className="space-y-4">
                {selectedCustomer ? (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{selectedCustomer.name}</p>
                        <p className="text-sm text-gray-600">{selectedCustomer.phone}</p>
                        <p className="text-sm text-gray-600">{selectedCustomer.address}</p>
                      </div>
                      <button
                        onClick={clearCustomer}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم العميل
                      </label>
                      <input
                        type="text"
                        value={customerName}
                        onChange={(e) => setCustomerName(e.target.value)}
                        placeholder="اسم العميل (اختياري)"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <button
                      onClick={() => setShowCustomerModal(true)}
                      className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2"
                    >
                      <User className="h-4 w-4" />
                      اختيار من قائمة العملاء
                    </button>
                  </>
                )}
              </div>
            </div>

            {/* Invoice Total */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">إجمالي الفاتورة</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">المجموع الفرعي:</span>
                  <span className="font-medium">{formatNumber(calculateSubtotal())} د.ع</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Percent className="h-4 w-4 text-gray-400" />
                  <input
                    type="number"
                    value={discountAmount}
                    onChange={(e) => setDiscountAmount(Number(e.target.value))}
                    placeholder="مبلغ الخصم"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <span className="text-gray-600">د.ع</span>
                </div>
                
                <div className="border-t pt-3">
                  <div className="flex justify-between text-lg font-bold">
                    <span>المجموع النهائي:</span>
                    <span className="text-blue-600">{formatNumber(calculateFinalAmount())} د.ع</span>
                  </div>
                </div>

                {/* Invoice Status */}
                <div className="border-t pt-3 mt-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">حالة الفاتورة:</span>
                    <span className={`font-medium px-2 py-1 rounded-full text-xs ${
                      invoiceItems.length > 0
                        ? 'bg-green-100 text-green-700'
                        : 'bg-orange-100 text-orange-700'
                    }`}>
                      {invoiceItems.length > 0 ? '✓ جاهزة للحفظ' : '⚠ تحتاج عناصر'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-2">
                    <span className="text-gray-600">عدد العناصر:</span>
                    <span className="font-medium text-blue-600">{invoiceItems.length}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">طريقة الدفع</h2>

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setPaymentMethod('cash')}
                  className={`p-3 border-2 rounded-lg text-center transition-colors ${
                    paymentMethod === 'cash'
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : 'border-gray-300 text-gray-600 hover:border-green-300'
                  }`}
                >
                  <div className="font-medium">نقداً</div>
                  <div className="text-sm">دفع فوري</div>
                </button>

                <button
                  onClick={() => setPaymentMethod('credit')}
                  className={`p-3 border-2 rounded-lg text-center transition-colors ${
                    paymentMethod === 'credit'
                      ? 'border-orange-500 bg-orange-50 text-orange-700'
                      : 'border-gray-300 text-gray-600 hover:border-orange-300'
                  }`}
                >
                  <div className="font-medium">آجل</div>
                  <div className="text-sm">دفع لاحق</div>
                </button>
              </div>

              {paymentMethod === 'credit' && (
                <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <p className="text-orange-800 text-sm">
                    ⚠️ سيتم إضافة هذا المبلغ لحساب العميل كدين
                  </p>
                </div>
              )}
            </div>

            {/* Notes */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">الملاحظات</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات عامة (تظهر في الطباعة)
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="ملاحظات للعميل..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات خاصة (للنظام فقط)
                  </label>
                  <textarea
                    value={privateNotes}
                    onChange={(e) => setPrivateNotes(e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                    placeholder="ملاحظات داخلية..."
                  />
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              {/* Save Button - Always Visible */}
              <button
                onClick={handleSaveInvoice}
                disabled={invoiceItems.length === 0 || loading}
                className={`w-full px-4 py-3 rounded-lg flex items-center justify-center gap-2 font-semibold transition-all duration-200 min-h-[48px] ${
                  invoiceItems.length === 0 || loading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-lg transform hover:scale-105'
                }`}
              >
                <Save className="h-5 w-5" />
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    جاري الحفظ...
                  </>
                ) : (
                  'حفظ الفاتورة'
                )}
              </button>

              {/* Status Message */}
              {invoiceItems.length === 0 && (
                <p className="text-center text-sm text-gray-500 bg-gray-50 p-2 rounded-lg">
                  أضف عناصر للفاتورة لتفعيل زر الحفظ
                </p>
              )}

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={handlePrintInvoice}
                  disabled={invoiceItems.length === 0}
                  className="bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  معاينة وطباعة
                </button>

                <button
                  onClick={handleDirectPrint}
                  disabled={!currentInvoice}
                  className="bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  طباعة مباشرة
                </button>
              </div>

              <button
                onClick={handleExportInvoice}
                disabled={invoiceItems.length === 0}
                className="w-full bg-orange-600 text-white px-4 py-3 rounded-lg hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                <Download className="h-4 w-4" />
                تصدير للإكسل
              </button>
            </div>
          </div>
        </div>

        {/* Customer Selection Modal */}
        {showCustomerModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-96 overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">اختيار عميل</h2>
                <button
                  onClick={() => setShowCustomerModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-2">
                {customers.map((customer) => (
                  <div
                    key={customer.id}
                    onClick={() => selectCustomer(customer)}
                    className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                  >
                    <p className="font-medium text-gray-900">{customer.name}</p>
                    <p className="text-sm text-gray-600">{customer.phone}</p>
                    <p className="text-sm text-gray-600">{customer.address}</p>
                  </div>
                ))}
              </div>

              <div className="mt-4 pt-4 border-t">
                <button
                  onClick={() => setShowCustomerModal(false)}
                  className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Print Preview Modal */}
        {showPrintPreview && currentInvoice && (
          <PrintTemplate
            title="فاتورة مبيعات"
            data={currentInvoice}
            type="invoice"
            settings={printSettings}
            onClose={() => setShowPrintPreview(false)}
          >
            <InvoicePrint
              invoice={currentInvoice}
              type="sales"
              settings={printSettings}
            />
          </PrintTemplate>
        )}
      </div>
    </AppLayout>
  )
}
