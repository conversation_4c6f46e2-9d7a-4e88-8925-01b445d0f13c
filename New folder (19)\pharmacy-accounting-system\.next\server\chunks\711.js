"use strict";exports.id=711,exports.ids=[711],exports.modules={84622:(a,b,c)=>{c.r(b),c.d(b,{generateLarenInvoiceHTML:()=>d,generateLarenReportHTML:()=>e,generateLarenReturnHTML:()=>f});let d=(a,b,c)=>{let d,e,f,g;if("return"===b){d=a.return_invoice_items||a.sales_return_items||a.purchase_return_items||[];let b=a.type||a.return_type||"sales";e="sales"===b?a.customers?.name||a.customer_name||a.customerName||"عميل نقدي":a.suppliers?.name||a.supplier_name||a.supplierName||"غير محدد",f="sales"===b?"مرتجع مبيعات":"مرتجع مشتريات",g=a.return_number||a.invoice_number||"غير محدد"}else d="sales"===b?a.sales_invoice_items:a.purchase_invoice_items,e="sales"===b?a.customers?.name||a.customer_name||a.customerName||"عميل نقدي":a.suppliers?.name||a.supplier_name||a.supplierName||"غير محدد",f="sales"===b?"فاتورة مبيعات":"فاتورة مشتريات",g=a.invoice_number||"غير محدد";return`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${f} - ${g}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: 'Arial', sans-serif; 
          font-size: 14px;
          color: #000;
          background-color: #fff;
          line-height: 1.4;
          direction: rtl;
        }
        .container { 
          max-width: 210mm; 
          margin: 0 auto; 
          padding: 10mm;
          border: 2px solid #000;
          min-height: 297mm;
          position: relative;
        }
        
        /* Header Section */
        .header {
          border-bottom: 2px solid #000;
          padding-bottom: 15px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        
        .company-info {
          flex: 1;
          text-align: right;
        }
        
        .company-name-ar {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .company-name-en {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 10px;
          direction: ltr;
          text-align: left;
        }
        
        .company-address {
          font-size: 14px;
          margin-bottom: 5px;
        }
        
        .logo-section {
          width: 120px;
          height: 120px;
          border: 2px solid #000;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 20px;
          background-color: #f8f9fa;
        }
        
        .logo-text {
          font-size: 16px;
          font-weight: bold;
          text-align: center;
          line-height: 1.2;
        }
        
        /* Invoice Details Section */
        .invoice-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          border: 1px solid #000;
        }
        
        .invoice-info, .customer-info {
          flex: 1;
          padding: 10px;
          border-right: 1px solid #000;
        }
        
        .customer-info {
          border-right: none;
        }
        
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          padding: 2px 0;
        }
        
        .detail-label {
          font-weight: bold;
          min-width: 80px;
        }
        
        .detail-value {
          text-align: left;
        }
        
        /* Items Table */
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          border: 2px solid #000;
        }
        
        .items-table th,
        .items-table td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center;
          font-size: 12px;
        }
        
        .items-table th {
          background-color: #f0f0f0;
          font-weight: bold;
        }
        
        .items-table .item-name {
          text-align: right;
          padding-right: 10px;
        }
        
        /* Totals Section */
        .totals-section {
          width: 300px;
          margin-left: auto;
          border: 2px solid #000;
          margin-bottom: 20px;
        }
        
        .totals-section table {
          width: 100%;
          border-collapse: collapse;
        }
        
        .totals-section td {
          border: 1px solid #000;
          padding: 8px;
          font-size: 14px;
        }
        
        .totals-section .total-label {
          background-color: #f0f0f0;
          font-weight: bold;
          text-align: right;
        }
        
        .totals-section .total-value {
          text-align: center;
          font-weight: bold;
        }
        
        /* Footer Section */
        .footer {
          position: absolute;
          bottom: 10mm;
          left: 10mm;
          right: 10mm;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid #000;
          padding-top: 15px;
        }
        
        .signature-section {
          text-align: center;
          flex: 1;
        }
        
        .signature-box {
          width: 150px;
          height: 80px;
          border: 2px solid #000;
          border-radius: 50%;
          margin: 0 auto 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: #666;
        }
        
        .notes-section {
          margin-bottom: 30px;
          border: 1px solid #000;
          padding: 10px;
          min-height: 60px;
        }
        
        .notes-label {
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        @media print {
          body { margin: 0; }
          .container { 
            border: 2px solid #000;
            box-shadow: none;
            margin: 0;
            padding: 10mm;
          }
          @page {
            size: A4;
            margin: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <!-- Header -->
        <div class="header">
          <div class="company-info">
            <div class="company-name-ar">${c.companyName}</div>
            <div class="company-name-en">${c.companyNameEn}</div>
            <div class="company-address">${c.companyAddress}</div>
          </div>
          
          <div class="logo-section">
            <div class="logo-text">
              LAREN<br>
              لارين
            </div>
          </div>
        </div>

        <!-- Document Details -->
        <div class="invoice-details">
          <div class="invoice-info">
            <div class="detail-row">
              <span class="detail-label">${"return"===b?"رقم المرتجع:":"رقم الفاتورة:"}</span>
              <span class="detail-value">${g}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">التاريخ:</span>
              <span class="detail-value">${new Date(a.created_at).toLocaleDateString("ar-EG")}</span>
            </div>
            ${"return"!==b?`
            <div class="detail-row">
              <span class="detail-label">طريقة الدفع:</span>
              <span class="detail-value">${"cash"===a.payment_method?"نقداً":"آجل"}</span>
            </div>`:""}
            ${"return"===b?`
            <div class="detail-row">
              <span class="detail-label">سبب المرتجع:</span>
              <span class="detail-value">${a.reason||"غير محدد"}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">الحالة:</span>
              <span class="detail-value">${"approved"===a.status?"مقبول":"rejected"===a.status?"مرفوض":"قيد المراجعة"}</span>
            </div>`:""}
            </div>
          </div>
          
          <div class="customer-info">
            <div class="detail-row">
              <span class="detail-label">${"return"===b?"purchase"===a.type||"purchase"===a.return_type?"المورد:":"العميل:":"sales"===b?"العميل:":"المورد:"}</span>
              <span class="detail-value">${e}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">الهاتف:</span>
              <span class="detail-value">${"return"===b?"purchase"===a.type||"purchase"===a.return_type?a.suppliers?.phone||a.supplier_phone||a.supplierPhone||"":a.customers?.phone||a.customer_phone||a.customerPhone||"":"sales"===b?a.customers?.phone||a.customer_phone||a.customerPhone||"":a.suppliers?.phone||a.supplier_phone||a.supplierPhone||""}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">العنوان:</span>
              <span class="detail-value">${"return"===b?"purchase"===a.type||"purchase"===a.return_type?a.suppliers?.address||a.supplier_address||a.supplierAddress||"":a.customers?.address||a.customer_address||a.customerAddress||"":"sales"===b?a.customers?.address||a.customer_address||a.customerAddress||"":a.suppliers?.address||a.supplier_address||a.supplierAddress||""}</span>
            </div>
          </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 40px;">ت</th>
              <th style="width: ${"return"===b?"150px":"200px"};">اسم المادة</th>
              <th style="width: 60px;">الكمية</th>
              <th style="width: 80px;">السعر</th>
              <th style="width: 100px;">المجموع</th>
              ${"return"===b?'<th style="width: 100px;">سبب المرتجع</th>':""}
              <th style="width: 60px;">EXP</th>
              <th style="width: 60px;">B.N</th>
            </tr>
          </thead>
          <tbody>
            ${d?.map((c,d)=>`
              <tr>
                <td>${d+1}</td>
                <td class="item-name">${"return"===b?c.medicine_name||c.medicineName||c.medicine_batches?.medicines?.name||c.medicines?.name||"غير محدد":"sales"===b?c.medicine_name||c.medicineName||c.medicine_batches?.medicines?.name||"غير محدد":c.medicine_name||c.medicineName||c.medicines?.name||c.name||"غير محدد"}</td>
                <td>${c.quantity}</td>
                <td>${"return"===b?"purchase"===a.type||"purchase"===a.return_type?(c.unit_cost||c.unitCost||0).toLocaleString():(c.unit_price||c.unitPrice||0).toLocaleString():"sales"===b?(c.unit_price||0).toLocaleString():(c.unit_cost||c.unitCost||0).toLocaleString()}</td>
                <td>${"return"===b?"purchase"===a.type||"purchase"===a.return_type?(c.total_cost||c.totalCost||0).toLocaleString():(c.total_price||c.totalPrice||0).toLocaleString():"sales"===b?(c.total_price||0).toLocaleString():(c.total_cost||c.totalCost||0).toLocaleString()}</td>
                ${"return"===b?`<td style="font-size: 10px;">${c.return_reason||a.reason||"غير محدد"}</td>`:""}
                <td>${"return"===b?c.expiry_date||c.expiryDate||c.medicine_batches?.expiry_date?new Date(c.expiry_date||c.expiryDate||c.medicine_batches.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"":"sales"===b?c.medicine_batches?.expiry_date?new Date(c.medicine_batches.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"":c.expiry_date||c.expiryDate?new Date(c.expiry_date||c.expiryDate).toLocaleDateString("en-GB").replace(/\//g,"/"):""}</td>
                <td>${"return"===b?c.batch_code||c.batchCode||c.medicine_batches?.batch_number||"":"sales"===b?c.medicine_batches?.batch_number||"":c.batch_code||c.batchCode||""}</td>
              </tr>
            `).join("")||""}
            
            <!-- Empty rows to fill space -->
            ${Array.from({length:Math.max(0,10-(d?.length||0))},(a,c)=>`
              <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                ${"return"===b?"<td>&nbsp;</td>":""}
                <td>&nbsp;</td>
                <td>&nbsp;</td>
              </tr>
            `).join("")}
          </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
          <table>
            ${"return"!==b?`
            <tr>
              <td class="total-label">المجموع الفرعي:</td>
              <td class="total-value">${a.total_amount?.toLocaleString()||0}</td>
            </tr>
            <tr>
              <td class="total-label">الخصم:</td>
              <td class="total-value">${a.discount_amount?.toLocaleString()||0}</td>
            </tr>
            <tr style="background-color: #f0f0f0;">
              <td class="total-label">المجموع النهائي:</td>
              <td class="total-value">${a.final_amount?.toLocaleString()||0}</td>
            </tr>`:`
            <tr style="background-color: #f0f0f0;">
              <td class="total-label">إجمالي المرتجع:</td>
              <td class="total-value">${a.total_amount?.toLocaleString()||0}</td>
            </tr>`}
          </table>
        </div>

        <!-- Notes -->
        <div class="notes-section">
          <div class="notes-label">ملاحظات: ${a.notes||""}</div>
          ${"return"===b&&a.rejection_reason?`
          <div class="notes-label" style="margin-top: 10px; color: #dc2626;">سبب الرفض: ${a.rejection_reason}</div>`:""}
        </div>

        <!-- Footer -->
        <div class="footer">
          <div style="font-size: 12px;">
            صفحة 1 من 1
          </div>
          
          <div class="signature-section">
            <div class="signature-box">
              ختم وتوقيع<br>
              المسؤول
            </div>
          </div>
          
          <div style="font-size: 12px; text-align: left;">
            ${c.footerText||"شكراً لتعاملكم معنا"}
          </div>
        </div>
      </div>
    </body>
    </html>
  `},e=(a,b,c,d)=>`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${c}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: 'Arial', sans-serif; 
          font-size: 12px;
          color: #000;
          background-color: #fff;
          line-height: 1.4;
          direction: rtl;
        }
        .container { 
          max-width: 210mm; 
          margin: 0 auto; 
          padding: 10mm;
          border: 2px solid #000;
        }
        
        .header {
          border-bottom: 2px solid #000;
          padding-bottom: 15px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        
        .company-info {
          flex: 1;
          text-align: right;
        }
        
        .company-name-ar {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .company-name-en {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
          direction: ltr;
          text-align: left;
        }
        
        .logo-section {
          width: 100px;
          height: 100px;
          border: 2px solid #000;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 20px;
          background-color: #f8f9fa;
        }
        
        .report-title {
          text-align: center;
          font-size: 18px;
          font-weight: bold;
          margin: 20px 0;
          padding: 10px;
          border: 1px solid #000;
          background-color: #f0f0f0;
        }
        
        .report-date {
          text-align: center;
          margin-bottom: 20px;
          font-size: 14px;
        }
        
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          border: 2px solid #000;
        }
        
        th, td {
          border: 1px solid #000;
          padding: 4px 6px;
          text-align: right;
          font-size: 10px;
          vertical-align: top;
          word-wrap: break-word;
          max-width: 120px;
        }

        th {
          background-color: #f0f0f0;
          font-weight: bold;
          text-align: center;
          font-size: 9px;
        }

        tr:nth-child(even) {
          background-color: #f9f9f9;
        }

        .number-cell {
          text-align: center;
          font-weight: bold;
          width: 30px;
        }
        
        .summary-section {
          display: flex;
          justify-content: space-around;
          margin-bottom: 20px;
          border: 1px solid #000;
          padding: 15px;
        }
        
        .summary-item {
          text-align: center;
        }
        
        .summary-label {
          font-size: 12px;
          margin-bottom: 5px;
        }
        
        .summary-value {
          font-size: 16px;
          font-weight: bold;
        }
        
        @media print {
          body { margin: 0; }
          .container { 
            border: 2px solid #000;
            box-shadow: none;
            margin: 0;
            padding: 10mm;
          }
          @page {
            size: A4;
            margin: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="company-info">
            <div class="company-name-ar">${d.companyName}</div>
            <div class="company-name-en">${d.companyNameEn}</div>
            <div>${d.companyAddress}</div>
          </div>
          
          <div class="logo-section">
            <div style="font-size: 14px; font-weight: bold; text-align: center;">
              LAREN<br>
              لارين
            </div>
          </div>
        </div>

        <div class="report-title">${c}</div>
        <div class="report-date">تاريخ الطباعة: ${new Date().toLocaleDateString("ar-EG")}</div>

        ${Array.isArray(a)&&a.length>0?`
          <div class="summary-section">
            <div class="summary-item">
              <div class="summary-label">عدد السجلات</div>
              <div class="summary-value">${a.length}</div>
            </div>
            ${b.includes("sales")||b.includes("purchases")?`
              <div class="summary-item">
                <div class="summary-label">إجمالي المبلغ</div>
                <div class="summary-value">${a.reduce((a,b)=>a+(b.final_amount||b.total_amount||0),0).toLocaleString()} د.ع</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">متوسط المبلغ</div>
                <div class="summary-value">${Math.round(a.reduce((a,b)=>a+(b.final_amount||b.total_amount||0),0)/a.length).toLocaleString()} د.ع</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">الفواتير المدفوعة</div>
                <div class="summary-value">${a.filter(a=>"paid"===a.payment_status).length}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">الفواتير المعلقة</div>
                <div class="summary-value">${a.filter(a=>"pending"===a.payment_status).length}</div>
              </div>
            `:""}
            ${"inventory"===b?`
              <div class="summary-item">
                <div class="summary-label">إجمالي الكمية</div>
                <div class="summary-value">${a.reduce((a,b)=>a+(b.quantity||0),0).toLocaleString()}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">الأدوية منتهية الصلاحية</div>
                <div class="summary-value">${a.filter(a=>new Date(a.expiry_date)<new Date).length}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">الأدوية قليلة الكمية</div>
                <div class="summary-value">${a.filter(a=>10>(a.quantity||0)).length}</div>
              </div>
            `:""}
          </div>

          <table>
            <thead>
              <tr>
                <th style="width: 30px;">ت</th>
                ${b.includes("sales")?`
                  <th>رقم الفاتورة</th>
                  <th>العميل</th>
                  <th>المبلغ النهائي</th>
                  <th>حالة الدفع</th>
                  <th>التاريخ</th>
                `:""}
                ${b.includes("purchases")?`
                  <th>رقم الفاتورة</th>
                  <th>المورد</th>
                  <th>المبلغ النهائي</th>
                  <th>حالة الدفع</th>
                  <th>التاريخ</th>
                `:""}
                ${"inventory"===b?`
                  <th>اسم الدواء</th>
                  <th>الفئة</th>
                  <th>الكمية</th>
                  <th>تاريخ الانتهاء</th>
                  <th>الحالة</th>
                `:""}
                ${"financial"===b?`
                  <th>نوع العملية</th>
                  <th>المبلغ</th>
                  <th>الوصف</th>
                  <th>التاريخ</th>
                `:""}
                ${"customers"===b?`
                  <th>اسم العميل</th>
                  <th>الهاتف</th>
                  <th>العنوان</th>
                  <th>إجمالي المشتريات</th>
                `:""}
                ${"suppliers"===b?`
                  <th>اسم المورد</th>
                  <th>الهاتف</th>
                  <th>العنوان</th>
                  <th>إجمالي المشتريات</th>
                `:""}
                ${!b.includes("sales")&&!b.includes("purchases")&&"inventory"!==b&&"financial"!==b&&"customers"!==b&&"suppliers"!==b?`
                  ${Object.keys(a[0]||{}).slice(0,6).map(a=>`<th>${(a=>({id:"الرقم",name:"الاسم",invoice_number:"رقم الفاتورة",customer_name:"اسم العميل",supplier_name:"اسم المورد",total_amount:"المبلغ الإجمالي",final_amount:"المبلغ النهائي",payment_status:"حالة الدفع",created_at:"التاريخ",phone:"الهاتف",address:"العنوان",category:"الفئة",quantity:"الكمية",expiry_date:"تاريخ الانتهاء",medicine_name:"اسم الدواء",batch_code:"رقم الدفعة",unit_price:"سعر الوحدة",discount:"الخصم",notes:"ملاحظات"})[a]||a)(a)}</th>`).join("")}
                `:""}
              </tr>
            </thead>
            <tbody>
              ${a.map((c,d)=>`
                <tr>
                  <td class="number-cell">${d+1}</td>
                  ${b.includes("sales")?`
                    <td>${c.invoice_number}</td>
                    <td>${c.customers?.name||c.customer_name||"عميل نقدي"}</td>
                    <td>${c.final_amount?.toLocaleString()} د.ع</td>
                    <td>${"paid"===c.payment_status?"مدفوع":"معلق"}</td>
                    <td>${new Date(c.created_at).toLocaleDateString("ar-EG")}</td>
                  `:""}
                  ${b.includes("purchases")?`
                    <td>${c.invoice_number}</td>
                    <td>${c.suppliers?.name||"غير محدد"}</td>
                    <td>${c.final_amount?.toLocaleString()} د.ع</td>
                    <td>${"paid"===c.payment_status?"مدفوع":"معلق"}</td>
                    <td>${new Date(c.created_at).toLocaleDateString("ar-EG")}</td>
                  `:""}
                  ${"inventory"===b?`
                    <td>${c.medicines?.name||c.medicine_name||"غير محدد"}</td>
                    <td>${c.medicines?.category||c.category||"غير محدد"}</td>
                    <td>${c.quantity||0}</td>
                    <td>${c.expiry_date?new Date(c.expiry_date).toLocaleDateString("ar-EG"):"غير محدد"}</td>
                    <td>${10>(c.quantity||0)?"كمية قليلة":"طبيعي"}</td>
                  `:""}
                  ${"financial"===b?`
                    <td>${"income"===c.type?"دخل":"مصروف"}</td>
                    <td>${c.amount?.toLocaleString()||0} د.ع</td>
                    <td>${c.description||"غير محدد"}</td>
                    <td>${c.created_at?new Date(c.created_at).toLocaleDateString("ar-EG"):"غير محدد"}</td>
                  `:""}
                  ${"customers"===b?`
                    <td>${c.name||"غير محدد"}</td>
                    <td>${c.phone||"غير محدد"}</td>
                    <td>${c.address||"غير محدد"}</td>
                    <td>${c.total_purchases?.toLocaleString()||0} د.ع</td>
                  `:""}
                  ${"suppliers"===b?`
                    <td>${c.name||"غير محدد"}</td>
                    <td>${c.phone||"غير محدد"}</td>
                    <td>${c.address||"غير محدد"}</td>
                    <td>${c.total_purchases?.toLocaleString()||0} د.ع</td>
                  `:""}
                  ${!b.includes("sales")&&!b.includes("purchases")&&"inventory"!==b&&"financial"!==b&&"customers"!==b&&"suppliers"!==b?`
                    ${Object.keys(a[0]||{}).slice(0,6).map(a=>`
                      <td>${((a,b)=>{if(null==a)return"غير محدد";if(b.includes("date")||b.includes("created_at"))try{return new Date(a).toLocaleDateString("ar-EG")}catch{return a.toString()}if(b.includes("amount")||b.includes("price")||b.includes("cost")){let b=parseFloat(a);return isNaN(b)?a.toString():`${b.toLocaleString()} د.ع`}return"payment_status"===b?"paid"===a?"مدفوع":"pending"===a?"معلق":a.toString():a.toString()})(c[a],a)}</td>
                    `).join("")}
                  `:""}
                </tr>
              `).join("")}
            </tbody>
          </table>
        `:`
          <div style="text-align: center; padding: 50px; border: 1px solid #000;">
            لا توجد بيانات للعرض
          </div>
        `}
      </div>
    </body>
    </html>
  `,f=(a,b)=>{let c=a.return_items||[],d="purchase_return"===a.type,e=d?a.suppliers?.name||"غير محدد":a.customers?.name||a.customer_name||"عميل نقدي";return`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>سند إرجاع - ${a.return_number}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
          font-family: 'Arial', sans-serif;
          font-size: 14px;
          color: #000;
          background-color: #fff;
          line-height: 1.4;
          direction: rtl;
        }
        .container {
          max-width: 210mm;
          margin: 0 auto;
          padding: 10mm;
          border: 2px solid #000;
          min-height: 297mm;
          position: relative;
        }

        .header {
          border-bottom: 2px solid #000;
          padding-bottom: 15px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .company-info {
          flex: 1;
          text-align: right;
        }

        .company-name-ar {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .company-name-en {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 10px;
          direction: ltr;
          text-align: left;
        }

        .logo-section {
          width: 120px;
          height: 120px;
          border: 2px solid #000;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 20px;
          background-color: #f8f9fa;
        }

        .return-title {
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          margin: 20px 0;
          padding: 10px;
          border: 2px solid #000;
          background-color: #f0f0f0;
        }

        .return-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          border: 1px solid #000;
        }

        .return-info, .customer-info {
          flex: 1;
          padding: 10px;
          border-right: 1px solid #000;
        }

        .customer-info {
          border-right: none;
        }

        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          padding: 2px 0;
        }

        .detail-label {
          font-weight: bold;
          min-width: 100px;
        }

        .detail-value {
          text-align: left;
        }

        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          border: 2px solid #000;
        }

        .items-table th,
        .items-table td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center;
          font-size: 12px;
        }

        .items-table th {
          background-color: #f0f0f0;
          font-weight: bold;
        }

        .items-table .item-name {
          text-align: right;
          padding-right: 10px;
        }

        .totals-section {
          width: 300px;
          margin-left: auto;
          border: 2px solid #000;
          margin-bottom: 20px;
        }

        .totals-section table {
          width: 100%;
          border-collapse: collapse;
        }

        .totals-section td {
          border: 1px solid #000;
          padding: 8px;
          font-size: 14px;
        }

        .totals-section .total-label {
          background-color: #f0f0f0;
          font-weight: bold;
          text-align: right;
        }

        .totals-section .total-value {
          text-align: center;
          font-weight: bold;
        }

        .footer {
          position: absolute;
          bottom: 10mm;
          left: 10mm;
          right: 10mm;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid #000;
          padding-top: 15px;
        }

        .signature-section {
          text-align: center;
          flex: 1;
        }

        .signature-box {
          width: 150px;
          height: 80px;
          border: 2px solid #000;
          border-radius: 50%;
          margin: 0 auto 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: #666;
        }

        .notes-section {
          margin-bottom: 30px;
          border: 1px solid #000;
          padding: 10px;
          min-height: 60px;
        }

        @media print {
          body { margin: 0; }
          .container {
            border: 2px solid #000;
            box-shadow: none;
            margin: 0;
            padding: 10mm;
          }
          @page {
            size: A4;
            margin: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="company-info">
            <div class="company-name-ar">${b.companyName}</div>
            <div class="company-name-en">${b.companyNameEn}</div>
            <div class="company-address">${b.companyAddress}</div>
          </div>

          <div class="logo-section">
            <div style="font-size: 16px; font-weight: bold; text-align: center; line-height: 1.2;">
              LAREN<br>
              لارين
            </div>
          </div>
        </div>

        <div class="return-title">
          سند إرجاع ${d?"مشتريات":"مبيعات"}
        </div>

        <div class="return-details">
          <div class="return-info">
            <div class="detail-row">
              <span class="detail-label">رقم سند الإرجاع:</span>
              <span class="detail-value">${a.return_number}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">التاريخ:</span>
              <span class="detail-value">${new Date(a.created_at).toLocaleDateString("ar-EG")}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">رقم الفاتورة الأصلية:</span>
              <span class="detail-value">${a.original_invoice_number||"غير محدد"}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">نوع الإرجاع:</span>
              <span class="detail-value">${d?"إرجاع للمورد":"إرجاع من العميل"}</span>
            </div>
          </div>

          <div class="customer-info">
            <div class="detail-row">
              <span class="detail-label">${d?"المورد:":"العميل:"}</span>
              <span class="detail-value">${e}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">الهاتف:</span>
              <span class="detail-value">${d?a.suppliers?.phone||"":a.customers?.phone||""}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">العنوان:</span>
              <span class="detail-value">${d?a.suppliers?.address||"":a.customers?.address||""}</span>
            </div>
          </div>
        </div>

        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 40px;">ت</th>
              <th style="width: 200px;">اسم المادة</th>
              <th style="width: 60px;">الكمية</th>
              <th style="width: 80px;">السعر</th>
              <th style="width: 100px;">المجموع</th>
              <th style="width: 100px;">سبب الإرجاع</th>
              <th style="width: 60px;">EXP</th>
            </tr>
          </thead>
          <tbody>
            ${c?.map((a,b)=>`
              <tr>
                <td>${b+1}</td>
                <td class="item-name">${a.medicine_name||a.medicines?.name||a.medicine?.name||"غير محدد"}</td>
                <td>${a.quantity}</td>
                <td>${(a.unit_price||0).toLocaleString()}</td>
                <td>${(a.total_amount||0).toLocaleString()}</td>
                <td style="font-size: 10px;">${a.return_reason||"غير محدد"}</td>
                <td>${a.expiry_date?new Date(a.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):""}</td>
              </tr>
            `).join("")||""}

            ${Array.from({length:Math.max(0,8-(c?.length||0))},(a,b)=>`
              <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
              </tr>
            `).join("")}
          </tbody>
        </table>

        <div class="totals-section">
          <table>
            <tr>
              <td class="total-label">إجمالي المبلغ المرتجع:</td>
              <td class="total-value">${a.total_amount?.toLocaleString()||0} د.ع</td>
            </tr>
          </table>
        </div>

        <div class="notes-section">
          <div style="font-weight: bold; margin-bottom: 5px;">ملاحظات:</div>
          <div>${a.notes||""}</div>
        </div>

        <div class="footer">
          <div style="font-size: 12px;">
            صفحة 1 من 1
          </div>

          <div class="signature-section">
            <div class="signature-box">
              ختم وتوقيع<br>
              المسؤول
            </div>
          </div>

          <div style="font-size: 12px; text-align: left;">
            ${b.footerText||"شكراً لتعاملكم معنا"}
          </div>
        </div>
      </div>
    </body>
    </html>
  `}},97711:(a,b,c)=>{c.r(b),c.d(b,{printInvoice:()=>g,printReport:()=>h,usePrintSettings:()=>f});var d=c(43210);let e={companyName:"مكتب لارين العلمي",companyNameEn:"LAREN SCIENTIFIC BUREAU",companyAddress:"بغداد - شارع فلسطين",companyPhone:"+*********** 4567",companyEmail:"<EMAIL>",showLogo:!0,showHeader:!0,showFooter:!0,footerText:"شكراً لتعاملكم معنا",fontSize:"medium",paperSize:"A4",showBorders:!0,showColors:!1,includeBarcode:!1,includeQRCode:!1,showWatermark:!1,headerColor:"#1f2937",accentColor:"#3b82f6",textColor:"#374151",backgroundColor:"#ffffff"};function f(){let[a,b]=(0,d.useState)(e),[c,f]=(0,d.useState)(!0),g=c=>{let d={...a,...c};b(d);try{localStorage.setItem("printSettings",JSON.stringify(d))}catch(a){console.error("Error saving print settings:",a)}};return{settings:a,printSettings:a,loading:c,updateSettings:g,resetSettings:()=>{b(e);try{localStorage.removeItem("printSettings")}catch(a){console.error("Error resetting print settings:",a)}},exportSettings:()=>{let b=new Blob([JSON.stringify(a,null,2)],{type:"application/json"}),c=URL.createObjectURL(b),d=document.createElement("a");d.href=c,d.download="print-settings.json",d.click(),URL.revokeObjectURL(c)},importSettings:a=>new Promise((b,c)=>{let d=new FileReader;d.onload=a=>{try{let c=JSON.parse(a.target?.result);g(c),b()}catch(a){c(a)}},d.onerror=()=>c(Error("Failed to read file")),d.readAsText(a)})}}let g=async(a,b,d)=>{try{console.log("\uD83D\uDDA8️ بدء طباعة الفاتورة:",a);let e=a;if(a.id&&"sales"===b){let{getSalesInvoiceForPrint:b}=c(31836),d=await b(a.id);d.success&&d.data&&(e={...d.data,customerName:a.customerName||d.data.customer_name,customerPhone:a.customerPhone||d.data.customers?.phone,customerAddress:a.customerAddress||d.data.customers?.address},console.log("✅ تم استرجاع بيانات فاتورة المبيعات من قاعدة البيانات:",e))}else if(a.id&&"purchase"===b){let{getPurchaseInvoiceForPrint:b}=c(31836),d=await b(a.id);d.success&&d.data&&(e={...d.data,supplierName:a.supplierName||d.data.suppliers?.name,supplierPhone:a.supplierPhone||d.data.suppliers?.phone,supplierAddress:a.supplierAddress||d.data.suppliers?.address},console.log("✅ تم استرجاع بيانات فاتورة المشتريات من قاعدة البيانات:",e))}else if(a.id&&"return"===b){let{getReturnForPrint:b}=c(31836),d=await b(a.id);d.success&&d.data&&(e={...d.data,customerName:a.customerName||d.data.customer_name,supplierName:a.supplierName||d.data.supplier_name},console.log("✅ تم استرجاع بيانات المرتجع من قاعدة البيانات:",e))}let f=window.open("","_blank");if(!f)return void console.error("❌ فشل في فتح نافذة الطباعة");let{generateLarenInvoiceHTML:g}=c(84622),h=g(e,b,d);f.document.write(h),f.document.close(),f.focus(),f.print(),f.close(),console.log("✅ تمت الطباعة بنجاح")}catch(h){console.error("❌ خطأ في الطباعة:",h);let e=window.open("","_blank");if(!e)return;let{generateLarenInvoiceHTML:f}=c(84622),g=f(a,b,d);e.document.write(g),e.document.close(),e.focus(),e.print(),e.close()}},h=(a,b,d,e)=>{let f=window.open("","_blank");if(!f)return;let{generateLarenReportHTML:g}=c(84622),h=g(a,b,d,e);f.document.write(h),f.document.close(),f.focus(),f.print(),f.close()}}};