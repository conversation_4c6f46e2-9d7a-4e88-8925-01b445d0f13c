(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4573],{4516:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},16785:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},19420:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},27577:(e,t,s)=>{Promise.resolve().then(s.bind(s,58441))},33109:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},48136:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},57434:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},58441:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>L});var a=s(95155),r=s(12115),l=s(61932),c=s(33109),n=s(68500),i=s(81304),d=s(84616),o=s(39785),x=s(55868),m=s(57434),h=s(47924),p=s(53904),g=s(27809),u=s(37108),y=s(92657),b=s(66932),j=s(19946);let N=(0,j.A)("banknote",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M6 12h.01M18 12h.01",key:"113zkx"}]]),f=(0,j.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var v=s(69074),w=s(17580),k=s(40646),_=s(48136),A=s(22711),S=s(53363),C=s(10988);function L(){var e,t,s;let[j,L]=(0,r.useState)([]),[M,D]=(0,r.useState)([]),[E,z]=(0,r.useState)(0),[q,I]=(0,r.useState)([]),[O,H]=(0,r.useState)([]),[G,J]=(0,r.useState)([]),[T,P]=(0,r.useState)([]),[R,V]=(0,r.useState)([]),[B,F]=(0,r.useState)(""),[W,Z]=(0,r.useState)("all"),[K,Q]=(0,r.useState)(""),[U,X]=(0,r.useState)(!1),[Y,$]=(0,r.useState)(!1),[ee,et]=(0,r.useState)("invoices"),[es,ea]=(0,r.useState)(!1),[er,el]=(0,r.useState)(null),{settings:ec}=(0,S.usePrintSettings)(),[en,ei]=(0,r.useState)({transaction_type:"expense",category:"",amount:0,description:"",payment_method:"cash",notes:""});(0,r.useEffect)(()=>{ed()},[]),(0,r.useEffect)(()=>{em()},[j,B,W,K]),(0,r.useEffect)(()=>{ex()},[G,T,B,W]);let ed=async()=>{console.log("Loading cash box data..."),$(!0);try{await (0,C.initializeSystemData)();let[t,s,a,r]=await Promise.all([(0,C.getCashTransactions)(),(0,C.getCashBalance)(),(0,C.getCustomerDebts)(),(0,C.getSupplierDebts)()]);if(console.log("Cash box data results:",{transactions:t,balance:s,customerDebts:a,supplierDebts:r}),t.success){var e;console.log("Setting transactions:",(null==(e=t.data)?void 0:e.length)||0,"items"),L(t.data||[])}s.success&&(console.log("Setting cash balance:",s.data||0),z(s.data||0)),a.success&&I(a.data||[]),r.success&&H(r.data||[]),await eo()}catch(e){console.error("Error loading cash box data:",e)}finally{$(!1)}},eo=async()=>{try{let e=JSON.parse(localStorage.getItem("sales_invoices")||"[]");J(e);let t=JSON.parse(localStorage.getItem("purchase_invoices")||"[]");P(t),console.log("Loaded ".concat(e.length," sales invoices and ").concat(t.length," purchase invoices"))}catch(e){console.error("Error loading invoices:",e)}},ex=()=>{let e=[];("all"===W||"sales"===W)&&(e=[...e,...G.map(e=>({...e,type:"sales"}))]),("all"===W||"purchases"===W)&&(e=[...e,...T.map(e=>({...e,type:"purchases"}))]),B&&(e=e.filter(e=>{let t="customer_name"in e?e.customer_name:"",s="supplier_name"in e?e.supplier_name:"";return e.invoice_number.toLowerCase().includes(B.toLowerCase())||t.toLowerCase().includes(B.toLowerCase())||s.toLowerCase().includes(B.toLowerCase())})),e.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()),V(e)},em=()=>{let e=j;B&&(e=e.filter(e=>e.description.toLowerCase().includes(B.toLowerCase())||e.category.toLowerCase().includes(B.toLowerCase()))),"all"!==W&&(e=e.filter(e=>e.transaction_type===W)),K&&(e=e.filter(e=>e.category===K)),D(e)},eh=async()=>{if(!en.category||!en.description||en.amount<=0)return void alert("يرجى ملء جميع الحقول المطلوبة");$(!0);try{console.log("Adding cash transaction:",en);let e=await (0,C.addCashTransaction)(en);console.log("Add transaction result:",e),e.success?(alert("تم إضافة المعاملة بنجاح!"),X(!1),ei({transaction_type:"expense",category:"",amount:0,description:"",payment_method:"cash",notes:""}),console.log("Reloading data after successful transaction..."),await ed()):(console.error("Failed to add transaction:",e),alert("حدث خطأ أثناء إضافة المعاملة"))}catch(e){console.error("Error adding transaction:",e),alert("حدث خطأ أثناء إضافة المعاملة")}finally{$(!1)}},ep=async(e,t,s)=>{if(confirm("هل تريد تسديد هذا الدين بمبلغ ".concat(s.toLocaleString()," د.ع؟"))){$(!0);try{(await (0,C.updatePaymentStatus)(e,t,"paid",s)).success?(alert("تم تسديد الدين بنجاح!"),await ed()):alert("حدث خطأ أثناء تسديد الدين")}catch(e){console.error("Error paying debt:",e),alert("حدث خطأ أثناء تسديد الدين")}finally{$(!1)}}},eg=async e=>{try{let t="customer_name"in e?"sales":"purchases",s=await eu(e,t);(0,S.printInvoice)(s,t,ec)}catch(s){console.error("Error printing invoice:",s);let t="customer_name"in e?"sales":"purchases";(0,S.printInvoice)(e,t,ec)}},eu=async(e,t)=>{try{let s=JSON.parse(localStorage.getItem("sales"===t?"sales_invoice_items":"purchase_invoice_items")||"[]"),a=JSON.parse(localStorage.getItem("medicines")||"[]"),r=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),l=s.filter(t=>t.invoice_id===e.id).map(e=>{let t=r.find(t=>t.id===e.medicine_batch_id),s=a.find(e=>e.id===(null==t?void 0:t.medicine_id)),l=(null==s?void 0:s.name)||e.medicine_name||e.medicineName||"غير محدد";return{...e,medicine_name:l,medicineName:l,medicine_batches:{batch_code:(null==t?void 0:t.batch_code)||"",expiry_date:(null==t?void 0:t.expiry_date)||"",medicines:{name:l,category:(null==s?void 0:s.category)||"",manufacturer:(null==s?void 0:s.manufacturer)||"",strength:(null==s?void 0:s.strength)||"",form:(null==s?void 0:s.form)||""}}}});return{...e,["sales"===t?"sales_invoice_items":"purchase_invoice_items"]:l}}catch(t){return console.error("Error enhancing invoice:",t),e}},ey=e=>"income"===e?"text-green-600":"text-red-600",eb=()=>j.filter(e=>"income"===e.transaction_type).reduce((e,t)=>e+t.amount,0),ej=()=>j.filter(e=>"expense"===e.transaction_type).reduce((e,t)=>e+t.amount,0),eN=["رواتب","إيجار","كهرباء","ماء","هاتف وإنترنت","صيانة","مواد تنظيف","قرطاسية","مواصلات","ضرائب ورسوم","تأمين","دعاية وإعلان","أخرى"],ef=["مبيعات","خدمات","استشارات","أخرى"];return(0,a.jsxs)(l.A,{children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة الصندوق"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"متابعة الواردات والمصروفات والمديونيات"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("button",{onClick:()=>{M.length>0?ea(!0):alert("لا توجد معاملات للطباعة")},disabled:0===M.length,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),"معاينة وطباعة"]}),(0,a.jsxs)("button",{onClick:()=>{M.length>0?(0,S.printReport)(M,"cashbox","تقرير الصندوق",ec):alert("لا توجد معاملات للطباعة")},disabled:0===M.length,className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),"طباعة مباشرة"]}),(0,a.jsxs)("button",{onClick:()=>X(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),"إضافة معاملة"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"رصيد الصندوق"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-blue-600",children:[E.toLocaleString()," د.ع"]})]}),(0,a.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الواردات"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[eb().toLocaleString()," د.ع"]})]}),(0,a.jsx)(c.A,{className:"h-8 w-8 text-green-600"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي المصروفات"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:[ej().toLocaleString()," د.ع"]})]}),(0,a.jsx)(n.A,{className:"h-8 w-8 text-red-600"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"صافي الربح"}),(0,a.jsxs)("p",{className:"text-2xl font-bold ".concat(eb()-ej()>=0?"text-green-600":"text-red-600"),children:[(eb()-ej()).toLocaleString()," د.ع"]})]}),(0,a.jsx)(x.A,{className:"h-8 w-8 text-gray-600"})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"flex space-x-8 px-6",children:[(0,a.jsx)("button",{onClick:()=>et("invoices"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("invoices"===ee?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"جميع الفواتير"]})}),(0,a.jsx)("button",{onClick:()=>et("transactions"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("transactions"===ee?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"المعاملات المالية"}),(0,a.jsx)("button",{onClick:()=>et("debts"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("debts"===ee?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"المديونيات"})]})})}),"invoices"===ee&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البحث"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",value:B,onChange:e=>F(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"البحث في الفواتير..."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"نوع الفاتورة"}),(0,a.jsxs)("select",{value:W,onChange:e=>Z(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"جميع الفواتير"}),(0,a.jsx)("option",{value:"sales",children:"فواتير المبيعات"}),(0,a.jsx)("option",{value:"purchases",children:"فواتير المشتريات"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)("button",{onClick:()=>eo(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"تحديث"]})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"فواتير المبيعات"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:G.length})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-green-600"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"فواتير المشتريات"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:T.length})]}),(0,a.jsx)(u.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الفواتير"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:R.length})]}),(0,a.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"قائمة الفواتير"})}),(0,a.jsxs)("div",{className:"overflow-x-auto",children:[(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم الفاتورة"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النوع"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"العميل/المورد"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ النهائي"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"حالة الدفع"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:R.map(e=>{var t;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.invoice_number}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("sales"===e.type?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:"sales"===e.type?"مبيعات":"مشتريات"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.customer_name||e.supplier_name}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[null==(t=e.final_amount)?void 0:t.toLocaleString()," د.ع"]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("paid"===e.payment_status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"paid"===e.payment_status?"مدفوع":"معلق"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("ar-EG")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>{el(e),ea(!0)},className:"text-blue-600 hover:text-blue-900",title:"عرض التفاصيل",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>eg(e),className:"text-green-600 hover:text-green-900",title:"طباعة",children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})]})})]},e.id)})})]}),0===R.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"لا توجد فواتير"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"لم يتم العثور على فواتير تطابق معايير البحث."})]})]})]})]}),"transactions"===ee&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البحث"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",value:B,onChange:e=>F(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"البحث في المعاملات..."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"نوع المعاملة"}),(0,a.jsxs)("select",{value:W,onChange:e=>Z(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"جميع المعاملات"}),(0,a.jsx)("option",{value:"income",children:"واردات"}),(0,a.jsx)("option",{value:"expense",children:"مصروفات"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الفئة"}),(0,a.jsxs)("select",{value:K,onChange:e=>Q(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"جميع الفئات"}),[...new Set([...eN,...ef])].map((e,t)=>(0,a.jsx)("option",{value:e,children:e},"filter_category_".concat(t,"_").concat(e)))]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)("button",{onClick:()=>{F(""),Z("all"),Q("")},className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"إعادة تعيين"]})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===M.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد معاملات"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"لم يتم تسجيل أي معاملات مالية بعد"}),(0,a.jsx)("button",{onClick:()=>X(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"إضافة أول معاملة"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النوع"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الفئة"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الوصف"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"طريقة الدفع"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:M.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:["income"===e.transaction_type?(0,a.jsx)(c.A,{className:"h-4 w-4 text-green-500"}):(0,a.jsx)(n.A,{className:"h-4 w-4 text-red-500"}),(0,a.jsx)("span",{className:"mr-2 text-sm font-medium ".concat(ey(e.transaction_type)),children:"income"===e.transaction_type?"وارد":"مصروف"})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"text-sm text-gray-900",children:e.category})}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("span",{className:"text-sm text-gray-900",children:e.description}),e.notes&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.notes})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,a.jsxs)("span",{className:"text-sm font-medium ".concat(ey(e.transaction_type)),children:["income"===e.transaction_type?"+":"-",e.amount.toLocaleString()," د.ع"]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:["cash"===e.payment_method?(0,a.jsx)(N,{className:"h-4 w-4 text-green-500 mr-1"}):(0,a.jsx)(f,{className:"h-4 w-4 text-blue-500 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:"cash"===e.payment_method?"نقداً":"بنكي"})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:new Date(e.created_at).toLocaleDateString("ar-EG")})]})})]},e.id))})]})})})]}),"debts"===ee&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"ديون العملاء"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["إجمالي: ",q.reduce((e,t)=>e+t.final_amount,0).toLocaleString()," د.ع"]})]})]}),0===q.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(k.A,{className:"h-12 w-12 text-green-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لا توجد ديون على العملاء"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"رقم الفاتورة"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"العميل"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المبلغ"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"التاريخ"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"إجراءات"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200",children:q.map(e=>{var t;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:e.invoice_number}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:(null==(t=e.customers)?void 0:t.name)||e.customer_name}),(0,a.jsxs)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium text-red-600",children:[e.final_amount.toLocaleString()," د.ع"]}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("ar-EG")}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,a.jsx)("button",{onClick:()=>ep("sales",e.id,e.final_amount),className:"bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700",children:"تسديد"})})]},e.id)})})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"ديون الموردين"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"h-5 w-5 text-orange-600"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["إجمالي: ",O.reduce((e,t)=>e+t.final_amount,0).toLocaleString()," د.ع"]})]})]}),0===O.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(k.A,{className:"h-12 w-12 text-green-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لا توجد ديون للموردين"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"رقم الفاتورة"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المورد"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المبلغ"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"التاريخ"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"إجراءات"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200",children:O.map(e=>{var t;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:e.invoice_number}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:null==(t=e.suppliers)?void 0:t.name}),(0,a.jsxs)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium text-orange-600",children:[e.final_amount.toLocaleString()," د.ع"]}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("ar-EG")}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,a.jsx)("button",{onClick:()=>ep("purchase",e.id,e.final_amount),className:"bg-orange-600 text-white px-3 py-1 rounded text-xs hover:bg-orange-700",children:"دفع"})})]},e.id)})})]})})]})]}),U&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"إضافة معاملة جديدة"}),(0,a.jsx)("button",{onClick:()=>X(!1),className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"نوع المعاملة"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("button",{onClick:()=>ei({...en,transaction_type:"income"}),className:"p-3 border-2 rounded-lg text-center transition-colors ".concat("income"===en.transaction_type?"border-green-500 bg-green-50 text-green-700":"border-gray-300 text-gray-600 hover:border-green-300"),children:[(0,a.jsx)(c.A,{className:"h-5 w-5 mx-auto mb-1"}),(0,a.jsx)("div",{className:"text-sm font-medium",children:"وارد"})]}),(0,a.jsxs)("button",{onClick:()=>ei({...en,transaction_type:"expense"}),className:"p-3 border-2 rounded-lg text-center transition-colors ".concat("expense"===en.transaction_type?"border-red-500 bg-red-50 text-red-700":"border-gray-300 text-gray-600 hover:border-red-300"),children:[(0,a.jsx)(n.A,{className:"h-5 w-5 mx-auto mb-1"}),(0,a.jsx)("div",{className:"text-sm font-medium",children:"مصروف"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الفئة *"}),(0,a.jsxs)("select",{value:en.category,onChange:e=>ei({...en,category:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"اختر الفئة"}),("expense"===en.transaction_type?eN:ef).map((e,t)=>(0,a.jsx)("option",{value:e,children:e},"new_transaction_category_".concat(t,"_").concat(e)))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"المبلغ *"}),(0,a.jsx)("input",{type:"number",value:en.amount,onChange:e=>ei({...en,amount:Number(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الوصف *"}),(0,a.jsx)("input",{type:"text",value:en.description,onChange:e=>ei({...en,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"وصف المعاملة"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"طريقة الدفع"}),(0,a.jsxs)("select",{value:en.payment_method,onChange:e=>ei({...en,payment_method:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"cash",children:"نقداً"}),(0,a.jsx)("option",{value:"bank",children:"بنكي"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات"}),(0,a.jsx)("textarea",{value:en.notes,onChange:e=>ei({...en,notes:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ملاحظات إضافية..."})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>X(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"إلغاء"}),(0,a.jsx)("button",{onClick:eh,disabled:Y||!en.category||!en.description||en.amount<=0,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed",children:Y?"جاري الإضافة...":"إضافة المعاملة"})]})]})})]}),es&&!er&&M.length>0&&(0,a.jsx)(A.Ay,{title:"تقرير الصندوق",data:M,type:"report",settings:ec,onClose:()=>ea(!1),children:(0,a.jsx)(A.W1,{reportData:M,reportType:"cashbox",title:"تقرير الصندوق",settings:ec})}),es&&er&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["معاينة الفاتورة - ",er.invoice_number]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("button",{onClick:()=>eg(er),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),"طباعة"]}),(0,a.jsx)("button",{onClick:()=>{ea(!1),el(null)},className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]})]}),(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-6 bg-gray-50",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"صيدلية الشفاء"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["فاتورة ","customer_name"in er?"مبيعات":"مشتريات"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"معلومات الفاتورة"}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"رقم الفاتورة:"})," ",er.invoice_number]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"التاريخ:"})," ",new Date(er.created_at).toLocaleDateString("ar-EG")]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"طريقة الدفع:"})," ","cash"===er.payment_method?"نقداً":"آجل"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"حالة الدفع:"})," ","paid"===er.payment_status?"مدفوع":"معلق"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"customer_name"in er?"معلومات العميل":"معلومات المورد"}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"الاسم:"})," ","customer_name"in er?er.customer_name:er.supplier_name]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"تفاصيل الفاتورة"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border border-gray-300",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"الدواء"}),(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"الكمية"}),(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"السعر"}),(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"المجموع"})]})}),(0,a.jsx)("tbody",{children:(0,a.jsx)("tr",{children:(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-2",colSpan:4,children:(0,a.jsx)("div",{className:"text-center text-gray-500 py-4",children:"لا توجد تفاصيل متاحة للعرض"})})})})]})})]}),(0,a.jsx)("div",{className:"border-t pt-4",children:(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("div",{className:"w-64",children:[(0,a.jsxs)("div",{className:"flex justify-between py-2",children:[(0,a.jsx)("span",{children:"المجموع الفرعي:"}),(0,a.jsxs)("span",{children:[null==(e=er.total_amount)?void 0:e.toLocaleString()," د.ع"]})]}),(0,a.jsxs)("div",{className:"flex justify-between py-2",children:[(0,a.jsx)("span",{children:"الخصم:"}),(0,a.jsxs)("span",{children:[null==(t=er.discount_amount)?void 0:t.toLocaleString()," د.ع"]})]}),(0,a.jsxs)("div",{className:"flex justify-between py-2 border-t font-bold",children:[(0,a.jsx)("span",{children:"المجموع النهائي:"}),(0,a.jsxs)("span",{children:[null==(s=er.final_amount)?void 0:s.toLocaleString()," د.ع"]})]})]})})}),er.notes&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"ملاحظات:"}),(0,a.jsx)("p",{className:"text-gray-700",children:er.notes})]})]})})]})})]})}},66932:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68500:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94498:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]])}},e=>{e.O(0,[705,6874,6543,5647,8080,1932,988,3363,2711,8441,5964,7358],()=>e(e.s=27577)),_N_E=e.O()}]);