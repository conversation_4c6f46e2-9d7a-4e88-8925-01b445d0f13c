'use client'

import React from 'react'
import {
  Printer, Download, X, FileText, Calendar, User, Phone, MapPin, Hash, CreditCard,
  CheckCircle, Clock, AlertCircle, TrendingUp, ShoppingCart, Target, Layers,
  BarChart3
} from 'lucide-react'
import '../styles/classic-print.css'

// Classic Print Styles - Similar to traditional invoice format
const printStyles = `
  @media print {
    body {
      margin: 0;
      padding: 0;
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      line-height: 1.2;
      color: #000;
      background: white;
    }

    .print-content {
      max-width: none;
      margin: 0;
      padding: 10mm;
      box-shadow: none;
      border: none;
      background: white;
    }

    .no-print { display: none !important; }

    /* Classic table styles */
    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 5px;
      border: 2px solid #000;
    }

    th, td {
      border: 1px solid #000;
      padding: 4px 6px;
      text-align: center;
      vertical-align: middle;
      font-size: 11px;
      height: 25px;
    }

    th {
      background-color: #f0f0f0 !important;
      font-weight: bold;
      text-align: center;
    }

    /* Header border styling */
    .border-2 {
      border: 2px solid #000 !important;
    }

    .border {
      border: 1px solid #000 !important;
    }

    .border-black {
      border-color: #000 !important;
    }

    .border-r-2 {
      border-right: 2px solid #000 !important;
    }

    .border-b-2 {
      border-bottom: 2px solid #000 !important;
    }

    .border-t-2 {
      border-top: 2px solid #000 !important;
    }

    /* Grid layout for print */
    .grid {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    .grid-cols-3 > div {
      display: table-cell;
      width: 33.333%;
      vertical-align: top;
    }

    .grid-cols-4 > div {
      display: table-cell;
      width: 25%;
      vertical-align: top;
    }

    .grid-cols-2 > div {
      display: table-cell;
      width: 50%;
      vertical-align: top;
    }

    /* Text alignment */
    .text-center { text-align: center !important; }
    .text-right { text-align: right !important; }
    .text-left { text-align: left !important; }

    /* Font weights and sizes */
    .font-bold { font-weight: bold !important; }
    .text-xl { font-size: 18px !important; }
    .text-lg { font-size: 16px !important; }
    .text-md { font-size: 14px !important; }
    .text-sm { font-size: 12px !important; }
    .text-xs { font-size: 10px !important; }

    /* Hide modern styling elements */
    .rounded, .rounded-lg, .rounded-full, .shadow, .shadow-sm {
      border-radius: 0 !important;
      box-shadow: none !important;
    }

    /* Color adjustments for print */
    .text-blue-600, .bg-blue-100 { color: #000 !important; background: transparent !important; }
    .text-green-600, .bg-green-100 { color: #000 !important; background: transparent !important; }
    .text-red-600, .bg-red-100 { color: #000 !important; background: transparent !important; }
    .text-yellow-600, .bg-yellow-100 { color: #000 !important; background: transparent !important; }
    .text-gray-600 { color: #333 !important; }

    /* Background colors */
    .bg-gray-100 { background-color: #f5f5f5 !important; }

    /* Hide interactive elements */
    button, .btn, .no-print, .print-button {
      display: none !important;
    }

    /* Signature circle */
    .rounded-full {
      border: 2px solid #000 !important;
      border-radius: 50% !important;
    }

    /* Font family for numbers */
    .font-mono { font-family: 'Courier New', monospace !important; }
  }
`

interface PrintSettings {
  companyName: string
  companyAddress: string
  companyPhone: string
  companyEmail: string
  logo?: string
  showLogo: boolean
  showHeader: boolean
  showFooter: boolean
  footerText: string
  fontSize: 'small' | 'medium' | 'large'
  paperSize: 'A4' | 'A5' | 'thermal'
  showBorders: boolean
  showColors: boolean
  includeBarcode: boolean
  includeQRCode: boolean
  watermark?: string
  showWatermark: boolean
  headerColor: string
  accentColor: string
  textColor: string
  backgroundColor: string
}

interface PrintTemplateProps {
  children: React.ReactNode
  title: string
  settings?: Partial<PrintSettings>
  onClose?: () => void
  onPrint?: () => void
  onDownload?: () => void
}

const defaultSettings: PrintSettings = {
  companyName: 'صيدلية الشفاء',
  companyAddress: 'بغداد - العراق',
  companyPhone: '+*********** 4567',
  companyEmail: '<EMAIL>',
  showLogo: true,
  showHeader: true,
  showFooter: true,
  footerText: 'شكراً لتعاملكم معنا',
  fontSize: 'medium',
  paperSize: 'A4',
  showBorders: true,
  showColors: true,
  includeBarcode: false,
  includeQRCode: false,
  showWatermark: false,
  headerColor: '#1f2937',
  accentColor: '#3b82f6',
  textColor: '#374151',
  backgroundColor: '#ffffff'
}

export default function PrintTemplate({ 
  children, 
  title, 
  settings, 
  onClose, 
  onPrint, 
  onDownload 
}: PrintTemplateProps) {
  const printSettings = { ...defaultSettings, ...settings }

  const handlePrint = () => {
    window.print()
    onPrint?.()
  }

  const handleDownload = () => {
    // تنفيذ تحميل PDF
    onDownload?.()
  }

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: printStyles }} />
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* Header Controls */}
          <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <h2 className="text-lg font-semibold text-gray-900">معاينة الطباعة</h2>
          <div className="flex items-center gap-2">
            <button
              onClick={handlePrint}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              طباعة
            </button>
            <button
              onClick={handleDownload}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              تحميل
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              إغلاق
            </button>
          </div>
          </div>

          {/* Print Content */}
          <div className="overflow-auto max-h-[calc(90vh-80px)]">
            <div
            className={`print-content bg-white p-8 ${
              printSettings.fontSize === 'small' ? 'text-sm' : 
              printSettings.fontSize === 'large' ? 'text-lg' : 'text-base'
            }`}
            style={{ 
              color: printSettings.textColor,
              backgroundColor: printSettings.backgroundColor 
            }}
          >
            {/* Enhanced Header */}
            {printSettings.showHeader && (
              <div className="mb-8 relative">
                {/* Watermark */}
                {printSettings.showWatermark && printSettings.watermark && (
                  <div className="absolute inset-0 flex items-center justify-center opacity-5 pointer-events-none">
                    <div className="text-6xl font-bold transform rotate-45 text-gray-400">
                      {printSettings.watermark}
                    </div>
                  </div>
                )}
                
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center">
                  {printSettings.showLogo && (
                    <div className="mb-4">
                      <div 
                        className="w-24 h-24 rounded-full mx-auto flex items-center justify-center text-white text-3xl font-bold shadow-lg"
                        style={{ backgroundColor: printSettings.accentColor }}
                      >
                        {printSettings.companyName.charAt(0)}
                      </div>
                    </div>
                  )}
                  
                  <h1 
                    className="text-3xl font-bold mb-3"
                    style={{ color: printSettings.headerColor }}
                  >
                    {printSettings.companyName}
                  </h1>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 text-sm">
                    <div className="flex items-center justify-center gap-2 text-gray-600">
                      <MapPin className="h-4 w-4" style={{ color: printSettings.accentColor }} />
                      <span>{printSettings.companyAddress}</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-gray-600">
                      <Phone className="h-4 w-4" style={{ color: printSettings.accentColor }} />
                      <span>{printSettings.companyPhone}</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-gray-600">
                      <FileText className="h-4 w-4" style={{ color: printSettings.accentColor }} />
                      <span>{printSettings.companyEmail}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Document Title */}
            <div className="text-center mb-8">
              <div className="bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 shadow-sm">
                <div className="flex items-center justify-center gap-3 mb-3">
                  <FileText className="h-6 w-6" style={{ color: printSettings.accentColor }} />
                  <h2 
                    className="text-2xl font-bold"
                    style={{ color: printSettings.headerColor }}
                  >
                    {title}
                  </h2>
                </div>
                
                <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" style={{ color: printSettings.accentColor }} />
                    <span>تاريخ الطباعة: {new Date().toLocaleDateString('ar-EG')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" style={{ color: printSettings.accentColor }} />
                    <span>الوقت: {new Date().toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })}</span>
                  </div>
                </div>
                
                {/* Document ID */}
                <div className="mt-3 text-xs text-gray-500">
                  رقم المستند: DOC-{Date.now().toString().slice(-8)}
                </div>
              </div>
            </div>

            {/* Content */}
            {children}

            {/* Enhanced Footer */}
            {printSettings.showFooter && (
              <div className="mt-8 border-t-2 border-gray-200 pt-6">
                <div className="bg-gray-50 rounded-lg p-6 text-center">
                  <div className="mb-4">
                    <p className="text-lg font-semibold text-gray-800 mb-2">
                      {printSettings.footerText}
                    </p>
                    <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>مستند معتمد ومطبوع إلكترونياً</span>
                    </div>
                  </div>
                  
                  <div className="border-t border-gray-200 pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-500">
                      <div>
                        <strong>نظام إدارة الصيدلية</strong><br />
                        الإصدار 2.0 - 2024
                      </div>
                      <div>
                        <strong>تاريخ الإنشاء:</strong><br />
                        {new Date().toLocaleDateString('ar-EG')} - {new Date().toLocaleTimeString('ar-EG')}
                      </div>
                      <div>
                        <strong>حالة المستند:</strong><br />
                        <span className="text-green-600 font-semibold">✓ صالح ومعتمد</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* QR Code placeholder */}
                  {printSettings.includeQRCode && (
                    <div className="mt-4 flex justify-center">
                      <div className="w-16 h-16 bg-gray-200 border-2 border-dashed border-gray-400 rounded flex items-center justify-center text-xs text-gray-500">
                        QR Code
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          </div>
        </div>
      </div>
    </>
  )
}

// Classic Invoice Print Component - Similar to the provided image
export function InvoicePrint({ invoice, type = 'sales', settings }: {
  invoice: any,
  type: 'sales' | 'purchase',
  settings?: Partial<PrintSettings>
}) {
  const printSettings = { ...defaultSettings, ...settings }

  return (
    <div className="print-content bg-white" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Classic Header - Similar to LAREN SCIENTIFIC BUREAU */}
      <div className="invoice-header">
        <div className="header-row border-b-2">
          {/* Left: Company Name in English */}
          <div className="header-cell english-text">
            <h1 className="text-xl font-bold mb-2" style={{ letterSpacing: '2px' }}>
              {printSettings.companyName.toUpperCase()}
            </h1>
            <h2 className="text-lg font-bold">
              PHARMACY
            </h2>
            <h3 className="text-md font-bold">
              SYSTEM
            </h3>
          </div>

          {/* Center: Logo */}
          <div className="header-cell">
            <div className="logo-container">
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {printSettings.companyName.charAt(0)}
                </div>
                <div className="text-xs font-bold">
                  LOGO
                </div>
              </div>
            </div>
          </div>

          {/* Right: Company Name in Arabic */}
          <div className="header-cell arabic-text">
            <h1 className="text-xl font-bold mb-2">
              {printSettings.companyName}
            </h1>
            <p className="text-sm">
              {printSettings.companyAddress}
            </p>
            <p className="text-sm">
              {printSettings.companyPhone}
            </p>
          </div>
        </div>

        {/* Invoice Details Row */}
        <div className="header-row text-sm">
          {/* Customer Name */}
          <div className="header-cell arabic-text">
            <div className="font-bold">اسم الزبون رقم المريض</div>
            <div className="mt-1">
              {type === 'sales'
                ? (invoice.customers?.name || invoice.customer_name || 'عميل نقدي')
                : (invoice.suppliers?.name || 'غير محدد')
              }
            </div>
          </div>

          {/* Date */}
          <div className="header-cell">
            <div className="font-bold">التاريخ: بين</div>
            <div className="mt-1">
              {new Date(invoice.created_at).toLocaleDateString('ar-EG')}
            </div>
          </div>

          {/* Serial */}
          <div className="header-cell">
            <div className="font-bold">فاتورة رقم:</div>
            <div className="mt-1 font-mono">
              {invoice.invoice_number}
            </div>
          </div>

          {/* Type */}
          <div className="header-cell">
            <div className="font-bold">رقم الفاتورة:</div>
            <div className="mt-1">
              {type === 'sales' ? 'مبيعات' : 'مشتريات'}
            </div>
          </div>
        </div>

        {/* Additional Info Row */}
        <div className="header-row text-sm border-t">
          <div className="header-cell arabic-text" style={{ width: '50%' }}>
            <span className="font-bold">المنطقة: الجمهورية</span>
          </div>
          <div className="header-cell arabic-text" style={{ width: '50%' }}>
            <span className="font-bold">المندوب الخاص: {printSettings.companyName}</span>
          </div>
        </div>
      </div>

      {/* Classic Invoice Table */}
      <table className="invoice-table">
        <thead>
          <tr>
            <th className="col-serial">م</th>
            <th className="col-medicine">اسم الدواء</th>
            <th className="col-quantity">الكمية</th>
            <th className="col-price">سعر الوحدة</th>
            <th className="col-total">الإجمالي</th>
            <th className="col-batch">B.N</th>
            <th className="col-expiry">EXP</th>
          </tr>
        </thead>
        <tbody>
          {(type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items)?.map((item: any, index: number) => (
            <tr key={`item_${index}_${item.id || index}`}>
              <td className="number-cell">
                {index + 1}
              </td>
              <td className="medicine-cell arabic-text">
                <div className="font-bold">
                  {type === 'sales'
                    ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || 'غير محدد')
                    : (item.medicine_name || item.medicineName || item.medicines?.name || 'غير محدد')
                  }
                </div>
                {type === 'sales' && item.medicine_batches?.medicines?.category && (
                  <div className="text-xs">
                    {item.medicine_batches.medicines.category}
                  </div>
                )}
              </td>
              <td className="number-cell">
                {item.quantity}
              </td>
              <td className="number-cell">
                {(item.unit_price || item.unit_cost || 0).toLocaleString()}
              </td>
              <td className="number-cell">
                {(item.total_price || item.total_cost || 0).toLocaleString()}
              </td>
              <td className="number-cell text-xs">
                {type === 'sales' && item.medicine_batches?.batch_number
                  ? item.medicine_batches.batch_number.slice(-6)
                  : '---'
                }
              </td>
              <td className="number-cell text-xs">
                {type === 'sales' && item.medicine_batches?.expiry_date
                  ? new Date(item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\//g, '/')
                  : '---'
                }
              </td>
            </tr>
          ))}

          {/* Empty rows to fill space */}
          {Array.from({ length: Math.max(0, 8 - ((type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items)?.length || 0)) }).map((_, index) => (
            <tr key={`empty_${index}`}>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          ))}

          {/* Total Row */}
          <tr className="bg-gray-100">
            <td className="text-center font-bold arabic-text" colSpan={4}>
              المجموع الكلي
            </td>
            <td className="number-cell">
              {invoice.final_amount?.toLocaleString() || 0}
            </td>
            <td colSpan={2}>
              &nbsp;
            </td>
          </tr>
        </tbody>
      </table>

      {/* Notes Section */}
      <div className="notes-section">
        <div className="font-bold mb-2 text-sm">ملاحظات: تاريخ صرف سنة البداية</div>
        <div className="min-h-16 text-sm arabic-text">
          {invoice.notes || ''}
        </div>
      </div>

      {/* Signature and Footer Section */}
      <div className="signature-area">
        <div className="grid grid-cols-2">
          {/* Left: Signature */}
          <div className="text-center">
            <div className="signature-circle">
              <div>
                <div>ختم وتوقيع</div>
                <div>الصيدلي</div>
              </div>
            </div>
          </div>

          {/* Right: Payment Info */}
          <div className="text-sm arabic-text">
            <div className="mb-2">
              <span className="font-bold">طريقة الدفع: </span>
              <span className="font-bold">
                {invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}
              </span>
            </div>

            <div className="mb-2">
              <span className="font-bold">حالة الدفع: </span>
              <span className="font-bold">
                {invoice.payment_status === 'paid' ? 'مدفوع بالكامل' :
                 invoice.payment_status === 'partial' ? 'مدفوع جزئياً' : 'معلق'}
              </span>
            </div>

            {invoice.payment_status !== 'paid' && (
              <div className="font-bold">
                المبلغ المستحق: {(invoice.final_amount - (invoice.paid_amount || 0)).toLocaleString()}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="print-footer">
        <div className="grid grid-cols-2">
          <div>صفحة 1 من 1</div>
          <div>نظام إدارة الصيدلية - تم الطباعة في {new Date().toLocaleString('ar-EG')}</div>
        </div>
      </div>
    </div>
  )
}




// Enhanced Report Print Component
export function ReportPrint({ reportData, reportType, title, settings }: {
  reportData: any,
  reportType: string,
  title: string,
  settings?: Partial<PrintSettings>
}) {
  const printSettings = { ...defaultSettings, ...settings }

  return (
    <div className="print-content">
      {/* Enhanced Report Header */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center gap-4 mb-4">
            <div
              className="w-16 h-16 rounded-lg flex items-center justify-center text-white"
              style={{ backgroundColor: printSettings.accentColor }}
            >
              <FileText className="h-8 w-8" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">{title}</h3>
              <p className="text-gray-600">تقرير مفصل ومحدث</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" style={{ color: printSettings.accentColor }} />
              <span className="font-semibold">تاريخ التقرير:</span>
              <span>{new Date().toLocaleDateString('ar-EG')}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" style={{ color: printSettings.accentColor }} />
              <span className="font-semibold">وقت الإنشاء:</span>
              <span>{new Date().toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })}</span>
            </div>
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4" style={{ color: printSettings.accentColor }} />
              <span className="font-semibold">رقم التقرير:</span>
              <span className="font-mono">RPT-{Date.now().toString().slice(-6)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Report Summary */}
      {Array.isArray(reportData) && reportData.length > 0 && (
        <div className="mb-8">
          <h4 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
            <BarChart3 className="h-5 w-5" style={{ color: printSettings.accentColor }} />
            الملخص الإحصائي
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Total Records */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 text-center shadow-sm">
              <div className="flex items-center justify-center mb-2">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <FileText className="h-5 w-5 text-white" />
                </div>
              </div>
              <p className="text-blue-600 text-sm font-medium mb-1">عدد السجلات</p>
              <p className="text-2xl font-bold text-blue-800">{reportData.length}</p>
            </div>

            {/* Sales Total */}
            {reportType.includes('sales') && (
              <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 text-center shadow-sm">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-white" />
                  </div>
                </div>
                <p className="text-green-600 text-sm font-medium mb-1">إجمالي المبيعات</p>
                <p className="text-xl font-bold text-green-800 font-mono">
                  {reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0).toLocaleString()} د.ع
                </p>
              </div>
            )}

            {/* Purchases Total */}
            {reportType.includes('purchases') && (
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4 text-center shadow-sm">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                    <ShoppingCart className="h-5 w-5 text-white" />
                  </div>
                </div>
                <p className="text-orange-600 text-sm font-medium mb-1">إجمالي المشتريات</p>
                <p className="text-xl font-bold text-orange-800 font-mono">
                  {reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0).toLocaleString()} د.ع
                </p>
              </div>
            )}

            {/* Average Value */}
            {(reportType.includes('sales') || reportType.includes('purchases')) && (
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 text-center shadow-sm">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                    <Target className="h-5 w-5 text-white" />
                  </div>
                </div>
                <p className="text-purple-600 text-sm font-medium mb-1">متوسط القيمة</p>
                <p className="text-xl font-bold text-purple-800 font-mono">
                  {Math.round(reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0) / reportData.length).toLocaleString()} د.ع
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Enhanced Report Table */}
      {Array.isArray(reportData) && reportData.length > 0 && (
        <div className="mb-6">
          <h4 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
            <Layers className="h-5 w-5" style={{ color: printSettings.accentColor }} />
            البيانات التفصيلية
          </h4>

          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr
                    className="text-white font-semibold"
                    style={{ backgroundColor: printSettings.accentColor }}
                  >
                    <th className="px-3 py-3 text-center border-r border-white border-opacity-20">
                      #
                    </th>
                    {reportType.includes('sales') && (
                      <>
                        <th className="px-3 py-3 text-right border-r border-white border-opacity-20">رقم الفاتورة</th>
                        <th className="px-3 py-3 text-right border-r border-white border-opacity-20">العميل</th>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">المبلغ النهائي</th>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">حالة الدفع</th>
                        <th className="px-3 py-3 text-center">التاريخ</th>
                      </>
                    )}
                    {reportType.includes('purchases') && (
                      <>
                        <th className="px-3 py-3 text-right border-r border-white border-opacity-20">رقم الفاتورة</th>
                        <th className="px-3 py-3 text-right border-r border-white border-opacity-20">المورد</th>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">المبلغ النهائي</th>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">حالة الدفع</th>
                        <th className="px-3 py-3 text-center">التاريخ</th>
                      </>
                    )}
                    {reportType === 'inventory' && (
                      <>
                        <th className="px-3 py-3 text-right border-r border-white border-opacity-20">اسم الدواء</th>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">الفئة</th>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">الكمية</th>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">تاريخ الانتهاء</th>
                        <th className="px-3 py-3 text-center">الحالة</th>
                      </>
                    )}
                    {reportType === 'cashbox' && (
                      <>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">النوع</th>
                        <th className="px-3 py-3 text-right border-r border-white border-opacity-20">الفئة</th>
                        <th className="px-3 py-3 text-right border-r border-white border-opacity-20">الوصف</th>
                        <th className="px-3 py-3 text-center border-r border-white border-opacity-20">المبلغ</th>
                        <th className="px-3 py-3 text-center">التاريخ</th>
                      </>
                    )}
                  </tr>
                </thead>
                <tbody>
                  {reportData.slice(0, 100).map((item: any, index: number) => (
                    <tr key={`report_item_${index}_${item.id || index}`} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="px-3 py-2 text-center font-mono text-sm text-gray-500 border-r border-gray-200">
                        {index + 1}
                      </td>

                      {reportType.includes('sales') && (
                        <>
                          <td className="px-3 py-2 border-r border-gray-200">
                            <span className="font-mono text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              {item.invoice_number}
                            </span>
                          </td>
                          <td className="px-3 py-2 border-r border-gray-200">
                            <div>
                              <p className="font-semibold text-gray-900">
                                {item.customers?.name || item.customer_name || 'عميل نقدي'}
                              </p>
                              {item.customers?.phone && (
                                <p className="text-xs text-gray-500">{item.customers.phone}</p>
                              )}
                            </div>
                          </td>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className="font-mono font-bold text-green-600">
                              {item.final_amount?.toLocaleString()} د.ع
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                              item.payment_status === 'paid'
                                ? 'bg-green-100 text-green-800'
                                : item.payment_status === 'partial'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {item.payment_status === 'paid' ? 'مدفوع' :
                               item.payment_status === 'partial' ? 'جزئي' : 'معلق'}
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center">
                            <span className="text-sm text-gray-600">
                              {new Date(item.created_at).toLocaleDateString('ar-EG')}
                            </span>
                          </td>
                        </>
                      )}

                      {reportType.includes('purchases') && (
                        <>
                          <td className="px-3 py-2 border-r border-gray-200">
                            <span className="font-mono text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded">
                              {item.invoice_number}
                            </span>
                          </td>
                          <td className="px-3 py-2 border-r border-gray-200">
                            <div>
                              <p className="font-semibold text-gray-900">
                                {item.suppliers?.name || 'غير محدد'}
                              </p>
                              {item.suppliers?.phone && (
                                <p className="text-xs text-gray-500">{item.suppliers.phone}</p>
                              )}
                            </div>
                          </td>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className="font-mono font-bold text-orange-600">
                              {item.final_amount?.toLocaleString()} د.ع
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                              item.payment_status === 'paid'
                                ? 'bg-green-100 text-green-800'
                                : item.payment_status === 'partial'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {item.payment_status === 'paid' ? 'مدفوع' :
                               item.payment_status === 'partial' ? 'جزئي' : 'معلق'}
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center">
                            <span className="text-sm text-gray-600">
                              {new Date(item.created_at).toLocaleDateString('ar-EG')}
                            </span>
                          </td>
                        </>
                      )}

                      {reportType === 'inventory' && (
                        <>
                          <td className="px-3 py-2 border-r border-gray-200">
                            <div>
                              <p className="font-semibold text-gray-900">
                                {item.medicines?.name || 'غير محدد'}
                              </p>
                              {item.medicine_batches?.batch_number && (
                                <p className="text-xs text-gray-500">
                                  دفعة: {item.medicine_batches.batch_number}
                                </p>
                              )}
                            </div>
                          </td>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                              {item.medicines?.category || 'غير محدد'}
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className={`font-bold px-2 py-1 rounded ${
                              item.quantity < 10
                                ? 'bg-red-100 text-red-800'
                                : item.quantity < 50
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {item.quantity}
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className="text-sm text-gray-600">
                              {new Date(item.expiry_date).toLocaleDateString('ar-EG')}
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center">
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                              item.quantity < 10
                                ? 'bg-red-100 text-red-800'
                                : item.quantity < 50
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {item.quantity < 10 ? 'كمية قليلة' :
                               item.quantity < 50 ? 'كمية متوسطة' : 'كمية جيدة'}
                            </span>
                          </td>
                        </>
                      )}

                      {reportType === 'cashbox' && (
                        <>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                              item.transaction_type === 'income'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {item.transaction_type === 'income' ? 'وارد' : 'مصروف'}
                            </span>
                          </td>
                          <td className="px-3 py-2 border-r border-gray-200">
                            <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">
                              {item.category}
                            </span>
                          </td>
                          <td className="px-3 py-2 border-r border-gray-200">
                            <p className="text-sm text-gray-900">{item.description}</p>
                          </td>
                          <td className="px-3 py-2 text-center border-r border-gray-200">
                            <span className={`font-mono font-bold ${
                              item.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {item.transaction_type === 'income' ? '+' : '-'}{item.amount?.toLocaleString()} د.ع
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center">
                            <span className="text-sm text-gray-600">
                              {new Date(item.created_at).toLocaleDateString('ar-EG')}
                            </span>
                          </td>
                        </>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination Info */}
            {reportData.length > 100 && (
              <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                  <AlertCircle className="h-4 w-4" />
                  <span>
                    تم عرض أول 100 سجل من أصل <strong>{reportData.length}</strong> سجل
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Empty State */}
      {(!Array.isArray(reportData) || reportData.length === 0) && (
        <div className="text-center py-12">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-8">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">لا توجد بيانات</h3>
            <p className="text-gray-600">لا توجد بيانات متاحة لعرضها في هذا التقرير</p>
          </div>
        </div>
      )}

      {/* Report Footer */}
      <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Report Statistics */}
          <div>
            <h5 className="font-bold text-gray-800 mb-3">إحصائيات التقرير:</h5>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>عدد السجلات المعروضة:</span>
                <span className="font-semibold">
                  {Array.isArray(reportData) ? Math.min(reportData.length, 100) : 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span>إجمالي السجلات:</span>
                <span className="font-semibold">
                  {Array.isArray(reportData) ? reportData.length : 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span>تاريخ آخر تحديث:</span>
                <span className="font-semibold">
                  {new Date().toLocaleDateString('ar-EG')}
                </span>
              </div>
            </div>
          </div>

          {/* Report Notes */}
          <div>
            <h5 className="font-bold text-gray-800 mb-3">ملاحظات مهمة:</h5>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• هذا التقرير تم إنشاؤه تلقائياً من النظام</li>
              <li>• البيانات محدثة حتى وقت الطباعة</li>
              <li>• يرجى التحقق من صحة البيانات قبل اتخاذ القرارات</li>
              <li>• للاستفسار يرجى التواصل مع الإدارة</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
