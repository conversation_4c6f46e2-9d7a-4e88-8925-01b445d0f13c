(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7313],{4229:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4516:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},10724:(e,t,s)=>{"use strict";s.d(t,{F:()=>r});var a=s(12115);function r(){let[e,t]=(0,a.useState)(!1),[s,r]=(0,a.useState)("");return(0,a.useEffect)(()=>{t(!0),r(new Date().toLocaleDateString("ar-EG"))},[]),{mounted:e,currentDate:s,generateInvoiceNumber:()=>"INV-".concat(Date.now()),getCurrentDateISO:()=>new Date().toISOString().split("T")[0],formatNumber:t=>e?t.toLocaleString():t.toString()}}},15573:(e,t,s)=>{Promise.resolve().then(s.bind(s,42711))},16785:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},19420:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},33109:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},42711:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(95155),r=s(12115),l=s(61932),n=s(48136),i=s(54416),c=s(84616),d=s(37108),o=s(4229),u=s(81304),m=s(22711),x=s(53363),g=s(10988),h=s(10724);function p(){let[e,t]=(0,r.useState)([]),[s,p]=(0,r.useState)(null),[b,y]=(0,r.useState)(0),[v,j]=(0,r.useState)(""),[f,N]=(0,r.useState)(!1),[w,C]=(0,r.useState)(!1),[k,A]=(0,r.useState)({name:"",category:"",manufacturer:"",activeIngredient:"",strength:"",form:"",batchCode:"",quantity:1,unitCost:0,sellingPrice:0,expiryDate:"",receivedDate:""}),[S,D]=(0,r.useState)([]),[_,M]=(0,r.useState)([]),[I,P]=(0,r.useState)(!1),[q,L]=(0,r.useState)("cash"),[E,z]=(0,r.useState)(""),[O,H]=(0,r.useState)(""),[F,U]=(0,r.useState)(!1),[V,J]=(0,r.useState)(null),[T,B]=(0,r.useState)([]),[G,R]=(0,r.useState)(""),[Z,K]=(0,r.useState)(!1),[Q,W]=(0,r.useState)([]),{settings:X}=(0,x.usePrintSettings)(),{mounted:Y,currentDate:$,generateInvoiceNumber:ee,getCurrentDateISO:et,formatNumber:es}=(0,h.F)();(0,r.useEffect)(()=>{ea(),er(),el()},[]),(0,r.useEffect)(()=>{Y&&!k.receivedDate&&A(e=>({...e,receivedDate:et()}))},[Y,et]),(0,r.useEffect)(()=>{if(G.length>=2){let e=T.filter(e=>{var t,s,a,r;return(null==(t=e.medicineName)?void 0:t.toLowerCase().includes(G.toLowerCase()))||(null==(s=e.category)?void 0:s.toLowerCase().includes(G.toLowerCase()))||(null==(a=e.manufacturer)?void 0:a.toLowerCase().includes(G.toLowerCase()))||(null==(r=e.activeIngredient)?void 0:r.toLowerCase().includes(G.toLowerCase()))});W(e),K(e.length>0)}else K(!1),W([])},[G,T]);let ea=async()=>{try{let e=await (0,g.getMedicines)();e.success&&e.data?D(e.data):(console.error("Failed to load medicines:",e.error),D([{id:"1",name:"باراسيتامول 500mg",category:"مسكنات"},{id:"2",name:"أموكسيسيلين 250mg",category:"مضادات حيوية"}]))}catch(e){console.error("Error loading medicines:",e),D([{id:"1",name:"باراسيتامول 500mg",category:"مسكنات"},{id:"2",name:"أموكسيسيلين 250mg",category:"مضادات حيوية"}])}},er=async()=>{try{let e=await (0,g.getSuppliers)();e.success&&e.data?M(e.data):(console.error("Failed to load suppliers:",e.error),M([{id:"1",name:"شركة الأدوية العراقية",contact_person:"أحمد محمد",phone:"07901234567"},{id:"2",name:"شركة بغداد للأدوية",contact_person:"فاطمة علي",phone:"07801234567"}]))}catch(e){console.error("Error loading suppliers:",e),M([{id:"1",name:"شركة الأدوية العراقية",contact_person:"أحمد محمد",phone:"07901234567"},{id:"2",name:"شركة بغداد للأدوية",contact_person:"فاطمة علي",phone:"07801234567"}])}},el=async()=>{try{let e=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),t=new Map;e.forEach(e=>{let s="".concat(e.medicineName,"_").concat(e.manufacturer,"_").concat(e.activeIngredient);if(t.has(s)){let a=t.get(s);a.purchaseCount+=1,new Date(e.created_at)>new Date(a.lastPurchaseDate)&&(a.lastUnitCost=e.unitCost,a.lastSellingPrice=e.sellingPrice,a.lastPurchaseDate=e.created_at)}else t.set(s,{medicineName:e.medicineName,category:e.category,manufacturer:e.manufacturer,activeIngredient:e.activeIngredient,strength:e.strength,form:e.form,lastUnitCost:e.unitCost,lastSellingPrice:e.sellingPrice,lastPurchaseDate:e.created_at,purchaseCount:1})}),B(Array.from(t.values()))}catch(e){console.error("Error loading previous purchases:",e)}},en=()=>{A({name:"",category:"",manufacturer:"",activeIngredient:"",strength:"",form:"",batchCode:"",quantity:1,unitCost:0,sellingPrice:0,expiryDate:"",receivedDate:et()}),R(""),K(!1)},ei=(e,s)=>{let a=Math.max(0,s||0);t(t=>t.map(t=>t.id===e?{...t,quantity:a,totalCost:a*t.unitCost}:t))},ec=()=>e.reduce((e,t)=>e+t.totalCost,0),ed=()=>ec()-b,eo=async()=>{if(!s)return void alert("يرجى اختيار المورد");if(0===e.length)return void alert("يرجى إضافة عناصر للفاتورة");P(!0);try{let r="PUR-".concat(Date.now()),l={invoice_number:r,supplier_id:s.id,total_amount:ec(),discount_amount:b,final_amount:ed(),payment_method:q,payment_status:"cash"===q?"paid":"pending",notes:E,private_notes:O},n=e.map(e=>({medicineId:e.medicineId||null,medicineName:e.medicineName,category:e.category,manufacturer:e.manufacturer,activeIngredient:e.activeIngredient,strength:e.strength,form:e.form,batchCode:e.batchCode,quantity:e.quantity,unitCost:e.unitCost,totalCost:e.totalCost,expiryDate:e.expiryDate,receivedDate:e.receivedDate,sellingPrice:e.sellingPrice})),i=await (0,g.completePurchaseTransaction)(l,n);if(i.success){let a={...l,invoiceNumber:r,date:et(),supplierName:null==s?void 0:s.name,supplierPhone:null==s?void 0:s.phone,supplierAddress:null==s?void 0:s.address,items:e,subtotal:ec(),discount:b,finalAmount:ed(),purchase_invoice_items:n.map(e=>({...e,unit_cost:e.unitCost,total_cost:e.totalCost,batch_code:e.batchCode,expiry_date:e.expiryDate,medicine_name:e.medicineName,medicines:{name:e.medicineName||"غير محدد",category:e.category||"",manufacturer:e.manufacturer||"",strength:e.strength||"",form:e.form||""}}))};J(a),alert("تم حفظ فاتورة الشراء بنجاح!"),setTimeout(()=>{(0,x.printInvoice)(a,"purchase",X)},500);let i=JSON.parse(localStorage.getItem("purchase_invoice_items")||"[]"),c=n.map(e=>({...e,created_at:new Date().toISOString()}));i.push(...c),localStorage.setItem("purchase_invoice_items",JSON.stringify(i)),await el(),t([]),p(null),y(0),L("cash"),z(""),H(""),await ea()}else{var a;alert("حدث خطأ أثناء حفظ الفاتورة: "+((null==(a=i.error)?void 0:a.message)||"خطأ غير معروف"))}}catch(e){console.error("Error saving purchase invoice:",e),alert("حدث خطأ أثناء حفظ الفاتورة")}finally{P(!1)}};return(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"فواتير المشتريات"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"إنشاء وإدارة فواتير شراء الأدوية من الموردين"})]}),(0,a.jsx)("div",{className:"flex items-center gap-3",children:(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg px-4 py-2",children:(0,a.jsx)("p",{className:"text-green-800 text-sm font-medium",children:"\uD83D\uDCA1 الطريقة الوحيدة لإضافة أدوية للمخزون"})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"اختيار المورد"}),s?(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-blue-900",children:s.name}),(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:[s.contact_person," • ",s.phone]})]})]}),(0,a.jsx)("button",{onClick:()=>p(null),className:"text-blue-600 hover:text-blue-800",children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})]}):(0,a.jsxs)("button",{onClick:()=>N(!0),className:"w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-colors",children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-600",children:"انقر لاختيار مورد"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"إضافة الأدوية"}),(0,a.jsxs)("button",{onClick:()=>C(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),"إضافة دواء"]})]}),0===e.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(d.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"لم يتم إضافة أي أدوية بعد"}),(0,a.jsx)("p",{className:"text-sm",children:'انقر على "إضافة دواء" لبدء إنشاء الفاتورة'})]}):(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 text-lg",children:e.medicineName}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"الفئة:"})," ",e.category]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"الشركة المصنعة:"})," ",e.manufacturer]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"المادة الفعالة:"})," ",e.activeIngredient]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"التركيز:"})," ",e.strength]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"الشكل:"})," ",e.form]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"رقم الوجبة:"})," ",e.batchCode]})]})]}),(0,a.jsx)("button",{onClick:()=>{var s;return s=e.id,void t(e=>e.filter(e=>e.id!==s))},className:"p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200",children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"الكمية"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 justify-center",children:[(0,a.jsx)("button",{onClick:()=>ei(e.id,e.quantity-1),className:"w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300",children:"-"}),(0,a.jsx)("input",{type:"number",value:e.quantity||"",onChange:t=>{let s=t.target.value;if(""===s)ei(e.id,0);else{let t=parseInt(s);isNaN(t)||ei(e.id,t)}},onBlur:t=>{(""===t.target.value||0===parseInt(t.target.value))&&ei(e.id,1)},className:"w-16 text-center font-medium border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500",min:"0",placeholder:"1"}),(0,a.jsx)("button",{onClick:()=>ei(e.id,e.quantity+1),className:"w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300",children:"+"})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"سعر الشراء"}),(0,a.jsx)("input",{type:"number",value:e.unitCost,onChange:s=>{var a,r;return a=e.id,r=Number(s.target.value),void t(e=>e.map(e=>e.id===a?{...e,unitCost:r,totalCost:e.quantity*r}:e))},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded text-center",placeholder:"0"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"سعر البيع"}),(0,a.jsx)("input",{type:"number",value:e.sellingPrice,onChange:s=>{var a,r;return a=e.id,r=Number(s.target.value),void t(e=>e.map(e=>e.id===a?{...e,sellingPrice:r}:e))},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded text-center",placeholder:"0"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"الربح"}),(0,a.jsxs)("p",{className:"font-medium text-green-600",children:[es((e.sellingPrice-e.unitCost)*e.quantity)," د.ع"]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"المجموع"}),(0,a.jsxs)("p",{className:"font-medium text-blue-600",children:[es(e.totalCost)," د.ع"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-3 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"تاريخ الانتهاء:"})," ",e.expiryDate]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"تاريخ الاستلام:"})," ",e.receivedDate]})]})]},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"ملخص الفاتورة"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"المجموع الفرعي:"}),(0,a.jsxs)("span",{className:"font-medium",children:[es(ec())," د.ع"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"الخصم:"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"number",value:b,onChange:e=>y(Number(e.target.value)),className:"w-20 px-2 py-1 text-sm border border-gray-300 rounded text-center",placeholder:"0",min:"0"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"د.ع"})]})]}),(0,a.jsx)("div",{className:"border-t pt-3",children:(0,a.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,a.jsx)("span",{children:"المجموع النهائي:"}),(0,a.jsxs)("span",{className:"text-blue-600",children:[es(ed())," د.ع"]})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"طريقة الدفع"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("button",{onClick:()=>L("cash"),className:"p-3 border-2 rounded-lg text-center transition-colors ".concat("cash"===q?"border-green-500 bg-green-50 text-green-700":"border-gray-300 text-gray-600 hover:border-green-300"),children:[(0,a.jsx)("div",{className:"font-medium",children:"نقداً"}),(0,a.jsx)("div",{className:"text-sm",children:"دفع فوري"})]}),(0,a.jsxs)("button",{onClick:()=>L("credit"),className:"p-3 border-2 rounded-lg text-center transition-colors ".concat("credit"===q?"border-orange-500 bg-orange-50 text-orange-700":"border-gray-300 text-gray-600 hover:border-orange-300"),children:[(0,a.jsx)("div",{className:"font-medium",children:"آجل"}),(0,a.jsx)("div",{className:"text-sm",children:"دفع لاحق"})]})]}),"credit"===q&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-orange-800 text-sm",children:"⚠️ سيتم إضافة هذا المبلغ لحساب المورد كدين"})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"الملاحظات"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات عامة (تظهر في الطباعة)"}),(0,a.jsx)("textarea",{value:E,onChange:e=>z(e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ملاحظات للمورد..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات خاصة (للنظام فقط)"}),(0,a.jsx)("textarea",{value:O,onChange:e=>H(e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50",placeholder:"ملاحظات داخلية..."})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:eo,disabled:0===e.length||!s||I,className:"w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),I?"جاري الحفظ...":"حفظ فاتورة الشراء"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("button",{onClick:()=>{!V&&e.length>0&&eo(),U(!0)},disabled:0===e.length,className:"bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"معاينة وطباعة"]}),(0,a.jsxs)("button",{onClick:()=>{V?(0,x.printInvoice)(V,"purchase",X):alert("لا توجد فاتورة للطباعة")},disabled:!V,className:"bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"طباعة مباشرة"]})]})]})]})]}),f&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"اختيار المورد"}),(0,a.jsx)("button",{onClick:()=>N(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"space-y-3",children:_.map(e=>(0,a.jsx)("button",{onClick:()=>{p(e),N(!1)},className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.contact_person," • ",e.phone]})]})]})},e.id))})]})}),w&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-screen overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"إضافة دواء للفاتورة"}),(0,a.jsxs)("p",{className:"text-sm text-blue-600 mt-1",children:["\uD83D\uDCA1 ابدأ بكتابة اسم الدواء للبحث في ",T.length," دواء من المشتريات السابقة"]})]}),(0,a.jsx)("button",{onClick:()=>{C(!1),en()},className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-md font-semibold text-gray-800 border-b pb-2",children:"المعلومات الأساسية"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم الدواء *"}),(0,a.jsx)("input",{type:"text",value:G||k.name,onChange:e=>{let t=e.target.value;R(t),A({...k,name:t})},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ابدأ بكتابة اسم الدواء للبحث في المشتريات السابقة..."}),Z&&Q.length>0&&(0,a.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:Q.map((e,t)=>(0,a.jsxs)("div",{onClick:()=>{A({...k,name:e.medicineName,category:e.category,manufacturer:e.manufacturer,activeIngredient:e.activeIngredient,strength:e.strength,form:e.form,unitCost:e.lastUnitCost||0,sellingPrice:e.lastSellingPrice||0}),R(e.medicineName),K(!1)},className:"p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.medicineName}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[e.manufacturer," - ",e.category]}),(0,a.jsxs)("div",{className:"text-xs text-blue-600 mt-1",children:["آخر سعر شراء: ",es(e.lastUnitCost||0)," د.ع | آخر سعر بيع: ",es(e.lastSellingPrice||0)," د.ع | تم شراؤه ",e.purchaseCount," مرة"]})]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الفئة *"}),(0,a.jsxs)("select",{value:k.category,onChange:e=>A({...k,category:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"اختر الفئة"}),(0,a.jsx)("option",{value:"مسكنات",children:"مسكنات"}),(0,a.jsx)("option",{value:"مضادات حيوية",children:"مضادات حيوية"}),(0,a.jsx)("option",{value:"فيتامينات",children:"فيتامينات"}),(0,a.jsx)("option",{value:"أدوية القلب",children:"أدوية القلب"}),(0,a.jsx)("option",{value:"أدوية الجهاز الهضمي",children:"أدوية الجهاز الهضمي"}),(0,a.jsx)("option",{value:"أدوية الجهاز التنفسي",children:"أدوية الجهاز التنفسي"}),(0,a.jsx)("option",{value:"أخرى",children:"أخرى"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الشركة المصنعة"}),(0,a.jsx)("input",{type:"text",value:k.manufacturer,onChange:e=>A({...k,manufacturer:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"اسم الشركة المصنعة"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"المادة الفعالة"}),(0,a.jsx)("input",{type:"text",value:k.activeIngredient,onChange:e=>A({...k,activeIngredient:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"المادة الفعالة"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"التركيز"}),(0,a.jsx)("input",{type:"text",value:k.strength,onChange:e=>A({...k,strength:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"500mg"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الشكل *"}),(0,a.jsxs)("select",{value:k.form,onChange:e=>A({...k,form:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"اختر الشكل"}),(0,a.jsx)("option",{value:"tablet",children:"قرص"}),(0,a.jsx)("option",{value:"capsule",children:"كبسولة"}),(0,a.jsx)("option",{value:"syrup",children:"شراب"}),(0,a.jsx)("option",{value:"injection",children:"حقنة"}),(0,a.jsx)("option",{value:"cream",children:"كريم"}),(0,a.jsx)("option",{value:"drops",children:"قطرة"}),(0,a.jsx)("option",{value:"powder",children:"بودرة"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-md font-semibold text-gray-800 border-b pb-2",children:"تفاصيل الشراء"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"كود الوجبة *"}),(0,a.jsx)("input",{type:"text",value:k.batchCode,onChange:e=>A({...k,batchCode:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"B001"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الكمية *"}),(0,a.jsx)("input",{type:"number",value:k.quantity,onChange:e=>A({...k,quantity:Number(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"تاريخ الاستلام"}),(0,a.jsx)("input",{type:"date",value:k.receivedDate,onChange:e=>A({...k,receivedDate:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"تاريخ الانتهاء *"}),(0,a.jsx)("input",{type:"date",value:k.expiryDate,onChange:e=>A({...k,expiryDate:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"سعر الشراء *"}),(0,a.jsx)("input",{type:"number",value:k.unitCost,onChange:e=>A({...k,unitCost:Number(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"سعر البيع *"}),(0,a.jsx)("input",{type:"number",value:k.sellingPrice,onChange:e=>A({...k,sellingPrice:Number(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0",min:"0",step:"0.01"})]})]}),(0,a.jsx)("div",{className:"p-3 bg-green-50 rounded-lg",children:(0,a.jsxs)("div",{className:"text-sm text-green-800",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"إجمالي التكلفة:"}),(0,a.jsxs)("span",{className:"font-medium",children:[es(k.quantity*k.unitCost)," د.ع"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"الربح المتوقع:"}),(0,a.jsxs)("span",{className:"font-medium",children:[es((k.sellingPrice-k.unitCost)*k.quantity)," د.ع"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"نسبة الربح:"}),(0,a.jsx)("span",{className:"font-medium",children:k.unitCost>0?"".concat(((k.sellingPrice-k.unitCost)/k.unitCost*100).toFixed(1),"%"):"0%"})]})]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{C(!1),en()},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"إلغاء"}),(0,a.jsx)("button",{type:"button",onClick:()=>{if(!k.name||!k.category||!k.form||!k.batchCode||!k.expiryDate||k.unitCost<=0||k.sellingPrice<=0)return void alert("يرجى ملء جميع الحقول المطلوبة");let s=S.find(e=>e.name.toLowerCase()===k.name.toLowerCase());t([...e,{id:"".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),medicineId:(null==s?void 0:s.id)||null,medicineName:k.name,category:k.category,manufacturer:k.manufacturer,activeIngredient:k.activeIngredient,strength:k.strength,form:k.form,batchCode:k.batchCode,quantity:k.quantity,unitCost:k.unitCost,sellingPrice:k.sellingPrice,totalCost:k.quantity*k.unitCost,expiryDate:k.expiryDate,receivedDate:k.receivedDate}]),C(!1),en()},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"إضافة للفاتورة"})]})]})}),F&&V&&(0,a.jsx)(m.Ay,{title:"فاتورة مشتريات",data:V,type:"invoice",settings:X,onClose:()=>U(!1),children:(0,a.jsx)(m.dt,{invoice:V,type:"purchase",settings:X})})]})})}},48136:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},57434:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},94498:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]])}},e=>{e.O(0,[705,6874,6543,5647,8080,1932,988,3363,2711,8441,5964,7358],()=>e(e.s=15573)),_N_E=e.O()}]);