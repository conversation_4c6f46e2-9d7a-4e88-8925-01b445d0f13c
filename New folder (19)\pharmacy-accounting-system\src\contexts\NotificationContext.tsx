'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'

export interface Notification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  category: 'inventory' | 'sales' | 'system' | 'user' | 'financial'
  priority: 'low' | 'medium' | 'high' | 'critical'
  isRead: boolean
  actionUrl?: string
  actionLabel?: string
  data?: any
  createdAt: string
  expiresAt?: string
}

interface NotificationContextType {
  notifications: Notification[]
  unreadCount: number
  addNotification: (notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  removeNotification: (id: string) => void
  clearAll: () => void
  getNotificationsByCategory: (category: string) => Notification[]
  getNotificationsByPriority: (priority: string) => Notification[]
  refreshNotifications: () => Promise<void>
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export const useNotifications = () => {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

interface NotificationProviderProps {
  children: React.ReactNode
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([])

  // تحديث التنبيهات عند تحميل المكون
  useEffect(() => {
    loadNotifications()
    
    // تحديث التنبيهات كل دقيقة
    const interval = setInterval(loadNotifications, 60000)
    
    return () => clearInterval(interval)
  }, [])

  const loadNotifications = async () => {
    try {
      // تحميل التنبيهات من قاعدة البيانات أو إنشاء تنبيهات تجريبية
      const mockNotifications = await generateSystemNotifications()
      setNotifications(mockNotifications)
    } catch (error) {
      console.error('Error loading notifications:', error)
    }
  }

  const generateSystemNotifications = async (): Promise<Notification[]> => {
    const notifications: Notification[] = []
    const now = new Date()

    // تنبيهات المخزون
    notifications.push({
      id: '1',
      type: 'warning',
      title: 'أدوية قاربت على الانتهاء',
      message: 'يوجد 5 أدوية ستنتهي صلاحيتها خلال 30 يوم',
      category: 'inventory',
      priority: 'high',
      isRead: false,
      actionUrl: '/inventory?filter=expiring',
      actionLabel: 'عرض الأدوية',
      createdAt: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), // منذ ساعتين
    })

    notifications.push({
      id: '2',
      type: 'error',
      title: 'نفاد مخزون',
      message: 'باراسيتامول 500mg - الكمية المتبقية: 0',
      category: 'inventory',
      priority: 'critical',
      isRead: false,
      actionUrl: '/inventory?search=باراسيتامول',
      actionLabel: 'إضافة مخزون',
      createdAt: new Date(now.getTime() - 30 * 60 * 1000).toISOString(), // منذ 30 دقيقة
    })

    notifications.push({
      id: '3',
      type: 'warning',
      title: 'مخزون منخفض',
      message: 'يوجد 8 أدوية كميتها أقل من الحد الأدنى',
      category: 'inventory',
      priority: 'medium',
      isRead: false,
      actionUrl: '/inventory?filter=low-stock',
      actionLabel: 'عرض التفاصيل',
      createdAt: new Date(now.getTime() - 4 * 60 * 60 * 1000).toISOString(), // منذ 4 ساعات
    })

    // تنبيهات المبيعات
    notifications.push({
      id: '4',
      type: 'info',
      title: 'مبيعات اليوم',
      message: 'تم تحقيق 2,450,000 د.ع من المبيعات اليوم',
      category: 'sales',
      priority: 'low',
      isRead: true,
      actionUrl: '/sales-records',
      actionLabel: 'عرض التفاصيل',
      createdAt: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString(), // منذ 6 ساعات
    })

    // تنبيهات النظام
    notifications.push({
      id: '5',
      type: 'success',
      title: 'تحديث النظام',
      message: 'تم تحديث النظام بنجاح إلى الإصدار 1.0.1',
      category: 'system',
      priority: 'low',
      isRead: false,
      createdAt: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString(), // منذ يوم
    })

    // تنبيهات المستخدمين
    notifications.push({
      id: '6',
      type: 'info',
      title: 'مستخدم جديد',
      message: 'تم إضافة مستخدم جديد: أحمد الصيدلي',
      category: 'user',
      priority: 'low',
      isRead: true,
      actionUrl: '/users',
      actionLabel: 'إدارة المستخدمين',
      createdAt: new Date(now.getTime() - 12 * 60 * 60 * 1000).toISOString(), // منذ 12 ساعة
    })

    // تنبيهات مالية
    notifications.push({
      id: '7',
      type: 'warning',
      title: 'فواتير معلقة',
      message: 'يوجد 12 فاتورة معلقة الدفع بقيمة 850,000 د.ع',
      category: 'financial',
      priority: 'high',
      isRead: false,
      actionUrl: '/sales-records?filter=pending',
      actionLabel: 'عرض الفواتير',
      createdAt: new Date(now.getTime() - 8 * 60 * 60 * 1000).toISOString(), // منذ 8 ساعات
    })

    return notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }

  const addNotification = (notificationData: Omit<Notification, 'id' | 'isRead' | 'createdAt'>) => {
    const newNotification: Notification = {
      ...notificationData,
      id: Date.now().toString(),
      isRead: false,
      createdAt: new Date().toISOString(),
    }
    
    setNotifications(prev => [newNotification, ...prev])
  }

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, isRead: true }
          : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRead: true }))
    )
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }

  const clearAll = () => {
    setNotifications([])
  }

  const getNotificationsByCategory = (category: string) => {
    return notifications.filter(notification => notification.category === category)
  }

  const getNotificationsByPriority = (priority: string) => {
    return notifications.filter(notification => notification.priority === priority)
  }

  const refreshNotifications = async () => {
    await loadNotifications()
  }

  const unreadCount = notifications.filter(n => !n.isRead).length

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    getNotificationsByCategory,
    getNotificationsByPriority,
    refreshNotifications,
  }

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  )
}
