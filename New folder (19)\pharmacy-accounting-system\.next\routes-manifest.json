{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/activity-log", "regex": "^/activity\\-log(?:/)?$", "routeKeys": {}, "namedRegex": "^/activity\\-log(?:/)?$"}, {"page": "/cashbox", "regex": "^/cashbox(?:/)?$", "routeKeys": {}, "namedRegex": "^/cashbox(?:/)?$"}, {"page": "/classic-invoice", "regex": "^/classic\\-invoice(?:/)?$", "routeKeys": {}, "namedRegex": "^/classic\\-invoice(?:/)?$"}, {"page": "/customers", "regex": "^/customers(?:/)?$", "routeKeys": {}, "namedRegex": "^/customers(?:/)?$"}, {"page": "/debug", "regex": "^/debug(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug(?:/)?$"}, {"page": "/debug-data", "regex": "^/debug\\-data(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-data(?:/)?$"}, {"page": "/debug-sales", "regex": "^/debug\\-sales(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-sales(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/fix-data", "regex": "^/fix\\-data(?:/)?$", "routeKeys": {}, "namedRegex": "^/fix\\-data(?:/)?$"}, {"page": "/inventory", "regex": "^/inventory(?:/)?$", "routeKeys": {}, "namedRegex": "^/inventory(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/purchases", "regex": "^/purchases(?:/)?$", "routeKeys": {}, "namedRegex": "^/purchases(?:/)?$"}, {"page": "/purchases-history", "regex": "^/purchases\\-history(?:/)?$", "routeKeys": {}, "namedRegex": "^/purchases\\-history(?:/)?$"}, {"page": "/purchases-records", "regex": "^/purchases\\-records(?:/)?$", "routeKeys": {}, "namedRegex": "^/purchases\\-records(?:/)?$"}, {"page": "/reports", "regex": "^/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/reports(?:/)?$"}, {"page": "/returns", "regex": "^/returns(?:/)?$", "routeKeys": {}, "namedRegex": "^/returns(?:/)?$"}, {"page": "/returns-records", "regex": "^/returns\\-records(?:/)?$", "routeKeys": {}, "namedRegex": "^/returns\\-records(?:/)?$"}, {"page": "/sales", "regex": "^/sales(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales(?:/)?$"}, {"page": "/sales-records", "regex": "^/sales\\-records(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales\\-records(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/suppliers", "regex": "^/suppliers(?:/)?$", "routeKeys": {}, "namedRegex": "^/suppliers(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}, {"page": "/test-print", "regex": "^/test\\-print(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-print(?:/)?$"}, {"page": "/users", "regex": "^/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/users(?:/)?$"}, {"page": "/welcome", "regex": "^/welcome(?:/)?$", "routeKeys": {}, "namedRegex": "^/welcome(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}