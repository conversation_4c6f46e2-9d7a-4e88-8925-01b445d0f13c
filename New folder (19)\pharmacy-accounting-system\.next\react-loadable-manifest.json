{"..\\node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": 92410, "files": []}, "..\\node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch": {"id": 92410, "files": []}, "..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch": {"id": 92410, "files": []}, "..\\node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": 92410, "files": []}, "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> canvg": {"id": 34137, "files": ["static/chunks/bc98253f.ce50eee70995d8cd.js", "static/chunks/2121.192ca3d1fff3cc4d.js"]}, "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> dompurify": {"id": 10822, "files": ["static/chunks/822.d1eebe7df2d8fc0a.js"]}, "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> html2canvas": {"id": 52699, "files": ["static/chunks/ad2866b8.d24ec0a5e084098f.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app": {"id": 90472, "files": ["static/chunks/472.2c08b965bd9148e2.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error": {"id": 99341, "files": ["static/chunks/9341.8181fde8cccea77c.js"]}, "app\\debug-sales\\page.tsx -> @/hooks/usePrintSettings": {"id": 53363, "files": ["static/chunks/5647-e4593d751c5c3761.js", "static/chunks/988-0f7aaa30f5c33900.js", "static/chunks/3363-64dd1206ae1006e8.js"]}, "app\\debug-sales\\page.tsx -> @/lib/database": {"id": 10988, "files": ["static/chunks/5647-e4593d751c5c3761.js", "static/chunks/988-0f7aaa30f5c33900.js"]}, "app\\reports\\page.tsx -> jspdf": {"id": 26597, "files": ["static/chunks/164f4fb6.4faf4e2e52270b82.js", "static/chunks/913.da2802283863eaf4.js"]}, "app\\reports\\page.tsx -> xlsx": {"id": 3925, "files": ["static/chunks/2170a4aa.eed14fb0d4135265.js", "static/chunks/8436.cab94b59cca0a8ff.js"]}, "app\\returns-records\\page.tsx -> xlsx": {"id": 3925, "files": ["static/chunks/2170a4aa.eed14fb0d4135265.js", "static/chunks/8436.cab94b59cca0a8ff.js"]}, "app\\returns\\page.tsx -> @/hooks/usePrintSettings": {"id": 53363, "files": []}, "app\\returns\\page.tsx -> @/lib/database": {"id": 10988, "files": []}}