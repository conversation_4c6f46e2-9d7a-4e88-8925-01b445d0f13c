'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import { 
  Search, 
  Filter, 
  Eye, 
  Printer, 
  Download,
  Calendar,
  DollarSign,
  User,
  FileText,
  MoreVertical,
  Edit,
  Trash2,
  RefreshCw
} from 'lucide-react'
import PrintTemplate, { InvoicePrint } from '@/components/PrintTemplate'
import { usePrintSettings, printInvoice } from '@/hooks/usePrintSettings'
import { getSalesInvoices } from '@/lib/database'

interface SalesRecord {
  id: string
  invoice_number: string
  customer_name: string
  final_amount: number
  payment_status: 'paid' | 'partial' | 'pending'
  payment_method: 'cash' | 'credit'
  created_at: string
  customers?: {
    name: string
    phone: string
    address: string
  }
  sales_invoice_items?: any[]
}

export default function SalesRecordsPage() {
  const [salesRecords, setSalesRecords] = useState<SalesRecord[]>([])
  const [filteredRecords, setFilteredRecords] = useState<SalesRecord[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'paid' | 'partial' | 'pending'>('all')
  const [filterPayment, setFilterPayment] = useState<'all' | 'cash' | 'credit'>('all')
  const [selectedRecord, setSelectedRecord] = useState<SalesRecord | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [showPrintPreview, setShowPrintPreview] = useState(false)
  const [loading, setLoading] = useState(true)
  const [showActionsMenu, setShowActionsMenu] = useState<string | null>(null)
  const { printSettings } = usePrintSettings()

  useEffect(() => {
    loadSalesRecords()
  }, [])

  useEffect(() => {
    filterRecords()
  }, [salesRecords, searchTerm, filterStatus, filterPayment])

  const loadSalesRecords = async () => {
    try {
      setLoading(true)
      const result = await getSalesInvoices()
      if (result.success && result.data) {
        setSalesRecords(result.data)
      }
    } catch (error) {
      console.error('Error loading sales records:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterRecords = () => {
    let filtered = salesRecords

    // البحث
    if (searchTerm) {
      filtered = filtered.filter(record =>
        record.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.customers?.name?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // فلترة حالة الدفع
    if (filterStatus !== 'all') {
      filtered = filtered.filter(record => record.payment_status === filterStatus)
    }

    // فلترة طريقة الدفع
    if (filterPayment !== 'all') {
      filtered = filtered.filter(record => record.payment_method === filterPayment)
    }

    setFilteredRecords(filtered)
  }

  const handleViewDetails = (record: SalesRecord) => {
    setSelectedRecord(record)
    setShowDetailsModal(true)
    setShowActionsMenu(null)
  }

  const handlePrintInvoice = (record: SalesRecord) => {
    setSelectedRecord(record)
    setShowPrintPreview(true)
    setShowActionsMenu(null)
  }

  const handleDirectPrint = (record: SalesRecord) => {
    printInvoice(record, 'sales', printSettings)
    setShowActionsMenu(null)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'partial': return 'bg-yellow-100 text-yellow-800'
      case 'pending': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid': return 'مدفوع'
      case 'partial': return 'جزئي'
      case 'pending': return 'معلق'
      default: return 'غير محدد'
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً'
      case 'credit': return 'آجل'
      default: return 'غير محدد'
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">سجل المبيعات</h1>
            <p className="text-gray-600 mt-1">عرض وإدارة جميع فواتير المبيعات</p>
          </div>
          <button
            onClick={loadSalesRecords}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            تحديث
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="البحث برقم الفاتورة أو اسم العميل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Payment Status Filter */}
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">جميع حالات الدفع</option>
              <option value="paid">مدفوع</option>
              <option value="partial">جزئي</option>
              <option value="pending">معلق</option>
            </select>

            {/* Payment Method Filter */}
            <select
              value={filterPayment}
              onChange={(e) => setFilterPayment(e.target.value as any)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">جميع طرق الدفع</option>
              <option value="cash">نقداً</option>
              <option value="credit">آجل</option>
            </select>

            {/* Results Count */}
            <div className="flex items-center justify-center bg-gray-50 rounded-lg px-4 py-2">
              <span className="text-sm text-gray-600">
                {filteredRecords.length} من {salesRecords.length} فاتورة
              </span>
            </div>
          </div>
        </div>

        {/* Records Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">جاري تحميل السجلات...</p>
            </div>
          ) : filteredRecords.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">لا توجد فواتير مبيعات</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رقم الفاتورة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      العميل
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المبلغ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      حالة الدفع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      طريقة الدفع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      التاريخ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRecords.map((record) => (
                    <tr key={record.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 text-blue-500 mr-2" />
                          <span className="text-sm font-medium text-gray-900">
                            {record.invoice_number}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {record.customers?.name || record.customer_name || 'عميل نقدي'}
                          </div>
                          {record.customers?.phone && (
                            <div className="text-sm text-gray-500">
                              {record.customers.phone}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-sm font-medium text-gray-900">
                            {record.final_amount?.toLocaleString()} د.ع
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(record.payment_status)}`}>
                          {getStatusText(record.payment_status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {getPaymentMethodText(record.payment_method)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-900">
                            {new Date(record.created_at).toLocaleDateString('ar-EG')}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="relative">
                          <button
                            onClick={() => setShowActionsMenu(showActionsMenu === record.id ? null : record.id)}
                            className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </button>
                          
                          {showActionsMenu === record.id && (
                            <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                              <div className="py-1">
                                <button
                                  onClick={() => handleViewDetails(record)}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  عرض التفاصيل
                                </button>
                                <button
                                  onClick={() => handlePrintInvoice(record)}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <Printer className="h-4 w-4 mr-2" />
                                  معاينة الطباعة
                                </button>
                                <button
                                  onClick={() => handleDirectPrint(record)}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <Download className="h-4 w-4 mr-2" />
                                  طباعة مباشرة
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Details Modal */}
        {showDetailsModal && selectedRecord && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900">
                  تفاصيل الفاتورة {selectedRecord.invoice_number}
                </h2>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات الفاتورة</h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-500">رقم الفاتورة:</span>
                        <p className="text-sm text-gray-900">{selectedRecord.invoice_number}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">التاريخ:</span>
                        <p className="text-sm text-gray-900">
                          {new Date(selectedRecord.created_at).toLocaleDateString('ar-EG')}
                        </p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">المبلغ الإجمالي:</span>
                        <p className="text-sm text-gray-900">{selectedRecord.final_amount?.toLocaleString()} د.ع</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">حالة الدفع:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedRecord.payment_status)}`}>
                          {getStatusText(selectedRecord.payment_status)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات العميل</h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-500">الاسم:</span>
                        <p className="text-sm text-gray-900">
                          {selectedRecord.customers?.name || selectedRecord.customer_name || 'عميل نقدي'}
                        </p>
                      </div>
                      {selectedRecord.customers?.phone && (
                        <div>
                          <span className="text-sm font-medium text-gray-500">الهاتف:</span>
                          <p className="text-sm text-gray-900">{selectedRecord.customers.phone}</p>
                        </div>
                      )}
                      {selectedRecord.customers?.address && (
                        <div>
                          <span className="text-sm font-medium text-gray-500">العنوان:</span>
                          <p className="text-sm text-gray-900">{selectedRecord.customers.address}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Items Table */}
                {selectedRecord.sales_invoice_items && selectedRecord.sales_invoice_items.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">عناصر الفاتورة</h3>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الدواء</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المجموع</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {selectedRecord.sales_invoice_items.map((item: any, index: number) => (
                            <tr key={index}>
                              <td className="px-4 py-3 text-sm text-gray-900">
                                {item.medicine_batches?.medicines?.name || 'غير محدد'}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900">{item.quantity}</td>
                              <td className="px-4 py-3 text-sm text-gray-900">
                                {item.unit_price?.toLocaleString()} د.ع
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900">
                                {item.total_price?.toLocaleString()} د.ع
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
                >
                  إغلاق
                </button>
                <button
                  onClick={() => {
                    handlePrintInvoice(selectedRecord)
                    setShowDetailsModal(false)
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  طباعة
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Print Preview Modal */}
        {showPrintPreview && selectedRecord && (
          <PrintTemplate
            title="فاتورة مبيعات"
            settings={printSettings}
            onClose={() => setShowPrintPreview(false)}
            onPrint={() => printInvoice(selectedRecord, 'sales', printSettings)}
          >
            <InvoicePrint
              invoice={selectedRecord}
              type="sales"
              settings={printSettings}
            />
          </PrintTemplate>
        )}
      </div>
    </AppLayout>
  )
}
