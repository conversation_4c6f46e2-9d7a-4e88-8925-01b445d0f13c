"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8080],{40283:(e,t,s)=>{s.d(t,{As:()=>u,AuthProvider:()=>_,Sk:()=>d,Zy:()=>p});var r=s(95155),i=s(12115);let a=async function(e,t){return(arguments.length>2&&void 0!==arguments[2]&&arguments[2],"admin"===e&&"admin123"===t)?{success:!0,user:{id:"1",username:"admin",email:"<EMAIL>",full_name:"مدير النظام",role:"admin",permissions:{sales_view:!0,sales_create:!0,sales_edit:!0,sales_delete:!0,sales_print:!0,sales_view_prices:!0,purchases_view:!0,purchases_create:!0,purchases_edit:!0,purchases_delete:!0,purchases_print:!0,inventory_view:!0,inventory_create:!0,inventory_edit:!0,inventory_delete:!0,inventory_print:!0,customers_view:!0,customers_create:!0,customers_edit:!0,customers_delete:!0,suppliers_view:!0,suppliers_create:!0,suppliers_edit:!0,suppliers_delete:!0,reports_view:!0,reports_financial:!0,reports_detailed:!0,reports_export:!0,users_view:!0,users_create:!0,users_edit:!0,users_delete:!0,settings_view:!0,settings_edit:!0,cashbox_view:!0,cashbox_manage:!0,returns_view:!0,returns_create:!0,returns_edit:!0,returns_delete:!0},is_active:!0,last_login:new Date().toISOString(),created_at:"2024-01-01T00:00:00Z"},session:{token:"mock-session-token"}}:{success:!1,error:"اسم المستخدم أو كلمة المرور غير صحيحة"}},n=async e=>({success:!0}),c=async e=>"mock-session-token"===e?{success:!0,user:{id:"1",username:"admin",email:"<EMAIL>",full_name:"مدير النظام",role:"admin",permissions:{sales_view:!0,sales_create:!0,sales_edit:!0,sales_delete:!0,sales_print:!0,sales_view_prices:!0,purchases_view:!0,purchases_create:!0,purchases_edit:!0,purchases_delete:!0,purchases_print:!0,inventory_view:!0,inventory_create:!0,inventory_edit:!0,inventory_delete:!0,inventory_print:!0,customers_view:!0,customers_create:!0,customers_edit:!0,customers_delete:!0,suppliers_view:!0,suppliers_create:!0,suppliers_edit:!0,suppliers_delete:!0,reports_view:!0,reports_financial:!0,reports_detailed:!0,reports_export:!0,users_view:!0,users_create:!0,users_edit:!0,users_delete:!0,settings_view:!0,settings_edit:!0,cashbox_view:!0,cashbox_manage:!0,returns_view:!0,returns_create:!0,returns_edit:!0,returns_delete:!0},is_active:!0}}:{success:!1,error:"جلسة غير صالحة"},o=async e=>(console.log("Activity logged:",e),{success:!0}),l=(0,i.createContext)(void 0),u=()=>{let e=(0,i.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},_=e=>{let{children:t}=e,[s,o]=(0,i.useState)(null),[u,_]=(0,i.useState)(null),[d,p]=(0,i.useState)(!0);(0,i.useEffect)(()=>{v()},[]);let v=async()=>{try{let e=localStorage.getItem("sessionToken");if(!e)return void p(!1);let t=await c(e);t.success&&t.user?(o(t.user),_(t.user.permissions)):localStorage.removeItem("sessionToken")}catch(e){console.error("Error checking session:",e),localStorage.removeItem("sessionToken")}finally{p(!1)}},g=async function(e,t){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{p(!0);let r=await a(e,t,s);if(r.success&&r.user&&r.session)return o(r.user),_(r.user.permissions),localStorage.setItem("sessionToken",r.session.token),{success:!0};return{success:!1,error:r.error||"فشل في تسجيل الدخول"}}catch(e){return{success:!1,error:e.message||"حدث خطأ غير متوقع"}}finally{p(!1)}},w=async()=>{try{let e=localStorage.getItem("sessionToken");e&&await n(e),o(null),_(null),localStorage.removeItem("sessionToken")}catch(e){console.error("Error during logout:",e),o(null),_(null),localStorage.removeItem("sessionToken")}},m=async()=>{await v()};return(0,r.jsx)(l.Provider,{value:{user:s,permissions:u,isLoading:d,isAuthenticated:!!s,login:g,logout:w,hasPermission:e=>!!u&&!0===u[e],hasAnyPermission:e=>!!u&&e.some(e=>!0===u[e]),hasRole:e=>!!s&&s.role===e,refreshUser:m},children:t})},d=()=>{let{permissions:e,hasPermission:t,hasAnyPermission:s}=u();return{permissions:e,hasPermission:t,hasAnyPermission:s,canViewSales:t("sales_view"),canCreateSales:t("sales_create"),canEditSales:t("sales_edit"),canDeleteSales:t("sales_delete"),canPrintSales:t("sales_print"),canViewPrices:t("sales_view_prices"),canViewPurchases:t("purchases_view"),canCreatePurchases:t("purchases_create"),canEditPurchases:t("purchases_edit"),canDeletePurchases:t("purchases_delete"),canPrintPurchases:t("purchases_print"),canViewInventory:t("inventory_view"),canCreateInventory:t("inventory_create"),canEditInventory:t("inventory_edit"),canDeleteInventory:t("inventory_delete"),canPrintInventory:t("inventory_print"),canViewCustomers:t("customers_view"),canCreateCustomers:t("customers_create"),canEditCustomers:t("customers_edit"),canDeleteCustomers:t("customers_delete"),canViewSuppliers:t("suppliers_view"),canCreateSuppliers:t("suppliers_create"),canEditSuppliers:t("suppliers_edit"),canDeleteSuppliers:t("suppliers_delete"),canViewReports:t("reports_view"),canViewFinancialReports:t("reports_financial"),canViewDetailedReports:t("reports_detailed"),canExportReports:t("reports_export"),canViewUsers:t("users_view"),canCreateUsers:t("users_create"),canEditUsers:t("users_edit"),canDeleteUsers:t("users_delete"),canViewSettings:t("settings_view"),canEditSettings:t("settings_edit"),canViewCashbox:t("cashbox_view"),canManageCashbox:t("cashbox_manage"),canViewReturns:t("returns_view"),canCreateReturns:t("returns_create"),canEditReturns:t("returns_edit"),canDeleteReturns:t("returns_delete"),canAccessSalesModule:s(["sales_view","sales_create","sales_edit"]),canAccessPurchasesModule:s(["purchases_view","purchases_create","purchases_edit"]),canAccessInventoryModule:s(["inventory_view","inventory_create","inventory_edit"]),canAccessReportsModule:t("reports_view"),canAccessUsersModule:s(["users_view","users_create","users_edit"]),canAccessSettingsModule:s(["settings_view","settings_edit"])}},p=()=>{let{user:e}=u();return{logUserActivity:async(t,s,r)=>{if(e)try{await o({user_id:e.id,action:t,description:s,...r})}catch(e){console.error("Error logging activity:",e)}}}}},77470:(e,t,s)=>{s.d(t,{E:()=>n,NotificationProvider:()=>c});var r=s(95155),i=s(12115);let a=(0,i.createContext)(void 0),n=()=>{let e=(0,i.useContext)(a);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e},c=e=>{let{children:t}=e,[s,n]=(0,i.useState)([]);(0,i.useEffect)(()=>{c();let e=setInterval(c,6e4);return()=>clearInterval(e)},[]);let c=async()=>{try{let e=await o();n(e)}catch(e){console.error("Error loading notifications:",e)}},o=async()=>{let e=[],t=new Date;return e.push({id:"1",type:"warning",title:"أدوية قاربت على الانتهاء",message:"يوجد 5 أدوية ستنتهي صلاحيتها خلال 30 يوم",category:"inventory",priority:"high",isRead:!1,actionUrl:"/inventory?filter=expiring",actionLabel:"عرض الأدوية",createdAt:new Date(t.getTime()-72e5).toISOString()}),e.push({id:"2",type:"error",title:"نفاد مخزون",message:"باراسيتامول 500mg - الكمية المتبقية: 0",category:"inventory",priority:"critical",isRead:!1,actionUrl:"/inventory?search=باراسيتامول",actionLabel:"إضافة مخزون",createdAt:new Date(t.getTime()-18e5).toISOString()}),e.push({id:"3",type:"warning",title:"مخزون منخفض",message:"يوجد 8 أدوية كميتها أقل من الحد الأدنى",category:"inventory",priority:"medium",isRead:!1,actionUrl:"/inventory?filter=low-stock",actionLabel:"عرض التفاصيل",createdAt:new Date(t.getTime()-144e5).toISOString()}),e.push({id:"4",type:"info",title:"مبيعات اليوم",message:"تم تحقيق 2,450,000 د.ع من المبيعات اليوم",category:"sales",priority:"low",isRead:!0,actionUrl:"/sales-records",actionLabel:"عرض التفاصيل",createdAt:new Date(t.getTime()-216e5).toISOString()}),e.push({id:"5",type:"success",title:"تحديث النظام",message:"تم تحديث النظام بنجاح إلى الإصدار 1.0.1",category:"system",priority:"low",isRead:!1,createdAt:new Date(t.getTime()-864e5).toISOString()}),e.push({id:"6",type:"info",title:"مستخدم جديد",message:"تم إضافة مستخدم جديد: أحمد الصيدلي",category:"user",priority:"low",isRead:!0,actionUrl:"/users",actionLabel:"إدارة المستخدمين",createdAt:new Date(t.getTime()-432e5).toISOString()}),e.push({id:"7",type:"warning",title:"فواتير معلقة",message:"يوجد 12 فاتورة معلقة الدفع بقيمة 850,000 د.ع",category:"financial",priority:"high",isRead:!1,actionUrl:"/sales-records?filter=pending",actionLabel:"عرض الفواتير",createdAt:new Date(t.getTime()-288e5).toISOString()}),e.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())},l=async()=>{await c()},u=s.filter(e=>!e.isRead).length;return(0,r.jsx)(a.Provider,{value:{notifications:s,unreadCount:u,addNotification:e=>{let t={...e,id:Date.now().toString(),isRead:!1,createdAt:new Date().toISOString()};n(e=>[t,...e])},markAsRead:e=>{n(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))},markAllAsRead:()=>{n(e=>e.map(e=>({...e,isRead:!0})))},removeNotification:e=>{n(t=>t.filter(t=>t.id!==e))},clearAll:()=>{n([])},getNotificationsByCategory:e=>s.filter(t=>t.category===e),getNotificationsByPriority:e=>s.filter(t=>t.priority===e),refreshNotifications:l},children:t})}}}]);