(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2711],{22711:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>f,W1:()=>w,dt:()=>v});var r=t(95155);t(12115);var n=t(81304),a=t(91788),l=t(54416),i=t(4516),c=t(19420),d=t(71007),o=t(57434),x=t(69074),m=t(40646),h=t(14186),p=t(72713),b=t(94498),g=t(33109),j=t(27809),u=t(16785),y=t(85339);t(85616);let N={companyName:"صيدلية الشفاء",companyAddress:"بغداد - العراق",companyPhone:"+*********** 4567",companyEmail:"<EMAIL>",showHeader:!0,showFooter:!0,showLogo:!0,showWatermark:!1,watermark:"نسخة أصلية",footerText:"شكراً لثقتكم بنا - نتمنى لكم الشفاء العاجل",fontSize:"medium",includeDate:!0,includePageNumbers:!0,includeQRCode:!1,headerColor:"#1f2937",accentColor:"#3b82f6",textColor:"#374151",backgroundColor:"#ffffff"};function f(e){let{children:s,title:t,settings:h,onClose:p,onPrint:b,onDownload:g}=e,j={...N,...h};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n  @media print {\n    body {\n      margin: 0;\n      padding: 0;\n      font-family: 'Arial', sans-serif;\n      font-size: 12px;\n      line-height: 1.2;\n      color: #000;\n      background: white;\n    }\n\n    .print-content {\n      max-width: none;\n      margin: 0;\n      padding: 10mm;\n      box-shadow: none;\n      border: none;\n      background: white;\n    }\n\n    .no-print { display: none !important; }\n\n    /* Classic table styles */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin-bottom: 5px;\n      border: 2px solid #000;\n    }\n\n    th, td {\n      border: 1px solid #000;\n      padding: 4px 6px;\n      text-align: center;\n      vertical-align: middle;\n      font-size: 11px;\n      height: 25px;\n    }\n\n    th {\n      background-color: #f0f0f0 !important;\n      font-weight: bold;\n      text-align: center;\n    }\n\n    /* Header border styling */\n    .border-2 {\n      border: 2px solid #000 !important;\n    }\n\n    .border {\n      border: 1px solid #000 !important;\n    }\n\n    .border-black {\n      border-color: #000 !important;\n    }\n\n    .border-r-2 {\n      border-right: 2px solid #000 !important;\n    }\n\n    .border-b-2 {\n      border-bottom: 2px solid #000 !important;\n    }\n\n    .border-t-2 {\n      border-top: 2px solid #000 !important;\n    }\n\n    /* Grid layout for print */\n    .grid {\n      display: table;\n      width: 100%;\n      table-layout: fixed;\n    }\n\n    .grid-cols-3 > div {\n      display: table-cell;\n      width: 33.333%;\n      vertical-align: top;\n    }\n\n    .grid-cols-4 > div {\n      display: table-cell;\n      width: 25%;\n      vertical-align: top;\n    }\n\n    .grid-cols-2 > div {\n      display: table-cell;\n      width: 50%;\n      vertical-align: top;\n    }\n\n    /* Text alignment */\n    .text-center { text-align: center !important; }\n    .text-right { text-align: right !important; }\n    .text-left { text-align: left !important; }\n\n    /* Font weights and sizes */\n    .font-bold { font-weight: bold !important; }\n    .text-xl { font-size: 18px !important; }\n    .text-lg { font-size: 16px !important; }\n    .text-md { font-size: 14px !important; }\n    .text-sm { font-size: 12px !important; }\n    .text-xs { font-size: 10px !important; }\n\n    /* Hide modern styling elements */\n    .rounded, .rounded-lg, .rounded-full, .shadow, .shadow-sm {\n      border-radius: 0 !important;\n      box-shadow: none !important;\n    }\n\n    /* Color adjustments for print */\n    .text-blue-600, .bg-blue-100 { color: #000 !important; background: transparent !important; }\n    .text-green-600, .bg-green-100 { color: #000 !important; background: transparent !important; }\n    .text-red-600, .bg-red-100 { color: #000 !important; background: transparent !important; }\n    .text-yellow-600, .bg-yellow-100 { color: #000 !important; background: transparent !important; }\n    .text-gray-600 { color: #333 !important; }\n\n    /* Background colors */\n    .bg-gray-100 { background-color: #f5f5f5 !important; }\n\n    /* Hide interactive elements */\n    button, .btn, .no-print, .print-button {\n      display: none !important;\n    }\n\n    /* Signature circle */\n    .rounded-full {\n      border: 2px solid #000 !important;\n      border-radius: 50% !important;\n    }\n\n    /* Font family for numbers */\n    .font-mono { font-family: 'Courier New', monospace !important; }\n  }\n"}}),(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b bg-gray-50",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"معاينة الطباعة"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("button",{onClick:()=>{window.print(),null==b||b()},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),"طباعة"]}),(0,r.jsxs)("button",{onClick:()=>{null==g||g()},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,r.jsx)(a.A,{className:"h-4 w-4"}),"تحميل"]}),(0,r.jsxs)("button",{onClick:p,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),"إغلاق"]})]})]}),(0,r.jsx)("div",{className:"overflow-auto max-h-[calc(90vh-80px)]",children:(0,r.jsxs)("div",{className:"print-content bg-white p-8 ".concat("small"===j.fontSize?"text-sm":"large"===j.fontSize?"text-lg":"text-base"),style:{color:j.textColor,backgroundColor:j.backgroundColor},children:[j.showHeader&&(0,r.jsxs)("div",{className:"mb-8 relative",children:[j.showWatermark&&j.watermark&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-5 pointer-events-none",children:(0,r.jsx)("div",{className:"text-6xl font-bold transform rotate-45 text-gray-400",children:j.watermark})}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center",children:[j.showLogo&&(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("div",{className:"w-24 h-24 rounded-full mx-auto flex items-center justify-center text-white text-3xl font-bold shadow-lg",style:{backgroundColor:j.accentColor},children:j.companyName.charAt(0)})}),(0,r.jsx)("h1",{className:"text-3xl font-bold mb-3",style:{color:j.headerColor},children:j.companyName}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:j.companyAddress})]}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:j.companyPhone})]}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:j.companyEmail})]})]})]})]}),(0,r.jsx)("div",{className:"text-center mb-8",children:(0,r.jsxs)("div",{className:"bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-3",children:[(0,r.jsx)(o.A,{className:"h-6 w-6",style:{color:j.accentColor}}),(0,r.jsx)("h2",{className:"text-2xl font-bold",style:{color:j.headerColor},children:t})]}),j.includeDate&&(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["تاريخ الإنشاء: ",new Date().toLocaleDateString("ar-EG")]})]}),(0,r.jsxs)("div",{className:"mt-3 text-xs text-gray-500",children:["رقم المستند: DOC-",Date.now().toString().slice(-8)]})]})}),s,j.showFooter&&(0,r.jsx)("div",{className:"mt-8 border-t-2 border-gray-200 pt-6",children:(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 text-center",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-gray-800 mb-2",children:j.footerText}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{children:"مستند معتمد ومطبوع إلكترونياً"})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-500",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"نظام إدارة الصيدلية"}),(0,r.jsx)("br",{}),"الإصدار 2.0 - 2024"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"تاريخ الإنشاء:"}),(0,r.jsx)("br",{}),new Date().toLocaleDateString("ar-EG")," - ",new Date().toLocaleTimeString("ar-EG")]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"حالة المستند:"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-green-600 font-semibold",children:"✓ صالح ومعتمد"})]})]})}),j.includeQRCode&&(0,r.jsx)("div",{className:"mt-4 flex justify-center",children:(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-200 border-2 border-dashed border-gray-400 rounded flex items-center justify-center text-xs text-gray-500",children:"QR Code"})})]})})]})})]})})]})}function v(e){var s,t,n,a,l,i;let{invoice:c,type:d="sales",settings:o}=e,x={...N,...o};return(0,r.jsxs)("div",{className:"print-content bg-white",style:{fontFamily:"Arial, sans-serif"},children:[(0,r.jsxs)("div",{className:"invoice-header",children:[(0,r.jsxs)("div",{className:"header-row border-b-2",children:[(0,r.jsxs)("div",{className:"header-cell english-text",children:[(0,r.jsx)("h1",{className:"text-xl font-bold mb-2",style:{letterSpacing:"2px"},children:x.companyName.toUpperCase()}),(0,r.jsx)("h2",{className:"text-lg font-bold",children:"PHARMACY"}),(0,r.jsx)("h3",{className:"text-sm",children:"MANAGEMENT SYSTEM"})]}),(0,r.jsx)("div",{className:"header-cell",children:(0,r.jsx)("div",{className:"logo-container",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:x.companyName.charAt(0)}),(0,r.jsx)("div",{className:"text-xs font-bold",children:"LOGO"})]})})}),(0,r.jsxs)("div",{className:"header-cell arabic-text",children:[(0,r.jsx)("h1",{className:"text-xl font-bold mb-2",children:x.companyName}),(0,r.jsx)("p",{className:"text-sm",children:x.companyAddress}),(0,r.jsx)("p",{className:"text-sm",children:x.companyPhone})]})]}),(0,r.jsxs)("div",{className:"header-row text-sm",children:[(0,r.jsxs)("div",{className:"header-cell arabic-text",children:[(0,r.jsx)("div",{className:"font-bold",children:"اسم الزبون رقم المريض"}),(0,r.jsx)("div",{className:"mt-1",children:"sales"===d?(null==(s=c.customers)?void 0:s.name)||c.customer_name||"عميل نقدي":(null==(t=c.suppliers)?void 0:t.name)||"غير محدد"})]}),(0,r.jsxs)("div",{className:"header-cell",children:[(0,r.jsx)("div",{className:"font-bold",children:"التاريخ: بين"}),(0,r.jsx)("div",{className:"mt-1",children:new Date(c.created_at).toLocaleDateString("ar-EG")})]}),(0,r.jsxs)("div",{className:"header-cell",children:[(0,r.jsx)("div",{className:"font-bold",children:"فاتورة رقم:"}),(0,r.jsx)("div",{className:"mt-1 font-mono",children:c.invoice_number})]}),(0,r.jsxs)("div",{className:"header-cell",children:[(0,r.jsx)("div",{className:"font-bold",children:"رقم الفاتورة:"}),(0,r.jsx)("div",{className:"mt-1",children:"sales"===d?"مبيعات":"مشتريات"})]})]}),(0,r.jsxs)("div",{className:"header-row text-sm border-t",children:[(0,r.jsx)("div",{className:"header-cell arabic-text",style:{width:"50%"},children:(0,r.jsx)("span",{className:"font-bold",children:"المنطقة: الجمهورية"})}),(0,r.jsx)("div",{className:"header-cell",style:{width:"50%"},children:(0,r.jsxs)("span",{className:"font-bold",children:["العنوان: ",x.companyAddress]})})]})]}),(0,r.jsxs)("table",{className:"invoice-table",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"number-cell",children:"ت"}),(0,r.jsx)("th",{className:"medicine-cell",children:"اسم الدواء"}),(0,r.jsx)("th",{className:"number-cell",children:"الكمية"}),(0,r.jsx)("th",{className:"number-cell",children:"سعر الوحدة"}),(0,r.jsx)("th",{className:"number-cell",children:"المجموع"}),"return"===d&&(0,r.jsx)("th",{className:"number-cell",children:"سبب المرتجع"}),(0,r.jsx)("th",{className:"number-cell",children:"رقم الدفعة"}),(0,r.jsx)("th",{className:"number-cell",children:"تاريخ الانتهاء"})]})}),(0,r.jsxs)("tbody",{children:[null==(n="return"===d?c.return_invoice_items||c.sales_return_items||c.purchase_return_items||[]:"sales"===d?c.sales_invoice_items:c.purchase_invoice_items)?void 0:n.map((e,s)=>{var t,n,a,l,i,o,x,m,h,p,b,g,j,u,y,N,f,v,w,_;return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"number-cell",children:s+1}),(0,r.jsxs)("td",{className:"medicine-cell arabic-text",children:[(0,r.jsx)("div",{className:"font-bold",children:"return"===d?e.medicine_name||e.medicineName||(null==(n=e.medicine_batches)||null==(t=n.medicines)?void 0:t.name)||(null==(a=e.medicines)?void 0:a.name)||"غير محدد":"sales"===d?e.medicine_name||e.medicineName||(null==(i=e.medicine_batches)||null==(l=i.medicines)?void 0:l.name)||"غير محدد":e.medicine_name||e.medicineName||(null==(o=e.medicines)?void 0:o.name)||"غير محدد"}),("sales"===d&&(null==(m=e.medicine_batches)||null==(x=m.medicines)?void 0:x.category)||"purchase"===d&&(null==(h=e.medicines)?void 0:h.category)||"return"===d&&((null==(b=e.medicine_batches)||null==(p=b.medicines)?void 0:p.category)||(null==(g=e.medicines)?void 0:g.category)))&&(0,r.jsx)("div",{className:"text-xs text-gray-600",children:(null==(u=e.medicine_batches)||null==(j=u.medicines)?void 0:j.category)||(null==(y=e.medicines)?void 0:y.category)}),"sales"===d&&(null==(N=e.medicine_batches)?void 0:N.medicines)&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.medicine_batches.medicines.strength&&(0,r.jsxs)("span",{children:[e.medicine_batches.medicines.strength," "]}),e.medicine_batches.medicines.form&&(0,r.jsxs)("span",{children:["(",e.medicine_batches.medicines.form,")"]})]})]}),(0,r.jsx)("td",{className:"number-cell",children:e.quantity}),(0,r.jsx)("td",{className:"number-cell",children:"sales"===d?(e.unit_price||0).toLocaleString():(e.unit_cost||e.unitCost||0).toLocaleString()}),(0,r.jsx)("td",{className:"number-cell",children:"sales"===d?(e.total_price||0).toLocaleString():(e.total_cost||e.totalCost||0).toLocaleString()}),"return"===d&&(0,r.jsx)("td",{className:"number-cell text-xs",children:e.return_reason||c.reason||"غير محدد"}),(0,r.jsx)("td",{className:"number-cell text-xs",children:"return"===d?e.batch_code||e.batchCode||(null==(f=e.medicine_batches)?void 0:f.batch_number)||"---":"sales"===d?(null==(v=e.medicine_batches)?void 0:v.batch_number)?e.medicine_batches.batch_number.slice(-6):"---":e.batch_code||e.batchCode||"---"}),(0,r.jsx)("td",{className:"number-cell text-xs",children:"return"===d?e.expiry_date||e.expiryDate||(null==(w=e.medicine_batches)?void 0:w.expiry_date)?new Date(e.expiry_date||e.expiryDate||e.medicine_batches.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"---":"sales"===d?(null==(_=e.medicine_batches)?void 0:_.expiry_date)?new Date(e.medicine_batches.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"---":e.expiry_date||e.expiryDate?new Date(e.expiry_date||e.expiryDate).toLocaleDateString("en-GB").replace(/\//g,"/"):"---"})]},"item_".concat(s,"_").concat(e.id||s))}),Array.from({length:Math.max(0,8-((null==(a="return"===d?c.return_invoice_items||c.sales_return_items||c.purchase_return_items||[]:"sales"===d?c.sales_invoice_items:c.purchase_invoice_items)?void 0:a.length)||0))}).map((e,s)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:"\xa0"}),(0,r.jsx)("td",{children:"\xa0"}),(0,r.jsx)("td",{children:"\xa0"}),(0,r.jsx)("td",{children:"\xa0"}),(0,r.jsx)("td",{children:"\xa0"}),"return"===d&&(0,r.jsx)("td",{children:"\xa0"}),(0,r.jsx)("td",{children:"\xa0"}),(0,r.jsx)("td",{children:"\xa0"})]},"empty_".concat(s))),(0,r.jsxs)("tr",{className:"bg-gray-100",children:[(0,r.jsx)("td",{className:"text-center font-bold arabic-text",colSpan:4,children:"المجموع الكلي"}),(0,r.jsx)("td",{className:"number-cell",children:(null==(l=c.final_amount)?void 0:l.toLocaleString())||(null==(i=c.total_amount)?void 0:i.toLocaleString())||0}),(0,r.jsx)("td",{colSpan:"return"===d?3:2,children:"\xa0"})]})]})]}),(0,r.jsxs)("div",{className:"notes-section",children:[(0,r.jsx)("div",{className:"font-bold mb-2 text-sm",children:"ملاحظات: تاريخ صرف سنة البداية"}),(0,r.jsx)("div",{className:"min-h-16 text-sm arabic-text",children:c.notes||""})]}),(0,r.jsx)("div",{className:"signature-area",children:(0,r.jsxs)("div",{className:"grid grid-cols-2",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("div",{className:"signature-circle",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs",children:"توقيع"}),(0,r.jsx)("div",{className:"text-xs",children:"الصيدلي"})]})})}),(0,r.jsxs)("div",{className:"text-sm arabic-text",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("span",{className:"font-bold",children:"طريقة الدفع: "}),(0,r.jsx)("span",{className:"font-bold",children:"cash"===c.payment_method?"نقداً":"آجل"})]}),(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("span",{className:"font-bold",children:"حالة الدفع: "}),(0,r.jsx)("span",{className:"font-bold",children:"paid"===c.payment_status?"مدفوع بالكامل":"partial"===c.payment_status?"مدفوع جزئياً":"معلق"})]}),"paid"!==c.payment_status&&(0,r.jsxs)("div",{className:"font-bold",children:["المبلغ المستحق: ",(c.final_amount-(c.paid_amount||0)).toLocaleString()]})]})]})}),(0,r.jsx)("div",{className:"print-footer",children:(0,r.jsxs)("div",{className:"grid grid-cols-2",children:[(0,r.jsx)("div",{children:"صفحة 1 من 1"}),(0,r.jsx)("div",{className:"text-right",children:new Date().toLocaleDateString("ar-EG")})]})})]})}function w(e){let{reportData:s,reportType:t,title:n,settings:a,onInvoiceClick:l}=e,i={...N,...a};return(0,r.jsxs)("div",{className:"print-content",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-white",style:{backgroundColor:i.accentColor},children:(0,r.jsx)(o.A,{className:"h-8 w-8"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:n}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["تاريخ التقرير: ",new Date().toLocaleDateString("ar-EG")]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["وقت الإنشاء: ",new Date().toLocaleTimeString("ar-EG")]})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-blue-100",children:[(0,r.jsx)("h2",{className:"font-bold text-gray-800 mb-2",children:i.companyName}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:i.companyAddress})]})]})}),Array.isArray(s)&&s.length>0&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h4",{className:"text-lg font-bold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),"ملخص التقرير"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 text-center shadow-sm",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-white"})})}),(0,r.jsx)("p",{className:"text-xs text-blue-600 mb-1",children:"إجمالي السجلات"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-800",children:s.length})]}),t.includes("sales")&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 text-center shadow-sm",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,r.jsx)("div",{className:"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center",children:(0,r.jsx)(g.A,{className:"h-5 w-5 text-white"})})}),(0,r.jsx)("p",{className:"text-xs text-green-600 mb-1",children:"إجمالي المبيعات"}),(0,r.jsxs)("p",{className:"text-lg font-bold text-green-800",children:[s.reduce((e,s)=>e+(s.final_amount||0),0).toLocaleString()," د.ع"]})]}),t.includes("purchases")&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4 text-center shadow-sm",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,r.jsx)("div",{className:"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center",children:(0,r.jsx)(j.A,{className:"h-5 w-5 text-white"})})}),(0,r.jsx)("p",{className:"text-xs text-orange-600 mb-1",children:"إجمالي المشتريات"}),(0,r.jsxs)("p",{className:"text-lg font-bold text-orange-800",children:[s.reduce((e,s)=>e+(s.final_amount||0),0).toLocaleString()," د.ع"]})]}),(t.includes("sales")||t.includes("purchases"))&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 text-center shadow-sm",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,r.jsx)("div",{className:"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-white"})})}),(0,r.jsx)("p",{className:"text-xs text-purple-600 mb-1",children:"متوسط القيمة"}),(0,r.jsxs)("p",{className:"text-lg font-bold text-purple-800",children:[Math.round(s.reduce((e,s)=>e+(s.final_amount||0),0)/s.length).toLocaleString()," د.ع"]})]})]})]}),Array.isArray(s)&&s.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h4",{className:"text-lg font-bold text-gray-900 mb-4 flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-5 w-5"}),"تفاصيل البيانات"]}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"text-white font-semibold",style:{backgroundColor:i.accentColor},children:[(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"#"}),t.includes("sales")&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"رقم الفاتورة"}),(0,r.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"اسم العميل"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"المبلغ الإجمالي"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"حالة الدفع"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center",children:"التاريخ"})]}),t.includes("purchases")&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"رقم الفاتورة"}),(0,r.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"اسم المورد"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"المبلغ الإجمالي"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"حالة الدفع"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center",children:"التاريخ"})]}),"inventory"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"اسم الدواء"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"الفئة"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"الكمية المتاحة"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"تاريخ الانتهاء"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center",children:"الحالة"})]}),"cashbox"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"النوع"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"الفئة"}),(0,r.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"الوصف"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"المبلغ"}),(0,r.jsx)("th",{className:"px-3 py-3 text-center",children:"التاريخ"})]})]})}),(0,r.jsx)("tbody",{children:s.slice(0,100).map((e,s)=>{var n,a,i,c,d,o,x,m,h,p;return(0,r.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-3 py-2 text-center font-mono text-sm text-gray-500 border-r border-gray-200",children:s+1}),t.includes("sales")&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,r.jsx)("button",{onClick:()=>null==l?void 0:l(e),className:"font-mono text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200 cursor-pointer transition-colors",title:"انقر لعرض تفاصيل الفاتورة",children:e.invoice_number})}),(0,r.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:(null==(n=e.customers)?void 0:n.name)||e.customer_name||"عميل نقدي"}),(null==(a=e.customers)?void 0:a.phone)&&(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.customers.phone})]})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsxs)("span",{className:"font-bold text-gray-900",children:[(null==(i=e.final_amount)?void 0:i.toLocaleString())||0," د.ع"]})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-semibold ".concat("paid"===e.payment_status?"bg-green-100 text-green-800":"partial"===e.payment_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:"paid"===e.payment_status?"مدفوع":"partial"===e.payment_status?"جزئي":"معلق"})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center",children:(0,r.jsx)("span",{className:"text-sm text-gray-600",children:new Date(e.created_at).toLocaleDateString("ar-EG")})})]}),t.includes("purchases")&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,r.jsx)("button",{onClick:()=>null==l?void 0:l(e),className:"font-mono text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded hover:bg-orange-200 cursor-pointer transition-colors",title:"انقر لعرض تفاصيل الفاتورة",children:e.invoice_number})}),(0,r.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:(null==(c=e.suppliers)?void 0:c.name)||"غير محدد"}),(null==(d=e.suppliers)?void 0:d.phone)&&(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.suppliers.phone})]})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsxs)("span",{className:"font-bold text-gray-900",children:[(null==(o=e.final_amount)?void 0:o.toLocaleString())||0," د.ع"]})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-semibold ".concat("paid"===e.payment_status?"bg-green-100 text-green-800":"partial"===e.payment_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:"paid"===e.payment_status?"مدفوع":"partial"===e.payment_status?"جزئي":"معلق"})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center",children:(0,r.jsx)("span",{className:"text-sm text-gray-600",children:new Date(e.created_at).toLocaleDateString("ar-EG")})})]}),"inventory"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:(null==(x=e.medicines)?void 0:x.name)||"غير محدد"}),(null==(m=e.medicine_batches)?void 0:m.batch_number)&&(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["دفعة: ",e.medicine_batches.batch_number]})]})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsx)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs",children:(null==(h=e.medicines)?void 0:h.category)||"غير محدد"})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsx)("span",{className:"font-bold px-2 py-1 rounded ".concat(e.quantity<10?"bg-red-100 text-red-800":e.quantity<50?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:e.quantity})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsx)("span",{className:"text-sm text-gray-600",children:new Date(e.expiry_date).toLocaleDateString("ar-EG")})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center",children:(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-semibold ".concat(e.quantity<10?"bg-red-100 text-red-800":e.quantity<50?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:e.quantity<10?"كمية قليلة":e.quantity<50?"كمية متوسطة":"كمية جيدة"})})]}),"cashbox"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-semibold ".concat("income"===e.transaction_type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"income"===e.transaction_type?"وارد":"مصروف"})}),(0,r.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,r.jsx)("span",{className:"bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs",children:e.category})}),(0,r.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,r.jsx)("p",{className:"text-sm text-gray-900",children:e.description})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,r.jsxs)("span",{className:"font-mono font-bold ".concat("income"===e.transaction_type?"text-green-600":"text-red-600"),children:[(null==(p=e.amount)?void 0:p.toLocaleString())||0," د.ع"]})}),(0,r.jsx)("td",{className:"px-3 py-2 text-center",children:(0,r.jsx)("span",{className:"text-sm text-gray-600",children:new Date(e.created_at).toLocaleDateString("ar-EG")})})]})]},"report_item_".concat(s,"_").concat(e.id||s))})})]})}),s.length>100&&(0,r.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["يتم عرض أول 100 سجل من إجمالي ",s.length," سجل"]})]})})]})]}),(!Array.isArray(s)||0===s.length)&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-8",children:[(0,r.jsx)(y.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"لا توجد بيانات"}),(0,r.jsx)("p",{className:"text-gray-600",children:"لا توجد بيانات متاحة لعرضها في هذا التقرير"})]})}),(0,r.jsx)("div",{className:"mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"font-bold text-gray-800 mb-3",children:"إحصائيات التقرير:"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"عدد السجلات المعروضة:"}),(0,r.jsx)("span",{className:"font-semibold",children:Array.isArray(s)?Math.min(s.length,100):0})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"إجمالي السجلات:"}),(0,r.jsx)("span",{className:"font-semibold",children:Array.isArray(s)?s.length:0})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"تاريخ آخر تحديث:"}),(0,r.jsx)("span",{className:"font-semibold",children:new Date().toLocaleDateString("ar-EG")})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"font-bold text-gray-800 mb-3",children:"ملاحظات مهمة:"}),(0,r.jsxs)("ul",{className:"text-xs text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة الصيدلية"}),(0,r.jsx)("li",{children:"• جميع البيانات محدثة حتى تاريخ إنشاء التقرير"}),(0,r.jsx)("li",{children:"• للاستفسارات يرجى التواصل مع إدارة النظام"}),(0,r.jsx)("li",{children:"• يُنصح بحفظ نسخة من هذا التقرير للمراجعة المستقبلية"})]})]})]})})]})}},85616:()=>{}}]);