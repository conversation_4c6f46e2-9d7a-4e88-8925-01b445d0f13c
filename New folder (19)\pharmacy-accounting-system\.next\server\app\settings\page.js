(()=>{var a={};a.id=662,a.ids=[662],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32178:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r});var d=c(60687),e=c(43210),f=c(21979),g=c(79410),h=c(23928),i=c(97051),j=c(99891),k=c(61611),l=c(71444),m=c(31158);let n=(0,c(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var o=c(13943),p=c(8819),q=c(97711);function r(){let[a,b]=(0,e.useState)("company"),{settings:c,updateSettings:r,resetSettings:s,exportSettings:t,importSettings:u}=(0,q.usePrintSettings)(),[v,w]=(0,e.useState)({company:{name:"مكتب لارين العلمي",nameEn:"LAREN SCIENTIFIC BUREAU",address:"بغداد - شارع فلسطين",phone:"07901234567",email:"<EMAIL>",license:"LSB-2024-001",manager:"د. أحمد محمد علي"},currency:{primary:"IQD",symbol:"د.ع",decimalPlaces:0,thousandSeparator:","},notifications:{lowStock:!0,expiryWarning:!0,expiryDays:30,dailyReport:!1,emailNotifications:!0},security:{sessionTimeout:60,passwordExpiry:90,maxLoginAttempts:3,twoFactorAuth:!1},backup:{autoBackup:!0,backupFrequency:"daily",backupTime:"02:00",retentionDays:30},printing:{invoiceTemplate:"standard",printLogo:!0,printFooter:!0,paperSize:"A4"}}),x=[{id:"company",label:"معلومات الشركة",icon:g.A},{id:"currency",label:"العملة والأرقام",icon:h.A},{id:"notifications",label:"التنبيهات",icon:i.A},{id:"security",label:"الأمان",icon:j.A},{id:"backup",label:"النسخ الاحتياطي",icon:k.A},{id:"printing",label:"الطباعة",icon:l.A}],y=(a,b,c)=>{w(d=>({...d,[a]:{...d[a],[b]:c}}))};return(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"الإعدادات"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة إعدادات النظام والتفضيلات"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"file",accept:".json",onChange:a=>{let b=a.target.files?.[0];if(b){let a=new FileReader;a.onload=a=>{try{let b=JSON.parse(a.target?.result);w(b),alert("تم استيراد الإعدادات بنجاح!")}catch(a){alert("خطأ في قراءة ملف الإعدادات")}},a.readAsText(b)}},className:"hidden",id:"import-settings"}),(0,d.jsxs)("label",{htmlFor:"import-settings",className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 cursor-pointer flex items-center gap-2",children:[(0,d.jsx)(n,{className:"h-4 w-4"}),"استيراد"]}),(0,d.jsxs)("button",{onClick:()=>{let a=new Blob([JSON.stringify(v,null,2)],{type:"application/json"}),b=URL.createObjectURL(a),c=document.createElement("a");c.href=b,c.download="pharmacy-settings.json",c.click()},className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"تصدير"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:(0,d.jsx)("nav",{className:"space-y-2",children:x.map(c=>{let e=c.icon;return(0,d.jsx)("button",{onClick:()=>b(c.id),className:`w-full text-right p-3 rounded-lg transition-colors ${a===c.id?"bg-blue-50 text-blue-600 border-r-4 border-blue-600":"text-gray-600 hover:bg-gray-50"}`,children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(e,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"font-medium",children:c.label})]})},c.id)})})})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(()=>{switch(a){case"company":return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"معلومات الشركة"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم المكتب (عربي)"}),(0,d.jsx)("input",{type:"text",value:v.company.name,onChange:a=>y("company","name",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم المكتب (إنجليزي)"}),(0,d.jsx)("input",{type:"text",value:v.company.nameEn,onChange:a=>y("company","nameEn",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"رقم الترخيص"}),(0,d.jsx)("input",{type:"text",value:v.company.license,onChange:a=>y("company","license",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم المدير"}),(0,d.jsx)("input",{type:"text",value:v.company.manager,onChange:a=>y("company","manager",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"رقم الهاتف"}),(0,d.jsx)("input",{type:"tel",value:v.company.phone,onChange:a=>y("company","phone",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,d.jsx)("input",{type:"email",value:v.company.email,onChange:a=>y("company","email",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العنوان"}),(0,d.jsx)("textarea",{value:v.company.address,onChange:a=>y("company","address",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",rows:3})]})]});case"currency":return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات العملة والأرقام"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العملة الأساسية"}),(0,d.jsxs)("select",{value:v.currency.primary,onChange:a=>y("currency","primary",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"IQD",children:"دينار عراقي (IQD)"}),(0,d.jsx)("option",{value:"USD",children:"دولار أمريكي (USD)"}),(0,d.jsx)("option",{value:"EUR",children:"يورو (EUR)"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"رمز العملة"}),(0,d.jsx)("input",{type:"text",value:v.currency.symbol,onChange:a=>y("currency","symbol",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"عدد الخانات العشرية"}),(0,d.jsxs)("select",{value:v.currency.decimalPlaces,onChange:a=>y("currency","decimalPlaces",Number(a.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:0,children:"0"}),(0,d.jsx)("option",{value:2,children:"2"}),(0,d.jsx)("option",{value:3,children:"3"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"فاصل الآلاف"}),(0,d.jsxs)("select",{value:v.currency.thousandSeparator,onChange:a=>y("currency","thousandSeparator",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:",",children:"فاصلة (,)"}),(0,d.jsx)("option",{value:".",children:"نقطة (.)"}),(0,d.jsx)("option",{value:" ",children:"مسافة ( )"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"معاينة التنسيق:"}),(0,d.jsxs)("p",{className:"text-lg font-medium text-gray-900",children:["1",v.currency.thousandSeparator,"234",v.currency.thousandSeparator,"567",v.currency.decimalPlaces>0&&`.${Array(v.currency.decimalPlaces).fill("0").join("")}`," ",v.currency.symbol]})]})]});case"notifications":return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات التنبيهات"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"تنبيه المخزون المنخفض"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"إرسال تنبيه عند انخفاض كمية الدواء"})]}),(0,d.jsx)("input",{type:"checkbox",checked:v.notifications.lowStock,onChange:a=>y("notifications","lowStock",a.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"تنبيه انتهاء الصلاحية"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"إرسال تنبيه قبل انتهاء صلاحية الأدوية"})]}),(0,d.jsx)("input",{type:"checkbox",checked:v.notifications.expiryWarning,onChange:a=>y("notifications","expiryWarning",a.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),v.notifications.expiryWarning&&(0,d.jsxs)("div",{className:"mr-6",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"عدد الأيام قبل انتهاء الصلاحية"}),(0,d.jsx)("input",{type:"number",value:v.notifications.expiryDays,onChange:a=>y("notifications","expiryDays",Number(a.target.value)),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"1",max:"365"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"التقرير اليومي"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"إرسال تقرير يومي عن المبيعات"})]}),(0,d.jsx)("input",{type:"checkbox",checked:v.notifications.dailyReport,onChange:a=>y("notifications","dailyReport",a.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"التنبيهات عبر البريد الإلكتروني"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"إرسال التنبيهات عبر البريد الإلكتروني"})]}),(0,d.jsx)("input",{type:"checkbox",checked:v.notifications.emailNotifications,onChange:a=>y("notifications","emailNotifications",a.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]})]});case"security":return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات الأمان"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"مهلة انتهاء الجلسة (بالدقائق)"}),(0,d.jsx)("input",{type:"number",value:v.security.sessionTimeout,onChange:a=>y("security","sessionTimeout",Number(a.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"5",max:"480"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"انتهاء صلاحية كلمة المرور (بالأيام)"}),(0,d.jsx)("input",{type:"number",value:v.security.passwordExpiry,onChange:a=>y("security","passwordExpiry",Number(a.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"30",max:"365"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الحد الأقصى لمحاولات تسجيل الدخول"}),(0,d.jsx)("input",{type:"number",value:v.security.maxLoginAttempts,onChange:a=>y("security","maxLoginAttempts",Number(a.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"3",max:"10"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"المصادقة الثنائية"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"تفعيل المصادقة الثنائية لحماية إضافية"})]}),(0,d.jsx)("input",{type:"checkbox",checked:v.security.twoFactorAuth,onChange:a=>y("security","twoFactorAuth",a.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]});case"backup":return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات النسخ الاحتياطي"}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"النسخ الاحتياطي التلقائي"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"تفعيل النسخ الاحتياطي التلقائي للبيانات"})]}),(0,d.jsx)("input",{type:"checkbox",checked:v.backup.autoBackup,onChange:a=>y("backup","autoBackup",a.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),v.backup.autoBackup&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"تكرار النسخ"}),(0,d.jsxs)("select",{value:v.backup.backupFrequency,onChange:a=>y("backup","backupFrequency",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"daily",children:"يومي"}),(0,d.jsx)("option",{value:"weekly",children:"أسبوعي"}),(0,d.jsx)("option",{value:"monthly",children:"شهري"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"وقت النسخ"}),(0,d.jsx)("input",{type:"time",value:v.backup.backupTime,onChange:a=>y("backup","backupTime",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"مدة الاحتفاظ (بالأيام)"}),(0,d.jsx)("input",{type:"number",value:v.backup.retentionDays,onChange:a=>y("backup","retentionDays",Number(a.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"7",max:"365"})]})]}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"إنشاء نسخة احتياطية الآن"]}),(0,d.jsxs)("button",{className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,d.jsx)(n,{className:"h-4 w-4"}),"استعادة من نسخة احتياطية"]})]})]});case"printing":return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"إعدادات الطباعة المتقدمة"}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"file",accept:".json",onChange:a=>{let b=a.target.files?.[0];b&&u(b).then(()=>{alert("تم استيراد إعدادات الطباعة بنجاح")}).catch(()=>{alert("حدث خطأ أثناء استيراد الإعدادات")})},className:"hidden",id:"import-print-settings"}),(0,d.jsxs)("label",{htmlFor:"import-print-settings",className:"bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 cursor-pointer flex items-center gap-2",children:[(0,d.jsx)(n,{className:"h-3 w-3"}),"استيراد"]}),(0,d.jsxs)("button",{onClick:t,className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-3 w-3"}),"تصدير"]}),(0,d.jsxs)("button",{onClick:()=>{confirm("هل تريد إعادة تعيين جميع إعدادات الطباعة؟")&&(s(),alert("تم إعادة تعيين إعدادات الطباعة"))},className:"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 flex items-center gap-2",children:[(0,d.jsx)(o.A,{className:"h-3 w-3"}),"إعادة تعيين"]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-4",children:"معلومات الشركة"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم الشركة (عربي)"}),(0,d.jsx)("input",{type:"text",value:c.companyName,onChange:a=>r({companyName:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم الشركة (إنجليزي)"}),(0,d.jsx)("input",{type:"text",value:c.companyNameEn,onChange:a=>r({companyNameEn:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"العنوان"}),(0,d.jsx)("input",{type:"text",value:c.companyAddress,onChange:a=>r({companyAddress:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"رقم الهاتف"}),(0,d.jsx)("input",{type:"text",value:c.companyPhone,onChange:a=>r({companyPhone:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البريد الإلكتروني"}),(0,d.jsx)("input",{type:"email",value:c.companyEmail,onChange:a=>r({companyEmail:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-4",children:"إعدادات التخطيط"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"حجم الخط"}),(0,d.jsxs)("select",{value:c.fontSize,onChange:a=>r({fontSize:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"small",children:"صغير"}),(0,d.jsx)("option",{value:"medium",children:"متوسط"}),(0,d.jsx)("option",{value:"large",children:"كبير"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"حجم الورق"}),(0,d.jsxs)("select",{value:c.paperSize,onChange:a=>r({paperSize:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"A4",children:"A4"}),(0,d.jsx)("option",{value:"A5",children:"A5"}),(0,d.jsx)("option",{value:"thermal",children:"حراري 80mm"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"نص التذييل"}),(0,d.jsx)("input",{type:"text",value:c.footerText,onChange:a=>r({footerText:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"شكراً لتعاملكم معنا"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-4",children:"خيارات العرض"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"إظهار الشعار"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"عرض شعار الشركة في الرأس"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.showLogo,onChange:a=>r({showLogo:a.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"إظهار الرأس"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"عرض معلومات الشركة في الأعلى"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.showHeader,onChange:a=>r({showHeader:a.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"إظهار التذييل"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"عرض نص التذييل في الأسفل"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.showFooter,onChange:a=>r({showFooter:a.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"إظهار الحدود"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"إضافة حدود للجداول والعناصر"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.showBorders,onChange:a=>r({showBorders:a.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"طباعة ملونة"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"استخدام الألوان في الطباعة"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.showColors,onChange:a=>r({showColors:a.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:"إضافة رمز QR"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"إضافة رمز QR للفواتير"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.includeQRCode,onChange:a=>r({includeQRCode:a.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]})]})]})]});default:return null}})(),(0,d.jsx)("div",{className:"flex justify-end mt-8 pt-6 border-t border-gray-200",children:(0,d.jsxs)("button",{onClick:()=>{console.log("Saving settings:",v),alert("تم حفظ الإعدادات بنجاح!")},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),"حفظ الإعدادات"]})})]})})]})]})})}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},57513:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,74198)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\settings\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\settings\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/settings/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},61611:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},74198:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\settings\\page.tsx","default")},74820:(a,b,c)=>{Promise.resolve().then(c.bind(c,32178))},79410:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},93380:(a,b,c)=>{Promise.resolve().then(c.bind(c,74198))},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,463,314,979,31,711],()=>b(b.s=57513));module.exports=c})();