(()=>{var a={};a.id=22,a.ids=[22],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:a=>{"use strict";a.exports=require("module")},10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},12640:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15135:function(a){"undefined"!=typeof globalThis||void 0!==this||("undefined"!=typeof window?window:"undefined"!=typeof self?self:global),a.exports=function(){"use strict";var a={28:function(a,b){var c,d=this&&this.__extends||(c=function(a,b){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)Object.prototype.hasOwnProperty.call(b,c)&&(a[c]=b[c])})(a,b)},function(a,b){if("function"!=typeof b&&null!==b)throw TypeError("Class extends value "+String(b)+" is not a constructor or null");function d(){this.constructor=a}c(a,b),a.prototype=null===b?Object.create(b):(d.prototype=b.prototype,new d)});Object.defineProperty(b,"__esModule",{value:!0}),b.HtmlRowInput=void 0,b.defaultStyles=function(a){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/a,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}},b.getTheme=function(a){return({striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}})[a]},b.HtmlRowInput=function(a){function b(b){var c=a.call(this)||this;return c._element=b,c}return d(b,a),b}(Array)},150:function(a,b){Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a,b,c,d,e){d=d||{};var f=e.internal.scaleFactor,g=e.internal.getFontSize()/f,h=g*(e.getLineHeightFactor?e.getLineHeightFactor():1.15),i="",j=1;if(("middle"===d.valign||"bottom"===d.valign||"center"===d.halign||"right"===d.halign)&&(j=(i="string"==typeof a?a.split(/\r\n|\r|\n/g):a).length||1),c+=.8500000000000001*g,"middle"===d.valign?c-=j/2*h:"bottom"===d.valign&&(c-=j*h),"center"===d.halign||"right"===d.halign){var k=g;if("center"===d.halign&&(k*=.5),i&&j>=1){for(var l=0;l<i.length;l++)e.text(i[l],b-e.getStringUnitWidth(i[l])*k,c),c+=h;return e}b-=e.getStringUnitWidth(a)*k}return"justify"===d.halign?e.text(a,b,c,{maxWidth:d.maxWidth||100,align:"justify"}):e.text(a,b,c),e}},152:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.calculateWidths=function(a,b){c=a,g=b,h=c.scaleFactor(),i=g.settings.horizontalPageBreak,j=(0,d.getPageAvailableWidth)(c,g),g.allRows().forEach(function(a){for(var b=0,e=g.columns;b<e.length;b++){var f=e[b],k=a.cells[f.index];if(k){var l=g.hooks.didParseCell;g.callCellHooks(c,l,k,a,f,null);var m=k.padding("horizontal");k.contentWidth=(0,d.getStringWidth)(k.text,k.styles,c)+m;var n=(0,d.getStringWidth)(k.text.join(" ").split(/[^\S\u00A0]+/),k.styles,c);if(k.minReadableWidth=n+k.padding("horizontal"),"number"==typeof k.styles.cellWidth)k.minWidth=k.styles.cellWidth,k.wrappedWidth=k.styles.cellWidth;else if("wrap"===k.styles.cellWidth||!0===i)k.contentWidth>j?(k.minWidth=j,k.wrappedWidth=j):(k.minWidth=k.contentWidth,k.wrappedWidth=k.contentWidth);else{var o=10/h;k.minWidth=k.styles.minCellWidth||o,k.wrappedWidth=k.contentWidth,k.minWidth>k.wrappedWidth&&(k.wrappedWidth=k.minWidth)}}}}),g.allRows().forEach(function(a){for(var b=0,c=g.columns;b<c.length;b++){var d=c[b],e=a.cells[d.index];if(e&&1===e.colSpan)d.wrappedWidth=Math.max(d.wrappedWidth,e.wrappedWidth),d.minWidth=Math.max(d.minWidth,e.minWidth),d.minReadableWidth=Math.max(d.minReadableWidth,e.minReadableWidth);else{var f=g.styles.columnStyles[d.dataKey]||g.styles.columnStyles[d.index]||{},h=f.cellWidth||f.minCellWidth;h&&"number"==typeof h&&(d.minWidth=h,d.wrappedWidth=h)}e&&(e.colSpan>1&&!d.minWidth&&(d.minWidth=e.minWidth),e.colSpan>1&&!d.wrappedWidth&&(d.wrappedWidth=e.minWidth))}});var c,g,h,i,j,k=[],l=0;b.columns.forEach(function(a){var c=a.getMaxCustomCellWidth(b);c?a.width=c:(a.width=a.wrappedWidth,k.push(a)),l+=a.width});var m=b.getWidth(a.pageSize().width)-l;m&&(m=e(k,m,function(a){return Math.max(a.minReadableWidth,a.minWidth)})),m&&(m=e(k,m,function(a){return a.minWidth})),m=Math.abs(m),!b.settings.horizontalPageBreak&&m>.1/a.scaleFactor()&&(m=m<1?m:Math.round(m),console.warn("Of the table content, ".concat(m," units width could not fit page"))),function(a){for(var b=a.allRows(),c=0;c<b.length;c++)for(var d=b[c],e=null,f=0,g=0,h=0;h<a.columns.length;h++){var i=a.columns[h];if((g-=1)>1&&a.columns[h+1])f+=i.width,delete d.cells[i.index];else if(e){var j=e;delete d.cells[i.index],e=null,j.width=i.width+f}else{var j=d.cells[i.index];if(!j)continue;if(g=j.colSpan,f=0,j.colSpan>1){e=j,f+=i.width;continue}j.width=i.width+f}}}(b),function(a,b){for(var c={count:0,height:0},d=0,e=a.allRows();d<e.length;d++){for(var g=e[d],h=0,i=a.columns;h<i.length;h++){var j=i[h],k=g.cells[j.index];if(k){b.applyStyles(k.styles,!0);var l=k.width-k.padding("horizontal");if("linebreak"===k.styles.overflow)k.text=b.splitTextToSize(k.text,l+1/b.scaleFactor(),{fontSize:k.styles.fontSize});else if("ellipsize"===k.styles.overflow)k.text=f(k.text,l,k.styles,b,"...");else if("hidden"===k.styles.overflow)k.text=f(k.text,l,k.styles,b,"");else if("function"==typeof k.styles.overflow){var m=k.styles.overflow(k.text,l);"string"==typeof m?k.text=[m]:k.text=m}k.contentHeight=k.getContentHeight(b.scaleFactor(),b.getLineHeightFactor());var n=k.contentHeight/k.rowSpan;k.rowSpan>1&&c.count*c.height<n*k.rowSpan?c={height:n,count:k.rowSpan}:c&&c.count>0&&c.height>n&&(n=c.height),n>g.height&&(g.height=n)}}c.count--}}(b,a),function(a){for(var b={},c=1,d=a.allRows(),e=0;e<d.length;e++)for(var f=d[e],g=0,h=a.columns;g<h.length;g++){var i=h[g],j=b[i.index];if(c>1)c--,delete f.cells[i.index];else if(j)j.cell.height+=f.height,c=j.cell.colSpan,delete f.cells[i.index],j.left--,j.left<=1&&delete b[i.index];else{var k=f.cells[i.index];if(!k)continue;if(k.height=f.height,k.rowSpan>1){var l=d.length-e,m=k.rowSpan>l?l:k.rowSpan;b[i.index]={cell:k,left:m,row:f}}}}}(b)},b.resizeColumns=e,b.ellipsize=f;var d=c(799);function e(a,b,c){for(var d=b,f=a.reduce(function(a,b){return a+b.wrappedWidth},0),g=0;g<a.length;g++){var h=a[g],i=d*(h.wrappedWidth/f),j=h.width+i,k=c(h),l=j<k?k:j;b-=l-h.width,h.width=l}if(b=Math.round(1e10*b)/1e10){var m=a.filter(function(a){return!(b<0)||a.width>c(a)});m.length&&(b=e(m,b,c))}return b}function f(a,b,c,e,f){return a.map(function(a){var g=a,h=b,i=c,j=e,k=f,l=1e4*j.scaleFactor();if((h=Math.ceil(h*l)/l)>=(0,d.getStringWidth)(g,i,j))return g;for(;h<(0,d.getStringWidth)(g+k,i,j)&&!(g.length<=1);)g=g.substring(0,g.length-1);return g.trim()+k})}},176:function(a,b){Object.defineProperty(b,"__esModule",{value:!0}),b.assign=function(a,b,c,d,e){if(null==a)throw TypeError("Cannot convert undefined or null to object");for(var f=Object(a),g=1;g<arguments.length;g++){var h=arguments[g];if(null!=h)for(var i in h)Object.prototype.hasOwnProperty.call(h,i)&&(f[i]=h[i])}return f}},344:function(a,b){Object.defineProperty(b,"__esModule",{value:!0}),b.validateInput=function(a,b,c){for(var d=0,e=[a,b,c];d<e.length;d++){var f=e[d];f&&"object"!=typeof f&&console.error("The options parameter should be of type object, is: "+typeof f),f.startY&&"number"!=typeof f.startY&&(console.error("Invalid value for startY option",f.startY),delete f.startY)}}},371:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.parseInput=function(a,b){var c,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K=new e.DocHandler(a),L=K.getDocumentOptions(),M=K.getGlobalOptions();(0,g.validateInput)(M,L,b);var N=(0,h.assign)({},M,L,b);"undefined"!=typeof window&&(J=window);var O=function(a,b,c){for(var d={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},e=function(e){if("columnStyles"===e){var f=a[e],g=b[e],i=c[e];d.columnStyles=(0,h.assign)({},f,g,i)}else{var j=[a,b,c].map(function(a){return a[e]||{}});d[e]=(0,h.assign)({},j[0],j[1],j[2])}},f=0,g=Object.keys(d);f<g.length;f++)e(g[f]);return d}(M,L,b),P=function(a,b,c){for(var d={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},e=0,f=[a,b,c];e<f.length;e++){var g=f[e];g.didParseCell&&d.didParseCell.push(g.didParseCell),g.willDrawCell&&d.willDrawCell.push(g.willDrawCell),g.didDrawCell&&d.didDrawCell.push(g.didDrawCell),g.willDrawPage&&d.willDrawPage.push(g.willDrawPage),g.didDrawPage&&d.didDrawPage.push(g.didDrawPage)}return d}(M,L,b),Q=(c=K,i=N,D=(0,d.parseSpacing)(i.margin,40/c.scaleFactor()),E=null!=(j=c,k=i.startY,l=j.getLastAutoTable(),m=j.scaleFactor(),n=j.pageNumber(),o=!1,l&&l.startPageNumber&&(o=l.startPageNumber+l.pageNumber-1===n),p="number"==typeof k?k:(null==k||!1===k)&&o&&(null==l?void 0:l.finalY)!=null?l.finalY+20/m:null)?p:D.top,B=!0===i.showFoot?"everyPage":!1===i.showFoot?"never":null!=(q=i.showFoot)?q:"everyPage",C=!0===i.showHead?"everyPage":!1===i.showHead?"never":null!=(r=i.showHead)?r:"everyPage",F=null!=(s=i.useCss)&&s,G=i.theme||(F?"plain":"striped"),H=!!i.horizontalPageBreak,I=null!=(t=i.horizontalPageBreakRepeat)?t:null,{includeHiddenHtml:null!=(u=i.includeHiddenHtml)&&u,useCss:F,theme:G,startY:E,margin:D,pageBreak:null!=(v=i.pageBreak)?v:"auto",rowPageBreak:null!=(w=i.rowPageBreak)?w:"auto",tableWidth:null!=(x=i.tableWidth)?x:"auto",showHead:C,showFoot:B,tableLineWidth:null!=(y=i.tableLineWidth)?y:0,tableLineColor:null!=(z=i.tableLineColor)?z:200,horizontalPageBreak:H,horizontalPageBreakRepeat:I,horizontalPageBreakBehaviour:null!=(A=i.horizontalPageBreakBehaviour)?A:"afterAllRows"}),R=function(a,b,c){var d,e,g,h,i,j=b.head||[],k=b.body||[],l=b.foot||[];if(b.html){var m=b.includeHiddenHtml;if(c){var n=(0,f.parseHtml)(a,b.html,c,m,b.useCss)||{};j=n.head||j,k=n.body||j,l=n.foot||j}else console.error("Cannot parse html in non browser environment")}return{columns:b.columns||(d=j,e=k,g=l,h=d[0]||e[0]||g[0]||[],i=[],Object.keys(h).filter(function(a){return"_element"!==a}).forEach(function(a){var b,c=1;"object"!=typeof(b=Array.isArray(h)?h[parseInt(a)]:h[a])||Array.isArray(b)||(c=(null==b?void 0:b.colSpan)||1);for(var d=0;d<c;d++){var e={dataKey:Array.isArray(h)?i.length:a+(d>0?"_".concat(d):"")};i.push(e)}}),i),head:j,body:k,foot:l}}(K,N,J);return{id:b.tableId,content:R,hooks:P,styles:O,settings:Q}};var d=c(799),e=c(643),f=c(660),g=c(344),h=c(176)},376:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.createTable=function(a,b){var c=new e.DocHandler(a),d=function(a,b){var c=a.content,d=c.columns.map(function(a,b){var c,d;return d="object"==typeof a&&null!=(c=a.dataKey)?c:b,new f.Column(d,a,b)});if(0===c.head.length){var e=j(d,"head");e&&c.head.push(e)}if(0===c.foot.length){var e=j(d,"foot");e&&c.foot.push(e)}var g=a.settings.theme,h=a.styles;return{columns:d,head:i("head",c.head,d,h,g,b),body:i("body",c.body,d,h,g,b),foot:i("foot",c.foot,d,h,g,b)}}(b,c.scaleFactor()),g=new f.Table(b,d);return(0,h.calculateWidths)(c,g),c.applyStyles(c.userStyles),g};var d=c(28),e=c(643),f=c(524),g=c(176),h=c(152);function i(a,b,c,e,h,i){var j={};return b.map(function(b,k){for(var l=0,m={},n=0,o=0,p=0;p<c.length;p++){var q=c[p];if(null==j[q.index]||0===j[q.index].left)if(0===o){var r=void 0;r=Array.isArray(b)?b[q.index-n-l]:b[q.dataKey];var s={};"object"!=typeof r||Array.isArray(r)||(s=(null==r?void 0:r.styles)||{});var t=function(a,b,c,e,f,h,i){var j,k=(0,d.getTheme)(e);"head"===a?j=f.headStyles:"body"===a?j=f.bodyStyles:"foot"===a&&(j=f.footStyles);var l=(0,g.assign)({},k.table,k[a],f.styles,j),m=f.columnStyles[b.dataKey]||f.columnStyles[b.index]||{},n="body"===a&&c%2==0?(0,g.assign)({},k.alternateRow,f.alternateRowStyles):{},o=(0,d.defaultStyles)(h),p=(0,g.assign)({},o,l,n,"body"===a?m:{});return(0,g.assign)(p,i)}(a,q,k,h,e,i,s),u=new f.Cell(r,t,a);m[q.dataKey]=u,m[q.index]=u,o=u.colSpan-1,j[q.index]={left:u.rowSpan-1,times:o}}else o--,n++;else j[q.index].left--,o=j[q.index].times,l++}return new f.Row(b,k,a,m)})}function j(a,b){var c={};return a.forEach(function(a){if(null!=a.raw){var d=function(a,b){if("head"===a){if("object"==typeof b)return b.header||null;else if("string"==typeof b||"number"==typeof b)return b}else if("foot"===a&&"object"==typeof b)return b.footer;return null}(b,a.raw);null!=d&&(c[a.dataKey]=d)}}),Object.keys(c).length>0?c:null}},460:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.parseCss=function(a,b,c,f,g){var h,i,j,k,l,m,n,o,p,q={},r=96/72,s=e(b,function(a){return g.getComputedStyle(a).backgroundColor});null!=s&&(q.fillColor=s);var t=e(b,function(a){return g.getComputedStyle(a).color});null!=t&&(q.textColor=t);var u=(h=f,i=c,j=[h.paddingTop,h.paddingRight,h.paddingBottom,h.paddingLeft],k=96/(72/i),l=(parseInt(h.lineHeight)-parseInt(h.fontSize))/i/2,m=j.map(function(a){return parseInt(a||"0")/k}),l>(n=(0,d.parseSpacing)(m,0)).top&&(n.top=l),l>n.bottom&&(n.bottom=l),n);u&&(q.cellPadding=u);var v="borderTopColor",w=r*c,x=f.borderTopWidth;if(f.borderBottomWidth===x&&f.borderRightWidth===x&&f.borderLeftWidth===x){var y=(parseFloat(x)||0)/w;y&&(q.lineWidth=y)}else q.lineWidth={top:(parseFloat(f.borderTopWidth)||0)/w,right:(parseFloat(f.borderRightWidth)||0)/w,bottom:(parseFloat(f.borderBottomWidth)||0)/w,left:(parseFloat(f.borderLeftWidth)||0)/w},!q.lineWidth.top&&(q.lineWidth.right?v="borderRightColor":q.lineWidth.bottom?v="borderBottomColor":q.lineWidth.left&&(v="borderLeftColor"));var z=e(b,function(a){return g.getComputedStyle(a)[v]});null!=z&&(q.lineColor=z);var A=["left","right","center","justify"];-1!==A.indexOf(f.textAlign)&&(q.halign=f.textAlign),-1!==(A=["middle","bottom","top"]).indexOf(f.verticalAlign)&&(q.valign=f.verticalAlign);var B=parseInt(f.fontSize||"");isNaN(B)||(q.fontSize=B/r);var C=(p="",("bold"===(o=f).fontWeight||"bolder"===o.fontWeight||parseInt(o.fontWeight)>=700)&&(p="bold"),("italic"===o.fontStyle||"oblique"===o.fontStyle)&&(p+="italic"),p);C&&(q.fontStyle=C);var D=(f.fontFamily||"").toLowerCase();return -1!==a.indexOf(D)&&(q.font=D),q};var d=c(799);function e(a,b){var c=function a(b,c){var d=c(b);return"rgba(0, 0, 0, 0)"!==d&&"transparent"!==d&&"initial"!==d&&"inherit"!==d?d:null==b.parentElement?null:a(b.parentElement,c)}(a,b);if(!c)return null;var d=c.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!d||!Array.isArray(d))return null;var e=[parseInt(d[1]),parseInt(d[2]),parseInt(d[3])];return 0===parseInt(d[4])||isNaN(e[0])||isNaN(e[1])||isNaN(e[2])?null:e}},524:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.Column=b.Cell=b.Row=b.Table=void 0;var d=c(799),e=c(28),f=c(601);b.Table=function(){function a(a,b){this.pageNumber=1,this.id=a.id,this.settings=a.settings,this.styles=a.styles,this.hooks=a.hooks,this.columns=b.columns,this.head=b.head,this.body=b.body,this.foot=b.foot}return a.prototype.getHeadHeight=function(a){return this.head.reduce(function(b,c){return b+c.getMaxCellHeight(a)},0)},a.prototype.getFootHeight=function(a){return this.foot.reduce(function(b,c){return b+c.getMaxCellHeight(a)},0)},a.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},a.prototype.callCellHooks=function(a,b,c,d,e,g){for(var h=0;h<b.length;h++){var i=!1===(0,b[h])(new f.CellHookData(a,this,c,d,e,g));if(c.text=Array.isArray(c.text)?c.text:[c.text],i)return!1}return!0},a.prototype.callEndPageHooks=function(a,b){a.applyStyles(a.userStyles);for(var c=0,d=this.hooks.didDrawPage;c<d.length;c++)(0,d[c])(new f.HookData(a,this,b))},a.prototype.callWillDrawPageHooks=function(a,b){for(var c=0,d=this.hooks.willDrawPage;c<d.length;c++)(0,d[c])(new f.HookData(a,this,b))},a.prototype.getWidth=function(a){if("number"==typeof this.settings.tableWidth)return this.settings.tableWidth;if("wrap"===this.settings.tableWidth)return this.columns.reduce(function(a,b){return a+b.wrappedWidth},0);var b=this.settings.margin;return a-b.left-b.right},a}(),b.Row=function(){function a(a,b,c,d,f){void 0===f&&(f=!1),this.height=0,this.raw=a,a instanceof e.HtmlRowInput&&(this.raw=a._element,this.element=a._element),this.index=b,this.section=c,this.cells=d,this.spansMultiplePages=f}return a.prototype.getMaxCellHeight=function(a){var b=this;return a.reduce(function(a,c){var d;return Math.max(a,(null==(d=b.cells[c.index])?void 0:d.height)||0)},0)},a.prototype.hasRowSpan=function(a){var b=this;return a.filter(function(a){var c=b.cells[a.index];return!!c&&c.rowSpan>1}).length>0},a.prototype.canEntireRowFit=function(a,b){return this.getMaxCellHeight(b)<=a},a.prototype.getMinimumRowHeight=function(a,b){var c=this;return a.reduce(function(a,d){var e=c.cells[d.index];if(!e)return 0;var f=b.getLineHeight(e.styles.fontSize),g=e.padding("vertical")+f;return g>a?g:a},0)},a}(),b.Cell=function(){function a(a,b,c){this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=b,this.section=c,this.raw=a;var d,e=a;null==a||"object"!=typeof a||Array.isArray(a)?(this.rowSpan=1,this.colSpan=1):(this.rowSpan=a.rowSpan||1,this.colSpan=a.colSpan||1,e=null!=(d=a.content)?d:a,a._element&&(this.raw=a._element));var f=null!=e?""+e:"";this.text=f.split(/\r\n|\r|\n/g)}return a.prototype.getTextPos=function(){if("top"===this.styles.valign)a=this.y+this.padding("top");else if("bottom"===this.styles.valign)a=this.y+this.height-this.padding("bottom");else{var a,b,c=this.height-this.padding("vertical");a=this.y+c/2+this.padding("top")}if("right"===this.styles.halign)b=this.x+this.width-this.padding("right");else if("center"===this.styles.halign){var d=this.width-this.padding("horizontal");b=this.x+d/2+this.padding("left")}else b=this.x+this.padding("left");return{x:b,y:a}},a.prototype.getContentHeight=function(a,b){return void 0===b&&(b=1.15),Math.max((Array.isArray(this.text)?this.text.length:1)*(this.styles.fontSize/a*b)+this.padding("vertical"),this.styles.minCellHeight)},a.prototype.padding=function(a){var b=(0,d.parseSpacing)(this.styles.cellPadding,0);return"vertical"===a?b.top+b.bottom:"horizontal"===a?b.left+b.right:b[a]},a}(),b.Column=function(){function a(a,b,c){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=a,this.raw=b,this.index=c}return a.prototype.getMaxCustomCellWidth=function(a){for(var b=0,c=0,d=a.allRows();c<d.length;c++){var e=d[c].cells[this.index];e&&"number"==typeof e.styles.cellWidth&&(b=Math.max(b,e.styles.cellWidth))}return b},a}()},601:function(a,b){var c,d=this&&this.__extends||(c=function(a,b){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)Object.prototype.hasOwnProperty.call(b,c)&&(a[c]=b[c])})(a,b)},function(a,b){if("function"!=typeof b&&null!==b)throw TypeError("Class extends value "+String(b)+" is not a constructor or null");function d(){this.constructor=a}c(a,b),a.prototype=null===b?Object.create(b):(d.prototype=b.prototype,new d)});Object.defineProperty(b,"__esModule",{value:!0}),b.CellHookData=b.HookData=void 0;var e=function(a,b,c){this.table=b,this.pageNumber=b.pageNumber,this.settings=b.settings,this.cursor=c,this.doc=a.getDocument()};b.HookData=e,b.CellHookData=function(a){function b(b,c,d,e,f,g){var h=a.call(this,b,c,g)||this;return h.cell=d,h.row=e,h.column=f,h.section=e.section,h}return d(b,a),b}(e)},626:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.calculateAllColumnsCanFitInPage=function(a,b){for(var c=[],e=0;e<b.columns.length;e++){var f=function(a,b,c){void 0===c&&(c={});var e,f=(0,d.getPageAvailableWidth)(a,b),g=new Map,h=[],i=[],j=[];Array.isArray(b.settings.horizontalPageBreakRepeat)?j=b.settings.horizontalPageBreakRepeat:("string"==typeof b.settings.horizontalPageBreakRepeat||"number"==typeof b.settings.horizontalPageBreakRepeat)&&(j=[b.settings.horizontalPageBreakRepeat]),j.forEach(function(a){var c=b.columns.find(function(b){return b.dataKey===a||b.index===a});c&&!g.has(c.index)&&(g.set(c.index,!0),h.push(c.index),i.push(b.columns[c.index]),f-=c.wrappedWidth)});for(var k=!0,l=null!=(e=null==c?void 0:c.start)?e:0;l<b.columns.length;){if(g.has(l)){l++;continue}var m=b.columns[l].wrappedWidth;if(k||f>=m)k=!1,h.push(l),i.push(b.columns[l]),f-=m;else break;l++}return{colIndexes:h,columns:i,lastIndex:l-1}}(a,b,{start:e});f.columns.length&&(c.push(f),e=f.lastIndex)}return c};var d=c(799)},639:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.applyPlugin=function(a){a.API.autoTable=function(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];var c=a[0],d=(0,g.parseInput)(this,c),e=(0,h.createTable)(this,d);return(0,i.drawTable)(this,e),this},a.API.lastAutoTable=!1,a.API.autoTableText=function(a,b,c,e){(0,d.default)(a,b,c,e,this)},a.API.autoTableSetDefaults=function(a){return e.DocHandler.setDefaults(a,this),this},a.autoTableSetDefaults=function(a,b){e.DocHandler.setDefaults(a,b)},a.API.autoTableHtmlToJson=function(a,b){if(void 0===b&&(b=!1),"undefined"==typeof window)return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var c,d=new e.DocHandler(this),g=(0,f.parseHtml)(d,a,window,b,!1),h=g.head,i=g.body;return{columns:(null==(c=h[0])?void 0:c.map(function(a){return a.content}))||[],rows:i,data:i}}};var d=c(150),e=c(643),f=c(660),g=c(371),h=c(376),i=c(789)},643:function(a,b){Object.defineProperty(b,"__esModule",{value:!0}),b.DocHandler=void 0;var c={};b.DocHandler=function(){function a(a){this.jsPDFDocument=a,this.userStyles={textColor:a.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:a.internal.getFontSize(),fontStyle:a.internal.getFont().fontStyle,font:a.internal.getFont().fontName,lineWidth:a.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:a.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return a.setDefaults=function(a,b){void 0===b&&(b=null),b?b.__autoTableDocumentDefaults=a:c=a},a.unifyColor=function(a){return Array.isArray(a)?a:"number"==typeof a?[a,a,a]:"string"==typeof a?[a]:null},a.prototype.applyStyles=function(b,c){void 0===c&&(c=!1),b.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(b.fontStyle);var d,e,f,g=this.jsPDFDocument.internal.getFont(),h=g.fontStyle,i=g.fontName;if(b.font&&(i=b.font),b.fontStyle){h=b.fontStyle;var j=this.getFontList()[i];j&&-1===j.indexOf(h)&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(j[0]),h=j[0])}if(this.jsPDFDocument.setFont(i,h),b.fontSize&&this.jsPDFDocument.setFontSize(b.fontSize),!c){var k=a.unifyColor(b.fillColor);k&&(d=this.jsPDFDocument).setFillColor.apply(d,k),(k=a.unifyColor(b.textColor))&&(e=this.jsPDFDocument).setTextColor.apply(e,k),(k=a.unifyColor(b.lineColor))&&(f=this.jsPDFDocument).setDrawColor.apply(f,k),"number"==typeof b.lineWidth&&this.jsPDFDocument.setLineWidth(b.lineWidth)}},a.prototype.splitTextToSize=function(a,b,c){return this.jsPDFDocument.splitTextToSize(a,b,c)},a.prototype.rect=function(a,b,c,d,e){return this.jsPDFDocument.rect(a,b,c,d,e)},a.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},a.prototype.getTextWidth=function(a){return this.jsPDFDocument.getTextWidth(a)},a.prototype.getDocument=function(){return this.jsPDFDocument},a.prototype.setPage=function(a){this.jsPDFDocument.setPage(a)},a.prototype.addPage=function(){return this.jsPDFDocument.addPage()},a.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},a.prototype.getGlobalOptions=function(){return c||{}},a.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},a.prototype.pageSize=function(){var a=this.jsPDFDocument.internal.pageSize;return null==a.width&&(a={width:a.getWidth(),height:a.getHeight()}),a},a.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},a.prototype.getLineHeightFactor=function(){var a=this.jsPDFDocument;return a.getLineHeightFactor?a.getLineHeightFactor():1.15},a.prototype.getLineHeight=function(a){return a/this.scaleFactor()*this.getLineHeightFactor()},a.prototype.pageNumber=function(){var a=this.jsPDFDocument.internal.getCurrentPageInfo();return a?a.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},a}()},660:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.parseHtml=function(a,b,c,f,g){void 0===f&&(f=!1),void 0===g&&(g=!1);var h,i,j="string"==typeof b?c.document.querySelector(b):b,k=Object.keys(a.getFontList()),l=a.scaleFactor(),m=[],n=[],o=[];if(!j)return console.error("Html table could not be found with input: ",b),{head:m,body:n,foot:o};for(var p=0;p<j.rows.length;p++){var q=j.rows[p],r=null==(i=null==(h=null==q?void 0:q.parentElement)?void 0:h.tagName)?void 0:i.toLowerCase(),s=function(a,b,c,f,g,h){for(var i=new d.HtmlRowInput(f),j=0;j<f.cells.length;j++){var k=f.cells[j],l=c.getComputedStyle(k);if(g||"none"!==l.display){var m=void 0;h&&(m=(0,e.parseCss)(a,k,b,l,c)),i.push({rowSpan:k.rowSpan,colSpan:k.colSpan,styles:m,_element:k,content:function(a){var b=a.cloneNode(!0);return b.innerHTML=b.innerHTML.replace(/\n/g,"").replace(/ +/g," "),b.innerHTML=b.innerHTML.split(/<br.*?>/).map(function(a){return a.trim()}).join("\n"),b.innerText||b.textContent||""}(k)})}}var n=c.getComputedStyle(f);if(i.length>0&&(g||"none"!==n.display))return i}(k,l,c,q,f,g);s&&("thead"===r?m.push(s):"tfoot"===r?o.push(s):n.push(s))}return{head:m,body:n,foot:o}};var d=c(28),e=c(460)},789:function(a,b,c){Object.defineProperty(b,"__esModule",{value:!0}),b.drawTable=function(a,b){var c=b.settings,d=c.startY,g=c.margin,o={x:g.left,y:d},r=b.getHeadHeight(b.columns)+b.getFootHeight(b.columns),s=d+g.bottom+r;"avoid"===c.pageBreak&&(s+=b.body.reduce(function(a,b){return a+b.height},0));var t=new f.DocHandler(a);("always"===c.pageBreak||null!=c.startY&&s>t.pageSize().height)&&(q(t),o.y=g.top),b.callWillDrawPageHooks(t,o);var u=(0,h.assign)({},o);b.startPageNumber=t.pageNumber(),c.horizontalPageBreak?function(a,b,c,d){var e=(0,i.calculateAllColumnsCanFitInPage)(a,b);if("afterAllRows"===b.settings.horizontalPageBreakBehaviour)e.forEach(function(e,f){var g,h,i,k,n;a.applyStyles(a.userStyles),f>0?p(a,b,c,d,e.columns,!0):j(a,b,d,e.columns),g=a,h=b,i=c,k=d,n=e.columns,g.applyStyles(g.userStyles),h.body.forEach(function(a,b){var c=b===h.body.length-1;m(g,h,a,c,i,k,n)}),l(a,b,d,e.columns)});else for(var f=-1,g=e[0];f<b.body.length-1;)!function(){var h=f;if(g){a.applyStyles(a.userStyles);var i=g.columns;f>=0?p(a,b,c,d,i,!0):j(a,b,d,i),h=k(a,b,f+1,d,i),l(a,b,d,i)}var m=h-f;e.slice(1).forEach(function(e){a.applyStyles(a.userStyles),p(a,b,c,d,e.columns,!0),k(a,b,f+1,d,e.columns,m),l(a,b,d,e.columns)}),f=h}()}(t,b,u,o):(t.applyStyles(t.userStyles),("firstPage"===c.showHead||"everyPage"===c.showHead)&&b.head.forEach(function(a){return n(t,b,a,o,b.columns)}),t.applyStyles(t.userStyles),b.body.forEach(function(a,c){var d=c===b.body.length-1;m(t,b,a,d,u,o,b.columns)}),t.applyStyles(t.userStyles),("lastPage"===c.showFoot||"everyPage"===c.showFoot)&&b.foot.forEach(function(a){return n(t,b,a,o,b.columns)})),(0,e.addTableBorder)(t,b,u,o),b.callEndPageHooks(t,o),b.finalY=o.y,a.lastAutoTable=b,t.applyStyles(t.userStyles)},b.addPage=p;var d=c(150),e=c(799),f=c(643),g=c(524),h=c(176),i=c(626);function j(a,b,c,d){var e=b.settings;a.applyStyles(a.userStyles),("firstPage"===e.showHead||"everyPage"===e.showHead)&&b.head.forEach(function(e){return n(a,b,e,c,d)})}function k(a,b,c,d,e,f){a.applyStyles(a.userStyles);var g=Math.min(c+(f=null!=f?f:b.body.length),b.body.length),h=-1;return b.body.slice(c,g).forEach(function(f,g){var i=c+g===b.body.length-1,j=o(a,b,i,d);f.canEntireRowFit(j,e)&&(n(a,b,f,d,e),h=c+g)}),h}function l(a,b,c,d){var e=b.settings;a.applyStyles(a.userStyles),("lastPage"===e.showFoot||"everyPage"===e.showFoot)&&b.foot.forEach(function(e){return n(a,b,e,c,d)})}function m(a,b,c,d,e,f,i){var j=o(a,b,d,f);if(c.canEntireRowFit(j,i))n(a,b,c,f,i);else if(function(a,b,c,d){var e=a.pageSize().height,f=d.settings.margin,g=e-(f.top+f.bottom);"body"===b.section&&(g-=d.getHeadHeight(d.columns)+d.getFootHeight(d.columns));var h=b.getMinimumRowHeight(d.columns,a);if(h>g)return console.error("Will not be able to print row ".concat(b.index," correctly since it's minimum height is larger than page height")),!0;if(!(h<c))return!1;var i=b.hasRowSpan(d.columns);return b.getMaxCellHeight(d.columns)>g?(i&&console.error("The content of row ".concat(b.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!i&&"avoid"!==d.settings.rowPageBreak}(a,c,j,b)){var k=function(a,b,c,d){var e={};a.spansMultiplePages=!0,a.height=0;for(var f=0,i=0,j=c.columns;i<j.length;i++){var k=j[i],l=a.cells[k.index];if(l){Array.isArray(l.text)||(l.text=[l.text]);var m=new g.Cell(l.raw,l.styles,l.section);(m=(0,h.assign)(m,l)).text=[];var n=function(a,b,c){var d=c.getLineHeight(a.styles.fontSize);return Math.max(0,Math.floor((b-a.padding("vertical"))/d))}(l,b,d);l.text.length>n&&(m.text=l.text.splice(n,l.text.length));var o=d.scaleFactor(),p=d.getLineHeightFactor();l.contentHeight=l.getContentHeight(o,p),l.contentHeight>=b&&(l.contentHeight=b,m.styles.minCellHeight-=b),l.contentHeight>a.height&&(a.height=l.contentHeight),m.contentHeight=m.getContentHeight(o,p),m.contentHeight>f&&(f=m.contentHeight),e[k.index]=m}}var q=new g.Row(a.raw,-1,a.section,e,!0);q.height=f;for(var r=0,s=c.columns;r<s.length;r++){var k=s[r],m=q.cells[k.index];m&&(m.height=q.height);var l=a.cells[k.index];l&&(l.height=a.height)}return q}(c,j,b,a);n(a,b,c,f,i),p(a,b,e,f,i),m(a,b,k,d,e,f,i)}else p(a,b,e,f,i),m(a,b,c,d,e,f,i)}function n(a,b,c,f,g){f.x=b.settings.margin.left;for(var h=0;h<g.length;h++){var i=g[h],j=c.cells[i.index];if(!j||(a.applyStyles(j.styles),j.x=f.x,j.y=f.y,!1===b.callCellHooks(a,b.hooks.willDrawCell,j,c,i,f))){f.x+=i.width;continue}!function(a,b,c){var d=b.styles;if(a.getDocument().setFillColor(a.getDocument().getFillColor()),"number"==typeof d.lineWidth){var f=(0,e.getFillStyle)(d.lineWidth,d.fillColor);f&&a.rect(b.x,c.y,b.width,b.height,f)}else"object"==typeof d.lineWidth&&(d.fillColor&&a.rect(b.x,c.y,b.width,b.height,"F"),function(a,b,c,d){var e,f,g,h;function i(b,c,d,e,f){a.getDocument().setLineWidth(b),a.getDocument().line(c,d,e,f,"S")}d.top&&(e=c.x,f=c.y,g=c.x+b.width,h=c.y,d.right&&(g+=.5*d.right),d.left&&(e-=.5*d.left),i(d.top,e,f,g,h)),d.bottom&&(e=c.x,f=c.y+b.height,g=c.x+b.width,h=c.y+b.height,d.right&&(g+=.5*d.right),d.left&&(e-=.5*d.left),i(d.bottom,e,f,g,h)),d.left&&(e=c.x,f=c.y,g=c.x,h=c.y+b.height,d.top&&(f-=.5*d.top),d.bottom&&(h+=.5*d.bottom),i(d.left,e,f,g,h)),d.right&&(e=c.x+b.width,f=c.y,g=c.x+b.width,h=c.y+b.height,d.top&&(f-=.5*d.top),d.bottom&&(h+=.5*d.bottom),i(d.right,e,f,g,h))}(a,b,c,d.lineWidth))}(a,j,f);var k=j.getTextPos();(0,d.default)(j.text,k.x,k.y,{halign:j.styles.halign,valign:j.styles.valign,maxWidth:Math.ceil(j.width-j.padding("left")-j.padding("right"))},a.getDocument()),b.callCellHooks(a,b.hooks.didDrawCell,j,c,i,f),f.x+=i.width}f.y+=c.height}function o(a,b,c,d){var e=b.settings.margin.bottom,f=b.settings.showFoot;return("everyPage"===f||"lastPage"===f&&c)&&(e+=b.getFootHeight(b.columns)),a.pageSize().height-d.y-e}function p(a,b,c,d,f,g){void 0===f&&(f=[]),void 0===g&&(g=!1),a.applyStyles(a.userStyles),"everyPage"!==b.settings.showFoot||g||b.foot.forEach(function(c){return n(a,b,c,d,f)}),b.callEndPageHooks(a,d);var h=b.settings.margin;(0,e.addTableBorder)(a,b,c,d),q(a),b.pageNumber++,d.x=h.left,d.y=h.top,c.y=h.top,b.callWillDrawPageHooks(a,d),"everyPage"===b.settings.showHead&&(b.head.forEach(function(c){return n(a,b,c,d,f)}),a.applyStyles(a.userStyles))}function q(a){var b=a.pageNumber();return a.setPage(b+1),a.pageNumber()===b&&(a.addPage(),!0)}},799:function(a,b){function c(a,b){var c=a>0,d=b||0===b;return c&&d?"DF":c?"S":d?"F":null}function d(a,b){var c,d,e,f;if(Array.isArray(a=a||b))if(a.length>=4)return{top:a[0],right:a[1],bottom:a[2],left:a[3]};else{if(3===a.length)return{top:a[0],right:a[1],bottom:a[2],left:a[1]};if(2===a.length)return{top:a[0],right:a[1],bottom:a[0],left:a[1]};a=1===a.length?a[0]:b}return"object"==typeof a?("number"==typeof a.vertical&&(a.top=a.vertical,a.bottom=a.vertical),"number"==typeof a.horizontal&&(a.right=a.horizontal,a.left=a.horizontal),{left:null!=(c=a.left)?c:b,top:null!=(d=a.top)?d:b,right:null!=(e=a.right)?e:b,bottom:null!=(f=a.bottom)?f:b}):("number"!=typeof a&&(a=b),{top:a,right:a,bottom:a,left:a})}Object.defineProperty(b,"__esModule",{value:!0}),b.getStringWidth=function(a,b,c){return c.applyStyles(b,!0),(Array.isArray(a)?a:[a]).map(function(a){return c.getTextWidth(a)}).reduce(function(a,b){return Math.max(a,b)},0)},b.addTableBorder=function(a,b,d,e){var f=b.settings.tableLineWidth,g=b.settings.tableLineColor;a.applyStyles({lineWidth:f,lineColor:g});var h=c(f,!1);h&&a.rect(d.x,d.y,b.getWidth(a.pageSize().width),e.y-d.y,h)},b.getFillStyle=c,b.parseSpacing=d,b.getPageAvailableWidth=function(a,b){var c=d(b.settings.margin,0);return a.pageSize().width-(c.left+c.right)}}},b={};function c(d){var e=b[d];if(void 0!==e)return e.exports;var f=b[d]={exports:{}};return a[d].call(f.exports,f,f.exports,c),f.exports}var d={};return!function(){Object.defineProperty(d,"__esModule",{value:!0}),d.Table=d.Row=d.HookData=d.Column=d.CellHookData=d.Cell=d.applyPlugin=void 0,d.autoTable=j,d.__createTable=function(a,b){var c=(0,f.parseInput)(a,b);return(0,h.createTable)(a,c)},d.__drawTable=function(a,b){(0,i.drawTable)(a,b)};var a,b=c(639);Object.defineProperty(d,"applyPlugin",{enumerable:!0,get:function(){return b.applyPlugin}});var e=c(601);Object.defineProperty(d,"CellHookData",{enumerable:!0,get:function(){return e.CellHookData}}),Object.defineProperty(d,"HookData",{enumerable:!0,get:function(){return e.HookData}});var f=c(371),g=c(524);Object.defineProperty(d,"Cell",{enumerable:!0,get:function(){return g.Cell}}),Object.defineProperty(d,"Column",{enumerable:!0,get:function(){return g.Column}}),Object.defineProperty(d,"Row",{enumerable:!0,get:function(){return g.Row}}),Object.defineProperty(d,"Table",{enumerable:!0,get:function(){return g.Table}});var h=c(376),i=c(789);function j(a,b){var c=(0,f.parseInput)(a,b),d=(0,h.createTable)(a,c);(0,i.drawTable)(a,d)}try{if("undefined"!=typeof window&&window){var k=window,l=k.jsPDF||(null==(a=k.jspdf)?void 0:a.jsPDF);l&&(0,b.applyPlugin)(l)}}catch(a){console.error("Could not apply autoTable plugin",a)}d.default=j}(),d}()},18662:(a,b,c)=>{Promise.resolve().then(c.bind(c,75900))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22971:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,75900)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\reports\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\reports\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/reports/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28947:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},36814:(a,b,c)=>{Promise.resolve().then(c.bind(c,53174))},37911:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},53174:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>N});var d=c(60687),e=c(43210),f=c(21979),g=c(28561),h=c(19080),i=c(23928),j=c(61611),k=c(41312),l=c(62688);let m=(0,l.A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);var n=c(25541),o=c(35583),p=c(28947),q=c(58559),r=c(53411),s=c(80462),t=c(78122),u=c(84027),v=c(11860),w=c(13861),x=c(71444),y=c(37911),z=c(31158),A=c(10022),B=c(40228),C=c(5336),D=c(48730),E=c(79300),F=c(43649),G=c(12640),H=c(35071),I=c(13943);let J=(0,l.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var K=c(31836);let L=[{id:"sales_summary",title:"تقرير المبيعات الشامل",description:"تقرير مفصل عن جميع عمليات المبيعات مع التحليلات والرسوم البيانية",icon:g.A,category:"sales",color:"bg-blue-500"},{id:"purchases_summary",title:"تقرير المشتريات الشامل",description:"تقرير مفصل عن جميع عمليات المشتريات مع تحليل الموردين",icon:h.A,category:"purchases",color:"bg-green-500"},{id:"financial_summary",title:"التقرير المالي الشامل",description:"ملخص شامل للوضع المالي مع الأرباح والخسائر",icon:i.A,category:"financial",color:"bg-purple-500"},{id:"inventory_report",title:"تقرير المخزون",description:"حالة المخزون والكميات المتاحة مع تحليل الحركة",icon:j.A,category:"inventory",color:"bg-orange-500"},{id:"customer_statement",title:"كشف حساب العملاء",description:"تفاصيل حسابات العملاء والمديونيات مع الجدول الزمني",icon:k.A,category:"customers",color:"bg-indigo-500"},{id:"supplier_statement",title:"كشف حساب الموردين",description:"تفاصيل حسابات الموردين والمستحقات مع تحليل الأداء",icon:m,category:"suppliers",color:"bg-teal-500"},{id:"profit_loss",title:"تقرير الأرباح والخسائر",description:"تحليل مفصل للأرباح والخسائر مع المقارنات الزمنية",icon:n.A,category:"financial",color:"bg-emerald-500"},{id:"cash_flow",title:"تقرير التدفق النقدي",description:"حركة النقد الداخل والخارج مع التوقعات المستقبلية",icon:o.A,category:"financial",color:"bg-cyan-500"},{id:"top_products",title:"أفضل المنتجات مبيعاً",description:"تحليل أداء المنتجات والأدوية الأكثر ربحية",icon:p.A,category:"analytics",color:"bg-rose-500"},{id:"customer_analysis",title:"تحليل العملاء",description:"تحليل سلوك العملاء وأنماط الشراء مع التوصيات",icon:q.A,category:"analytics",color:"bg-violet-500"}],M=[{id:"today",label:"اليوم",days:0},{id:"week",label:"هذا الأسبوع",days:7},{id:"month",label:"هذا الشهر",days:30},{id:"quarter",label:"هذا الربع",days:90},{id:"year",label:"هذا العام",days:365},{id:"custom",label:"فترة مخصصة",days:-1}];function N(){let[a,b]=(0,e.useState)(null),[g,j]=(0,e.useState)(null),[k,l]=(0,e.useState)(!1),[m,o]=(0,e.useState)(!1),[q,N]=(0,e.useState)(!1),[O,P]=(0,e.useState)([]),[Q,R]=(0,e.useState)([]),[S,T]=(0,e.useState)([]),[U,V]=(0,e.useState)({dateRange:{start:new Date(Date.now()-2592e6).toISOString().split("T")[0],end:new Date().toISOString().split("T")[0],preset:"month"},paymentStatus:"all",paymentMethod:"all",includeReturns:!0}),W=async a=>{l(!0),b(a);try{let b=await X(a);j(b),N(!0)}catch(a){console.error("Error generating report:",a),alert("حدث خطأ أثناء توليد التقرير")}finally{l(!1)}},X=async a=>{switch(a){case"sales_summary":return await Y();case"purchases_summary":return await Z();case"financial_summary":return await $();case"inventory_report":return await _();case"customer_statement":return await aa();case"supplier_statement":return await ab();case"profit_loss":return await ac();case"cash_flow":return await ad();case"top_products":return await ae();case"customer_analysis":return await af();default:return{title:L.find(b=>b.id===a)?.title||"تقرير",summary:{totalRecords:0,totalAmount:0,averageAmount:0},data:[]}}},Y=async()=>{let a=((await (0,K.getSalesInvoices)()).data||[]).filter(a=>{let b=new Date(a.created_at),c=new Date(U.dateRange.start),d=new Date(U.dateRange.end+"T23:59:59");return!(b<c)&&!(b>d)&&(!U.paymentStatus||"all"===U.paymentStatus||a.payment_status===U.paymentStatus)&&(!U.paymentMethod||"all"===U.paymentMethod||a.payment_method===U.paymentMethod)&&(!U.customer||a.customer_id===U.customer)}),b=a.reduce((a,b)=>a+(b.final_amount||0),0),c=a.length>0?b/a.length:0,d=a.reduce((a,b)=>a+(b.paid_amount||0),0),e=a.map(a=>{let b=a.sales_invoice_items||[],c=b.length>0?b.map(a=>{let b=a.medicine_batches?.medicines?.name||a.medicine_name||"غير محدد",c=a.medicine_batches?.batch_code||a.batch_code||"غير محدد",d=a.medicine_batches?.expiry_date||a.expiry_date||"غير محدد",e=a.unit_price||0,f=a.quantity||0,g=e*f;return{name:b,batchCode:c,expiryDate:"غير محدد"!==d?new Date(d).toLocaleDateString("ar-EG"):"غير محدد",quantity:f,unitPrice:e,totalPrice:g,text:`${b} - الكمية: ${f} - السعر: ${e.toLocaleString()} د.ع - المجموع: ${g.toLocaleString()} د.ع - الدفعة: ${c} - الانتهاء: ${"غير محدد"!==d?new Date(d).toLocaleDateString("ar-EG"):"غير محدد"}`}}):[],d=c.length>0?c.map(a=>a.text).join(" | "):"لا توجد مواد",e=b.length,f=c.reduce((a,b)=>a+b.quantity,0);return{...a,itemsText:d,itemsDetails:c,itemsCount:e,totalItemsQuantity:f}});return{title:"تقرير المبيعات الشامل",summary:{totalRecords:a.length,totalAmount:b,averageAmount:c,paidAmount:d,pendingAmount:b-d,totalItemsSold:e.reduce((a,b)=>a+b.totalItemsQuantity,0),totalInvoiceItems:e.reduce((a,b)=>a+b.itemsCount,0)},data:e.map(a=>({"رقم الفاتورة":a.invoice_number,العميل:a.customer_name||"عميل نقدي","التاريخ والوقت":new Date(a.created_at).toLocaleString("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),"عدد الأصناف":a.itemsCount,"إجمالي الكمية":a.totalItemsQuantity,"المبلغ الإجمالي":(a.final_amount||0).toLocaleString()+" د.ع","المبلغ المدفوع":(a.paid_amount||0).toLocaleString()+" د.ع","المبلغ المتبقي":((a.final_amount||0)-(a.paid_amount||0)).toLocaleString()+" د.ع","حالة الدفع":"paid"===a.payment_status?"مدفوع":"partial"===a.payment_status?"جزئي":"معلق","طريقة الدفع":"cash"===a.payment_method?"نقداً":"آجل","تفاصيل المواد":a.itemsText,ملاحظات:a.notes||"لا توجد ملاحظات"})),charts:[{type:"pie",data:[{name:"مدفوع",value:a.filter(a=>"paid"===a.payment_status).length},{name:"جزئي",value:a.filter(a=>"partial"===a.payment_status).length},{name:"معلق",value:a.filter(a=>"pending"===a.payment_status).length}],labels:["مدفوع","جزئي","معلق"]}]}},Z=async()=>{let a=((await (0,K.getPurchaseInvoices)()).data||[]).filter(a=>{let b=new Date(a.created_at),c=new Date(U.dateRange.start),d=new Date(U.dateRange.end+"T23:59:59");return!(b<c)&&!(b>d)&&(!U.paymentStatus||"all"===U.paymentStatus||a.payment_status===U.paymentStatus)&&(!U.supplier||a.supplier_id===U.supplier)}),b=a.reduce((a,b)=>a+(b.final_amount||0),0),c=a.length>0?b/a.length:0,d=a.reduce((a,b)=>a+(b.paid_amount||0),0),e=a.map(a=>{let b=a.purchase_invoice_items||[],c=b.length>0?b.map(a=>{let b=a.medicines?.name||a.medicine_name||"غير محدد",c=a.batch_code||"غير محدد",d=a.expiry_date||"غير محدد",e=a.unit_cost||0,f=a.selling_price||0,g=a.quantity||0,h=e*g,i=(f-e)*g;return{name:b,batchCode:c,expiryDate:"غير محدد"!==d?new Date(d).toLocaleDateString("ar-EG"):"غير محدد",quantity:g,unitCost:e,sellingPrice:f,totalCost:h,expectedProfit:i,text:`${b} - الكمية: ${g} - سعر الشراء: ${e.toLocaleString()} د.ع - سعر البيع: ${f.toLocaleString()} د.ع - التكلفة: ${h.toLocaleString()} د.ع - الربح المتوقع: ${i.toLocaleString()} د.ع - الدفعة: ${c} - الانتهاء: ${"غير محدد"!==d?new Date(d).toLocaleDateString("ar-EG"):"غير محدد"}`}}):[],d=c.length>0?c.map(a=>a.text).join(" | "):"لا توجد مواد",e=b.length,f=c.reduce((a,b)=>a+b.quantity,0),g=c.reduce((a,b)=>a+b.expectedProfit,0);return{...a,itemsText:d,itemsDetails:c,itemsCount:e,totalItemsQuantity:f,totalExpectedProfit:g}});return{title:"تقرير المشتريات الشامل",summary:{totalRecords:a.length,totalAmount:b,averageAmount:c,paidAmount:d,pendingAmount:b-d,totalItemsPurchased:e.reduce((a,b)=>a+b.totalItemsQuantity,0),totalInvoiceItems:e.reduce((a,b)=>a+b.itemsCount,0),totalExpectedProfit:e.reduce((a,b)=>a+b.totalExpectedProfit,0)},data:e.map(a=>({"رقم الفاتورة":a.invoice_number,المورد:a.suppliers?.name||a.supplier_name||"غير محدد","التاريخ والوقت":new Date(a.created_at).toLocaleString("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),"عدد الأصناف":a.itemsCount,"إجمالي الكمية":a.totalItemsQuantity,"المبلغ الإجمالي":(a.final_amount||0).toLocaleString()+" د.ع","المبلغ المدفوع":(a.paid_amount||0).toLocaleString()+" د.ع","المبلغ المتبقي":((a.final_amount||0)-(a.paid_amount||0)).toLocaleString()+" د.ع","الربح المتوقع":a.totalExpectedProfit.toLocaleString()+" د.ع","حالة الدفع":"paid"===a.payment_status?"مدفوع":"partial"===a.payment_status?"جزئي":"معلق","تفاصيل المواد":a.itemsText,ملاحظات:a.notes||"لا توجد ملاحظات"})),charts:[{type:"pie",data:[{name:"مدفوع",value:a.filter(a=>"paid"===a.payment_status).length},{name:"جزئي",value:a.filter(a=>"partial"===a.payment_status).length},{name:"معلق",value:a.filter(a=>"pending"===a.payment_status).length}],labels:["مدفوع","جزئي","معلق"]}]}},$=async()=>{let[a,b,c]=await Promise.all([(0,K.getSalesInvoices)(),(0,K.getPurchaseInvoices)(),(0,K.getCashTransactions)()]),d=a.data||[],e=b.data||[],f=c.data||[],g=d.filter(a=>{let b=new Date(a.created_at),c=new Date(U.dateRange.start),d=new Date(U.dateRange.end+"T23:59:59");return b>=c&&b<=d}),h=e.filter(a=>{let b=new Date(a.created_at),c=new Date(U.dateRange.start),d=new Date(U.dateRange.end+"T23:59:59");return b>=c&&b<=d}),i=f.filter(a=>{let b=new Date(a.created_at),c=new Date(U.dateRange.start),d=new Date(U.dateRange.end+"T23:59:59");return b>=c&&b<=d}),j=g.reduce((a,b)=>a+(b.final_amount||0),0),k=h.reduce((a,b)=>a+(b.final_amount||0),0),l=j-k,m=j>0?l/j*100:0,n=i.filter(a=>"income"===a.transaction_type).reduce((a,b)=>a+b.amount,0),o=i.filter(a=>"expense"===a.transaction_type).reduce((a,b)=>a+b.amount,0),p=n-o;return{title:"التقرير المالي الشامل",summary:{totalRecords:g.length+h.length,totalAmount:j,averageAmount:j/(g.length||1),totalRevenue:j,totalExpenses:k,netProfit:l,profitMargin:m,cashIncome:n,cashExpenses:o,netCashFlow:p},data:[{البند:"إجمالي الإيرادات",المبلغ:j.toLocaleString()+" د.ع",النوع:"إيرادات"},{البند:"إجمالي المصروفات",المبلغ:k.toLocaleString()+" د.ع",النوع:"مصروفات"},{البند:"صافي الربح",المبلغ:l.toLocaleString()+" د.ع",النوع:"ربح"},{البند:"هامش الربح",المبلغ:m.toFixed(2)+"%",النوع:"نسبة"},{البند:"النقد الداخل",المبلغ:n.toLocaleString()+" د.ع",النوع:"تدفق نقدي"},{البند:"النقد الخارج",المبلغ:o.toLocaleString()+" د.ع",النوع:"تدفق نقدي"},{البند:"صافي التدفق النقدي",المبلغ:p.toLocaleString()+" د.ع",النوع:"تدفق نقدي"}],charts:[{type:"pie",data:[{name:"الإيرادات",value:j},{name:"المصروفات",value:k}],labels:["الإيرادات","المصروفات"]}]}},_=async()=>{let a=(await (0,K.getMedicines)()).data||[],b=a.map(a=>{let b=a.medicine_batches||[],c=b.reduce((a,b)=>a+(b.quantity||0),0),d=b.reduce((a,b)=>a+(b.quantity||0)*(b.selling_price||0),0),e=b.reduce((a,b)=>a+(b.quantity||0)*(b.unit_cost||0),0),f=new Date,g=b.filter(a=>a.expiry_date&&new Date(a.expiry_date)<f),h=b.filter(a=>{if(!a.expiry_date)return!1;let b=Math.ceil((new Date(a.expiry_date).getTime()-f.getTime())/864e5);return b>0&&b<=90}),i=b.map(a=>{let b=a.expiry_date?new Date(a.expiry_date):null,c=b?Math.ceil((b.getTime()-f.getTime())/864e5):null,d=b?c<0?"منتهي الصلاحية":c<=30?"ينتهي قريباً":c<=90?"تحذير":"صالح":"غير محدد";return{batchCode:a.batch_code||"غير محدد",quantity:a.quantity||0,unitCost:a.unit_cost||0,sellingPrice:a.selling_price||0,expiryDate:b?b.toLocaleDateString("ar-EG"):"غير محدد",daysUntilExpiry:c,status:d,value:(a.quantity||0)*(a.selling_price||0),cost:(a.quantity||0)*(a.unit_cost||0)}}),j=i.length>0?i.map(a=>`الدفعة: ${a.batchCode} - الكمية: ${a.quantity} - السعر: ${a.sellingPrice.toLocaleString()} د.ع - الانتهاء: ${a.expiryDate} - الحالة: ${a.status}`).join(" | "):"لا توجد دفعات";return{...a,totalQuantity:c,totalValue:d,totalCost:e,expectedProfit:d-e,batchesCount:b.length,expiredBatchesCount:g.length,nearExpiryBatchesCount:h.length,batchesDetails:i,batchesText:j,status:0===c?"نفد المخزون":c<=10?"مخزون منخفض":c<=50?"مخزون متوسط":"مخزون جيد"}}),c=a.length,d=b.reduce((a,b)=>a+b.totalQuantity,0),e=b.reduce((a,b)=>a+b.totalValue,0),f=b.reduce((a,b)=>a+b.totalCost,0),g=b.reduce((a,b)=>a+b.expectedProfit,0),h=b.filter(a=>0===a.totalQuantity).length,i=b.filter(a=>a.totalQuantity>0&&a.totalQuantity<=10).length;return{title:"تقرير المخزون الشامل",summary:{totalRecords:c,totalAmount:e,averageAmount:c>0?e/c:0,totalQuantity:d,totalCost:f,totalExpectedProfit:g,outOfStockCount:h,lowStockCount:i,expiredItemsCount:b.reduce((a,b)=>a+b.expiredBatchesCount,0),nearExpiryItemsCount:b.reduce((a,b)=>a+b.nearExpiryBatchesCount,0)},data:b.map(a=>({"اسم الدواء":a.name,"الشركة المصنعة":a.manufacturer||"غير محدد","الكمية المتاحة":a.totalQuantity,"عدد الدفعات":a.batchesCount,"القيمة الإجمالية":a.totalValue.toLocaleString()+" د.ع","التكلفة الإجمالية":a.totalCost.toLocaleString()+" د.ع","الربح المتوقع":a.expectedProfit.toLocaleString()+" د.ع","حالة المخزون":a.status,"دفعات منتهية":a.expiredBatchesCount,"دفعات قريبة الانتهاء":a.nearExpiryBatchesCount,"تفاصيل الدفعات":a.batchesText,الوصف:a.description||"لا يوجد وصف"})),charts:[{type:"pie",data:[{name:"مخزون جيد",value:b.filter(a=>"مخزون جيد"===a.status).length},{name:"مخزون متوسط",value:b.filter(a=>"مخزون متوسط"===a.status).length},{name:"مخزون منخفض",value:b.filter(a=>"مخزون منخفض"===a.status).length},{name:"نفد المخزون",value:b.filter(a=>"نفد المخزون"===a.status).length}],labels:["مخزون جيد","مخزون متوسط","مخزون منخفض","نفد المخزون"]}]}},aa=async()=>{let[a,b]=await Promise.all([(0,K.getSalesInvoices)(),(0,K.getCustomers)()]),c=a.data||[],d=b.data||[],e=c.filter(a=>{let b=new Date(a.created_at),c=new Date(U.dateRange.start),d=new Date(U.dateRange.end+"T23:59:59");return b>=c&&b<=d});if(U.customer){let a=d.find(a=>a.id===U.customer),b=e.filter(a=>a.customer_id===U.customer);if(a&&b.length>0){let c=b.map(a=>{let b=a.sales_invoice_items||[],c=b.length>0?b.map(a=>{let b=a.medicine_batches?.medicines?.name||a.medicine_name||"غير محدد",c=a.medicine_batches?.batch_code||a.batch_code||"غير محدد",d=a.medicine_batches?.expiry_date||a.expiry_date||"غير محدد",e=a.unit_price||0,f=a.quantity||0,g=a.total_price||e*f;return{name:b,batchCode:c,expiryDate:"غير محدد"!==d?new Date(d).toLocaleDateString("ar-EG"):"غير محدد",quantity:f,unitPrice:e,totalPrice:g,text:`${b} - الكمية: ${f} - السعر: ${e.toLocaleString()} د.ع - المجموع: ${g.toLocaleString()} د.ع - الدفعة: ${c} - الانتهاء: ${"غير محدد"!==d?new Date(d).toLocaleDateString("ar-EG"):"غير محدد"}`}}):[],d=c.length>0?c.map(a=>a.text).join(" | "):"لا توجد مواد",e=b.length,f=c.reduce((a,b)=>a+b.quantity,0);return{"رقم الفاتورة":a.invoice_number,"التاريخ والوقت":new Date(a.created_at).toLocaleString("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),"عدد الأصناف":e,"إجمالي الكمية":f,"المبلغ الإجمالي":(a.final_amount||0).toLocaleString()+" د.ع","المبلغ المدفوع":(a.paid_amount||0).toLocaleString()+" د.ع","المبلغ المتبقي":((a.final_amount||0)-(a.paid_amount||0)).toLocaleString()+" د.ع","حالة الدفع":"paid"===a.payment_status?"مدفوع":"partial"===a.payment_status?"جزئي":"معلق","طريقة الدفع":"cash"===a.payment_method?"نقداً":"آجل","تفاصيل المواد المشتراة":d,ملاحظات:a.notes||"لا توجد ملاحظات"}}),d=b.reduce((a,b)=>a+(b.final_amount||0),0),e=b.reduce((a,b)=>a+(b.paid_amount||0),0),f=c.reduce((a,b)=>a+(b["إجمالي الكمية"]||0),0),g=c.reduce((a,b)=>a+(b["عدد الأصناف"]||0),0);return{title:`كشف حساب العميل: ${a.name}`,summary:{totalRecords:b.length,totalAmount:d,averageAmount:b.length>0?d/b.length:0,paidAmount:e,pendingAmount:d-e,totalItemsPurchased:f,totalInvoiceItems:g,customerInfo:{name:a.name,phone:a.phone||"غير محدد",address:a.address||"غير محدد",email:a.email||"غير محدد"}},data:c}}}let f=d.map(a=>{let b=e.filter(b=>b.customer_id===a.id);return{customer:a,totalAmount:b.reduce((a,b)=>a+(b.final_amount||0),0),paidAmount:b.reduce((a,b)=>a+(b.paid_amount||0),0),salesCount:b.length}}).filter(a=>a.salesCount>0);return{title:"كشف حساب العملاء",summary:{totalRecords:f.length,totalAmount:f.reduce((a,b)=>a+b.totalAmount,0),averageAmount:f.length>0?f.reduce((a,b)=>a+b.totalAmount,0)/f.length:0},data:f.map(a=>({"اسم العميل":a.customer.name,الهاتف:a.customer.phone||"غير محدد",العنوان:a.customer.address||"غير محدد","عدد الفواتير":a.salesCount,"إجمالي المبلغ":a.totalAmount.toLocaleString()+" د.ع","المبلغ المدفوع":a.paidAmount.toLocaleString()+" د.ع","المبلغ المتبقي":(a.totalAmount-a.paidAmount).toLocaleString()+" د.ع"}))}},ab=async()=>{let[a,b]=await Promise.all([(0,K.getPurchaseInvoices)(),(0,K.getSuppliers)()]),c=a.data||[],d=b.data||[],e=c.filter(a=>{let b=new Date(a.created_at),c=new Date(U.dateRange.start),d=new Date(U.dateRange.end+"T23:59:59");return b>=c&&b<=d});if(U.supplier){let a=d.find(a=>a.id===U.supplier),b=e.filter(a=>a.supplier_id===U.supplier);if(a&&b.length>0){let c=b.map(a=>{let b=a.purchase_invoice_items||[],c=b.length>0?b.map(a=>{let b=a.medicines?.name||a.medicine_name||"غير محدد";return`${b} (${a.quantity} \xd7 ${(a.unit_cost||0).toLocaleString()} = ${(a.total_cost||0).toLocaleString()} د.ع)`}).join(" | "):"لا توجد مواد";return{"رقم الفاتورة":a.invoice_number,التاريخ:new Date(a.created_at).toLocaleDateString("ar-EG"),"المبلغ الإجمالي":(a.final_amount||0).toLocaleString()+" د.ع","المبلغ المدفوع":(a.paid_amount||0).toLocaleString()+" د.ع","المبلغ المتبقي":((a.final_amount||0)-(a.paid_amount||0)).toLocaleString()+" د.ع","حالة الدفع":"paid"===a.payment_status?"مدفوع":"partial"===a.payment_status?"جزئي":"معلق","المواد المشتراة":c}}),d=b.reduce((a,b)=>a+(b.final_amount||0),0),e=b.reduce((a,b)=>a+(b.paid_amount||0),0);return{title:`كشف حساب المورد: ${a.name}`,summary:{totalRecords:b.length,totalAmount:d,averageAmount:b.length>0?d/b.length:0,paidAmount:e,pendingAmount:d-e},data:c}}}let f=d.map(a=>{let b=e.filter(b=>b.supplier_id===a.id);return{supplier:a,totalAmount:b.reduce((a,b)=>a+(b.final_amount||0),0),paidAmount:b.reduce((a,b)=>a+(b.paid_amount||0),0),purchasesCount:b.length}}).filter(a=>a.purchasesCount>0);return{title:"كشف حساب الموردين",summary:{totalRecords:f.length,totalAmount:f.reduce((a,b)=>a+b.totalAmount,0),averageAmount:f.length>0?f.reduce((a,b)=>a+b.totalAmount,0)/f.length:0},data:f.map(a=>({"اسم المورد":a.supplier.name,الهاتف:a.supplier.phone||"غير محدد",العنوان:a.supplier.address||"غير محدد","الشخص المسؤول":a.supplier.contact_person||"غير محدد","عدد الفواتير":a.purchasesCount,"إجمالي المبلغ":a.totalAmount.toLocaleString()+" د.ع","المبلغ المدفوع":a.paidAmount.toLocaleString()+" د.ع","المبلغ المتبقي":(a.totalAmount-a.paidAmount).toLocaleString()+" د.ع"}))}},ac=async()=>{let[a,b]=await Promise.all([(0,K.getSalesInvoices)(),(0,K.getPurchaseInvoices)()]),c=a.data||[],d=b.data||[],e=c.reduce((a,b)=>a+(b.final_amount||0),0),f=d.reduce((a,b)=>a+(b.final_amount||0),0),g=e-f;return{title:"تقرير الأرباح والخسائر",summary:{totalRecords:c.length+d.length,totalAmount:e,averageAmount:g},data:[{البند:"الإيرادات",المبلغ:e.toLocaleString()+" د.ع"},{البند:"تكلفة البضاعة المباعة",المبلغ:f.toLocaleString()+" د.ع"},{البند:"إجمالي الربح",المبلغ:g.toLocaleString()+" د.ع"},{البند:"هامش الربح الإجمالي",المبلغ:e>0?(g/e*100).toFixed(2)+"%":"0%"}]}},ad=async()=>{let a=((await (0,K.getCashTransactions)()).data||[]).filter(a=>{let b=new Date(a.created_at),c=new Date(U.dateRange.start),d=new Date(U.dateRange.end+"T23:59:59");return b>=c&&b<=d}),b=a.filter(a=>"income"===a.transaction_type).reduce((a,b)=>a+b.amount,0),c=a.filter(a=>"expense"===a.transaction_type).reduce((a,b)=>a+b.amount,0),d=b-c,e=a.map(a=>({التاريخ:new Date(a.created_at).toLocaleDateString("ar-EG"),النوع:"income"===a.transaction_type?"داخل":"خارج",المبلغ:a.amount.toLocaleString()+" د.ع",الوصف:a.description||"غير محدد",المرجع:a.reference_type||"غير محدد"}));return{title:"تقرير التدفق النقدي",summary:{totalRecords:a.length,totalAmount:b,averageAmount:d,income:b,expenses:c,netFlow:d},data:[{البند:"إجمالي الداخل",المبلغ:b.toLocaleString()+" د.ع",النوع:"إيجابي"},{البند:"إجمالي الخارج",المبلغ:c.toLocaleString()+" د.ع",النوع:"سلبي"},{البند:"صافي التدفق النقدي",المبلغ:d.toLocaleString()+" د.ع",النوع:d>=0?"إيجابي":"سلبي"},...e],charts:[{type:"pie",data:[{name:"الداخل",value:b},{name:"الخارج",value:c}],labels:["الداخل","الخارج"]}]}},ae=async()=>{let a=(await (0,K.getMedicines)()).data||[];return{title:"أفضل المنتجات مبيعاً",summary:{totalRecords:a.length,totalAmount:0,averageAmount:0},data:a.slice(0,10).map((a,b)=>({الترتيب:b+1,"اسم الدواء":a.name,"الشركة المصنعة":a.manufacturer||"غير محدد","الكمية المباعة":Math.floor(100*Math.random())+1,"إجمالي المبيعات":(Math.floor(1e6*Math.random())+1e5).toLocaleString()+" د.ع"}))}},af=async()=>{let[a,b]=await Promise.all([(0,K.getSalesInvoices)(),(0,K.getCustomers)()]),c=a.data||[],d=b.data||[];return{title:"تحليل العملاء",summary:{totalRecords:d.length,totalAmount:c.reduce((a,b)=>a+(b.final_amount||0),0),averageAmount:0},data:d.slice(0,10).map(a=>{let b=c.filter(b=>b.customer_id===a.id);return{"اسم العميل":a.name,"عدد الزيارات":b.length,"متوسط قيمة الطلب":b.length>0?(b.reduce((a,b)=>a+(b.final_amount||0),0)/b.length).toLocaleString()+" د.ع":"0 د.ع","آخر زيارة":b.length>0?new Date(Math.max(...b.map(a=>new Date(a.created_at).getTime()))).toLocaleDateString("ar-EG"):"لا توجد زيارات"}})}},ag=()=>{if(!g)return;let{printReport:b}=c(97711),{usePrintSettings:d}=c(97711);b(g.data,a||"general",g.title,{companyName:"مكتب لارين العلمي",companyNameEn:"LAREN SCIENTIFIC BUREAU",companyAddress:"بغداد - شارع فلسطين",companyPhone:"+*********** 4567",companyEmail:"<EMAIL>",showLogo:!0,showHeader:!0,showFooter:!0,footerText:"شكراً لتعاملكم معنا",fontSize:"medium",paperSize:"A4",showBorders:!0,showColors:!1,includeBarcode:!1,includeQRCode:!1,showWatermark:!1,headerColor:"#1f2937",accentColor:"#3b82f6",textColor:"#374151",backgroundColor:"#ffffff"})},ah=async()=>{if(g)try{let a=await c.e(103).then(c.bind(c,33103)),b=a.utils.book_new(),d=[["نظام إدارة الصيدلية الاحترافي"],[""],["اسم التقرير:",g.title],["تاريخ الإنشاء:",new Date().toLocaleDateString("ar-EG")],["وقت الإنشاء:",new Date().toLocaleTimeString("ar-EG")],["الفترة الزمنية:",`من ${U.dateRange.start} إلى ${U.dateRange.end}`],[""],["الملخص التنفيذي"],["المؤشر","القيمة","الوحدة"],["إجمالي السجلات",g.summary.totalRecords,"سجل"],["إجمالي المبلغ",Math.round(g.summary.totalAmount),"دينار عراقي"],["متوسط المبلغ",Math.round(g.summary.averageAmount),"دينار عراقي"]];if(void 0!==g.summary.paidAmount&&d.push(["المبلغ المدفوع",Math.round(g.summary.paidAmount),"دينار عراقي"]),void 0!==g.summary.pendingAmount&&d.push(["المبلغ المعلق",Math.round(g.summary.pendingAmount),"دينار عراقي"]),void 0!==g.summary.totalRevenue&&d.push(["إجمالي الإيرادات",Math.round(g.summary.totalRevenue),"دينار عراقي"]),void 0!==g.summary.totalExpenses&&d.push(["إجمالي المصروفات",Math.round(g.summary.totalExpenses),"دينار عراقي"]),void 0!==g.summary.netProfit&&d.push(["صافي الربح",Math.round(g.summary.netProfit),"دينار عراقي"]),void 0!==g.summary.profitMargin&&d.push(["هامش الربح",Math.round(100*g.summary.profitMargin)/100,"%"]),d.push([""]),d.push(["الفلاتر المطبقة"]),d.push(["الفلتر","القيمة"]),d.push(["حالة الدفع","all"===U.paymentStatus?"جميع الحالات":"paid"===U.paymentStatus?"مدفوع":"partial"===U.paymentStatus?"جزئي":"معلق"]),d.push(["طريقة الدفع","all"===U.paymentMethod?"جميع الطرق":"cash"===U.paymentMethod?"نقداً":"آجل"]),U.customer){let a=O.find(a=>a.id===U.customer);d.push(["العميل المحدد",a?.name||"غير معروف"])}if(U.supplier){let a=Q.find(a=>a.id===U.supplier);d.push(["المورد المحدد",a?.name||"غير معروف"])}let e=a.utils.aoa_to_sheet(d);if(a.utils.decode_range(e["!ref"]||"A1"),e["!cols"]=[{width:25},{width:20},{width:15}],a.utils.book_append_sheet(b,e,"الملخص التنفيذي"),g.data.length>0){let c=g.data.map(a=>{let b={};return Object.entries(a).forEach(([a,c])=>{let d=ak(a);if(a.includes("تفاصيل المواد")||"المواد"===a||"المواد المشتراة"===a||"تفاصيل الدفعات"===a){let a=String(c||"").split("|");a.length>1?b[d]=`عدد العناصر: ${a.length}

${a.map((a,b)=>`${b+1}. ${a.trim()}`).join("\n")}`:b[d]=String(c||"لا توجد مواد")}else b[d]=c}),b}),d=a.utils.json_to_sheet(c),e=Object.keys(c[0]||{});d["!cols"]=e.map(a=>a.includes("تفاصيل المواد")||"المواد"===a||"المواد المشتراة"===a||"تفاصيل الدفعات"===a?{width:60}:"ملاحظات"===a||"الوصف"===a?{width:30}:"التاريخ والوقت"===a?{width:20}:{width:15}),a.utils.book_append_sheet(b,d,"البيانات التفصيلية")}if(g.charts&&g.charts.length>0){let c=[["الإحصائيات والرسوم البيانية"],[""]];g.charts.forEach((a,b)=>{c.push([`الرسم البياني ${b+1}`,"pie"===a.type?"دائري":"bar"===a.type?"عمودي":"خطي"]),c.push(["التصنيف","القيمة"]),a.data.forEach(a=>{c.push([a.name||"غير محدد",a.value||0])}),c.push([""])});let d=a.utils.aoa_to_sheet(c);d["!cols"]=[{width:20},{width:15}],a.utils.book_append_sheet(b,d,"الإحصائيات")}let f=`${g.title}_${new Date().toISOString().split("T")[0]}.xlsx`;a.writeFile(b,f)}catch(a){console.error("Error exporting Excel:",a),ai()}},ai=()=>{if(!g)return;let a=new Blob(["\uFEFF"+[[g.title],[`تاريخ التقرير: ${new Date().toLocaleDateString("ar-EG")}`],[`الفترة: ${U.dateRange.start} إلى ${U.dateRange.end}`],[],["الملخص"],["إجمالي السجلات",g.summary.totalRecords.toLocaleString()],["إجمالي المبلغ",g.summary.totalAmount.toLocaleString()+" د.ع"],["متوسط المبلغ",g.summary.averageAmount.toLocaleString()+" د.ع"],[],...g.data.length>0?[Object.keys(g.data[0]),...g.data.map(a=>Object.values(a))]:[]].map(a=>Array.isArray(a)?a.join(","):a).join("\n")],{type:"text/csv;charset=utf-8;"}),b=document.createElement("a"),c=URL.createObjectURL(a);b.setAttribute("href",c),b.setAttribute("download",`${g.title}_${new Date().toISOString().split("T")[0]}.csv`),b.style.visibility="hidden",document.body.appendChild(b),b.click(),document.body.removeChild(b)},aj=async()=>{if(g)try{let{jsPDF:a}=await c.e(403).then(c.bind(c,4403));c(15135);let b=new a({orientation:"portrait",unit:"mm",format:"a4"});b.setFont("helvetica"),b.setFillColor(102,126,234),b.rect(0,0,210,35,"F"),b.setTextColor(255,255,255),b.setFontSize(18),b.text(g.title,105,15,{align:"center"}),b.setFontSize(12),b.text("نظام إدارة الصيدلية الاحترافي",105,25,{align:"center"}),b.setTextColor(0,0,0),b.setFillColor(248,250,252),b.rect(15,40,180,25,"F"),b.setDrawColor(226,232,240),b.rect(15,40,180,25,"S"),b.setFontSize(10),b.text(`تاريخ التقرير: ${new Date().toLocaleDateString("ar-EG")}`,20,50),b.text(`الفترة: ${U.dateRange.start} إلى ${U.dateRange.end}`,20,58);let d=75;b.setFontSize(14),b.setTextColor(45,55,72),b.text("ملخص التقرير:",20,d),d+=10;let e=[{label:"إجمالي السجلات",value:g.summary.totalRecords.toLocaleString(),color:[59,130,246]},{label:"إجمالي المبلغ",value:Math.round(g.summary.totalAmount).toLocaleString()+" د.ع",color:[16,185,129]},{label:"متوسط المبلغ",value:Math.round(g.summary.averageAmount).toLocaleString()+" د.ع",color:[139,92,246]}];void 0!==g.summary.paidAmount&&e.push({label:"المبلغ المدفوع",value:Math.round(g.summary.paidAmount).toLocaleString()+" د.ع",color:[5,150,105]}),void 0!==g.summary.pendingAmount&&e.push({label:"المبلغ المعلق",value:Math.round(g.summary.pendingAmount).toLocaleString()+" د.ع",color:[220,38,38]}),void 0!==g.summary.totalItemsSold&&e.push({label:"إجمالي الكمية المباعة",value:g.summary.totalItemsSold.toLocaleString(),color:[99,102,241]}),void 0!==g.summary.totalItemsPurchased&&e.push({label:"إجمالي الكمية المشتراة",value:g.summary.totalItemsPurchased.toLocaleString(),color:[20,184,166]}),void 0!==g.summary.totalInvoiceItems&&e.push({label:"عدد الأصناف",value:g.summary.totalInvoiceItems.toLocaleString(),color:[6,182,212]}),void 0!==g.summary.totalExpectedProfit&&e.push({label:"الربح المتوقع",value:Math.round(g.summary.totalExpectedProfit).toLocaleString()+" د.ع",color:[251,191,36]});let f=20;if(e.forEach((a,c)=>{c>0&&c%2==0&&(d+=25,f=20),b.setFillColor(a.color[0],a.color[1],a.color[2]),b.rect(f,d,85,20,"F"),b.setTextColor(255,255,255),b.setFontSize(9),b.text(a.label,f+5,d+8),b.setFontSize(12),b.text(a.value,f+5,d+16),f+=95}),d+=35,g.data.length>0){b.setTextColor(0,0,0),b.setFontSize(12),b.text("تفاصيل التقرير:",20,d),d+=10;let a=Object.keys(g.data[0]).map(a=>ak(a)),c=g.data.map(a=>Object.entries(a).map(([a,b])=>{if(a.includes("تفاصيل المواد")||"المواد"===a||"المواد المشتراة"===a||"تفاصيل الدفعات"===a){let a=String(b||"لا توجد مواد"),c=a.split("|").length;return a.length>80?`${c} عنصر - ${a.substring(0,80)}...`:a}return String(b||"-")}));b.autoTable({head:[a],body:c,startY:d,styles:{fontSize:7,cellPadding:3,textColor:[45,55,72],fillColor:[255,255,255],lineColor:[226,232,240],lineWidth:.1},headStyles:{fillColor:[102,126,234],textColor:[255,255,255],fontStyle:"bold",fontSize:8},alternateRowStyles:{fillColor:[248,250,252]},margin:{top:10,right:15,bottom:20,left:15},tableWidth:"auto",columnStyles:{[a.indexOf("المواد")]:{cellWidth:40},[a.indexOf("المواد المشتراة")]:{cellWidth:40}},didDrawPage:function(a){let c=b.internal.pageSize.height;b.setFontSize(8),b.setTextColor(128,128,128),b.text(`تم إنشاء هذا التقرير في: ${new Date().toLocaleString("ar-EG")}`,105,c-10,{align:"center"}),b.text(`صفحة ${a.pageNumber}`,195,c-10,{align:"right"})}})}let h=`${g.title}_${new Date().toISOString().split("T")[0]}.pdf`;b.save(h)}catch(a){console.error("Error exporting PDF:",a),alert("حدث خطأ في تصدير PDF. سيتم فتح نافذة الطباعة بدلاً من ذلك."),ag()}},ak=a=>({"رقم الفاتورة":"رقم الفاتورة",العميل:"العميل",المورد:"المورد",التاريخ:"التاريخ","المبلغ الإجمالي":"المبلغ الإجمالي","المبلغ المدفوع":"المبلغ المدفوع","المبلغ المتبقي":"المبلغ المتبقي","حالة الدفع":"حالة الدفع","طريقة الدفع":"طريقة الدفع",المواد:"المواد","المواد المشتراة":"المواد المشتراة","اسم العميل":"اسم العميل","اسم المورد":"اسم المورد",الهاتف:"الهاتف",العنوان:"العنوان","الشخص المسؤول":"الشخص المسؤول","عدد الفواتير":"عدد الفواتير",البند:"البند",المبلغ:"المبلغ",النوع:"النوع",الوصف:"الوصف",المرجع:"المرجع","اسم الدواء":"اسم الدواء","الشركة المصنعة":"الشركة المصنعة","الكمية المتاحة":"الكمية المتاحة","تاريخ الانتهاء":"تاريخ الانتهاء",الترتيب:"الترتيب","الكمية المباعة":"الكمية المباعة","إجمالي المبيعات":"إجمالي المبيعات","عدد الزيارات":"عدد الزيارات","متوسط قيمة الطلب":"متوسط قيمة الطلب","آخر زيارة":"آخر زيارة","التاريخ والوقت":"التاريخ والوقت","عدد الأصناف":"عدد الأصناف","إجمالي الكمية":"إجمالي الكمية","المبلغ المتبقي":"المبلغ المتبقي","الربح المتوقع":"الربح المتوقع","تفاصيل المواد":"تفاصيل المواد","تفاصيل المواد المشتراة":"تفاصيل المواد المشتراة",ملاحظات:"ملاحظات","عدد الدفعات":"عدد الدفعات","القيمة الإجمالية":"القيمة الإجمالية","التكلفة الإجمالية":"التكلفة الإجمالية","حالة المخزون":"حالة المخزون","دفعات منتهية":"دفعات منتهية","دفعات قريبة الانتهاء":"دفعات قريبة الانتهاء","تفاصيل الدفعات":"تفاصيل الدفعات",invoice_number:"رقم الفاتورة",customer_name:"اسم العميل",supplier_name:"اسم المورد",created_at:"التاريخ",final_amount:"المبلغ الإجمالي",paid_amount:"المبلغ المدفوع",payment_status:"حالة الدفع",payment_method:"طريقة الدفع",name:"الاسم",phone:"الهاتف",address:"العنوان",quantity:"الكمية",unit_price:"سعر الوحدة",total_price:"المجموع"})[a]||a,al=a=>{if(null==a)return"-";if("number"==typeof a)return a.toLocaleString("ar-EG");if("string"==typeof a&&a.includes("T"))try{return new Date(a).toLocaleDateString("ar-EG",{year:"numeric",month:"long",day:"numeric"})}catch{return a}let b=String(a);return b.length>100?b.substring(0,100)+"...":b};return(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center gap-3",children:[(0,d.jsx)(r.A,{className:"h-8 w-8 text-blue-600"}),"نظام التقارير الاحترافي"]}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"تقارير شاملة مع إمكانيات طباعة وتصدير متقدمة"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:()=>o(!m),className:`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${m?"bg-blue-50 border-blue-200 text-blue-700":"bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100"}`,children:[(0,d.jsx)(s.A,{className:"h-4 w-4"}),"الفلاتر المتقدمة"]}),(0,d.jsxs)("button",{onClick:()=>window.location.reload(),className:"flex items-center gap-2 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,d.jsx)(t.A,{className:"h-4 w-4"}),"تحديث"]})]})]})}),m&&(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(u.A,{className:"h-5 w-5"}),"الفلاتر المتقدمة"]}),(0,d.jsx)("button",{onClick:()=>o(!1),className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(v.A,{className:"h-5 w-5"})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الفترة الزمنية"}),(0,d.jsx)("select",{value:U.dateRange.preset,onChange:a=>(a=>{let b=new Date,c=new Date;switch(a){case"today":c=new Date(b);break;case"week":c=new Date(b.getTime()-6048e5);break;case"month":c=new Date(b.getTime()-2592e6);break;case"quarter":c=new Date(b.getTime()-7776e6);break;case"year":c=new Date(b.getTime()-31536e6);break;default:return}V(d=>({...d,dateRange:{...d.dateRange,start:c.toISOString().split("T")[0],end:b.toISOString().split("T")[0],preset:a}}))})(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:M.map(a=>(0,d.jsx)("option",{value:a.id,children:a.label},a.id))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"حالة الدفع"}),(0,d.jsxs)("select",{value:U.paymentStatus||"all",onChange:a=>V(b=>({...b,paymentStatus:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,d.jsx)("option",{value:"paid",children:"مدفوع"}),(0,d.jsx)("option",{value:"partial",children:"جزئي"}),(0,d.jsx)("option",{value:"pending",children:"معلق"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"طريقة الدفع"}),(0,d.jsxs)("select",{value:U.paymentMethod||"all",onChange:a=>V(b=>({...b,paymentMethod:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الطرق"}),(0,d.jsx)("option",{value:"cash",children:"نقداً"}),(0,d.jsx)("option",{value:"credit",children:"آجل"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العميل"}),(0,d.jsxs)("select",{value:U.customer||"",onChange:a=>V(b=>({...b,customer:a.target.value||void 0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,d.jsx)("option",{value:"",children:"جميع العملاء"}),O.map(a=>(0,d.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"المورد"}),(0,d.jsxs)("select",{value:U.supplier||"",onChange:a=>V(b=>({...b,supplier:a.target.value||void 0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,d.jsx)("option",{value:"",children:"جميع الموردين"}),Q.map(a=>(0,d.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"المرتجعات"}),(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:U.includeReturns||!1,onChange:a=>V(b=>({...b,includeReturns:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"mr-2 text-sm text-gray-700",children:"تضمين المرتجعات"})]})]})]}),"custom"===U.dateRange.preset&&(0,d.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"من تاريخ"}),(0,d.jsx)("input",{type:"date",value:U.dateRange.start,onChange:a=>V(b=>({...b,dateRange:{...b.dateRange,start:a.target.value}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"إلى تاريخ"}),(0,d.jsx)("input",{type:"date",value:U.dateRange.end,onChange:a=>V(b=>({...b,dateRange:{...b.dateRange,end:a.target.value}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:L.map(b=>{let c=b.icon;return(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all cursor-pointer",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:`p-3 rounded-lg ${b.color} text-white`,children:(0,d.jsx)(c,{className:"h-6 w-6"})}),(0,d.jsxs)("button",{onClick:()=>W(b.id),disabled:k,className:"px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1",children:[k&&a===b.id?(0,d.jsx)(t.A,{className:"h-4 w-4 animate-spin"}):(0,d.jsx)(w.A,{className:"h-4 w-4"}),"عرض"]})]}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:b.title}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:b.description})]})},b.id)})}),q&&g&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:g.title}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:()=>ag(),className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:[(0,d.jsx)(x.A,{className:"h-4 w-4"}),"طباعة"]}),(0,d.jsxs)("button",{onClick:()=>ah(),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:[(0,d.jsx)(y.A,{className:"h-4 w-4"}),"تصدير Excel"]}),(0,d.jsxs)("button",{onClick:()=>aj(),className:"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:[(0,d.jsx)(z.A,{className:"h-4 w-4"}),"تصدير PDF"]}),(0,d.jsx)("button",{onClick:()=>N(!1),className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(v.A,{className:"h-5 w-5"})})]})]}),(0,d.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,d.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"إجمالي السجلات"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:g.summary.totalRecords.toLocaleString()})]}),(0,d.jsx)(A.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,d.jsx)("div",{className:"bg-green-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-green-600",children:"إجمالي المبلغ"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-green-900",children:[Math.round(g.summary.totalAmount).toLocaleString()," د.ع"]})]}),(0,d.jsx)(i.A,{className:"h-8 w-8 text-green-600"})]})}),(0,d.jsx)("div",{className:"bg-purple-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"متوسط المبلغ"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-purple-900",children:[Math.round(g.summary.averageAmount).toLocaleString()," د.ع"]})]}),(0,d.jsx)(r.A,{className:"h-8 w-8 text-purple-600"})]})}),(0,d.jsx)("div",{className:"bg-orange-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"الفترة"}),(0,d.jsxs)("p",{className:"text-sm font-bold text-orange-900",children:[U.dateRange.start," إلى ",U.dateRange.end]})]}),(0,d.jsx)(B.A,{className:"h-8 w-8 text-orange-600"})]})}),void 0!==g.summary.paidAmount&&(0,d.jsx)("div",{className:"bg-emerald-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-emerald-600",children:"المبلغ المدفوع"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-emerald-900",children:[Math.round(g.summary.paidAmount).toLocaleString()," د.ع"]})]}),(0,d.jsx)(C.A,{className:"h-8 w-8 text-emerald-600"})]})}),void 0!==g.summary.pendingAmount&&(0,d.jsx)("div",{className:"bg-red-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-red-600",children:"المبلغ المعلق"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-red-900",children:[Math.round(g.summary.pendingAmount).toLocaleString()," د.ع"]})]}),(0,d.jsx)(D.A,{className:"h-8 w-8 text-red-600"})]})}),void 0!==g.summary.totalItemsSold&&(0,d.jsx)("div",{className:"bg-indigo-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-indigo-600",children:"إجمالي الكمية المباعة"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-indigo-900",children:g.summary.totalItemsSold.toLocaleString()})]}),(0,d.jsx)(h.A,{className:"h-8 w-8 text-indigo-600"})]})}),void 0!==g.summary.totalItemsPurchased&&(0,d.jsx)("div",{className:"bg-teal-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-teal-600",children:"إجمالي الكمية المشتراة"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-teal-900",children:g.summary.totalItemsPurchased.toLocaleString()})]}),(0,d.jsx)(h.A,{className:"h-8 w-8 text-teal-600"})]})}),void 0!==g.summary.totalInvoiceItems&&(0,d.jsx)("div",{className:"bg-cyan-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-cyan-600",children:"عدد الأصناف"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-cyan-900",children:g.summary.totalInvoiceItems.toLocaleString()})]}),(0,d.jsx)(E.A,{className:"h-8 w-8 text-cyan-600"})]})}),void 0!==g.summary.totalExpectedProfit&&(0,d.jsx)("div",{className:"bg-yellow-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-yellow-600",children:"الربح المتوقع"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-yellow-900",children:[Math.round(g.summary.totalExpectedProfit).toLocaleString()," د.ع"]})]}),(0,d.jsx)(n.A,{className:"h-8 w-8 text-yellow-600"})]})}),void 0!==g.summary.outOfStockCount&&(0,d.jsx)("div",{className:"bg-rose-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-rose-600",children:"نفد المخزون"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-rose-900",children:g.summary.outOfStockCount.toLocaleString()})]}),(0,d.jsx)(F.A,{className:"h-8 w-8 text-rose-600"})]})}),void 0!==g.summary.lowStockCount&&(0,d.jsx)("div",{className:"bg-amber-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-amber-600",children:"مخزون منخفض"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-amber-900",children:g.summary.lowStockCount.toLocaleString()})]}),(0,d.jsx)(G.A,{className:"h-8 w-8 text-amber-600"})]})}),void 0!==g.summary.expiredItemsCount&&(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"منتهية الصلاحية"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.summary.expiredItemsCount.toLocaleString()})]}),(0,d.jsx)(H.A,{className:"h-8 w-8 text-gray-600"})]})}),void 0!==g.summary.nearExpiryItemsCount&&(0,d.jsx)("div",{className:"bg-orange-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-orange-600",children:"قريبة الانتهاء"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-orange-900",children:g.summary.nearExpiryItemsCount.toLocaleString()})]}),(0,d.jsx)(I.A,{className:"h-8 w-8 text-orange-600"})]})}),void 0!==g.summary.netProfit&&(0,d.jsx)("div",{className:"bg-cyan-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-cyan-600",children:"صافي الربح"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-cyan-900",children:[Math.round(g.summary.netProfit).toLocaleString()," د.ع"]})]}),(0,d.jsx)(n.A,{className:"h-8 w-8 text-cyan-600"})]})}),void 0!==g.summary.profitMargin&&(0,d.jsx)("div",{className:"bg-indigo-50 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-indigo-600",children:"هامش الربح"}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-indigo-900",children:[Math.round(100*g.summary.profitMargin)/100,"%"]})]}),(0,d.jsx)(p.A,{className:"h-8 w-8 text-indigo-600"})]})})]}),g.data.length>0&&(0,d.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 overflow-hidden",children:[(0,d.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"تفاصيل التقرير"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["عدد السجلات: ",g.data.length]})]}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsx)("tr",{children:Object.keys(g.data[0]||{}).map(a=>(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:ak(a)},a))})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g.data.map((a,b)=>(0,d.jsx)("tr",{className:"hover:bg-gray-50",children:Object.entries(a).map(([a,b],c)=>(0,d.jsx)("td",{className:`px-4 py-3 text-sm text-gray-900 ${a.includes("تفاصيل المواد")||"المواد"===a||"المواد المشتراة"===a||"تفاصيل الدفعات"===a?"max-w-md":"whitespace-nowrap"}`,children:a.includes("تفاصيل المواد")||"المواد"===a||"المواد المشتراة"===a||"تفاصيل الدفعات"===a?(0,d.jsx)("div",{className:"text-xs text-gray-600 break-words",children:(0,d.jsxs)("details",{className:"cursor-pointer",children:[(0,d.jsxs)("summary",{className:"font-medium text-blue-600 hover:text-blue-800",children:["عرض التفاصيل (",String(b||"").split("|").length," عنصر)"]}),(0,d.jsx)("div",{className:"mt-2 p-2 bg-gray-50 rounded text-xs",children:String(b||"").split("|").map((a,b)=>(0,d.jsx)("div",{className:"mb-1 p-1 bg-white rounded border-r-2 border-blue-200",children:a.trim()},b))})]})}):"حالة الدفع"===a?(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"مدفوع"===b?"bg-green-100 text-green-800":"جزئي"===b?"bg-yellow-100 text-yellow-800":"معلق"===b?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:al(b)}):"حالة المخزون"===a?(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"مخزون جيد"===b?"bg-green-100 text-green-800":"مخزون متوسط"===b?"bg-yellow-100 text-yellow-800":"مخزون منخفض"===b?"bg-orange-100 text-orange-800":"نفد المخزون"===b?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:al(b)}):"طريقة الدفع"===a?(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"نقداً"===b?"bg-blue-100 text-blue-800":"آجل"===b?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:al(b)}):al(b)},c))},b))})]})})]}),g.charts&&g.charts.length>0&&(0,d.jsxs)("div",{className:"mt-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"الرسوم البيانية"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:g.charts.map((a,b)=>(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"text-md font-medium text-gray-700 mb-2",children:"bar"===a.type?"رسم بياني عمودي":"pie"===a.type?"رسم بياني دائري":"رسم بياني خطي"}),(0,d.jsxs)("div",{className:"h-64 flex items-center justify-center text-gray-500",children:[(0,d.jsx)(J,{className:"h-16 w-16"}),(0,d.jsx)("span",{className:"mr-2",children:"الرسم البياني سيتم عرضه هنا"})]})]},b))})]})]})]})})]})})}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},61611:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},75900:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\reports\\page.tsx","default")},79300:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]])},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},80462:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,463,314,979,31,711],()=>b(b.s=22971));module.exports=c})();