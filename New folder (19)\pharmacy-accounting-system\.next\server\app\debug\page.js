(()=>{var a={};a.id=302,a.ids=[302],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46479:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,48024)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/debug/page",pathname:"/debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/debug/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},48024:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\debug\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug\\page.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},60258:(a,b,c)=>{Promise.resolve().then(c.bind(c,48024))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68286:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h});var d=c(60687),e=c(43210),f=c(16391),g=c(31836);function h(){let[a,b]=(0,e.useState)([]),[c,h]=(0,e.useState)(!1),i=a=>{let c=new Date().toLocaleTimeString(),d=`[${c}] ${a}`;b(a=>[...a,d]),console.log(d)},j=()=>{b([])},k=async()=>{i("\uD83D\uDD04 اختبار اتصال Supabase...");try{let{data:a,error:b}=await f.N.from("medicines").select("id, name").limit(1);if(b)return i(`❌ خطأ في Supabase: ${b.message}`),i(`📋 تفاصيل الخطأ: ${JSON.stringify(b)}`),!1;return i(`✅ Supabase متصل بنجاح`),i(`📊 عدد الأدوية المسترجعة: ${a?.length||0}`),a&&a.length>0&&i(`📋 أول دواء: ${JSON.stringify(a[0])}`),!0}catch(a){return i(`❌ خطأ في الاتصال: ${a.message}`),!1}},l=()=>{i("\uD83D\uDD04 فحص بيانات localStorage..."),["medicines","medicine_batches","sales_invoices","sales_invoice_items","customers"].forEach(a=>{try{let b=localStorage.getItem(a);if(b){let c=JSON.parse(b);i(`📊 ${a}: ${c.length} عنصر`),"medicines"===a&&c.length>0&&i(`📋 أول دواء في localStorage: ${JSON.stringify(c[0])}`),"sales_invoices"===a&&c.length>0&&i(`📋 أول فاتورة في localStorage: ${JSON.stringify(c[0])}`)}else i(`📊 ${a}: لا توجد بيانات`)}catch(b){i(`❌ خطأ في قراءة ${a}: ${b.message}`)}})},m=async()=>{i("\uD83D\uDD04 اختبار إنشاء فاتورة...");try{let a={invoice_number:`DEBUG-${Date.now()}`,customer_name:"عميل تجريبي",total_amount:100,discount_amount:0,final_amount:100,payment_method:"cash",payment_status:"paid",notes:"فاتورة اختبار تشخيص"};i(`📋 بيانات الفاتورة: ${JSON.stringify(a)}`);let b=await (0,g.createSalesInvoice)(a);if(i(`📋 نتيجة إنشاء الفاتورة: ${JSON.stringify(b)}`),!b.success)return i(`❌ فشل في إنشاء الفاتورة: ${b.error?.message}`),null;{i(`✅ تم إنشاء الفاتورة بنجاح. ID: ${b.data?.id}`);let a=JSON.parse(localStorage.getItem("sales_invoices")||"[]");return i(`📊 عدد الفواتير في localStorage بعد الإنشاء: ${a.length}`),b.data}}catch(a){return i(`❌ خطأ في إنشاء الفاتورة: ${a.message}`),i(`📋 تفاصيل الخطأ: ${JSON.stringify(a)}`),null}},n=async()=>{i("\uD83D\uDD04 اختبار معاملة كاملة...");try{let a=await (0,g.getMedicines)();if(!a.success||!a.data||0===a.data.length)return i("❌ لا توجد أدوية متاحة للاختبار"),null;let b=a.data[0],c=b.batches?.[0]||b.medicine_batches?.[0];if(!c)return i("❌ لا توجد دفعات متاحة للاختبار"),null;i(`📦 استخدام الدواء: ${b.name}`),i(`📦 استخدام الدفعة: ${c.batch_code}`);let d={invoice_number:`COMPLETE-${Date.now()}`,customer_name:"عميل اختبار كامل",total_amount:1500,discount_amount:0,final_amount:1500,payment_method:"cash",payment_status:"paid",notes:"اختبار معاملة كاملة"},e=[{medicine_batch_id:c.id,quantity:2,unit_price:750,total_price:1500,is_gift:!1,medicine_name:b.name,medicineName:b.name}];i(`📋 بيانات المعاملة: ${JSON.stringify(d)}`),i(`📦 عناصر المعاملة: ${JSON.stringify(e)}`);let f=await (0,g.completeSalesTransaction)(d,e);if(i(`📋 نتيجة المعاملة الكاملة: ${JSON.stringify(f)}`),!f.success)return i(`❌ فشلت المعاملة الكاملة: ${f.error?.message}`),null;{i(`✅ تمت المعاملة الكاملة بنجاح`);let a=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),b=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]");return i(`📊 عدد الفواتير في localStorage: ${a.length}`),i(`📊 عدد العناصر في localStorage: ${b.length}`),f.data}}catch(a){return i(`❌ خطأ في المعاملة الكاملة: ${a.message}`),i(`📋 تفاصيل الخطأ: ${JSON.stringify(a)}`),null}},o=async()=>{i("\uD83D\uDD04 اختبار استرجاع الفواتير...");try{let a=await (0,g.getSalesInvoices)();if(i(`📋 نتيجة استرجاع الفواتير: ${JSON.stringify(a)}`),!a.success)return i(`❌ فشل في استرجاع الفواتير: ${a.error?.message}`),null;if(i(`✅ تم استرجاع ${a.data?.length||0} فاتورة`),a.data&&a.data.length>0){let b=a.data[0];i(`📋 أول فاتورة: ${JSON.stringify(b)}`);let c=b.sales_invoice_items||[];if(c.length>0){let a=c[0];i(`📋 أول عنصر: ${JSON.stringify(a)}`);let b=a.medicine_batches?.medicines?.name||a.medicine_name||a.medicineName;i(`🔍 اسم الدواء في أول عنصر: ${b||"غير متوفر"}`)}}return a.data}catch(a){return i(`❌ خطأ في استرجاع الفواتير: ${a.message}`),i(`📋 تفاصيل الخطأ: ${JSON.stringify(a)}`),null}},p=async()=>{i("\uD83D\uDD04 اختبار تحميل الأدوية...");try{let a=await (0,g.getMedicines)();if(i(`📋 نتيجة تحميل الأدوية: ${JSON.stringify(a)}`),!a.success)return i(`❌ فشل في تحميل الأدوية: ${a.error?.message}`),null;if(i(`✅ تم تحميل ${a.data?.length||0} دواء`),a.data&&a.data.length>0){let b=a.data[0];i(`📋 أول دواء: ${JSON.stringify(b)}`),b.batches&&b.batches.length>0&&(i(`📦 عدد الدفعات للدواء الأول: ${b.batches.length}`),i(`📦 أول دفعة: ${JSON.stringify(b.batches[0])}`))}return a.data}catch(a){return i(`❌ خطأ في تحميل الأدوية: ${a.message}`),i(`📋 تفاصيل الخطأ: ${JSON.stringify(a)}`),null}},q=async()=>{i("\uD83D\uDD04 اختبار بيانات الطباعة...");try{let a=await (0,g.getSalesInvoices)();if(!a.success||!a.data||0===a.data.length)return i("❌ لا توجد فواتير للاختبار"),null;let b=a.data[0];i(`📋 اختبار طباعة الفاتورة: ${b.invoice_number}`);let c=await (0,g.getSalesInvoiceForPrint)(b.id);if(i(`📋 نتيجة استرجاع بيانات الطباعة: ${JSON.stringify(c)}`),!c.success||!c.data)return i(`❌ فشل في استرجاع بيانات الطباعة: ${c.error?.message}`),null;{let a=c.data;i(`📋 بيانات الفاتورة للطباعة: ${JSON.stringify(a)}`);let b=a.sales_invoice_items||[];return i(`📦 عدد العناصر: ${b.length}`),b.forEach((a,b)=>{let c=a.medicine_batches?.medicines?.name||a.medicine_name||a.medicineName||"غير متوفر";i(`📦 العنصر ${b+1}: ${c}`),i(`📦 تفاصيل العنصر ${b+1}: ${JSON.stringify(a)}`)}),a}}catch(a){return i(`❌ خطأ في اختبار الطباعة: ${a.message}`),i(`📋 تفاصيل الخطأ: ${JSON.stringify(a)}`),null}},r=async()=>{i("\uD83D\uDD04 اختبار تهيئة البيانات...");try{let a=await (0,g.initializeSystemData)();if(i(`📋 نتيجة التهيئة: ${JSON.stringify(a)}`),!a.success)return i(`❌ فشلت تهيئة البيانات: ${a.error?.message}`),null;{i(`✅ تمت تهيئة البيانات بنجاح`);let b=JSON.parse(localStorage.getItem("medicines")||"[]"),c=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),d=JSON.parse(localStorage.getItem("customers")||"[]");return i(`📊 الأدوية: ${b.length}`),i(`📊 الدفعات: ${c.length}`),i(`📊 العملاء: ${d.length}`),a.data}}catch(a){return i(`❌ خطأ في تهيئة البيانات: ${a.message}`),null}},s=async()=>{h(!0),j(),i("\uD83D\uDE80 بدء التشخيص الشامل..."),await r(),await k(),l(),await p(),await m(),await o(),await n(),await q(),i("\uD83C\uDF89 انتهى التشخيص الشامل!"),h(!1)};return(0,d.jsx)("div",{className:"container mx-auto p-6",children:(0,d.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-6 text-center",children:"\uD83D\uDD0D صفحة التشخيص"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,d.jsx)("h2",{className:"text-xl font-bold mb-4",children:"أدوات التشخيص"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("button",{onClick:s,disabled:c,className:"w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:c?"جاري التشخيص...":"\uD83D\uDE80 تشخيص شامل"}),(0,d.jsx)("button",{onClick:r,disabled:c,className:"w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDD27 تهيئة البيانات"}),(0,d.jsx)("button",{onClick:k,disabled:c,className:"w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDD17 اختبار Supabase"}),(0,d.jsx)("button",{onClick:l,disabled:c,className:"w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDCBE فحص localStorage"}),(0,d.jsx)("button",{onClick:p,disabled:c,className:"w-full bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDC8A اختبار الأدوية"}),(0,d.jsx)("button",{onClick:n,disabled:c,className:"w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDD04 اختبار معاملة كاملة"}),(0,d.jsx)("button",{onClick:q,disabled:c,className:"w-full bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDDA8️ اختبار بيانات الطباعة"}),(0,d.jsx)("button",{onClick:()=>{["sales_invoices","sales_invoice_items","medicines","medicine_batches","customers"].forEach(a=>localStorage.removeItem(a)),i("\uD83D\uDDD1️ تم مسح جميع البيانات من localStorage")},className:"w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg",children:"\uD83D\uDDD1️ مسح localStorage"}),(0,d.jsx)("button",{onClick:j,className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"\uD83D\uDDD1️ مسح السجل"})]}),(0,d.jsx)("div",{className:"mt-6",children:(0,d.jsx)("a",{href:"/sales",className:"block w-full text-center bg-gray-700 hover:bg-gray-800 text-white px-4 py-2 rounded-lg",children:"← العودة إلى المبيعات"})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,d.jsx)("h2",{className:"text-xl font-bold mb-4",children:"سجل التشخيص"}),(0,d.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm",children:0===a.length?(0,d.jsx)("p",{className:"text-gray-500",children:"لم يتم تشغيل أي اختبار بعد..."}):a.map((a,b)=>(0,d.jsx)("div",{className:"mb-1",children:a},b))})]})]})]})})}},74075:a=>{"use strict";a.exports=require("zlib")},78410:(a,b,c)=>{Promise.resolve().then(c.bind(c,68286))},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,463,314,31],()=>b(b.s=46479));module.exports=c})();