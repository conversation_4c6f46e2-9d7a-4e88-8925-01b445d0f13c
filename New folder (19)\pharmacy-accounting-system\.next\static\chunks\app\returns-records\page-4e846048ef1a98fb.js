(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7977],{23389:(e,t,n)=>{Promise.resolve().then(n.bind(n,25855))},25855:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>_});var s=n(95155),a=n(12115),r=n(61932),l=n(40646),i=n(54861),d=n(14186),c=n(1243),o=n(40133),m=n(81304),x=n(64261),p=n(37108),u=n(53904),h=n(47924),g=n(57434),y=n(55868),v=n(69074),b=n(44020),f=n(92657),j=n(5196),N=n(54416),w=n(10988);function _(){var e,t,_,k,D,S,A,E,F,C,L,z,I;let[M,q]=(0,a.useState)([]),[R,O]=(0,a.useState)([]),[G,T]=(0,a.useState)(""),[B,P]=(0,a.useState)("all"),[H,J]=(0,a.useState)("all"),[U,V]=(0,a.useState)(null),[Z,Y]=(0,a.useState)(!1),[K,Q]=(0,a.useState)(!0),[W,X]=(0,a.useState)(null),[$,ee]=(0,a.useState)(null);(0,a.useEffect)(()=>{en()},[]),(0,a.useEffect)(()=>{es()},[M,G,B,H]);let et=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&(localStorage.removeItem("sales_returns"),localStorage.removeItem("purchase_returns"),localStorage.removeItem("customers"),localStorage.removeItem("suppliers"));let t=[{id:"sr-001",return_number:"SR-2024-001",customer_id:"cust-001",customer_name:"أحمد محمد علي",total_amount:15e4,status:"pending",reason:"دواء منتهي الصلاحية",created_at:new Date().toISOString(),return_items:[{id:"sri-001",medicine_name:"باراسيتامول 500 مجم",quantity:2,unit_price:25e3,total_price:5e4,reason:"منتهي الصلاحية",expiry_date:"2024-01-15"},{id:"sri-002",medicine_name:"أموكسيسيلين 250 مجم",quantity:4,unit_price:25e3,total_price:1e5,reason:"عيب في التصنيع",expiry_date:"2024-06-20"}]},{id:"sr-002",return_number:"SR-2024-002",customer_id:"cust-002",customer_name:"فاطمة أحمد حسن",total_amount:75e3,status:"approved",reason:"رد فعل تحسسي",created_at:new Date(Date.now()-864e5).toISOString(),return_items:[{id:"sri-003",medicine_name:"إيبوبروفين 400 مجم",quantity:3,unit_price:25e3,total_price:75e3,reason:"رد فعل تحسسي",expiry_date:"2024-12-30"}]}],n=[{id:"pr-001",return_number:"PR-2024-001",supplier_id:"supp-001",supplier_name:"شركة الأدوية المتحدة",total_amount:5e5,status:"pending",reason:"شحنة تالفة",created_at:new Date(Date.now()-1728e5).toISOString(),return_items:[{id:"pri-001",medicine_name:"أسبرين 100 مجم",quantity:10,unit_cost:2e4,total_cost:2e5,reason:"عبوات مكسورة",expiry_date:"2025-03-15"},{id:"pri-002",medicine_name:"فيتامين د 1000 وحدة",quantity:15,unit_cost:2e4,total_cost:3e5,reason:"تاريخ انتهاء قريب",expiry_date:"2024-02-28"}]}];localStorage.setItem("sales_returns",JSON.stringify(t)),localStorage.setItem("purchase_returns",JSON.stringify(n)),localStorage.setItem("customers",JSON.stringify([{id:"cust-001",name:"أحمد محمد علي",phone:"07901234567",address:"بغداد - الكرادة"},{id:"cust-002",name:"فاطمة أحمد حسن",phone:"07907654321",address:"بغداد - المنصور"}])),localStorage.setItem("suppliers",JSON.stringify([{id:"supp-001",name:"شركة الأدوية المتحدة",phone:"07801234567",address:"بغداد - المنطقة الصناعية"}])),console.log("Sample returns data created:",{salesReturns:t.length,purchaseReturns:n.length,salesItems:t.reduce((e,t)=>{var n;return e+((null==(n=t.return_items)?void 0:n.length)||0)},0),purchaseItems:n.reduce((e,t)=>{var n;return e+((null==(n=t.return_items)?void 0:n.length)||0)},0)})},en=async()=>{try{Q(!0);let e=localStorage.getItem("sales_returns");e&&0!==JSON.parse(e).length||et();let t=await (0,w.getReturns)();t.success&&t.data&&(console.log("Loaded return records:",t.data),t.data.forEach((e,t)=>{var n;console.log("Return ".concat(t+1," (").concat(e.return_number,"):"),{id:e.id,return_items:e.return_items,items_count:(null==(n=e.return_items)?void 0:n.length)||0})}),q(t.data))}catch(e){console.error("Error loading return records:",e)}finally{Q(!1)}},es=()=>{let e=M;G&&(e=e.filter(e=>{var t,n,s,a,r,l,i;return e.return_number.toLowerCase().includes(G.toLowerCase())||(null==(t=e.customer_name)?void 0:t.toLowerCase().includes(G.toLowerCase()))||(null==(n=e.supplier_name)?void 0:n.toLowerCase().includes(G.toLowerCase()))||(null==(a=e.customers)||null==(s=a.name)?void 0:s.toLowerCase().includes(G.toLowerCase()))||(null==(l=e.suppliers)||null==(r=l.name)?void 0:r.toLowerCase().includes(G.toLowerCase()))||(null==(i=e.reason)?void 0:i.toLowerCase().includes(G.toLowerCase()))})),"all"!==B&&(e=e.filter(e=>e.return_type===B)),"all"!==H&&(e=e.filter(e=>e.status===H)),O(e)},ea=async e=>{try{let t=e,n=await (0,w.getReturnById)(e.id);n.success&&n.data&&(t=n.data,console.log("Full record for details:",t)),V(t),Y(!0),X(null)}catch(t){console.error("Error loading return details:",t),V(e),Y(!0),X(null)}},er=async e=>{try{ee(e.id),(await (0,w.updateReturn)(e.id,{status:"approved"})).success?(await en(),alert("تم قبول المرتجع بنجاح")):alert("حدث خطأ أثناء قبول المرتجع")}catch(e){console.error("Error approving return:",e),alert("حدث خطأ أثناء قبول المرتجع")}finally{ee(null),X(null)}},el=async e=>{let t=prompt("يرجى إدخال سبب رفض المرتجع:");if(t)try{ee(e.id),(await (0,w.updateReturn)(e.id,{status:"rejected",rejection_reason:t})).success?(await en(),alert("تم رفض المرتجع")):alert("حدث خطأ أثناء رفض المرتجع")}catch(e){console.error("Error rejecting return:",e),alert("حدث خطأ أثناء رفض المرتجع")}finally{ee(null),X(null)}},ei=async e=>{console.log("Printing return record with Laren template:",e);let t=e;try{let n=await (0,w.getReturnById)(e.id);n.success&&n.data&&(t=n.data,console.log("Full return record with items:",t))}catch(e){console.error("Error fetching full return details:",e)}let{generateLarenReturnHTML:s}=n(80214),a=window.open("","_blank");if(!a)return;let r=s(t,{companyName:"مكتب لارين العلمي",companyNameEn:"LAREN SCIENTIFIC BUREAU",companyAddress:"بغداد - شارع فلسطين",companyPhone:"+*********** 4567",companyEmail:"<EMAIL>",footerText:"شكراً لتعاملكم معنا"});a.document.write(r),a.document.close(),a.focus(),setTimeout(()=>{a.print()},500)},ed=async()=>{try{let e=await Promise.all([n.e(3524),n.e(8436)]).then(n.bind(n,3925)),t=e.utils.book_new(),s=[["تقرير المرتجعات الشامل"],["نظام إدارة الصيدلية"],[""],["تاريخ التقرير:",new Date().toLocaleDateString("ar-EG")],["وقت التقرير:",new Date().toLocaleTimeString("ar-EG")],[""],["الملخص الإحصائي"],["المؤشر","العدد"],["إجمالي المرتجعات",R.length],["في الانتظار",R.filter(e=>"pending"===e.status).length],["مقبولة",R.filter(e=>"approved"===e.status).length],["مرفوضة",R.filter(e=>"rejected"===e.status).length]],a=e.utils.aoa_to_sheet(s);e.utils.book_append_sheet(t,a,"الملخص");let r=R.map(e=>{var t,n,s,a,r,l;return{"رقم المرتجع":e.return_number,النوع:"sales"===e.return_type?"مرتجع مبيعات":"مرتجع مشتريات","العميل/المورد":"sales"===e.return_type?(null==(t=e.customers)?void 0:t.name)||e.customer_name||"غير محدد":(null==(n=e.suppliers)?void 0:n.name)||e.supplier_name||"غير محدد","المبلغ الإجمالي":e.total_amount,الحالة:"approved"===e.status?"مقبول":"rejected"===e.status?"مرفوض":"في الانتظار","سبب المرتجع":e.reason||"غير محدد",التاريخ:new Date(e.created_at).toLocaleDateString("ar-EG"),الهاتف:"sales"===e.return_type?(null==(s=e.customers)?void 0:s.phone)||"غير محدد":(null==(a=e.suppliers)?void 0:a.phone)||"غير محدد",العنوان:"sales"===e.return_type?(null==(r=e.customers)?void 0:r.address)||"غير محدد":(null==(l=e.suppliers)?void 0:l.address)||"غير محدد"}}),l=e.utils.json_to_sheet(r);e.utils.book_append_sheet(t,l,"البيانات التفصيلية");let i=[];if(R.forEach(e=>{e.return_items&&e.return_items.length>0&&e.return_items.forEach(t=>{var n;i.push({"رقم المرتجع":e.return_number,"اسم الدواء":t.medicine_name||(null==(n=t.medicines)?void 0:n.name)||"غير محدد",الكمية:t.quantity||0,"سعر الوحدة":t.unit_price||t.unit_cost||0,المجموع:t.total_price||t.total_cost||0,"تاريخ الانتهاء":t.expiry_date?new Date(t.expiry_date).toLocaleDateString("ar-EG"):"غير محدد"})})}),i.length>0){let n=e.utils.json_to_sheet(i);e.utils.book_append_sheet(t,n,"المواد المرتجعة")}let d="تقرير_المرتجعات_".concat(new Date().toISOString().split("T")[0],".xlsx");e.writeFile(t,d)}catch(e){console.error("Error exporting Excel:",e),alert("حدث خطأ أثناء تصدير Excel")}},ec=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},eo=e=>{switch(e){case"approved":return"مقبول";case"rejected":return"مرفوض";case"pending":return"في الانتظار";default:return"غير محدد"}},em=e=>{switch(e){case"approved":return(0,s.jsx)(l.A,{className:"h-4 w-4"});case"rejected":return(0,s.jsx)(i.A,{className:"h-4 w-4"});case"pending":return(0,s.jsx)(d.A,{className:"h-4 w-4"});default:return(0,s.jsx)(c.A,{className:"h-4 w-4"})}},ex=e=>{switch(e){case"sales":return"مرتجع مبيعات";case"purchase":return"مرتجع مشتريات";default:return"غير محدد"}};return(0,s.jsx)(r.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center gap-3",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 text-blue-600"}),"سجل المرتجعات"]}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"عرض وإدارة جميع المرتجعات مع إمكانيات طباعة وتصدير متقدمة"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("button",{onClick:()=>{let e=window.open("","_blank");if(!e)return;let t='\n      <!DOCTYPE html>\n      <html dir="rtl" lang="ar">\n      <head>\n        <meta charset="UTF-8">\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">\n        <title>تقرير المرتجعات</title>\n        <style>\n          @import url(\'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap\');\n\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: \'Cairo\', \'Segoe UI\', Tahoma, Geneva, Verdana, sans-serif;\n            line-height: 1.6;\n            color: #2D3748;\n            background: #FFFFFF;\n            direction: rtl;\n            padding: 20px;\n          }\n\n          .print-container {\n            max-width: 1200px;\n            margin: 0 auto;\n            background: white;\n          }\n\n          .header {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n            border-radius: 12px 12px 0 0;\n            margin-bottom: 30px;\n          }\n\n          .header h1 {\n            font-size: 28px;\n            font-weight: 700;\n            margin-bottom: 10px;\n          }\n\n          .header .subtitle {\n            font-size: 16px;\n            opacity: 0.9;\n          }\n\n          .summary {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 20px;\n            margin-bottom: 30px;\n          }\n\n          .summary-card {\n            background: #F7FAFC;\n            border: 1px solid #E2E8F0;\n            border-radius: 12px;\n            padding: 20px;\n            text-align: center;\n            border-right: 4px solid #667eea;\n          }\n\n          .summary-card h3 {\n            font-size: 14px;\n            font-weight: 600;\n            color: #4A5568;\n            margin-bottom: 8px;\n          }\n\n          .summary-card p {\n            font-size: 24px;\n            font-weight: 700;\n            color: #667eea;\n          }\n\n          .returns-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n            gap: 20px;\n            margin-bottom: 30px;\n          }\n\n          .return-card {\n            background: white;\n            border: 1px solid #E2E8F0;\n            border-radius: 12px;\n            overflow: hidden;\n            box-shadow: 0 4px 6px rgba(0,0,0,0.05);\n            break-inside: avoid;\n            margin-bottom: 20px;\n          }\n\n          .return-header {\n            background: linear-gradient(135deg, #F7FAFC 0%, #EDF2F7 100%);\n            padding: 15px 20px;\n            border-bottom: 1px solid #E2E8F0;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          }\n\n          .return-number h3 {\n            font-size: 18px;\n            font-weight: 700;\n            color: #2D3748;\n            margin-bottom: 5px;\n          }\n\n          .return-type {\n            font-size: 12px;\n            color: #718096;\n            background: #EDF2F7;\n            padding: 4px 8px;\n            border-radius: 6px;\n          }\n\n          .return-status {\n            color: white;\n            padding: 8px 16px;\n            border-radius: 20px;\n            font-size: 12px;\n            font-weight: 600;\n          }\n\n          .return-info {\n            padding: 20px;\n          }\n\n          .info-row {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n            gap: 15px;\n            margin-bottom: 15px;\n          }\n\n          .contact-info {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n            gap: 15px;\n            margin-bottom: 15px;\n            padding: 15px;\n            background: #F7FAFC;\n            border-radius: 8px;\n          }\n\n          .info-item {\n            display: flex;\n            flex-direction: column;\n          }\n\n          .info-item label {\n            font-size: 12px;\n            font-weight: 600;\n            color: #4A5568;\n            margin-bottom: 4px;\n          }\n\n          .info-item span {\n            font-size: 14px;\n            color: #2D3748;\n          }\n\n          .reason-section {\n            margin-bottom: 15px;\n          }\n\n          .reason-section label {\n            font-size: 12px;\n            font-weight: 600;\n            color: #4A5568;\n            margin-bottom: 8px;\n            display: block;\n          }\n\n          .reason-text {\n            background: #FFF5F5;\n            border: 1px solid #FED7D7;\n            border-radius: 8px;\n            padding: 12px;\n            font-size: 14px;\n            color: #2D3748;\n          }\n\n          .items-section h4 {\n            font-size: 14px;\n            font-weight: 600;\n            color: #4A5568;\n            margin-bottom: 12px;\n          }\n\n          .items-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 10px;\n          }\n\n          .item-card {\n            background: #F0FFF4;\n            border: 1px solid #C6F6D5;\n            border-radius: 8px;\n            padding: 12px;\n          }\n\n          .item-name {\n            font-weight: 600;\n            color: #2D3748;\n            margin-bottom: 8px;\n            font-size: 13px;\n          }\n\n          .item-details {\n            display: flex;\n            flex-direction: column;\n            gap: 4px;\n          }\n\n          .item-details span {\n            font-size: 11px;\n            color: #4A5568;\n            display: block;\n            margin-bottom: 2px;\n          }\n\n          .item-number {\n            background: #667eea;\n            color: white;\n            padding: 2px 6px;\n            border-radius: 4px;\n            font-size: 10px;\n            margin-left: 5px;\n          }\n\n          .items-total {\n            margin-top: 15px;\n            padding: 10px;\n            background: #EDF2F7;\n            border-radius: 6px;\n            text-align: center;\n            font-size: 14px;\n            color: #2D3748;\n          }\n\n          .no-items {\n            text-align: center;\n            color: #718096;\n            padding: 20px;\n            background: #F7FAFC;\n            border-radius: 8px;\n            border: 2px dashed #CBD5E0;\n          }\n\n          .no-items-icon {\n            font-size: 24px;\n            margin-bottom: 8px;\n          }\n\n          .footer {\n            background: #F7FAFC;\n            padding: 20px;\n            text-align: center;\n            border-top: 1px solid #E2E8F0;\n            color: #718096;\n            font-size: 12px;\n            margin-top: 30px;\n          }\n\n          @media print {\n            body {\n              margin: 0;\n              padding: 10px;\n            }\n\n            .returns-grid {\n              grid-template-columns: 1fr;\n            }\n\n            .return-card {\n              break-inside: avoid;\n              margin-bottom: 15px;\n            }\n\n            .summary {\n              grid-template-columns: repeat(4, 1fr);\n            }\n          }\n\n          @page {\n            margin: 1cm;\n            size: A4;\n          }\n        </style>\n      </head>\n      <body>\n        <div class="print-container">\n          <div class="header">\n            <h1>تقرير المرتجعات الشامل</h1>\n            <div class="subtitle">نظام إدارة الصيدلية الاحترافي</div>\n          </div>\n\n          <div class="summary">\n            <div class="summary-card">\n              <h3>إجمالي المرتجعات</h3>\n              <p>'.concat(R.length,'</p>\n            </div>\n            <div class="summary-card">\n              <h3>في الانتظار</h3>\n              <p>').concat(R.filter(e=>"pending"===e.status).length,'</p>\n            </div>\n            <div class="summary-card">\n              <h3>مقبولة</h3>\n              <p>').concat(R.filter(e=>"approved"===e.status).length,'</p>\n            </div>\n            <div class="summary-card">\n              <h3>مرفوضة</h3>\n              <p>').concat(R.filter(e=>"rejected"===e.status).length,'</p>\n            </div>\n          </div>\n\n          <div class="returns-grid">\n            ').concat(R.map(e=>(e=>{var t,n,s;let a="approved"===e.status?"#10B981":"rejected"===e.status?"#EF4444":"#F59E0B",r="approved"===e.status?"مقبول":"rejected"===e.status?"مرفوض":"في الانتظار",l="sales"===e.return_type?"مرتجع مبيعات":"مرتجع مشتريات",i="sales"===e.return_type?(null==(t=e.customers)?void 0:t.name)||e.customer_name||"عميل غير محدد":(null==(n=e.suppliers)?void 0:n.name)||e.supplier_name||"مورد غير محدد",d=e.return_items||[],c=d.length>0?'\n        <div class="items-section">\n          <h4>المواد المرتجعة ('.concat(d.length,' مادة):</h4>\n          <div class="items-grid">\n            ').concat(d.map((e,t)=>{var n,s;return'\n              <div class="item-card">\n                <div class="item-name">\n                  <span class="item-number">'.concat(t+1,".</span>\n                  ").concat(e.medicine_name||(null==(n=e.medicines)?void 0:n.name)||(null==(s=e.medicine)?void 0:s.name)||"دواء ".concat(t+1),'\n                </div>\n                <div class="item-details">\n                  <span>\uD83D\uDCE6 الكمية: ').concat(e.quantity||0,"</span>\n                  <span>\uD83D\uDCB0 السعر: ").concat((e.unit_price||e.unit_cost||e.price||0).toLocaleString()," د.ع</span>\n                  <span>\uD83D\uDCB5 المجموع: ").concat((e.total_price||e.total_cost||e.total||(e.quantity||0)*(e.unit_price||e.unit_cost||e.price||0)).toLocaleString()," د.ع</span>\n                  ").concat(e.expiry_date?"<span>\uD83D\uDCC5 انتهاء: ".concat(new Date(e.expiry_date).toLocaleDateString("ar-EG"),"</span>"):"","\n                  ").concat(e.reason||e.return_reason?"<span>\uD83D\uDCDD السبب: ".concat(e.reason||e.return_reason,"</span>"):"","\n                </div>\n              </div>\n            ")}).join(""),'\n          </div>\n          <div class="items-total">\n            <strong>المجموع الكلي: ').concat((null==(s=e.total_amount)?void 0:s.toLocaleString())||0," د.ع</strong>\n          </div>\n        </div>\n      "):'\n        <div class="no-items">\n          <div class="no-items-icon">\uD83D\uDCE6</div>\n          <div>لا توجد مواد مرتجعة</div>\n        </div>\n      ';return'\n        <div class="return-card">\n          <div class="return-header">\n            <div class="return-number">\n              <h3>'.concat(e.return_number,'</h3>\n              <span class="return-type">').concat(l,'</span>\n            </div>\n            <div class="return-status" style="background: ').concat(a,';">\n              ').concat(r,'\n            </div>\n          </div>\n\n          <div class="return-info">\n            <div class="info-row">\n              <div class="info-item">\n                <label>التاريخ:</label>\n                <span>').concat(new Date(e.created_at).toLocaleDateString("ar-EG"),'</span>\n              </div>\n              <div class="info-item">\n                <label>').concat("sales"===e.return_type?"العميل":"المورد",":</label>\n                <span>").concat(i,'</span>\n              </div>\n              <div class="info-item">\n                <label>المبلغ الإجمالي:</label>\n                <span>').concat(e.total_amount.toLocaleString()," د.ع</span>\n              </div>\n            </div>\n\n            ").concat("sales"===e.return_type&&e.customers?'\n              <div class="contact-info">\n                <div class="info-item">\n                  <label>الهاتف:</label>\n                  <span>'.concat(e.customers.phone||"غير محدد",'</span>\n                </div>\n                <div class="info-item">\n                  <label>العنوان:</label>\n                  <span>').concat(e.customers.address||"غير محدد","</span>\n                </div>\n              </div>\n            "):"","\n\n            ").concat("purchase"===e.return_type&&e.suppliers?'\n              <div class="contact-info">\n                <div class="info-item">\n                  <label>الهاتف:</label>\n                  <span>'.concat(e.suppliers.phone||"غير محدد",'</span>\n                </div>\n                <div class="info-item">\n                  <label>العنوان:</label>\n                  <span>').concat(e.suppliers.address||"غير محدد","</span>\n                </div>\n              </div>\n            "):"",'\n\n            <div class="reason-section">\n              <label>سبب المرتجع:</label>\n              <div class="reason-text">').concat(e.reason||"غير محدد","</div>\n            </div>\n\n            ").concat(c,"\n          </div>\n        </div>\n      ")})(e)).join(""),'\n          </div>\n\n          <div class="footer">\n            <div>تم إنشاء هذا التقرير في: ').concat(new Date().toLocaleString("ar-EG"),"</div>\n            <div>نظام إدارة الصيدلية - تقرير المرتجعات</div>\n          </div>\n        </div>\n      </body>\n      </html>\n    ");e.document.write(t),e.document.close(),e.focus(),setTimeout(()=>{e.print()},500)},className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2 transition-colors",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),"طباعة التقرير"]}),(0,s.jsxs)("button",{onClick:ed,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"تصدير Excel"]}),(0,s.jsxs)("button",{onClick:()=>{et(!0),setTimeout(()=>en(),100)},className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2 transition-colors",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),"إعادة إنشاء البيانات"]}),(0,s.jsxs)("button",{onClick:en,className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2 transition-colors",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),"تحديث"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي المرتجعات"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:R.length})]}),(0,s.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,s.jsx)(o.A,{className:"h-6 w-6 text-blue-600"})})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"في الانتظار"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:R.filter(e=>"pending"===e.status).length})]}),(0,s.jsx)("div",{className:"p-3 bg-yellow-100 rounded-full",children:(0,s.jsx)(d.A,{className:"h-6 w-6 text-yellow-600"})})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"مقبولة"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:R.filter(e=>"approved"===e.status).length})]}),(0,s.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,s.jsx)(l.A,{className:"h-6 w-6 text-green-600"})})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"مرفوضة"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-600",children:R.filter(e=>"rejected"===e.status).length})]}),(0,s.jsx)("div",{className:"p-3 bg-red-100 rounded-full",children:(0,s.jsx)(i.A,{className:"h-6 w-6 text-red-600"})})]})})]}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)("input",{type:"text",placeholder:"البحث برقم المرتجع أو اسم العميل...",value:G,onChange:e=>T(e.target.value),className:"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,s.jsxs)("select",{value:B,onChange:e=>P(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"all",children:"جميع الأنواع"}),(0,s.jsx)("option",{value:"sales",children:"مرتجع مبيعات"}),(0,s.jsx)("option",{value:"purchase",children:"مرتجع مشتريات"})]}),(0,s.jsxs)("select",{value:H,onChange:e=>J(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,s.jsx)("option",{value:"pending",children:"في الانتظار"}),(0,s.jsx)("option",{value:"approved",children:"مقبول"}),(0,s.jsx)("option",{value:"rejected",children:"مرفوض"})]}),(0,s.jsx)("div",{className:"flex items-center justify-center bg-yellow-50 rounded-lg px-4 py-2",children:(0,s.jsxs)("span",{className:"text-sm text-yellow-800",children:["في الانتظار: ",M.filter(e=>"pending"===e.status).length]})}),(0,s.jsx)("div",{className:"flex items-center justify-center bg-gray-50 rounded-lg px-4 py-2",children:(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[R.length," من ",M.length," مرتجع"]})})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:K?(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"جاري تحميل السجلات..."})]}):0===R.length?(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"لا توجد مرتجعات"})]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم المرتجع"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النوع"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"العميل/المورد"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المواد المرتجعة"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"السبب"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الحالة"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:R.map(e=>{var t,n,a,r,l,i,d;return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 text-purple-500 mr-2"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.return_number})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("sales"===e.return_type?"bg-blue-100 text-blue-800":"bg-orange-100 text-orange-800"),children:ex(e.return_type)})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"sales"===e.return_type?(null==(t=e.customers)?void 0:t.name)||e.customer_name||"عميل نقدي":(null==(n=e.suppliers)?void 0:n.name)||e.supplier_name||"غير محدد"}),("sales"===e.return_type&&(null==(a=e.customers)?void 0:a.phone)||"purchase"===e.return_type&&(null==(r=e.suppliers)?void 0:r.phone))&&(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"sales"===e.return_type?null==(l=e.customers)?void 0:l.phone:null==(i=e.suppliers)?void 0:i.phone})]})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"max-w-xs",children:(()=>{console.log("Rendering items for ".concat(e.return_number,":"),e.return_items);let t=e.return_items||[];return t.length>0?(0,s.jsxs)("div",{className:"space-y-1",children:[t.slice(0,2).map((e,t)=>{var n,a;let r=e.medicine_name||(null==(n=e.medicines)?void 0:n.name)||(null==(a=e.medicine)?void 0:a.name)||"دواء ".concat(t+1),l=e.quantity||0;return(0,s.jsxs)("div",{className:"flex items-center text-xs text-gray-600 bg-gray-50 rounded px-2 py-1",children:[(0,s.jsx)(p.A,{className:"h-3 w-3 text-blue-500 mr-1"}),(0,s.jsxs)("span",{className:"truncate",title:"".concat(r," (").concat(l,")"),children:[r," (",l,")"]})]},t)}),t.length>2&&(0,s.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["+",t.length-2," مواد أخرى"]})]}):(0,s.jsxs)("div",{className:"flex items-center text-xs text-gray-400 italic",children:[(0,s.jsx)(p.A,{className:"h-3 w-3 text-gray-400 mr-1"}),(0,s.jsx)("span",{children:"لا توجد مواد"})]})})()})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 text-red-500 mr-1"}),(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[null==(d=e.total_amount)?void 0:d.toLocaleString()," د.ع"]})]})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900 max-w-xs truncate",title:e.reason,children:e.reason})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ".concat(ec(e.status)),children:[em(e.status),(0,s.jsx)("span",{className:"mr-1",children:eo(e.status)})]})})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:new Date(e.created_at).toLocaleDateString("ar-EG")})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("button",{onClick:()=>X(W===e.id?null:e.id),className:"text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100",disabled:$===e.id,children:$===e.id?(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"}):(0,s.jsx)(b.A,{className:"h-4 w-4"})}),W===e.id&&(0,s.jsx)("div",{className:"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200",children:(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsxs)("button",{onClick:()=>ea(e),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"عرض التفاصيل"]}),(0,s.jsxs)("button",{onClick:()=>ei(e),className:"flex items-center w-full px-4 py-2 text-sm text-blue-700 hover:bg-blue-50",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"طباعة المرتجع (قالب لارين)"]}),"pending"===e.status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{onClick:()=>er(e),className:"flex items-center w-full px-4 py-2 text-sm text-green-700 hover:bg-green-50",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"قبول المرتجع"]}),(0,s.jsxs)("button",{onClick:()=>el(e),className:"flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"رفض المرتجع"]})]})]})})]})})]},e.id)})})]})})}),Z&&U&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["تفاصيل المرتجع ",U.return_number]}),(0,s.jsx)("button",{onClick:()=>Y(!1),className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,s.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"معلومات المرتجع"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"رقم المرتجع:"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.return_number})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"النوع:"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:ex(U.return_type)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"التاريخ:"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:new Date(U.created_at).toLocaleDateString("ar-EG")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"المبلغ الإجمالي:"}),(0,s.jsxs)("p",{className:"text-sm text-gray-900",children:[null==(e=U.total_amount)?void 0:e.toLocaleString()," د.ع"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"الحالة:"}),(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ".concat(ec(U.status)),children:[em(U.status),(0,s.jsx)("span",{className:"mr-1",children:eo(U.status)})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"سبب المرتجع:"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:U.reason})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"sales"===U.return_type?"معلومات العميل":"معلومات المورد"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"الاسم:"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:"sales"===U.return_type?(null==(t=U.customers)?void 0:t.name)||U.customer_name||"عميل نقدي":(null==(_=U.suppliers)?void 0:_.name)||U.supplier_name||"غير محدد"})]}),("sales"===U.return_type&&(null==(k=U.customers)?void 0:k.phone)||"purchase"===U.return_type&&(null==(D=U.suppliers)?void 0:D.phone))&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"الهاتف:"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:"sales"===U.return_type?null==(S=U.customers)?void 0:S.phone:null==(A=U.suppliers)?void 0:A.phone})]}),("sales"===U.return_type&&(null==(E=U.customers)?void 0:E.address)||"purchase"===U.return_type&&(null==(F=U.suppliers)?void 0:F.address))&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"العنوان:"}),(0,s.jsx)("p",{className:"text-sm text-gray-900",children:"sales"===U.return_type?null==(C=U.customers)?void 0:C.address:null==(L=U.suppliers)?void 0:L.address})]})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-blue-600"}),"المواد المرتجعة (",(null==(z=U.return_items)?void 0:z.length)||0," مادة)"]}),U.return_items&&U.return_items.length>0?(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"#"}),(0,s.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"اسم الدواء"}),(0,s.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"الكمية"}),(0,s.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"سعر الوحدة"}),(0,s.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المجموع"}),(0,s.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"تاريخ الانتهاء"}),(0,s.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"سبب الإرجاع"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:U.return_items.map((e,t)=>{var n,a;let r=e.medicine_name||(null==(n=e.medicines)?void 0:n.name)||(null==(a=e.medicine)?void 0:a.name)||"دواء ".concat(t+1),l=e.quantity||0,i=e.unit_price||e.unit_cost||e.price||0,d=e.total_price||e.total_cost||e.total||l*i;return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm font-medium text-gray-900",children:t+1}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-blue-500"}),(0,s.jsx)("span",{className:"font-medium",children:r})]})}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,s.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:l})}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm text-gray-900",children:[i.toLocaleString()," د.ع"]}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-medium text-gray-900",children:[d.toLocaleString()," د.ع"]}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.expiry_date?new Date(e.expiry_date).toLocaleDateString("ar-EG"):"غير محدد"}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,s.jsx)("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:e.reason||e.return_reason||"غير محدد"})})]},t)})}),(0,s.jsx)("tfoot",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{colSpan:4,className:"px-4 py-3 text-sm font-medium text-gray-900 text-right",children:"المجموع الكلي:"}),(0,s.jsxs)("td",{className:"px-4 py-3 text-sm font-bold text-gray-900",children:[(null==(I=U.total_amount)?void 0:I.toLocaleString())||0," د.ع"]}),(0,s.jsx)("td",{colSpan:2})]})})]})}):(0,s.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,s.jsx)(p.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد مواد مرتجعة"}),(0,s.jsx)("p",{className:"text-gray-500",children:"لم يتم تسجيل أي مواد لهذا المرتجع"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t bg-gray-50",children:[(0,s.jsx)("button",{onClick:()=>Y(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"إغلاق"}),"pending"===U.status&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{onClick:()=>{er(U),Y(!1)},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),"قبول المرتجع"]}),(0,s.jsxs)("button",{onClick:()=>{el(U),Y(!1)},className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center gap-2",children:[(0,s.jsx)(N.A,{className:"h-4 w-4"}),"رفض المرتجع"]})]})]})]})})]})})}},44020:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});let s=(0,n(19946).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},57434:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});let s=(0,n(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},64261:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});let s=(0,n(19946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},69074:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});let s=(0,n(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},92657:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});let s=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{e.O(0,[6874,6543,5647,8080,1932,988,3363,8441,5964,7358],()=>e(e.s=23389)),_N_E=e.O()}]);