'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import { 
  Settings, 
  Save, 
  Building, 
  DollarSign,
  Bell,
  Shield,
  Database,
  Printer,
  Mail,
  Phone,
  MapPin,
  User,
  Key,
  Download,
  Upload,
  RotateCcw
} from 'lucide-react'
import { usePrintSettings } from '@/hooks/usePrintSettings'

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('company')
  const {
    settings: printSettings,
    updateSettings: updatePrintSettings,
    resetSettings: resetPrintSettings,
    exportSettings: exportPrintSettings,
    importSettings: importPrintSettings
  } = usePrintSettings()
  const [settings, setSettings] = useState({
    company: {
      name: 'مكتب لارين العلمي',
      nameEn: 'LAREN SCIENTIFIC BUREAU',
      address: 'بغداد - شارع فلسطين',
      phone: '07901234567',
      email: '<EMAIL>',
      license: 'LSB-2024-001',
      manager: 'د. أحمد محمد علي'
    },
    currency: {
      primary: 'IQD',
      symbol: 'د.ع',
      decimalPlaces: 0,
      thousandSeparator: ','
    },
    notifications: {
      lowStock: true,
      expiryWarning: true,
      expiryDays: 30,
      dailyReport: false,
      emailNotifications: true
    },
    security: {
      sessionTimeout: 60,
      passwordExpiry: 90,
      maxLoginAttempts: 3,
      twoFactorAuth: false
    },
    backup: {
      autoBackup: true,
      backupFrequency: 'daily',
      backupTime: '02:00',
      retentionDays: 30
    },
    printing: {
      invoiceTemplate: 'standard',
      printLogo: true,
      printFooter: true,
      paperSize: 'A4'
    }
  })

  const tabs = [
    { id: 'company', label: 'معلومات الشركة', icon: Building },
    { id: 'currency', label: 'العملة والأرقام', icon: DollarSign },
    { id: 'notifications', label: 'التنبيهات', icon: Bell },
    { id: 'security', label: 'الأمان', icon: Shield },
    { id: 'backup', label: 'النسخ الاحتياطي', icon: Database },
    { id: 'printing', label: 'الطباعة', icon: Printer }
  ]

  const handleSave = () => {
    // هنا سيتم حفظ الإعدادات في قاعدة البيانات
    console.log('Saving settings:', settings)
    alert('تم حفظ الإعدادات بنجاح!')
  }

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'pharmacy-settings.json'
    link.click()
  }

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string)
          setSettings(importedSettings)
          alert('تم استيراد الإعدادات بنجاح!')
        } catch (error) {
          alert('خطأ في قراءة ملف الإعدادات')
        }
      }
      reader.readAsText(file)
    }
  }

  const updateSetting = (section: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [key]: value
      }
    }))
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'company':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">معلومات الشركة</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اسم المكتب (عربي)</label>
                <input
                  type="text"
                  value={settings.company.name}
                  onChange={(e) => updateSetting('company', 'name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اسم المكتب (إنجليزي)</label>
                <input
                  type="text"
                  value={settings.company.nameEn}
                  onChange={(e) => updateSetting('company', 'nameEn', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">رقم الترخيص</label>
                <input
                  type="text"
                  value={settings.company.license}
                  onChange={(e) => updateSetting('company', 'license', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اسم المدير</label>
                <input
                  type="text"
                  value={settings.company.manager}
                  onChange={(e) => updateSetting('company', 'manager', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                <input
                  type="tel"
                  value={settings.company.phone}
                  onChange={(e) => updateSetting('company', 'phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                <input
                  type="email"
                  value={settings.company.email}
                  onChange={(e) => updateSetting('company', 'email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
              <textarea
                value={settings.company.address}
                onChange={(e) => updateSetting('company', 'address', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
              />
            </div>
          </div>
        )

      case 'currency':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">إعدادات العملة والأرقام</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">العملة الأساسية</label>
                <select
                  value={settings.currency.primary}
                  onChange={(e) => updateSetting('currency', 'primary', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="IQD">دينار عراقي (IQD)</option>
                  <option value="USD">دولار أمريكي (USD)</option>
                  <option value="EUR">يورو (EUR)</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">رمز العملة</label>
                <input
                  type="text"
                  value={settings.currency.symbol}
                  onChange={(e) => updateSetting('currency', 'symbol', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">عدد الخانات العشرية</label>
                <select
                  value={settings.currency.decimalPlaces}
                  onChange={(e) => updateSetting('currency', 'decimalPlaces', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={0}>0</option>
                  <option value={2}>2</option>
                  <option value={3}>3</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">فاصل الآلاف</label>
                <select
                  value={settings.currency.thousandSeparator}
                  onChange={(e) => updateSetting('currency', 'thousandSeparator', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value=",">فاصلة (,)</option>
                  <option value=".">نقطة (.)</option>
                  <option value=" ">مسافة ( )</option>
                </select>
              </div>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">معاينة التنسيق:</p>
              <p className="text-lg font-medium text-gray-900">
                1{settings.currency.thousandSeparator}234{settings.currency.thousandSeparator}567
                {settings.currency.decimalPlaces > 0 && `.${Array(settings.currency.decimalPlaces).fill('0').join('')}`} {settings.currency.symbol}
              </p>
            </div>
          </div>
        )

      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">إعدادات التنبيهات</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">تنبيه المخزون المنخفض</p>
                  <p className="text-sm text-gray-500">إرسال تنبيه عند انخفاض كمية الدواء</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.notifications.lowStock}
                  onChange={(e) => updateSetting('notifications', 'lowStock', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">تنبيه انتهاء الصلاحية</p>
                  <p className="text-sm text-gray-500">إرسال تنبيه قبل انتهاء صلاحية الأدوية</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.notifications.expiryWarning}
                  onChange={(e) => updateSetting('notifications', 'expiryWarning', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
              
              {settings.notifications.expiryWarning && (
                <div className="mr-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    عدد الأيام قبل انتهاء الصلاحية
                  </label>
                  <input
                    type="number"
                    value={settings.notifications.expiryDays}
                    onChange={(e) => updateSetting('notifications', 'expiryDays', Number(e.target.value))}
                    className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="1"
                    max="365"
                  />
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">التقرير اليومي</p>
                  <p className="text-sm text-gray-500">إرسال تقرير يومي عن المبيعات</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.notifications.dailyReport}
                  onChange={(e) => updateSetting('notifications', 'dailyReport', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">التنبيهات عبر البريد الإلكتروني</p>
                  <p className="text-sm text-gray-500">إرسال التنبيهات عبر البريد الإلكتروني</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.notifications.emailNotifications}
                  onChange={(e) => updateSetting('notifications', 'emailNotifications', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
            </div>
          </div>
        )

      case 'security':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">إعدادات الأمان</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  مهلة انتهاء الجلسة (بالدقائق)
                </label>
                <input
                  type="number"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => updateSetting('security', 'sessionTimeout', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="5"
                  max="480"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  انتهاء صلاحية كلمة المرور (بالأيام)
                </label>
                <input
                  type="number"
                  value={settings.security.passwordExpiry}
                  onChange={(e) => updateSetting('security', 'passwordExpiry', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="30"
                  max="365"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الحد الأقصى لمحاولات تسجيل الدخول
                </label>
                <input
                  type="number"
                  value={settings.security.maxLoginAttempts}
                  onChange={(e) => updateSetting('security', 'maxLoginAttempts', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="3"
                  max="10"
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">المصادقة الثنائية</p>
                <p className="text-sm text-gray-500">تفعيل المصادقة الثنائية لحماية إضافية</p>
              </div>
              <input
                type="checkbox"
                checked={settings.security.twoFactorAuth}
                onChange={(e) => updateSetting('security', 'twoFactorAuth', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
        )

      case 'backup':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">إعدادات النسخ الاحتياطي</h3>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">النسخ الاحتياطي التلقائي</p>
                <p className="text-sm text-gray-500">تفعيل النسخ الاحتياطي التلقائي للبيانات</p>
              </div>
              <input
                type="checkbox"
                checked={settings.backup.autoBackup}
                onChange={(e) => updateSetting('backup', 'autoBackup', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            
            {settings.backup.autoBackup && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">تكرار النسخ</label>
                  <select
                    value={settings.backup.backupFrequency}
                    onChange={(e) => updateSetting('backup', 'backupFrequency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="daily">يومي</option>
                    <option value="weekly">أسبوعي</option>
                    <option value="monthly">شهري</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">وقت النسخ</label>
                  <input
                    type="time"
                    value={settings.backup.backupTime}
                    onChange={(e) => updateSetting('backup', 'backupTime', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    مدة الاحتفاظ (بالأيام)
                  </label>
                  <input
                    type="number"
                    value={settings.backup.retentionDays}
                    onChange={(e) => updateSetting('backup', 'retentionDays', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="7"
                    max="365"
                  />
                </div>
              </div>
            )}
            
            <div className="flex gap-4">
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                <Download className="h-4 w-4" />
                إنشاء نسخة احتياطية الآن
              </button>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2">
                <Upload className="h-4 w-4" />
                استعادة من نسخة احتياطية
              </button>
            </div>
          </div>
        )

      case 'printing':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">إعدادات الطباعة المتقدمة</h3>
              <div className="flex items-center gap-3">
                <input
                  type="file"
                  accept=".json"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      importPrintSettings(file).then(() => {
                        alert('تم استيراد إعدادات الطباعة بنجاح')
                      }).catch(() => {
                        alert('حدث خطأ أثناء استيراد الإعدادات')
                      })
                    }
                  }}
                  className="hidden"
                  id="import-print-settings"
                />
                <label
                  htmlFor="import-print-settings"
                  className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 cursor-pointer flex items-center gap-2"
                >
                  <Upload className="h-3 w-3" />
                  استيراد
                </label>
                <button
                  onClick={exportPrintSettings}
                  className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-2"
                >
                  <Download className="h-3 w-3" />
                  تصدير
                </button>
                <button
                  onClick={() => {
                    if (confirm('هل تريد إعادة تعيين جميع إعدادات الطباعة؟')) {
                      resetPrintSettings()
                      alert('تم إعادة تعيين إعدادات الطباعة')
                    }
                  }}
                  className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 flex items-center gap-2"
                >
                  <RotateCcw className="h-3 w-3" />
                  إعادة تعيين
                </button>
              </div>
            </div>

            {/* Company Information */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-4">معلومات الشركة</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اسم الشركة (عربي)</label>
                  <input
                    type="text"
                    value={printSettings.companyName}
                    onChange={(e) => updatePrintSettings({ companyName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">اسم الشركة (إنجليزي)</label>
                  <input
                    type="text"
                    value={printSettings.companyNameEn}
                    onChange={(e) => updatePrintSettings({ companyNameEn: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                  <input
                    type="text"
                    value={printSettings.companyAddress}
                    onChange={(e) => updatePrintSettings({ companyAddress: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                  <input
                    type="text"
                    value={printSettings.companyPhone}
                    onChange={(e) => updatePrintSettings({ companyPhone: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={printSettings.companyEmail}
                    onChange={(e) => updatePrintSettings({ companyEmail: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Print Layout Settings */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-4">إعدادات التخطيط</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">حجم الخط</label>
                  <select
                    value={printSettings.fontSize}
                    onChange={(e) => updatePrintSettings({ fontSize: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="small">صغير</option>
                    <option value="medium">متوسط</option>
                    <option value="large">كبير</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">حجم الورق</label>
                  <select
                    value={printSettings.paperSize}
                    onChange={(e) => updatePrintSettings({ paperSize: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="A4">A4</option>
                    <option value="A5">A5</option>
                    <option value="thermal">حراري 80mm</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">نص التذييل</label>
                  <input
                    type="text"
                    value={printSettings.footerText}
                    onChange={(e) => updatePrintSettings({ footerText: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="شكراً لتعاملكم معنا"
                  />
                </div>
              </div>
            </div>

            {/* Display Options */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-4">خيارات العرض</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">إظهار الشعار</p>
                      <p className="text-sm text-gray-500">عرض شعار الشركة في الرأس</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={printSettings.showLogo}
                      onChange={(e) => updatePrintSettings({ showLogo: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">إظهار الرأس</p>
                      <p className="text-sm text-gray-500">عرض معلومات الشركة في الأعلى</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={printSettings.showHeader}
                      onChange={(e) => updatePrintSettings({ showHeader: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">إظهار التذييل</p>
                      <p className="text-sm text-gray-500">عرض نص التذييل في الأسفل</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={printSettings.showFooter}
                      onChange={(e) => updatePrintSettings({ showFooter: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">إظهار الحدود</p>
                      <p className="text-sm text-gray-500">إضافة حدود للجداول والعناصر</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={printSettings.showBorders}
                      onChange={(e) => updatePrintSettings({ showBorders: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">طباعة ملونة</p>
                      <p className="text-sm text-gray-500">استخدام الألوان في الطباعة</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={printSettings.showColors}
                      onChange={(e) => updatePrintSettings({ showColors: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">إضافة رمز QR</p>
                      <p className="text-sm text-gray-500">إضافة رمز QR للفواتير</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={printSettings.includeQRCode}
                      onChange={(e) => updatePrintSettings({ includeQRCode: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">الإعدادات</h1>
            <p className="text-gray-600 mt-1">إدارة إعدادات النظام والتفضيلات</p>
          </div>
          <div className="flex items-center gap-3">
            <input
              type="file"
              accept=".json"
              onChange={handleImportSettings}
              className="hidden"
              id="import-settings"
            />
            <label
              htmlFor="import-settings"
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 cursor-pointer flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              استيراد
            </label>
            <button
              onClick={handleExportSettings}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              تصدير
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full text-right p-3 rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600'
                          : 'text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <Icon className="h-4 w-4" />
                        <span className="font-medium">{tab.label}</span>
                      </div>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              {renderTabContent()}
              
              <div className="flex justify-end mt-8 pt-6 border-t border-gray-200">
                <button
                  onClick={handleSave}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  حفظ الإعدادات
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
