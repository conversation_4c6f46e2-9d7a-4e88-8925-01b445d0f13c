(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5115],{4229:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},46319:(e,t,a)=>{Promise.resolve().then(a.bind(a,65833))},65833:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var n=a(95155),i=a(12115),c=a(61932),l=a(30446),s=a(84616),d=a(4229);function r(){let[e,t]=(0,i.useState)([]),[r,m]=(0,i.useState)(null),[o,h]=(0,i.useState)(1),[u,b]=(0,i.useState)(0),[g,x]=(0,i.useState)([]),[p,v]=(0,i.useState)([]);(0,i.useEffect)(()=>{N()},[]);let y=e=>{let t=new Date().toLocaleTimeString();v(a=>[...a,"[".concat(t,"] ").concat(e)]),console.log(e)},N=()=>{try{let e=JSON.parse(localStorage.getItem("medicines")||"[]"),a=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),n=e.map(e=>({...e,batches:a.filter(t=>t.medicine_id===e.id)})).filter(e=>e.batches.length>0);t(n),y("✅ تم تحميل ".concat(n.length," دواء مع دفعات"))}catch(e){y("❌ خطأ في تحميل الأدوية: ".concat(e))}},_=async()=>{if(0===g.length)return void y("❌ لا توجد عناصر للاختبار");y("\uD83D\uDD04 بدء اختبار عملية الحفظ...");try{let t={invoice_number:"TEST-".concat(Date.now()),customer_id:null,customer_name:"عميل تجريبي",total_amount:g.reduce((e,t)=>e+t.totalPrice,0),discount_amount:0,final_amount:g.reduce((e,t)=>e+t.totalPrice,0),payment_method:"cash",payment_status:"paid",notes:"فاتورة تجريبية للاختبار",private_notes:""},n=g.map(e=>({medicine_batch_id:e.batchId,quantity:e.quantity,unit_price:e.unitPrice,total_price:e.totalPrice,is_gift:!1,medicine_name:e.medicineName,medicineName:e.medicineName}));y("\uD83D\uDCC4 بيانات الفاتورة التجريبية:"),y(JSON.stringify(t,null,2)),y("\uD83D\uDCE6 عناصر الفاتورة التجريبية:"),y(JSON.stringify(n,null,2));let{completeSalesTransaction:i}=await Promise.all([a.e(5647),a.e(988)]).then(a.bind(a,10988));y("\uD83D\uDCBE استدعاء completeSalesTransaction...");let c=await i(t,n);if(y("✅ نتيجة الحفظ:"),y(JSON.stringify(c,null,2)),c.success){y("\uD83C\uDF89 تم حفظ الفاتورة التجريبية بنجاح!");let t=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]").slice(-n.length);y("\uD83D\uDD0D العناصر المحفوظة فعلياً:"),t.forEach((e,t)=>{y("العنصر ".concat(t+1,": ").concat(e.medicine_name||e.medicineName||"غير محدد"))}),y("\uD83D\uDDA8️ اختبار عملية استرجاع البيانات للطباعة...");let i=c.data.invoiceId,{getSalesInvoiceForPrint:l}=await Promise.all([a.e(5647),a.e(988)]).then(a.bind(a,10988)),s=await l(i);if(y("\uD83D\uDCC4 نتيجة استرجاع بيانات الطباعة:"),y(JSON.stringify(s,null,2)),s.success&&s.data){var e;y("\uD83D\uDD0D أسماء الأدوية في بيانات الطباعة:"),null==(e=s.data.sales_invoice_items)||e.forEach((e,t)=>{var a,n,i,c;let l=(null==(n=e.medicine_batches)||null==(a=n.medicines)?void 0:a.name)||e.medicine_name||e.medicineName||"غير محدد";y("عنصر الطباعة ".concat(t+1,": ").concat(l)),y("  - medicine_name: ".concat(e.medicine_name)),y("  - medicineName: ".concat(e.medicineName)),y("  - medicine_batches.medicines.name: ".concat(null==(c=e.medicine_batches)||null==(i=c.medicines)?void 0:i.name)),y("  - batch_id: ".concat(e.medicine_batch_id))}),y("\uD83D\uDDA8️ اختبار قالب الطباعة الفعلي..."),j(s.data),y("\uD83D\uDDA8️ اختبار دالة الطباعة الفعلية..."),f(s.data)}}else y("❌ فشل في حفظ الفاتورة: ".concat(c.error))}catch(e){y("\uD83D\uDCA5 خطأ في اختبار الحفظ: ".concat(e))}},f=async e=>{try{y("\uD83D\uDCC4 اختبار دالة الطباعة الفعلية من النظام...");let{printInvoice:t}=await Promise.all([a.e(5647),a.e(988),a.e(3363)]).then(a.bind(a,53363)),{usePrintSettings:n}=await Promise.all([a.e(5647),a.e(988),a.e(3363)]).then(a.bind(a,53363)),i={companyName:"صيدلية الشفاء",companyNameEn:"Al-Shifa Pharmacy",companyAddress:"بغداد - الكرادة - شارع الرئيسي",footerText:"شكراً لتعاملكم معنا"};y("⚙️ إعدادات الطباعة:"),y(JSON.stringify(i,null,2)),y("\uD83D\uDCCB بيانات الفاتورة للطباعة:"),y(JSON.stringify(e,null,2)),y("\uD83D\uDDA8️ استدعاء دالة printInvoice..."),await t(e,"sales",i),y("✅ تم استدعاء دالة الطباعة - تحقق من النافذة المفتوحة!")}catch(e){y("❌ خطأ في اختبار الطباعة الفعلية: ".concat(e))}},j=e=>{var t,a,n;y("\uD83D\uDCC4 اختبار قالب الطباعة..."),null==(t=e.sales_invoice_items)||t.forEach((e,t)=>{var a,n;let i=null==(n=e.medicine_batches)||null==(a=n.medicines)?void 0:a.name,c=e.medicine_name,l=e.medicineName;y("قالب الطباعة - العنصر ".concat(t+1,":")),y("  الطريقة 1 (medicine_batches.medicines.name): ".concat(i)),y("  الطريقة 2 (medicine_name): ".concat(c)),y("  الطريقة 3 (medicineName): ".concat(l)),y("  النتيجة النهائية: ".concat(i||c||l||"غير محدد"))}),y("\uD83D\uDDA8️ إنشاء نافذة طباعة تجريبية...");let i='\n      <!DOCTYPE html>\n      <html dir="rtl" lang="ar">\n      <head>\n        <meta charset="UTF-8">\n        <title>فاتورة تجريبية - '.concat(e.invoice_number,'</title>\n        <style>\n          body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n          .header { text-align: center; margin-bottom: 20px; }\n          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }\n          .items-table th { background-color: #f5f5f5; }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>فاتورة تجريبية</h1>\n          <h2>رقم الفاتورة: ').concat(e.invoice_number,'</h2>\n        </div>\n\n        <table class="items-table">\n          <thead>\n            <tr>\n              <th>اسم الدواء</th>\n              <th>الكمية</th>\n              <th>سعر الوحدة</th>\n              <th>الإجمالي</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat((null==(a=e.sales_invoice_items)?void 0:a.map(e=>{var t,a;let n=(null==(a=e.medicine_batches)||null==(t=a.medicines)?void 0:t.name)||e.medicine_name||e.medicineName||"غير محدد";return"\n                <tr>\n                  <td>".concat(n,"</td>\n                  <td>").concat(e.quantity,"</td>\n                  <td>").concat(e.unit_price.toFixed(2),"</td>\n                  <td>").concat(e.total_price.toFixed(2),"</td>\n                </tr>\n              ")}).join(""))||'<tr><td colspan="4">لا توجد عناصر</td></tr>','\n          </tbody>\n        </table>\n\n        <div style="margin-top: 20px;">\n          <p><strong>المجموع النهائي:</strong> ').concat((null==(n=e.final_amount)?void 0:n.toFixed(2))||"0.00"," جنيه</p>\n        </div>\n      </body>\n      </html>\n    "),c=window.open("","_blank");c?(c.document.write(i),c.document.close(),y("✅ تم فتح نافذة الطباعة التجريبية - تحقق من أسماء الأدوية!")):y("❌ فشل في فتح نافذة الطباعة")};return(0,n.jsx)(c.A,{children:(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,n.jsx)("div",{className:"bg-red-50 p-3 rounded-lg",children:(0,n.jsx)(l.A,{className:"h-6 w-6 text-red-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"اختبار عملية إنشاء الفواتير"}),(0,n.jsx)("p",{className:"text-gray-600",children:"اختبار مباشر لعملية حفظ الفواتير وأسماء الأدوية"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اختر دواء:"}),(0,n.jsxs)("select",{value:(null==r?void 0:r.id)||"",onChange:t=>{var a;let n=e.find(e=>e.id===t.target.value);m(n),b((null==n||null==(a=n.batches[0])?void 0:a.selling_price)||0)},className:"w-full px-3 py-2 border border-gray-300 rounded-lg",children:[(0,n.jsx)("option",{value:"",children:"اختر دواء..."}),e.map(e=>(0,n.jsxs)("option",{value:e.id,children:[e.name," - ",e.batches.length," دفعة"]},e.id))]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الكمية:"}),(0,n.jsx)("input",{type:"number",value:o,onChange:e=>h(Number(e.target.value)),min:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-lg"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"سعر الوحدة:"}),(0,n.jsx)("input",{type:"number",value:u,onChange:e=>b(Number(e.target.value)),min:"0",step:"0.01",className:"w-full px-3 py-2 border border-gray-300 rounded-lg"})]})]})]}),(0,n.jsxs)("div",{className:"flex gap-3 mb-6",children:[(0,n.jsxs)("button",{onClick:()=>{if(!r||!r.batches[0])return void y("❌ يرجى اختيار دواء صحيح");let e=r.batches[0],t={id:"test_".concat(Date.now()),batchId:e.id,medicineName:r.name,medicine_name:r.name,quantity:o,unitPrice:u,totalPrice:o*u,batch:e};x(e=>[...e,t]),y("✅ تم إضافة عنصر: ".concat(r.name," - الكمية: ").concat(o))},disabled:!r,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 flex items-center gap-2",children:[(0,n.jsx)(s.A,{className:"h-4 w-4"}),"إضافة عنصر"]}),(0,n.jsxs)("button",{onClick:_,disabled:0===g.length,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 flex items-center gap-2",children:[(0,n.jsx)(d.A,{className:"h-4 w-4"}),"اختبار الحفظ"]}),(0,n.jsx)("button",{onClick:()=>{x([]),v([]),m(null),h(1),b(0)},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2",children:"مسح الاختبار"})]}),g.length>0&&(0,n.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[(0,n.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"العناصر التجريبية:"}),(0,n.jsx)("div",{className:"space-y-2",children:g.map((e,t)=>(0,n.jsx)("div",{className:"bg-white p-3 rounded border",children:(0,n.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"الدواء:"})," ",e.medicineName]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"الكمية:"})," ",e.quantity]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"السعر:"})," ",e.unitPrice]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"الإجمالي:"})," ",e.totalPrice]})]})},e.id))})]}),(0,n.jsxs)("div",{className:"bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto",children:[(0,n.jsx)("h3",{className:"text-white mb-2",children:"سجل التشخيص:"}),p.map((e,t)=>(0,n.jsx)("div",{className:"mb-1",children:e},t)),0===p.length&&(0,n.jsx)("div",{className:"text-gray-500",children:"لا توجد رسائل تشخيص بعد..."})]})]})})})}},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}},e=>{e.O(0,[6874,6543,8080,1932,8441,5964,7358],()=>e(e.s=46319)),_N_E=e.O()}]);