(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[693],{13717:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},19025:(e,s,a)=>{Promise.resolve().then(a.bind(a,88987))},69074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},88987:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(95155),r=a(12115),l=a(61932),c=a(37108),n=a(47924),d=a(20215),i=a(1243),m=a(69074),x=a(13717),o=a(62525),h=a(10988);function u(){let[e,s]=(0,r.useState)(""),[a,u]=(0,r.useState)("all"),[g,b]=(0,r.useState)(null),[p,y]=(0,r.useState)([]),[j,N]=(0,r.useState)(!1),f=[...new Set(["all","مسكنات","مضادات حيوية","فيتامينات","أخرى",...p.map(e=>e.category).filter(Boolean)])];(0,r.useEffect)(()=>{v()},[]);let v=async()=>{N(!0);try{let e=await (0,h.getMedicines)();e.success&&e.data?y(e.data):(console.error("Failed to load medicines:",e.error),y([{id:"1",name:"باراسيتامول",category:"مسكنات",manufacturer:"شركة الأدوية العراقية",strength:"500mg",form:"tablet",medicine_batches:[{id:"b1",batch_code:"B001",quantity:150,expiry_date:"2024-12-15",selling_price:750}]}]))}catch(e){console.error("Error loading medicines:",e),y([{id:"1",name:"باراسيتامول",category:"مسكنات",manufacturer:"شركة الأدوية العراقية",strength:"500mg",form:"tablet",medicine_batches:[{id:"b1",batch_code:"B001",quantity:150,expiry_date:"2024-12-15",selling_price:750}]}])}finally{N(!1)}},w=p.filter(s=>{let t=s.name.toLowerCase().includes(e.toLowerCase())||s.manufacturer&&s.manufacturer.toLowerCase().includes(e.toLowerCase()),r="all"===a||s.category===a;return t&&r}),_=e=>{if(!e||!Array.isArray(e))return[];let s=new Date,a=new Date;return a.setMonth(s.getMonth()+3),e.filter(e=>new Date(e.expiry_date)<=a)};return(0,t.jsx)(l.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة المخزون"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"عرض الأدوية والوجبات المتوفرة في المخزون"})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-2 rounded-full",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900",children:"إضافة أدوية جديدة"}),(0,t.jsx)("p",{className:"text-blue-700 text-sm mt-1",children:"لإضافة أدوية جديدة للمخزون، يرجى استخدام صفحة المشتريات"})]})]})})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)("input",{type:"text",placeholder:"البحث عن دواء...",value:e,onChange:e=>s(e.target.value),className:"w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,t.jsx)("div",{className:"md:w-48",children:(0,t.jsx)("select",{value:a,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:f.map((e,s)=>(0,t.jsx)("option",{value:e,children:"all"===e?"جميع الفئات":e},"category_".concat(s,"_").concat(e)))})})]})}),(0,t.jsx)("div",{className:"space-y-4",children:j?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"text-gray-500 mt-4",children:"جاري تحميل البيانات..."})]}):w.map(e=>{var s;let a=(s=e.medicine_batches)&&Array.isArray(s)?s.reduce((e,s)=>e+(s.quantity||0),0):0,r=_(e.medicine_batches);return(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,t.jsx)(d.A,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[e.strength," • ",e.form," • ",e.manufacturer]}),(0,t.jsx)("span",{className:"inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full mt-1",children:e.category})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"إجمالي الكمية"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:a})]}),r.length>0&&(0,t.jsx)("div",{className:"bg-orange-100 p-2 rounded-lg",children:(0,t.jsx)(i.A,{className:"h-5 w-5 text-orange-600"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"الوجبات المتوفرة:"}),e.medicine_batches&&e.medicine_batches.length>0?e.medicine_batches.map(s=>{let a=_([s]).length>0;return(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border ".concat(a?"border-orange-200 bg-orange-50":"border-gray-200 bg-gray-50"),children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium text-gray-900",children:["وجبة: ",s.batch_code]}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,t.jsxs)("span",{children:["الكمية: ",s.quantity]}),(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"h-3 w-3"}),"انتهاء: ",s.expiry_date]})]})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"سعر البيع"}),(0,t.jsxs)("p",{className:"font-medium text-gray-900",children:[s.selling_price," د.ع"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("button",{onClick:()=>{b({...s,medicineId:e.id})},className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg",children:(0,t.jsx)(x.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>{var a,t;return a=e.id,t=s.id,void(confirm("هل أنت متأكد من حذف هذه الوجبة؟")&&(console.log("Deleting batch:",{medicineId:a,batchId:t}),alert("تم حذف الوجبة بنجاح!")))},className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg",children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})]})]})]},s.id)}):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"لا توجد وجبات متوفرة"})]})]})},e.id)})}),0===w.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(c.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد أدوية"}),(0,t.jsx)("p",{className:"text-gray-500",children:"لم يتم العثور على أدوية تطابق معايير البحث"})]})]})})}}},e=>{e.O(0,[6874,6543,5647,8080,1932,988,8441,5964,7358],()=>e(e.s=19025)),_N_E=e.O()}]);