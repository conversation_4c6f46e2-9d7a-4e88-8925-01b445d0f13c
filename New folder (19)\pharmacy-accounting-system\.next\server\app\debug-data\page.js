(()=>{var a={};a.id=533,a.ids=[533],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13637:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["debug-data",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,61681)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug-data\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug-data\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/debug-data/page",pathname:"/debug-data",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/debug-data/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23733:(a,b,c)=>{Promise.resolve().then(c.bind(c,61681))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41323:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(60687),e=c(43210),f=c(21979),g=c(48210),h=c(61611),i=c(99270);function j(){let[a,b]=(0,e.useState)(null),[c,j]=(0,e.useState)(""),[k,l]=(0,e.useState)([]),[m,n]=(0,e.useState)([]),o=()=>{try{let a=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),c=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),d=JSON.parse(localStorage.getItem("medicines")||"[]"),e=JSON.parse(localStorage.getItem("medicine_batches")||"[]");console.log("\uD83D\uDCCA تحليل البيانات:"),console.log("الفواتير:",a),console.log("عناصر الفواتير:",c),console.log("الأدوية:",d),console.log("الدفعات:",e);let f={invoicesCount:a.length,itemsCount:c.length,medicinesCount:d.length,batchesCount:e.length,sampleInvoice:a[0]||null,sampleItem:c[0]||null,sampleMedicine:d[0]||null,sampleBatch:e[0]||null,itemsWithNames:c.filter(a=>a.medicine_name&&"غير محدد"!==a.medicine_name).length,itemsWithBatchStructure:c.filter(a=>a.medicine_batches?.medicines?.name).length,batchMedicineMapping:e.map(a=>{let b=d.find(b=>b.id===a.medicine_id);return{batchId:a.id,batchCode:a.batch_code,medicineId:a.medicine_id,medicineName:b?.name||"غير موجود"}}).slice(0,5),itemMedicineMapping:c.map(a=>{let b=e.find(b=>b.id===a.medicine_batch_id),c=d.find(a=>a.id===b?.medicine_id);return{itemId:a.id,batchId:a.medicine_batch_id,currentName:a.medicine_name||a.medicineName,batchStructureName:a.medicine_batches?.medicines?.name,actualMedicineName:c?.name,batchCode:b?.batch_code}}).slice(0,10)};b(f),l(a.slice(0,10)),n(d.slice(0,10))}catch(a){console.error("خطأ في تحليل البيانات:",a),b({error:a.toString()})}};return(0,d.jsx)(f.A,{children:(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,d.jsx)("div",{className:"bg-red-50 p-3 rounded-lg",children:(0,d.jsx)(g.A,{className:"h-6 w-6 text-red-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"تشخيص مشكلة البيانات"}),(0,d.jsx)("p",{className:"text-gray-600",children:"فحص مفصل لبيانات localStorage وهيكل الفواتير"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,d.jsxs)("button",{onClick:o,className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2 justify-center",children:[(0,d.jsx)(h.A,{className:"h-5 w-5"}),"تحليل البيانات العام"]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("input",{type:"text",value:c,onChange:a=>j(a.target.value),placeholder:"معرف الفاتورة",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg"}),(0,d.jsx)("button",{onClick:()=>{if(c)try{let a=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),d=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),e=JSON.parse(localStorage.getItem("medicines")||"[]"),f=JSON.parse(localStorage.getItem("medicine_batches")||"[]");console.log("\uD83D\uDD0D تحليل فاتورة محددة:",c),console.log("جميع الفواتير:",a.map(a=>({id:a.id,number:a.invoice_number}))),console.log("جميع العناصر:",d.map(a=>({id:a.id,invoice_id:a.invoice_id})));let g=a.find(a=>a.id===c),h=d.filter(a=>a.invoice_id===c);if(console.log("الفاتورة الموجودة:",g),console.log("العناصر المطابقة:",h),console.log("عدد العناصر المطابقة:",h.length),0===h.length){console.log("⚠️ لم يتم العثور على عناصر، جرب استراتيجيات أخرى...");let a=d.filter(a=>a.invoice_number===g?.invoice_number);console.log("العناصر بالرقم:",a);let b=d.filter(a=>a.invoice_id&&a.invoice_id.includes(c.slice(-5)));console.log("العناصر بالمطابقة الجزئية:",b),console.log("جميع العناصر للفحص اليدوي:",d)}let i={searchedId:c,invoice:g,itemsFound:h.length,allInvoiceIds:a.map(a=>a.id),allItemInvoiceIds:[...new Set(d.map(a=>a.invoice_id))],items:h.map(a=>{let b=f.find(b=>b.id===a.medicine_batch_id),c=e.find(a=>a.id===b?.medicine_id);return{...a,debug:{batchFound:!!b,medicineFound:!!c,batchData:b,medicineData:c,currentMedicineName:a.medicine_name||a.medicineName,batchStructureName:a.medicine_batches?.medicines?.name,calculatedName:c?.name||"غير موجود"}}})};b({specificInvoice:i}),console.log("تحليل مفصل:",i)}catch(a){console.error("خطأ في تحليل الفاتورة:",a)}},className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700",children:(0,d.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,d.jsxs)("button",{onClick:()=>{try{let a=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]"),b=JSON.parse(localStorage.getItem("medicines")||"[]"),c=JSON.parse(localStorage.getItem("medicine_batches")||"[]");console.log("\uD83D\uDD27 إصلاح هيكل البيانات..."),console.log("\uD83D\uDCE6 عناصر الفواتير:",a.length),console.log("\uD83D\uDC8A الأدوية:",b.length),console.log("\uD83D\uDCCB الدفعات:",c.length);let d=new Set,e=a.map(a=>{let e=c.find(b=>b.id===a.medicine_batch_id);if(e){let c=b.find(a=>a.id===e.medicine_id);return c?.name?(console.log(`✅ إصلاح: ${c.name} للعنصر ${a.id}`),{...a,medicine_name:c.name,medicineName:c.name,medicine_batches:{id:e.id,batch_code:e.batch_code||"",expiry_date:e.expiry_date||"",medicine_id:e.medicine_id,medicines:{id:c.id,name:c.name,category:c.category||"",manufacturer:c.manufacturer||"",strength:c.strength||"",form:c.form||""}}}):(console.log(`❌ لم يتم العثور على الدواء للدفعة ${e.id}`),a)}{d.add(a.medicine_batch_id),console.log(`⚠️ دفعة مفقودة: ${a.medicine_batch_id}`);let e=null;if(a.medicine_name&&"غير محدد"!==a.medicine_name&&(e=b.find(b=>b.name===a.medicine_name)),!e&&b.length>0&&(e=b[0],console.log(`🔄 استخدام دواء افتراضي: ${e.name}`)),!e)return console.log(`❌ لا توجد أدوية متاحة لإنشاء دفعة للعنصر ${a.id}`),a;{let b={id:a.medicine_batch_id,medicine_id:e.id,batch_code:`BATCH-${Date.now()}`,quantity:1e3,expiry_date:new Date(Date.now()+31536e6).toISOString().split("T")[0],created_at:new Date().toISOString()};return c.push(b),console.log(`✅ تم إنشاء دفعة جديدة: ${b.batch_code} للدواء ${e.name}`),{...a,medicine_name:e.name,medicineName:e.name,medicine_batches:{id:b.id,batch_code:b.batch_code,expiry_date:b.expiry_date,medicine_id:b.medicine_id,medicines:{id:e.id,name:e.name,category:e.category||"",manufacturer:e.manufacturer||"",strength:e.strength||"",form:e.form||""}}}}}});localStorage.setItem("medicine_batches",JSON.stringify(c)),localStorage.setItem("sales_invoice_items",JSON.stringify(e)),console.log(`✅ تم حفظ البيانات المصلحة`),console.log(`📋 تم إنشاء ${d.size} دفعة جديدة`),o()}catch(a){console.error("خطأ في إصلاح البيانات:",a)}},className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 flex items-center gap-2 justify-center",children:[(0,d.jsx)(g.A,{className:"h-5 w-5"}),"إصلاح فوري"]})]}),k.length>0&&(0,d.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-4",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"الفواتير المتاحة (أول 10):"}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2",children:k.map(a=>(0,d.jsxs)("button",{onClick:()=>j(a.id),className:"bg-white p-2 rounded border text-sm hover:bg-blue-100 text-left",children:[(0,d.jsx)("div",{className:"font-medium",children:a.invoice_number}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:[a.id.slice(0,8),"..."]})]},a.id))})]}),m.length>0&&(0,d.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 mb-4",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"الأدوية المتاحة (أول 10):"}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:m.map(a=>(0,d.jsxs)("div",{className:"bg-white p-2 rounded border text-sm",children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:a.category}),(0,d.jsxs)("div",{className:"text-xs text-gray-400",children:[a.id.slice(0,8),"..."]})]},a.id))})]}),a&&(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"نتائج التشخيص:"}),(0,d.jsx)("div",{className:"bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto",children:(0,d.jsx)("pre",{children:JSON.stringify(a,null,2)})})]})]})})})}},61611:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},61681:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\debug-data\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\debug-data\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65589:(a,b,c)=>{Promise.resolve().then(c.bind(c,41323))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,314,979],()=>b(b.s=13637));module.exports=c})();