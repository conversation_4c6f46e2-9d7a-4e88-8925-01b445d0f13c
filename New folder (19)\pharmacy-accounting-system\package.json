{"name": "pharmacy-accounting-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "export": "next build && next export", "start": "next start", "lint": "next lint", "deploy": "npm run build && firebase deploy", "firebase:init": "firebase init", "firebase:serve": "firebase serve"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@supabase/supabase-js": "^2.52.1", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.525.0", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-to-print": "^3.1.1", "recharts": "^3.1.0", "xlsx": "^0.18.5", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}