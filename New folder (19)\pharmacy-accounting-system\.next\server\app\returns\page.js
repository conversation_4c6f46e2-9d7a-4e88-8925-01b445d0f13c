(()=>{var a={};a.id=762,a.ids=[762],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17706:(a,b,c)=>{Promise.resolve().then(c.bind(c,94272))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},26891:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>A});var d=c(60687),e=c(43210),f=c(21979),g=c(48730),h=c(5336),i=c(35071),j=c(71444),k=c(96474),l=c(99270),m=c(80462),n=c(13943),o=c(10022),p=c(28561),q=c(19080),r=c(58869),s=c(79410),t=c(23928),u=c(40228),v=c(13861);let w=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var x=c(84997),y=c(97711),z=c(31836);function A(){let[a,b]=(0,e.useState)([]),[A,B]=(0,e.useState)([]),[C,D]=(0,e.useState)(""),[E,F]=(0,e.useState)("all"),[G,H]=(0,e.useState)("all"),[I,J]=(0,e.useState)(!1),[K,L]=(0,e.useState)("select-type"),[M,N]=(0,e.useState)("sales"),[O,P]=(0,e.useState)([]),[Q,R]=(0,e.useState)(null),[S,T]=(0,e.useState)([]),[U,V]=(0,e.useState)(""),[W,X]=(0,e.useState)(""),[Y,Z]=(0,e.useState)(!1),[$,_]=(0,e.useState)(!1),{settings:aa}=(0,y.usePrintSettings)(),ab=async()=>{console.log("Loading returns...");let a=await (0,z.getReturns)();console.log("Returns result:",a),a.success&&a.data?(console.log("Setting returns data:",a.data.length,"items"),b(a.data)):(console.log("No returns data or failed to load"),b([]))},ac=()=>{J(!0),L("select-type"),N("sales"),R(null),T([]),V(""),X("")},ad=async a=>{N(a),Z(!0);try{let b="sales"===a?await (0,z.getSalesInvoices)():await (0,z.getPurchaseInvoices)();b.success&&b.data&&(P(b.data),L("select-invoice"))}catch(a){console.error("Error loading invoices:",a)}finally{Z(!1)}},ae=()=>S.reduce((a,b)=>a+(b.selected_quantity||0)*b.unit_price,0),af=async()=>{if(!Q||!U)return void alert("يرجى ملء جميع الحقول المطلوبة");let a=S.filter(a=>(a.selected_quantity||0)>0);if(0===a.length)return void alert("يرجى اختيار عناصر للإرجاع");Z(!0);try{let b=`${"sales"===M?"SR":"PR"}-${Date.now()}`,c={return_number:b,original_invoice_id:Q.id,total_amount:ae(),reason:U,status:"pending",notes:W,..."sales"===M?{customer_id:Q.customer_id,customer_name:Q.customers?.name||Q.customer_name,customers:{name:Q.customers?.name||Q.customer_name}}:{supplier_id:Q.supplier_id,supplier_name:Q.suppliers?.name||Q.supplier_name,suppliers:{name:Q.suppliers?.name||Q.supplier_name}}},d=a.map(a=>({batchId:a.batchId||a.medicine_batch_id,medicineId:a.medicineId||a.medicine_id,medicine_name:a.medicine_name||a.medicineName||a.medicine_batches?.medicines?.name||a.medicines?.name||"غير محدد",batch_code:a.batch_code||a.batchCode||a.medicine_batches?.batch_number||"",expiry_date:a.expiry_date||a.expiryDate||a.medicine_batches?.expiry_date||"",quantity:a.selected_quantity||0,unitPrice:a.unit_price||a.unitPrice||a.unit_cost||a.unitCost||0,totalPrice:(a.selected_quantity||0)*(a.unit_price||a.unitPrice||a.unit_cost||a.unitCost||0),unit_price:a.unit_price||a.unitPrice||a.unit_cost||a.unitCost||0,total_price:(a.selected_quantity||0)*(a.unit_price||a.unitPrice||a.unit_cost||a.unitCost||0),unit_cost:a.unit_cost||a.unitCost||a.unit_price||a.unitPrice||0,total_cost:(a.selected_quantity||0)*(a.unit_cost||a.unitCost||a.unit_price||a.unitPrice||0),return_reason:U}));console.log("Processing return:",{returnType:M,returnData:c,processItems:d});let e=await (0,z.processReturn)(M,c,d);if(console.log("Process return result:",e),e.success){alert("تم إنشاء المرتجع بنجاح!");let a={...c,return_number:b,type:M,return_type:M,return_invoice_items:d,items:d,created_at:new Date().toISOString()};setTimeout(()=>{(0,y.printReport)([a],"returns","إيصال مرتجع",aa)},500),J(!1),console.log("Reloading returns after successful creation..."),await ab()}else console.error("Failed to create return:",e),alert("حدث خطأ أثناء إنشاء المرتجع")}catch(a){console.error("Error processing return:",a),alert("حدث خطأ أثناء إنشاء المرتجع")}finally{Z(!1)}},ag=async a=>{if(confirm("هل أنت متأكد من موافقة هذا المرتجع؟"))try{let{updateReturnStatus:b}=await Promise.resolve().then(c.bind(c,31836));(await b(a,"approved")).success?(await ab(),alert("تم قبول المرتجع بنجاح!")):alert("حدث خطأ أثناء قبول المرتجع")}catch(a){console.error("Error approving return:",a),alert("حدث خطأ أثناء قبول المرتجع")}},ah=async a=>{let b=prompt("يرجى إدخال سبب رفض المرتجع:");if(b)try{let{updateReturnStatus:d}=await Promise.resolve().then(c.bind(c,31836));(await d(a,"rejected",b)).success?(await ab(),alert("تم رفض المرتجع!")):alert("حدث خطأ أثناء رفض المرتجع")}catch(a){console.error("Error rejecting return:",a),alert("حدث خطأ أثناء رفض المرتجع")}},ai=async a=>{try{let{getReturnForPrint:b}=await Promise.resolve().then(c.bind(c,31836)),d=await b(a.id);if(d.success&&d.data){let{printInvoice:a}=await Promise.resolve().then(c.bind(c,97711));await a(d.data,"return",aa)}else{let b={...a,return_invoice_items:a.sales_return_items||a.purchase_return_items||[]},{printInvoice:d}=await Promise.resolve().then(c.bind(c,97711));await d(b,"return",aa)}}catch(a){console.error("Error printing return:",a),alert("حدث خطأ أثناء طباعة المرتجع")}};return(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة المرتجعات"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة مرتجعات المبيعات والمشتريات مع تأثير مباشر على الحسابات"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:()=>{A.length>0?_(!0):alert("لا توجد مرتجعات للطباعة")},disabled:0===A.length,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2",children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),"معاينة وطباعة"]}),(0,d.jsxs)("button",{onClick:()=>{A.length>0?(0,y.printReport)(A,"returns","تقرير المرتجعات",aa):alert("لا توجد مرتجعات للطباعة")},disabled:0===A.length,className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2",children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),"طباعة مباشرة"]}),(0,d.jsxs)("button",{onClick:ac,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),"إنشاء مرتجع جديد"]})]})]}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البحث"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",value:C,onChange:a=>D(a.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"البحث في المرتجعات..."})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"نوع المرتجع"}),(0,d.jsxs)("select",{value:E,onChange:a=>F(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الأنواع"}),(0,d.jsx)("option",{value:"sales",children:"مرتجعات المبيعات"}),(0,d.jsx)("option",{value:"purchase",children:"مرتجعات المشتريات"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الحالة"}),(0,d.jsxs)("select",{value:G,onChange:a=>H(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,d.jsx)("option",{value:"pending",children:"قيد المراجعة"}),(0,d.jsx)("option",{value:"approved",children:"مقبول"}),(0,d.jsx)("option",{value:"rejected",children:"مرفوض"})]})]}),(0,d.jsx)("div",{className:"flex items-end",children:(0,d.jsxs)("button",{onClick:()=>{D(""),F("all"),H("all")},className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"إعادة تعيين"]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===A.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(n.A,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد مرتجعات"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"لم يتم إنشاء أي مرتجعات بعد"}),(0,d.jsx)("button",{onClick:ac,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"إنشاء أول مرتجع"})]}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم المرتجع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النوع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"العميل/المورد"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"السبب"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الحالة"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"إجراءات"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:A.map((a,b)=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900",children:a.return_number})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"sales"===a.type?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"}`,children:"sales"===a.type?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"مرتجع مبيعات"]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(q.A,{className:"h-3 w-3 mr-1"}),"مرتجع مشتريات"]})})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:["sales"===a.type?(0,d.jsx)(r.A,{className:"h-4 w-4 text-gray-400 mr-2"}):(0,d.jsx)(s.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:a.customers?.name||a.customer_name||a.suppliers?.name||a.supplier_name||"غير محدد"})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(t.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,d.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[a.total_amount.toLocaleString()," د.ع"]})]})}),(0,d.jsx)("td",{className:"px-6 py-4",children:(0,d.jsx)("span",{className:"text-sm text-gray-900 line-clamp-2",children:a.reason})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(a=>{switch(a){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(a.status)}`,children:[(a=>{switch(a){case"pending":return(0,d.jsx)(g.A,{className:"h-4 w-4 text-yellow-500"});case"approved":return(0,d.jsx)(h.A,{className:"h-4 w-4 text-green-500"});case"rejected":return(0,d.jsx)(i.A,{className:"h-4 w-4 text-red-500"});default:return(0,d.jsx)(g.A,{className:"h-4 w-4 text-gray-500"})}})(a.status),(0,d.jsx)("span",{className:"mr-1",children:(a=>{switch(a){case"pending":return"قيد المراجعة";case"approved":return"مقبول";case"rejected":return"مرفوض";default:return"غير محدد"}})(a.status)})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:new Date(a.created_at).toLocaleDateString("ar-EG")})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>ai(a),className:"text-gray-600 hover:text-gray-800 p-1 rounded-md hover:bg-gray-100",title:"طباعة المرتجع",children:(0,d.jsx)(j.A,{className:"h-4 w-4"})}),"pending"===a.status&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("button",{onClick:()=>ag(a.id),className:"text-green-600 hover:text-green-800 p-1 rounded-md hover:bg-green-50",title:"قبول المرتجع",children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}),(0,d.jsx)("button",{onClick:()=>ah(a.id),className:"text-red-600 hover:text-red-800 p-1 rounded-md hover:bg-red-50",title:"رفض المرتجع",children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})]}),(0,d.jsx)("button",{className:"text-blue-600 hover:text-blue-800 p-1 rounded-md hover:bg-blue-50",title:"عرض التفاصيل",children:(0,d.jsx)(v.A,{className:"h-4 w-4"})})]})})]},a.id||`return_${b}_${Date.now()}`))})]})})}),I&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-4xl max-h-screen overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"إنشاء مرتجع جديد"}),(0,d.jsx)("button",{onClick:()=>J(!1),className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,d.jsxs)("div",{className:"p-6",children:["select-type"===K&&(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"اختر نوع المرتجع"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("button",{onClick:()=>ad("sales"),className:"p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors",children:[(0,d.jsx)(p.A,{className:"h-12 w-12 text-blue-600 mx-auto mb-4"}),(0,d.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"مرتجع مبيعات"}),(0,d.jsx)("p",{className:"text-gray-600",children:"إرجاع أدوية تم بيعها للعملاء"})]}),(0,d.jsxs)("button",{onClick:()=>ad("purchase"),className:"p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors",children:[(0,d.jsx)(q.A,{className:"h-12 w-12 text-purple-600 mx-auto mb-4"}),(0,d.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"مرتجع مشتريات"}),(0,d.jsx)("p",{className:"text-gray-600",children:"إرجاع أدوية تم شراؤها من الموردين"})]})]})]}),"select-invoice"===K&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["اختر الفاتورة الأصلية - ","sales"===M?"فواتير المبيعات":"فواتير المشتريات"]}),(0,d.jsxs)("button",{onClick:()=>L("select-type"),className:"flex items-center gap-2 text-gray-600 hover:text-gray-800",children:[(0,d.jsx)(w,{className:"h-4 w-4"}),"رجوع"]})]}),Y?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,d.jsx)("p",{className:"text-gray-500 mt-2",children:"جاري تحميل الفواتير..."})]}):(0,d.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:O.map((a,b)=>(0,d.jsx)("button",{onClick:()=>{R(a),T(("sales"===M?a.sales_invoice_items||[]:a.purchase_invoice_items||[]).map((a,b)=>({id:a.id||`item_${b}_${Date.now()}`,medicine_name:a.medicine_batches?.medicines?.name||a.medicines?.name||"Unknown",batch_code:a.medicine_batches?.batch_code||a.batch_code||"",quantity:a.quantity,unit_price:a.unit_price||a.unit_cost||0,total_price:a.total_price||a.total_cost||0,medicine_batch_id:a.medicine_batch_id,medicine_id:a.medicine_id,selected_quantity:0}))),L("select-items")},className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:a.invoice_number}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["sales"===M?a.customers?.name||a.customer_name:a.suppliers?.name," • ",a.final_amount.toLocaleString()," د.ع"]})]}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString("ar-EG")})]})},a.id||`invoice_${b}_${Date.now()}`))})]}),"select-items"===K&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"اختر العناصر المراد إرجاعها"}),(0,d.jsxs)("button",{onClick:()=>L("select-invoice"),className:"flex items-center gap-2 text-gray-600 hover:text-gray-800",children:[(0,d.jsx)(w,{className:"h-4 w-4"}),"رجوع"]})]}),(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-blue-800",children:[(0,d.jsx)("strong",{children:"الفاتورة:"})," ",Q?.invoice_number," •",(0,d.jsx)("strong",{children:" العميل/المورد:"})," ","sales"===M?Q?.customers?.name||Q?.customer_name:Q?.suppliers?.name]})}),(0,d.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:S.map((a,b)=>(0,d.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:a.medicine_name}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["وجبة: ",a.batch_code," • سعر الوحدة: ",a.unit_price.toLocaleString()," د.ع"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"text-sm text-gray-500",children:["الكمية الأصلية: ",a.quantity]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:"text-sm text-gray-700",children:"كمية الإرجاع:"}),(0,d.jsx)("input",{type:"number",min:"0",max:a.quantity,value:a.selected_quantity||0,onChange:b=>{var c,d;return c=a.id,d=Number(b.target.value),void T(a=>a.map(a=>a.id===c?{...a,selected_quantity:Math.min(Math.max(0,d),a.quantity)}:a))},className:"w-20 px-2 py-1 border border-gray-300 rounded text-center"})]})]})]})},a.id||`return_item_${b}_${Date.now()}`))}),(0,d.jsx)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:(0,d.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,d.jsx)("span",{children:"إجمالي مبلغ الإرجاع:"}),(0,d.jsxs)("span",{className:"text-blue-600",children:[ae().toLocaleString()," د.ع"]})]})}),(0,d.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"سبب الإرجاع *"}),(0,d.jsxs)("select",{value:U,onChange:a=>V(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"",children:"اختر السبب"}),(0,d.jsx)("option",{value:"دواء منتهي الصلاحية",children:"دواء منتهي الصلاحية"}),(0,d.jsx)("option",{value:"عيب في التصنيع",children:"عيب في التصنيع"}),(0,d.jsx)("option",{value:"عدم الحاجة للدواء",children:"عدم الحاجة للدواء"}),(0,d.jsx)("option",{value:"خطأ في الطلب",children:"خطأ في الطلب"}),(0,d.jsx)("option",{value:"تلف في التغليف",children:"تلف في التغليف"}),(0,d.jsx)("option",{value:"أخرى",children:"أخرى"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات إضافية"}),(0,d.jsx)("textarea",{value:W,onChange:a=>X(a.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"أي ملاحظات إضافية حول المرتجع..."})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,d.jsx)("button",{onClick:()=>J(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"إلغاء"}),(0,d.jsx)("button",{onClick:af,disabled:Y||0===ae()||!U,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed",children:Y?"جاري الإنشاء...":"إنشاء المرتجع"})]})]})]})]})}),$&&A.length>0&&(0,d.jsx)(x.Ay,{title:"تقرير المرتجعات",data:A,type:"report",settings:aa,onClose:()=>_(!1),children:(0,d.jsx)(x.W1,{reportData:A,reportType:"returns",title:"تقرير المرتجعات",settings:aa})})]})})}},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},36907:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["returns",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,94272)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\returns\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\returns\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/returns/page",pathname:"/returns",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/returns/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79410:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},80462:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},80754:(a,b,c)=>{Promise.resolve().then(c.bind(c,26891))},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94272:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\returns\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\returns\\page.tsx","default")},94735:a=>{"use strict";a.exports=require("events")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,463,314,979,31,711,997],()=>b(b.s=36907));module.exports=c})();