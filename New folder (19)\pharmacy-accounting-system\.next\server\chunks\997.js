exports.id=997,exports.ids=[997],exports.modules={10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},15290:()=>{},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},28947:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48340:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},79300:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]])},84997:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>w,W1:()=>y,dt:()=>x});var d=c(60687);c(43210);var e=c(71444),f=c(31158),g=c(11860),h=c(97992),i=c(48340),j=c(58869),k=c(10022),l=c(40228),m=c(5336),n=c(48730),o=c(53411),p=c(79300),q=c(25541),r=c(28561),s=c(28947),t=c(93613);c(15290);let u=`
  @media print {
    body {
      margin: 0;
      padding: 0;
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      line-height: 1.2;
      color: #000;
      background: white;
    }

    .print-content {
      max-width: none;
      margin: 0;
      padding: 10mm;
      box-shadow: none;
      border: none;
      background: white;
    }

    .no-print { display: none !important; }

    /* Classic table styles */
    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 5px;
      border: 2px solid #000;
    }

    th, td {
      border: 1px solid #000;
      padding: 4px 6px;
      text-align: center;
      vertical-align: middle;
      font-size: 11px;
      height: 25px;
    }

    th {
      background-color: #f0f0f0 !important;
      font-weight: bold;
      text-align: center;
    }

    /* Header border styling */
    .border-2 {
      border: 2px solid #000 !important;
    }

    .border {
      border: 1px solid #000 !important;
    }

    .border-black {
      border-color: #000 !important;
    }

    .border-r-2 {
      border-right: 2px solid #000 !important;
    }

    .border-b-2 {
      border-bottom: 2px solid #000 !important;
    }

    .border-t-2 {
      border-top: 2px solid #000 !important;
    }

    /* Grid layout for print */
    .grid {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    .grid-cols-3 > div {
      display: table-cell;
      width: 33.333%;
      vertical-align: top;
    }

    .grid-cols-4 > div {
      display: table-cell;
      width: 25%;
      vertical-align: top;
    }

    .grid-cols-2 > div {
      display: table-cell;
      width: 50%;
      vertical-align: top;
    }

    /* Text alignment */
    .text-center { text-align: center !important; }
    .text-right { text-align: right !important; }
    .text-left { text-align: left !important; }

    /* Font weights and sizes */
    .font-bold { font-weight: bold !important; }
    .text-xl { font-size: 18px !important; }
    .text-lg { font-size: 16px !important; }
    .text-md { font-size: 14px !important; }
    .text-sm { font-size: 12px !important; }
    .text-xs { font-size: 10px !important; }

    /* Hide modern styling elements */
    .rounded, .rounded-lg, .rounded-full, .shadow, .shadow-sm {
      border-radius: 0 !important;
      box-shadow: none !important;
    }

    /* Color adjustments for print */
    .text-blue-600, .bg-blue-100 { color: #000 !important; background: transparent !important; }
    .text-green-600, .bg-green-100 { color: #000 !important; background: transparent !important; }
    .text-red-600, .bg-red-100 { color: #000 !important; background: transparent !important; }
    .text-yellow-600, .bg-yellow-100 { color: #000 !important; background: transparent !important; }
    .text-gray-600 { color: #333 !important; }

    /* Background colors */
    .bg-gray-100 { background-color: #f5f5f5 !important; }

    /* Hide interactive elements */
    button, .btn, .no-print, .print-button {
      display: none !important;
    }

    /* Signature circle */
    .rounded-full {
      border: 2px solid #000 !important;
      border-radius: 50% !important;
    }

    /* Font family for numbers */
    .font-mono { font-family: 'Courier New', monospace !important; }
  }
`,v={companyName:"صيدلية الشفاء",companyAddress:"بغداد - العراق",companyPhone:"+*********** 4567",companyEmail:"<EMAIL>",showHeader:!0,showFooter:!0,showLogo:!0,showWatermark:!1,watermark:"نسخة أصلية",footerText:"شكراً لثقتكم بنا - نتمنى لكم الشفاء العاجل",fontSize:"medium",includeDate:!0,includePageNumbers:!0,includeQRCode:!1,headerColor:"#1f2937",accentColor:"#3b82f6",textColor:"#374151",backgroundColor:"#ffffff"};function w({children:a,title:b,settings:c,onClose:n,onPrint:o,onDownload:p}){let q={...v,...c};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:u}}),(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border-b bg-gray-50",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"معاينة الطباعة"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("button",{onClick:()=>{window.print(),o?.()},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(e.A,{className:"h-4 w-4"}),"طباعة"]}),(0,d.jsxs)("button",{onClick:()=>{p?.()},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,d.jsx)(f.A,{className:"h-4 w-4"}),"تحميل"]}),(0,d.jsxs)("button",{onClick:n,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center gap-2",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),"إغلاق"]})]})]}),(0,d.jsx)("div",{className:"overflow-auto max-h-[calc(90vh-80px)]",children:(0,d.jsxs)("div",{className:`print-content bg-white p-8 ${"small"===q.fontSize?"text-sm":"large"===q.fontSize?"text-lg":"text-base"}`,style:{color:q.textColor,backgroundColor:q.backgroundColor},children:[q.showHeader&&(0,d.jsxs)("div",{className:"mb-8 relative",children:[q.showWatermark&&q.watermark&&(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-5 pointer-events-none",children:(0,d.jsx)("div",{className:"text-6xl font-bold transform rotate-45 text-gray-400",children:q.watermark})}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center",children:[q.showLogo&&(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("div",{className:"w-24 h-24 rounded-full mx-auto flex items-center justify-center text-white text-3xl font-bold shadow-lg",style:{backgroundColor:q.accentColor},children:q.companyName.charAt(0)})}),(0,d.jsx)("h1",{className:"text-3xl font-bold mb-3",style:{color:q.headerColor},children:q.companyName}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,d.jsx)(h.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:q.companyAddress})]}),(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:q.companyPhone})]}),(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:q.companyEmail})]})]})]})]}),(0,d.jsx)("div",{className:"text-center mb-8",children:(0,d.jsxs)("div",{className:"bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 shadow-sm",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-3",children:[(0,d.jsx)(k.A,{className:"h-6 w-6",style:{color:q.accentColor}}),(0,d.jsx)("h2",{className:"text-2xl font-bold",style:{color:q.headerColor},children:b})]}),q.includeDate&&(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:["تاريخ الإنشاء: ",new Date().toLocaleDateString("ar-EG")]})]}),(0,d.jsxs)("div",{className:"mt-3 text-xs text-gray-500",children:["رقم المستند: DOC-",Date.now().toString().slice(-8)]})]})}),a,q.showFooter&&(0,d.jsx)("div",{className:"mt-8 border-t-2 border-gray-200 pt-6",children:(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 text-center",children:[(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("p",{className:"text-lg font-semibold text-gray-800 mb-2",children:q.footerText}),(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,d.jsx)("span",{children:"مستند معتمد ومطبوع إلكترونياً"})]})]}),(0,d.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-500",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"نظام إدارة الصيدلية"}),(0,d.jsx)("br",{}),"الإصدار 2.0 - 2024"]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"تاريخ الإنشاء:"}),(0,d.jsx)("br",{}),new Date().toLocaleDateString("ar-EG")," - ",new Date().toLocaleTimeString("ar-EG")]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"حالة المستند:"}),(0,d.jsx)("br",{}),(0,d.jsx)("span",{className:"text-green-600 font-semibold",children:"✓ صالح ومعتمد"})]})]})}),q.includeQRCode&&(0,d.jsx)("div",{className:"mt-4 flex justify-center",children:(0,d.jsx)("div",{className:"w-16 h-16 bg-gray-200 border-2 border-dashed border-gray-400 rounded flex items-center justify-center text-xs text-gray-500",children:"QR Code"})})]})})]})})]})})]})}function x({invoice:a,type:b="sales",settings:c}){let e={...v,...c};return(0,d.jsxs)("div",{className:"print-content bg-white",style:{fontFamily:"Arial, sans-serif"},children:[(0,d.jsxs)("div",{className:"invoice-header",children:[(0,d.jsxs)("div",{className:"header-row border-b-2",children:[(0,d.jsxs)("div",{className:"header-cell english-text",children:[(0,d.jsx)("h1",{className:"text-xl font-bold mb-2",style:{letterSpacing:"2px"},children:e.companyName.toUpperCase()}),(0,d.jsx)("h2",{className:"text-lg font-bold",children:"PHARMACY"}),(0,d.jsx)("h3",{className:"text-sm",children:"MANAGEMENT SYSTEM"})]}),(0,d.jsx)("div",{className:"header-cell",children:(0,d.jsx)("div",{className:"logo-container",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:e.companyName.charAt(0)}),(0,d.jsx)("div",{className:"text-xs font-bold",children:"LOGO"})]})})}),(0,d.jsxs)("div",{className:"header-cell arabic-text",children:[(0,d.jsx)("h1",{className:"text-xl font-bold mb-2",children:e.companyName}),(0,d.jsx)("p",{className:"text-sm",children:e.companyAddress}),(0,d.jsx)("p",{className:"text-sm",children:e.companyPhone})]})]}),(0,d.jsxs)("div",{className:"header-row text-sm",children:[(0,d.jsxs)("div",{className:"header-cell arabic-text",children:[(0,d.jsx)("div",{className:"font-bold",children:"اسم الزبون رقم المريض"}),(0,d.jsx)("div",{className:"mt-1",children:"sales"===b?a.customers?.name||a.customer_name||"عميل نقدي":a.suppliers?.name||"غير محدد"})]}),(0,d.jsxs)("div",{className:"header-cell",children:[(0,d.jsx)("div",{className:"font-bold",children:"التاريخ: بين"}),(0,d.jsx)("div",{className:"mt-1",children:new Date(a.created_at).toLocaleDateString("ar-EG")})]}),(0,d.jsxs)("div",{className:"header-cell",children:[(0,d.jsx)("div",{className:"font-bold",children:"فاتورة رقم:"}),(0,d.jsx)("div",{className:"mt-1 font-mono",children:a.invoice_number})]}),(0,d.jsxs)("div",{className:"header-cell",children:[(0,d.jsx)("div",{className:"font-bold",children:"رقم الفاتورة:"}),(0,d.jsx)("div",{className:"mt-1",children:"sales"===b?"مبيعات":"مشتريات"})]})]}),(0,d.jsxs)("div",{className:"header-row text-sm border-t",children:[(0,d.jsx)("div",{className:"header-cell arabic-text",style:{width:"50%"},children:(0,d.jsx)("span",{className:"font-bold",children:"المنطقة: الجمهورية"})}),(0,d.jsx)("div",{className:"header-cell",style:{width:"50%"},children:(0,d.jsxs)("span",{className:"font-bold",children:["العنوان: ",e.companyAddress]})})]})]}),(0,d.jsxs)("table",{className:"invoice-table",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"number-cell",children:"ت"}),(0,d.jsx)("th",{className:"medicine-cell",children:"اسم الدواء"}),(0,d.jsx)("th",{className:"number-cell",children:"الكمية"}),(0,d.jsx)("th",{className:"number-cell",children:"سعر الوحدة"}),(0,d.jsx)("th",{className:"number-cell",children:"المجموع"}),"return"===b&&(0,d.jsx)("th",{className:"number-cell",children:"سبب المرتجع"}),(0,d.jsx)("th",{className:"number-cell",children:"رقم الدفعة"}),(0,d.jsx)("th",{className:"number-cell",children:"تاريخ الانتهاء"})]})}),(0,d.jsxs)("tbody",{children:[("return"===b?a.return_invoice_items||a.sales_return_items||a.purchase_return_items||[]:"sales"===b?a.sales_invoice_items:a.purchase_invoice_items)?.map((c,e)=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{className:"number-cell",children:e+1}),(0,d.jsxs)("td",{className:"medicine-cell arabic-text",children:[(0,d.jsx)("div",{className:"font-bold",children:"return"===b?c.medicine_name||c.medicineName||c.medicine_batches?.medicines?.name||c.medicines?.name||"غير محدد":"sales"===b?c.medicine_name||c.medicineName||c.medicine_batches?.medicines?.name||"غير محدد":c.medicine_name||c.medicineName||c.medicines?.name||"غير محدد"}),("sales"===b&&c.medicine_batches?.medicines?.category||"purchase"===b&&c.medicines?.category||"return"===b&&(c.medicine_batches?.medicines?.category||c.medicines?.category))&&(0,d.jsx)("div",{className:"text-xs text-gray-600",children:c.medicine_batches?.medicines?.category||c.medicines?.category}),"sales"===b&&c.medicine_batches?.medicines&&(0,d.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[c.medicine_batches.medicines.strength&&(0,d.jsxs)("span",{children:[c.medicine_batches.medicines.strength," "]}),c.medicine_batches.medicines.form&&(0,d.jsxs)("span",{children:["(",c.medicine_batches.medicines.form,")"]})]})]}),(0,d.jsx)("td",{className:"number-cell",children:c.quantity}),(0,d.jsx)("td",{className:"number-cell",children:"sales"===b?(c.unit_price||0).toLocaleString():(c.unit_cost||c.unitCost||0).toLocaleString()}),(0,d.jsx)("td",{className:"number-cell",children:"sales"===b?(c.total_price||0).toLocaleString():(c.total_cost||c.totalCost||0).toLocaleString()}),"return"===b&&(0,d.jsx)("td",{className:"number-cell text-xs",children:c.return_reason||a.reason||"غير محدد"}),(0,d.jsx)("td",{className:"number-cell text-xs",children:"return"===b?c.batch_code||c.batchCode||c.medicine_batches?.batch_number||"---":"sales"===b?c.medicine_batches?.batch_number?c.medicine_batches.batch_number.slice(-6):"---":c.batch_code||c.batchCode||"---"}),(0,d.jsx)("td",{className:"number-cell text-xs",children:"return"===b?c.expiry_date||c.expiryDate||c.medicine_batches?.expiry_date?new Date(c.expiry_date||c.expiryDate||c.medicine_batches.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"---":"sales"===b?c.medicine_batches?.expiry_date?new Date(c.medicine_batches.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"---":c.expiry_date||c.expiryDate?new Date(c.expiry_date||c.expiryDate).toLocaleDateString("en-GB").replace(/\//g,"/"):"---"})]},`item_${e}_${c.id||e}`)),Array.from({length:Math.max(0,8-(("return"===b?a.return_invoice_items||a.sales_return_items||a.purchase_return_items||[]:"sales"===b?a.sales_invoice_items:a.purchase_invoice_items)?.length||0))}).map((a,c)=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{children:"\xa0"}),(0,d.jsx)("td",{children:"\xa0"}),(0,d.jsx)("td",{children:"\xa0"}),(0,d.jsx)("td",{children:"\xa0"}),(0,d.jsx)("td",{children:"\xa0"}),"return"===b&&(0,d.jsx)("td",{children:"\xa0"}),(0,d.jsx)("td",{children:"\xa0"}),(0,d.jsx)("td",{children:"\xa0"})]},`empty_${c}`)),(0,d.jsxs)("tr",{className:"bg-gray-100",children:[(0,d.jsx)("td",{className:"text-center font-bold arabic-text",colSpan:4,children:"المجموع الكلي"}),(0,d.jsx)("td",{className:"number-cell",children:a.final_amount?.toLocaleString()||a.total_amount?.toLocaleString()||0}),(0,d.jsx)("td",{colSpan:"return"===b?3:2,children:"\xa0"})]})]})]}),(0,d.jsxs)("div",{className:"notes-section",children:[(0,d.jsx)("div",{className:"font-bold mb-2 text-sm",children:"ملاحظات: تاريخ صرف سنة البداية"}),(0,d.jsx)("div",{className:"min-h-16 text-sm arabic-text",children:a.notes||""})]}),(0,d.jsx)("div",{className:"signature-area",children:(0,d.jsxs)("div",{className:"grid grid-cols-2",children:[(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("div",{className:"signature-circle",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-xs",children:"توقيع"}),(0,d.jsx)("div",{className:"text-xs",children:"الصيدلي"})]})})}),(0,d.jsxs)("div",{className:"text-sm arabic-text",children:[(0,d.jsxs)("div",{className:"mb-2",children:[(0,d.jsx)("span",{className:"font-bold",children:"طريقة الدفع: "}),(0,d.jsx)("span",{className:"font-bold",children:"cash"===a.payment_method?"نقداً":"آجل"})]}),(0,d.jsxs)("div",{className:"mb-2",children:[(0,d.jsx)("span",{className:"font-bold",children:"حالة الدفع: "}),(0,d.jsx)("span",{className:"font-bold",children:"paid"===a.payment_status?"مدفوع بالكامل":"partial"===a.payment_status?"مدفوع جزئياً":"معلق"})]}),"paid"!==a.payment_status&&(0,d.jsxs)("div",{className:"font-bold",children:["المبلغ المستحق: ",(a.final_amount-(a.paid_amount||0)).toLocaleString()]})]})]})}),(0,d.jsx)("div",{className:"print-footer",children:(0,d.jsxs)("div",{className:"grid grid-cols-2",children:[(0,d.jsx)("div",{children:"صفحة 1 من 1"}),(0,d.jsx)("div",{className:"text-right",children:new Date().toLocaleDateString("ar-EG")})]})})]})}function y({reportData:a,reportType:b,title:c,settings:e,onInvoiceClick:f}){let g={...v,...e};return(0,d.jsxs)("div",{className:"print-content",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,d.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-white",style:{backgroundColor:g.accentColor},children:(0,d.jsx)(k.A,{className:"h-8 w-8"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:c}),(0,d.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:["تاريخ التقرير: ",new Date().toLocaleDateString("ar-EG")]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:["وقت الإنشاء: ",new Date().toLocaleTimeString("ar-EG")]})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-blue-100",children:[(0,d.jsx)("h2",{className:"font-bold text-gray-800 mb-2",children:g.companyName}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:g.companyAddress})]})]})}),Array.isArray(a)&&a.length>0&&(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("h4",{className:"text-lg font-bold text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(o.A,{className:"h-5 w-5"}),"ملخص التقرير"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 text-center shadow-sm",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,d.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(p.A,{className:"h-5 w-5 text-white"})})}),(0,d.jsx)("p",{className:"text-xs text-blue-600 mb-1",children:"إجمالي السجلات"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-800",children:a.length})]}),b.includes("sales")&&(0,d.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 text-center shadow-sm",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,d.jsx)("div",{className:"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(q.A,{className:"h-5 w-5 text-white"})})}),(0,d.jsx)("p",{className:"text-xs text-green-600 mb-1",children:"إجمالي المبيعات"}),(0,d.jsxs)("p",{className:"text-lg font-bold text-green-800",children:[a.reduce((a,b)=>a+(b.final_amount||0),0).toLocaleString()," د.ع"]})]}),b.includes("purchases")&&(0,d.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4 text-center shadow-sm",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,d.jsx)("div",{className:"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(r.A,{className:"h-5 w-5 text-white"})})}),(0,d.jsx)("p",{className:"text-xs text-orange-600 mb-1",children:"إجمالي المشتريات"}),(0,d.jsxs)("p",{className:"text-lg font-bold text-orange-800",children:[a.reduce((a,b)=>a+(b.final_amount||0),0).toLocaleString()," د.ع"]})]}),(b.includes("sales")||b.includes("purchases"))&&(0,d.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 text-center shadow-sm",children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,d.jsx)("div",{className:"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(s.A,{className:"h-5 w-5 text-white"})})}),(0,d.jsx)("p",{className:"text-xs text-purple-600 mb-1",children:"متوسط القيمة"}),(0,d.jsxs)("p",{className:"text-lg font-bold text-purple-800",children:[Math.round(a.reduce((a,b)=>a+(b.final_amount||0),0)/a.length).toLocaleString()," د.ع"]})]})]})]}),Array.isArray(a)&&a.length>0&&(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("h4",{className:"text-lg font-bold text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(k.A,{className:"h-5 w-5"}),"تفاصيل البيانات"]}),(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm",children:[(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"text-white font-semibold",style:{backgroundColor:g.accentColor},children:[(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"#"}),b.includes("sales")&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"رقم الفاتورة"}),(0,d.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"اسم العميل"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"المبلغ الإجمالي"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"حالة الدفع"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center",children:"التاريخ"})]}),b.includes("purchases")&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"رقم الفاتورة"}),(0,d.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"اسم المورد"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"المبلغ الإجمالي"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"حالة الدفع"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center",children:"التاريخ"})]}),"inventory"===b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"اسم الدواء"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"الفئة"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"الكمية المتاحة"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"تاريخ الانتهاء"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center",children:"الحالة"})]}),"cashbox"===b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"النوع"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"الفئة"}),(0,d.jsx)("th",{className:"px-3 py-3 text-right border-r border-white border-opacity-20",children:"الوصف"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center border-r border-white border-opacity-20",children:"المبلغ"}),(0,d.jsx)("th",{className:"px-3 py-3 text-center",children:"التاريخ"})]})]})}),(0,d.jsx)("tbody",{children:a.slice(0,100).map((a,c)=>(0,d.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-3 py-2 text-center font-mono text-sm text-gray-500 border-r border-gray-200",children:c+1}),b.includes("sales")&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,d.jsx)("button",{onClick:()=>f?.(a),className:"font-mono text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200 cursor-pointer transition-colors",title:"انقر لعرض تفاصيل الفاتورة",children:a.invoice_number})}),(0,d.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:a.customers?.name||a.customer_name||"عميل نقدي"}),a.customers?.phone&&(0,d.jsx)("p",{className:"text-xs text-gray-500",children:a.customers.phone})]})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsxs)("span",{className:"font-bold text-gray-900",children:[a.final_amount?.toLocaleString()||0," د.ع"]})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${"paid"===a.payment_status?"bg-green-100 text-green-800":"partial"===a.payment_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:"paid"===a.payment_status?"مدفوع":"partial"===a.payment_status?"جزئي":"معلق"})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center",children:(0,d.jsx)("span",{className:"text-sm text-gray-600",children:new Date(a.created_at).toLocaleDateString("ar-EG")})})]}),b.includes("purchases")&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,d.jsx)("button",{onClick:()=>f?.(a),className:"font-mono text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded hover:bg-orange-200 cursor-pointer transition-colors",title:"انقر لعرض تفاصيل الفاتورة",children:a.invoice_number})}),(0,d.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:a.suppliers?.name||"غير محدد"}),a.suppliers?.phone&&(0,d.jsx)("p",{className:"text-xs text-gray-500",children:a.suppliers.phone})]})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsxs)("span",{className:"font-bold text-gray-900",children:[a.final_amount?.toLocaleString()||0," د.ع"]})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${"paid"===a.payment_status?"bg-green-100 text-green-800":"partial"===a.payment_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:"paid"===a.payment_status?"مدفوع":"partial"===a.payment_status?"جزئي":"معلق"})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center",children:(0,d.jsx)("span",{className:"text-sm text-gray-600",children:new Date(a.created_at).toLocaleDateString("ar-EG")})})]}),"inventory"===b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:a.medicines?.name||"غير محدد"}),a.medicine_batches?.batch_number&&(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["دفعة: ",a.medicine_batches.batch_number]})]})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsx)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs",children:a.medicines?.category||"غير محدد"})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsx)("span",{className:`font-bold px-2 py-1 rounded ${a.quantity<10?"bg-red-100 text-red-800":a.quantity<50?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:a.quantity})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsx)("span",{className:"text-sm text-gray-600",children:new Date(a.expiry_date).toLocaleDateString("ar-EG")})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center",children:(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${a.quantity<10?"bg-red-100 text-red-800":a.quantity<50?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:a.quantity<10?"كمية قليلة":a.quantity<50?"كمية متوسطة":"كمية جيدة"})})]}),"cashbox"===b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${"income"===a.transaction_type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"income"===a.transaction_type?"وارد":"مصروف"})}),(0,d.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,d.jsx)("span",{className:"bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs",children:a.category})}),(0,d.jsx)("td",{className:"px-3 py-2 border-r border-gray-200",children:(0,d.jsx)("p",{className:"text-sm text-gray-900",children:a.description})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center border-r border-gray-200",children:(0,d.jsxs)("span",{className:`font-mono font-bold ${"income"===a.transaction_type?"text-green-600":"text-red-600"}`,children:[a.amount?.toLocaleString()||0," د.ع"]})}),(0,d.jsx)("td",{className:"px-3 py-2 text-center",children:(0,d.jsx)("span",{className:"text-sm text-gray-600",children:new Date(a.created_at).toLocaleDateString("ar-EG")})})]})]},`report_item_${c}_${a.id||c}`))})]})}),a.length>100&&(0,d.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-t border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600",children:[(0,d.jsx)(t.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:["يتم عرض أول 100 سجل من إجمالي ",a.length," سجل"]})]})})]})]}),(!Array.isArray(a)||0===a.length)&&(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-8",children:[(0,d.jsx)(t.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"لا توجد بيانات"}),(0,d.jsx)("p",{className:"text-gray-600",children:"لا توجد بيانات متاحة لعرضها في هذا التقرير"})]})}),(0,d.jsx)("div",{className:"mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-bold text-gray-800 mb-3",children:"إحصائيات التقرير:"}),(0,d.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"عدد السجلات المعروضة:"}),(0,d.jsx)("span",{className:"font-semibold",children:Array.isArray(a)?Math.min(a.length,100):0})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"إجمالي السجلات:"}),(0,d.jsx)("span",{className:"font-semibold",children:Array.isArray(a)?a.length:0})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"تاريخ آخر تحديث:"}),(0,d.jsx)("span",{className:"font-semibold",children:new Date().toLocaleDateString("ar-EG")})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-bold text-gray-800 mb-3",children:"ملاحظات مهمة:"}),(0,d.jsxs)("ul",{className:"text-xs text-gray-600 space-y-1",children:[(0,d.jsx)("li",{children:"• هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة الصيدلية"}),(0,d.jsx)("li",{children:"• جميع البيانات محدثة حتى تاريخ إنشاء التقرير"}),(0,d.jsx)("li",{children:"• للاستفسارات يرجى التواصل مع إدارة النظام"}),(0,d.jsx)("li",{children:"• يُنصح بحفظ نسخة من هذا التقرير للمراجعة المستقبلية"})]})]})]})})]})}},93613:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};