(()=>{var a={};a.id=772,a.ids=[772],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7776:(a,b,c)=>{Promise.resolve().then(c.bind(c,54122))},10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14148:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u});var d=c(60687),e=c(43210),f=c(21979),g=c(96474),h=c(99270),i=c(79410),j=c(93508),k=c(48340),l=c(41550),m=c(97992),n=c(10022),o=c(63143),p=c(88233),q=c(11860),r=c(71444),s=c(23928),t=c(40228);function u(){let[a,b]=(0,e.useState)([{id:"1",name:"شركة الأدوية العراقية",contact_person:"أحمد محمد",phone:"07901234567",email:"<EMAIL>",address:"بغداد - الكرادة",notes:"مورد رئيسي للمسكنات",created_at:"2024-01-10"},{id:"2",name:"شركة بغداد للأدوية",contact_person:"فاطمة علي",phone:"07801234567",email:"<EMAIL>",address:"بغداد - الجادرية",notes:"متخصص في المضادات الحيوية",created_at:"2024-01-15"},{id:"3",name:"شركة النهرين للأدوية",contact_person:"محمد حسن",phone:"07701234567",email:"<EMAIL>",address:"بغداد - الأعظمية",created_at:"2024-02-01"},{id:"4",name:"شركة دجلة للأدوية",contact_person:"سارة أحمد",phone:"07601234567",email:"<EMAIL>",address:"بغداد - الكاظمية",notes:"فيتامينات ومكملات غذائية",created_at:"2024-02-10"}]),[c,u]=(0,e.useState)(""),[v,w]=(0,e.useState)(!1),[x,y]=(0,e.useState)(null),[z,A]=(0,e.useState)(!1),[B,C]=(0,e.useState)(null),[D,E]=(0,e.useState)([]),[F,G]=(0,e.useState)({name:"",contact_person:"",phone:"",email:"",address:"",notes:""}),H=a.filter(a=>a.name.toLowerCase().includes(c.toLowerCase())||a.contact_person?.toLowerCase().includes(c.toLowerCase())||a.phone?.includes(c)||a.email?.toLowerCase().includes(c.toLowerCase())),I=()=>{w(!1),y(null),G({name:"",contact_person:"",phone:"",email:"",address:"",notes:""})},J=a=>D.filter(b=>b.supplier_name===a&&"pending"===b.payment_status).reduce((a,b)=>a+b.final_amount,0),K=a=>D.filter(b=>b.supplier_name===a&&"paid"===b.payment_status).reduce((a,b)=>a+b.final_amount,0);return(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة الموردين"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة بيانات الموردين وشركات الأدوية"})]}),(0,d.jsxs)("button",{onClick:()=>{y(null),G({name:"",contact_person:"",phone:"",email:"",address:"",notes:""}),w(!0)},className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),"إضافة مورد جديد"]})]}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",placeholder:"البحث عن مورد...",value:c,onChange:a=>u(a.target.value),className:"w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["قائمة الموردين (",H.length,")"]})}),0===H.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(i.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد موردين"}),(0,d.jsx)("p",{className:"text-gray-500",children:"لم يتم العثور على موردين تطابق معايير البحث"})]}):(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:H.map(c=>(0,d.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)("div",{className:"bg-green-50 p-3 rounded-lg",children:(0,d.jsx)(i.A,{className:"h-6 w-6 text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:c.name}),c.contact_person&&(0,d.jsxs)("div",{className:"flex items-center gap-1 mt-1 text-sm text-gray-600",children:[(0,d.jsx)(j.A,{className:"h-3 w-3"}),(0,d.jsxs)("span",{children:["جهة الاتصال: ",c.contact_person]})]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-4 mt-2 text-sm text-gray-600",children:[c.phone&&(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(k.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:c.phone})]}),c.email&&(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(l.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:c.email})]}),c.address&&(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(m.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:c.address})]})]}),c.notes&&(0,d.jsxs)("div",{className:"flex items-center gap-1 mt-1 text-sm text-gray-500",children:[(0,d.jsx)(n.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:c.notes})]}),(0,d.jsxs)("p",{className:"text-xs text-gray-400 mt-2",children:["تاريخ الإضافة: ",c.created_at]})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>{C(c),(a=>{try{let b=JSON.parse(localStorage.getItem("purchase_invoices")||"[]").filter(b=>b.supplier_name===a);E(b)}catch(a){console.error("Error loading supplier invoices:",a),E([])}})(c.name),A(!0)},className:"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg",title:"كشف الحساب",children:(0,d.jsx)(n.A,{className:"h-4 w-4"})}),(0,d.jsx)("button",{onClick:()=>{y(c),G({name:c.name,contact_person:c.contact_person||"",phone:c.phone||"",email:c.email||"",address:c.address||"",notes:c.notes||""}),w(!0)},className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg",title:"تعديل",children:(0,d.jsx)(o.A,{className:"h-4 w-4"})}),(0,d.jsx)("button",{onClick:()=>{var d;return d=c.id,void(confirm("هل أنت متأكد من حذف هذا المورد؟")&&(b(a.filter(a=>a.id!==d)),alert("تم حذف المورد بنجاح!")))},className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg",title:"حذف",children:(0,d.jsx)(p.A,{className:"h-4 w-4"})})]})]})},c.id))})]}),v&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:x?"تعديل المورد":"إضافة مورد جديد"}),(0,d.jsx)("button",{onClick:I,className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{className:"h-5 w-5"})})]}),(0,d.jsxs)("form",{onSubmit:c=>{if(c.preventDefault(),!F.name.trim())return void alert("يرجى إدخال اسم المورد");x?(b(a.map(a=>a.id===x.id?{...a,...F}:a)),alert("تم تحديث بيانات المورد بنجاح!")):(b([...a,{id:Date.now().toString(),...F,created_at:new Date().toISOString().split("T")[0]}]),alert("تم إضافة المورد بنجاح!")),w(!1),y(null),G({name:"",contact_person:"",phone:"",email:"",address:"",notes:""})},className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم المورد *"}),(0,d.jsx)("input",{type:"text",value:F.name,onChange:a=>G({...F,name:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"اسم الشركة أو المورد",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"جهة الاتصال"}),(0,d.jsx)("input",{type:"text",value:F.contact_person,onChange:a=>G({...F,contact_person:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"اسم الشخص المسؤول"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"رقم الهاتف"}),(0,d.jsx)("input",{type:"tel",value:F.phone,onChange:a=>G({...F,phone:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"07901234567"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البريد الإلكتروني"}),(0,d.jsx)("input",{type:"email",value:F.email,onChange:a=>G({...F,email:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"العنوان"}),(0,d.jsx)("input",{type:"text",value:F.address,onChange:a=>G({...F,address:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"عنوان الشركة"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات"}),(0,d.jsx)("textarea",{value:F.notes,onChange:a=>G({...F,notes:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ملاحظات حول المورد أو التخصص",rows:3})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,d.jsx)("button",{type:"button",onClick:I,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"إلغاء"}),(0,d.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:x?"تحديث":"إضافة"})]})]})]})}),z&&B&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["كشف حساب المورد - ",B.name]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("button",{onClick:()=>{if(!B)return;let a=`
      <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1>صيدلية الشفاء</h1>
          <h2>كشف حساب المورد</h2>
        </div>

        <div style="margin-bottom: 20px;">
          <h3>معلومات المورد:</h3>
          <p><strong>اسم الشركة:</strong> ${B.name}</p>
          <p><strong>الشخص المسؤول:</strong> ${B.contact_person||"غير محدد"}</p>
          <p><strong>الهاتف:</strong> ${B.phone||"غير محدد"}</p>
          <p><strong>العنوان:</strong> ${B.address||"غير محدد"}</p>
          <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString("ar-EG")}</p>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 8px;">رقم الفاتورة</th>
              <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>
              <th style="border: 1px solid #ddd; padding: 8px;">المبلغ</th>
              <th style="border: 1px solid #ddd; padding: 8px;">حالة الدفع</th>
            </tr>
          </thead>
          <tbody>
            ${D.map(a=>`
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${a.invoice_number}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${new Date(a.created_at).toLocaleDateString("ar-EG")}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${a.final_amount.toLocaleString()} د.ع</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${"paid"===a.payment_status?"مدفوع":"معلق"}</td>
              </tr>
            `).join("")}
          </tbody>
        </table>

        <div style="margin-top: 20px;">
          <p><strong>إجمالي المبالغ المدفوعة:</strong> ${K(B.name).toLocaleString()} د.ع</p>
          <p><strong>إجمالي المبالغ المعلقة:</strong> ${J(B.name).toLocaleString()} د.ع</p>
          <p><strong>إجمالي المعاملات:</strong> ${(K(B.name)+J(B.name)).toLocaleString()} د.ع</p>
        </div>
      </div>
    `,b=window.open("","_blank");b&&(b.document.write(a),b.document.close(),b.print())},className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,d.jsx)(r.A,{className:"h-4 w-4"}),"طباعة"]}),(0,d.jsx)("button",{onClick:()=>{A(!1),C(null),E([])},className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{className:"h-5 w-5"})})]})]}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"معلومات المورد"}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"اسم الشركة:"})," ",B.name]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"الشخص المسؤول:"})," ",B.contact_person||"غير محدد"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"الهاتف:"})," ",B.phone||"غير محدد"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"العنوان:"})," ",B.address||"غير محدد"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"ملخص الحساب"}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"إجمالي المبالغ المدفوعة:"})," ",(0,d.jsxs)("span",{className:"text-green-600",children:[K(B.name).toLocaleString()," د.ع"]})]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"إجمالي المبالغ المعلقة:"})," ",(0,d.jsxs)("span",{className:"text-red-600",children:[J(B.name).toLocaleString()," د.ع"]})]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"font-medium",children:"إجمالي المعاملات:"})," ",(0,d.jsxs)("span",{className:"text-blue-600",children:[(K(B.name)+J(B.name)).toLocaleString()," د.ع"]})]})]})]})}),(0,d.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["فواتير المورد (",D.length,")"]})}),(0,d.jsx)("div",{className:"overflow-x-auto",children:0===D.length?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(n.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"لا توجد فواتير"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"لم يتم العثور على فواتير لهذا المورد."})]}):(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم الفاتورة"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ الإجمالي"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الخصم"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ النهائي"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"طريقة الدفع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"حالة الدفع"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:D.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:a.invoice_number}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString("ar-EG")}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[a.total_amount.toLocaleString()," د.ع"]}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[a.discount_amount.toLocaleString()," د.ع"]}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:[a.final_amount.toLocaleString()," د.ع"]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,d.jsxs)("div",{className:"flex items-center",children:["cash"===a.payment_method?(0,d.jsx)(s.A,{className:"h-4 w-4 text-green-500 mr-1"}):(0,d.jsx)(t.A,{className:"h-4 w-4 text-blue-500 mr-1"}),"cash"===a.payment_method?"نقداً":"آجل"]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,d.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"paid"===a.payment_status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"paid"===a.payment_status?"مدفوع":"معلق"})})]},a.id))})]})})]})]})})]})})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25612:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["suppliers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,54122)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\suppliers\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\suppliers\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/suppliers/page",pathname:"/suppliers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/suppliers/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41550:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},48340:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},54122:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\suppliers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\suppliers\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},71328:(a,b,c)=>{Promise.resolve().then(c.bind(c,14148))},79410:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,314,979],()=>b(b.s=25612));module.exports=c})();