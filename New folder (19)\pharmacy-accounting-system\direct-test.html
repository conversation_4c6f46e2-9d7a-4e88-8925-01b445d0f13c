<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مباشر للنظام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .button:hover {
            background: #2563eb;
        }
        
        .button.success {
            background: #10b981;
        }
        
        .button.danger {
            background: #ef4444;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        .info {
            color: #17a2b8;
        }
        
        .warning {
            color: #ffc107;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>اختبار مباشر لنظام الصيدلية</h1>

        <div>
            <button class="button danger" onclick="clearAllData()">مسح جميع البيانات</button>
            <button class="button success" onclick="initializeSystem()">تهيئة النظام</button>
            <button class="button" onclick="testCompleteFlow()">اختبار تدفق كامل</button>
            <button class="button" onclick="checkSalesHistory()">فحص سجل المبيعات</button>
            <button class="button" onclick="testPrintData()">اختبار بيانات الطباعة</button>
            <button class="button" onclick="simulatePrintFlow()">محاكاة تدفق الطباعة</button>
            <button class="button" onclick="clearLog()">مسح السجل</button>
        </div>

        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function clearAllData() {
            const keys = ['sales_invoices', 'sales_invoice_items', 'medicines', 'medicine_batches', 'customers'];
            keys.forEach(key => localStorage.removeItem(key));
            log('تم مسح جميع البيانات من localStorage', 'success');
        }

        function initializeSystem() {
            log('🔄 بدء تهيئة النظام...');

            // Create medicines
            const medicines = [{
                id: 'med_1',
                name: 'باراسيتامول 500 مجم',
                category: 'مسكنات',
                manufacturer: 'شركة الأدوية العراقية',
                strength: '500mg',
                form: 'أقراص',
                created_at: new Date().toISOString()
            }, {
                id: 'med_2',
                name: 'أموكسيسيلين 250 مجم',
                category: 'مضادات حيوية',
                manufacturer: 'شركة بغداد للأدوية',
                strength: '250mg',
                form: 'كبسولات',
                created_at: new Date().toISOString()
            }, {
                id: 'med_3',
                name: 'إيبوبروفين 400 مجم',
                category: 'مسكنات',
                manufacturer: 'شركة الرافدين',
                strength: '400mg',
                form: 'أقراص',
                created_at: new Date().toISOString()
            }];

            const batches = [{
                id: 'batch_1',
                medicine_id: 'med_1',
                batch_code: 'PAR001',
                expiry_date: '2025-12-31',
                quantity: 100,
                cost_price: 500,
                selling_price: 750,
                received_date: '2024-01-01',
                created_at: new Date().toISOString()
            }, {
                id: 'batch_2',
                medicine_id: 'med_2',
                batch_code: 'AMX001',
                expiry_date: '2025-06-30',
                quantity: 50,
                cost_price: 1000,
                selling_price: 1500,
                received_date: '2024-01-01',
                created_at: new Date().toISOString()
            }, {
                id: 'batch_3',
                medicine_id: 'med_3',
                batch_code: 'IBU001',
                expiry_date: '2025-09-30',
                quantity: 75,
                cost_price: 800,
                selling_price: 1200,
                received_date: '2024-01-01',
                created_at: new Date().toISOString()
            }];

            const customers = [{
                id: 'cust_1',
                name: 'أحمد محمد علي',
                phone: '07701234567',
                address: 'بغداد - الكرادة',
                created_at: new Date().toISOString()
            }, {
                id: 'cust_2',
                name: 'فاطمة حسن',
                phone: '07809876543',
                address: 'بغداد - الجادرية',
                created_at: new Date().toISOString()
            }];

            // Save to localStorage
            localStorage.setItem('medicines', JSON.stringify(medicines));
            localStorage.setItem('medicine_batches', JSON.stringify(batches));
            localStorage.setItem('customers', JSON.stringify(customers));
            localStorage.setItem('sales_invoices', JSON.stringify([]));
            localStorage.setItem('sales_invoice_items', JSON.stringify([]));

            log(`✅ تم إنشاء ${medicines.length} دواء`, 'success');
            log(`✅ تم إنشاء ${batches.length} دفعة`, 'success');
            log(`✅ تم إنشاء ${customers.length} عميل`, 'success');
            log('✅ تم تهيئة النظام بنجاح', 'success');
        }

        function testCompleteFlow() {
            log('🔄 بدء اختبار التدفق الكامل...');

            // Create invoice
            const invoiceData = {
                id: `invoice_${Date.now()}`,
                invoice_number: `TEST-${Date.now()}`,
                customer_name: 'عميل اختبار التدفق',
                total_amount: 2250,
                discount_amount: 0,
                final_amount: 2250,
                payment_method: 'cash',
                payment_status: 'paid',
                notes: 'اختبار تدفق كامل',
                created_at: new Date().toISOString()
            };

            // Create items with medicine names
            const invoiceItems = [{
                id: `item_${Date.now()}_1`,
                invoice_id: invoiceData.id,
                medicine_batch_id: 'batch_1',
                quantity: 2,
                unit_price: 750,
                total_price: 1500,
                is_gift: false,
                medicine_name: 'باراسيتامول 500 مجم',
                medicineName: 'باراسيتامول 500 مجم',
                created_at: new Date().toISOString()
            }, {
                id: `item_${Date.now()}_2`,
                invoice_id: invoiceData.id,
                medicine_batch_id: 'batch_2',
                quantity: 1,
                unit_price: 1500,
                total_price: 1500,
                is_gift: false,
                medicine_name: 'أموكسيسيلين 250 مجم',
                medicineName: 'أموكسيسيلين 250 مجم',
                created_at: new Date().toISOString()
            }, {
                id: `item_${Date.now()}_3`,
                invoice_id: invoiceData.id,
                medicine_batch_id: 'batch_3',
                quantity: 1,
                unit_price: 0,
                total_price: 0,
                is_gift: true,
                medicine_name: 'إيبوبروفين 400 مجم',
                medicineName: 'إيبوبروفين 400 مجم',
                created_at: new Date().toISOString()
            }];

            // Save invoice
            const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            existingInvoices.push(invoiceData);
            localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices));
            log(`✅ تم حفظ الفاتورة: ${invoiceData.invoice_number}`, 'success');

            // Save items
            const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            existingItems.push(...invoiceItems);
            localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems));
            log(`✅ تم حفظ ${invoiceItems.length} عنصر`, 'success');

            // Log details
            invoiceItems.forEach((item, index) => {
                const type = item.is_gift ? 'هدية' : 'مبيع';
                log(`📦 العنصر ${index + 1}: ${item.medicine_name} (${type}) - الكمية: ${item.quantity}`, 'info');
            });

            log('✅ تم إكمال التدفق الكامل بنجاح', 'success');
        }

        function checkSalesHistory() {
            log('🔍 فحص سجل المبيعات...');

            const invoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const items = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');

            log(`📊 إجمالي الفواتير: ${invoices.length}`, 'info');
            log(`📊 إجمالي العناصر: ${items.length}`, 'info');

            if (invoices.length === 0) {
                log('⚠️ لا توجد فواتير في السجل', 'warning');
                return;
            }

            invoices.forEach((invoice, index) => {
                log(`📋 الفاتورة ${index + 1}:`, 'info');
                log(`   رقم الفاتورة: ${invoice.invoice_number}`, 'info');
                log(`   اسم العميل: ${invoice.customer_name}`, 'info');
                log(`   المبلغ النهائي: ${invoice.final_amount}`, 'info');
                log(`   تاريخ الإنشاء: ${new Date(invoice.created_at).toLocaleString('ar-EG')}`, 'info');

                const invoiceItems = items.filter(item => item.invoice_id === invoice.id);
                log(`   عدد العناصر: ${invoiceItems.length}`, 'info');

                invoiceItems.forEach((item, itemIndex) => {
                    const type = item.is_gift ? 'هدية' : 'مبيع';
                    log(`     العنصر ${itemIndex + 1}: ${item.medicine_name || 'غير محدد'} (${type}) - الكمية: ${item.quantity}`, 'info');
                });
                log('', 'info'); // Empty line
            });
        }

        function testPrintData() {
            log('🖨️ اختبار بيانات الطباعة...');

            const invoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const items = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');

            if (invoices.length === 0) {
                log('⚠️ لا توجد فواتير للطباعة', 'warning');
                return;
            }

            const lastInvoice = invoices[invoices.length - 1];
            log(`🖨️ اختبار طباعة الفاتورة: ${lastInvoice.invoice_number}`, 'info');

            const invoiceItems = items.filter(item => item.invoice_id === lastInvoice.id);
            log(`📦 عدد العناصر للطباعة: ${invoiceItems.length}`, 'info');

            // Test medicine name resolution
            invoiceItems.forEach((item, index) => {
                log(`📦 العنصر ${index + 1}:`, 'info');
                log(`   medicine_name: ${item.medicine_name || 'غير موجود'}`, 'info');
                log(`   medicineName: ${item.medicineName || 'غير موجود'}`, 'info');

                // Try to resolve from batch
                const batch = batches.find(b => b.id === item.medicine_batch_id);
                const medicine = medicines.find(m => m.id === batch ? .medicine_id);

                log(`   من الدفعة: ${batch ? batch.batch_code : 'غير موجود'}`, 'info');
                log(`   من الدواء: ${medicine ? medicine.name : 'غير موجود'}`, 'info');

                // Final resolved name
                const resolvedName = item.medicine_name || item.medicineName || medicine ? .name || 'غير محدد';
                log(`   الاسم النهائي: ${resolvedName}`, resolvedName === 'غير محدد' ? 'error' : 'success');
                log('', 'info'); // Empty line
            });
        }

        function simulatePrintFlow() {
            log('🖨️ محاكاة تدفق الطباعة الكامل...');

            const invoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const items = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');

            if (invoices.length === 0) {
                log('⚠️ لا توجد فواتير للطباعة', 'warning');
                return;
            }

            const lastInvoice = invoices[invoices.length - 1];
            const invoiceItems = items.filter(item => item.invoice_id === lastInvoice.id);

            log(`🖨️ محاكاة طباعة الفاتورة: ${lastInvoice.invoice_number}`, 'info');

            // Simulate the exact data structure used in printing
            const printData = {
                ...lastInvoice,
                sales_invoice_items: invoiceItems.map(item => {
                    const batch = batches.find(b => b.id === item.medicine_batch_id);
                    const medicine = medicines.find(m => m.id === batch ? .medicine_id);

                    return {
                        ...item,
                        medicine_batches: {
                            batch_code: batch ? .batch_code || '',
                            expiry_date: batch ? .expiry_date || '',
                            medicines: {
                                name: medicine ? .name || item.medicine_name || item.medicineName || 'غير محدد',
                                category: medicine ? .category || '',
                                manufacturer: medicine ? .manufacturer || '',
                                strength: medicine ? .strength || '',
                                form: medicine ? .form || ''
                            }
                        }
                    };
                })
            };

            log('📋 بيانات الطباعة المحاكاة:', 'info');
            log(JSON.stringify(printData, null, 2), 'info');

            // Test each item's name resolution
            printData.sales_invoice_items.forEach((item, index) => {
                const nameFromStructure = item.medicine_batches ? .medicines ? .name;
                const nameFromItem = item.medicine_name || item.medicineName;
                const finalName = nameFromStructure || nameFromItem || 'غير محدد';

                log(`📦 العنصر ${index + 1}: ${finalName}`, finalName === 'غير محدد' ? 'error' : 'success');
            });
        }

        // Initialize on page load
        log('تم تحميل صفحة الاختبار المباشر', 'success');
        log('استخدم الأزرار أعلاه لاختبار النظام', 'info');
    </script>
</body>

</html>