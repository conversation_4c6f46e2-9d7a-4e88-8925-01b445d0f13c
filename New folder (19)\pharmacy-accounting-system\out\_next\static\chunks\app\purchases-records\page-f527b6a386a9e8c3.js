(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2602],{4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},7156:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(95155),r=t(12115),l=t(61932),i=t(53904),c=t(47924),n=t(57434),d=t(55868),x=t(69074),m=t(44020),h=t(92657),p=t(81304),o=t(91788),u=t(22711),y=t(53363),g=t(10988);function j(){var e,s,t,j;let[v,N]=(0,r.useState)([]),[f,b]=(0,r.useState)([]),[w,k]=(0,r.useState)(""),[A,_]=(0,r.useState)("all"),[C,M]=(0,r.useState)("all"),[S,L]=(0,r.useState)(null),[z,E]=(0,r.useState)(!1),[q,H]=(0,r.useState)(!1),[D,P]=(0,r.useState)(!0),[I,G]=(0,r.useState)(null),{printSettings:O}=(0,y.usePrintSettings)();(0,r.useEffect)(()=>{V()},[]),(0,r.useEffect)(()=>{Z()},[v,w,A,C]);let V=async()=>{try{P(!0);let e=await (0,g.getPurchaseInvoices)();e.success&&e.data&&N(e.data)}catch(e){console.error("Error loading purchase records:",e)}finally{P(!1)}},Z=()=>{let e=v;w&&(e=e.filter(e=>{var s,t,a;return e.invoice_number.toLowerCase().includes(w.toLowerCase())||(null==(s=e.supplier_name)?void 0:s.toLowerCase().includes(w.toLowerCase()))||(null==(a=e.suppliers)||null==(t=a.name)?void 0:t.toLowerCase().includes(w.toLowerCase()))})),"all"!==A&&(e=e.filter(e=>e.payment_status===A)),"all"!==C&&(e=e.filter(e=>e.payment_method===C)),b(e)},B=e=>{L(e),H(!0),G(null)},F=e=>{switch(e){case"paid":return"bg-green-100 text-green-800";case"partial":return"bg-yellow-100 text-yellow-800";case"pending":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},J=e=>{switch(e){case"paid":return"مدفوع";case"partial":return"جزئي";case"pending":return"معلق";default:return"غير محدد"}};return(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"سجل المشتريات"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"عرض وإدارة جميع فواتير المشتريات"})]}),(0,a.jsxs)("button",{onClick:V,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),"تحديث"]})]}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",placeholder:"البحث برقم الفاتورة أو اسم المورد...",value:w,onChange:e=>k(e.target.value),className:"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:A,onChange:e=>_(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"جميع حالات الدفع"}),(0,a.jsx)("option",{value:"paid",children:"مدفوع"}),(0,a.jsx)("option",{value:"partial",children:"جزئي"}),(0,a.jsx)("option",{value:"pending",children:"معلق"})]}),(0,a.jsxs)("select",{value:C,onChange:e=>M(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"جميع طرق الدفع"}),(0,a.jsx)("option",{value:"cash",children:"نقداً"}),(0,a.jsx)("option",{value:"credit",children:"آجل"})]}),(0,a.jsx)("div",{className:"flex items-center justify-center bg-gray-50 rounded-lg px-4 py-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[f.length," من ",v.length," فاتورة"]})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:D?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"جاري تحميل السجلات..."})]}):0===f.length?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)(n.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"لا توجد فواتير مشتريات"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم الفاتورة"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المورد"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"حالة الدفع"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"طريقة الدفع"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.map(e=>{var s,t,r;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 text-orange-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.invoice_number})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(null==(s=e.suppliers)?void 0:s.name)||e.supplier_name||"غير محدد"}),(null==(t=e.suppliers)?void 0:t.phone)&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.suppliers.phone})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-green-500 mr-1"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[null==(r=e.final_amount)?void 0:r.toLocaleString()," د.ع"]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(F(e.payment_status)),children:J(e.payment_status)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(e=>{switch(e){case"cash":return"نقداً";case"credit":return"آجل";default:return"غير محدد"}})(e.payment_method)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:new Date(e.created_at).toLocaleDateString("ar-EG")})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{onClick:()=>G(I===e.id?null:e.id),className:"text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),I===e.id&&(0,a.jsx)("div",{className:"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("button",{onClick:()=>{L(e),E(!0),G(null)},className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"عرض التفاصيل"]}),(0,a.jsxs)("button",{onClick:()=>B(e),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"معاينة الطباعة"]}),(0,a.jsxs)("button",{onClick:()=>{(0,y.printInvoice)(e,"purchase",O),G(null)},className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"طباعة مباشرة"]})]})})]})})]},e.id)})})]})})}),z&&S&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["تفاصيل فاتورة المشتريات ",S.invoice_number]}),(0,a.jsx)("button",{onClick:()=>E(!1),className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"معلومات الفاتورة"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"رقم الفاتورة:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:S.invoice_number})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"التاريخ:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:new Date(S.created_at).toLocaleDateString("ar-EG")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"المبلغ الإجمالي:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:[null==(e=S.final_amount)?void 0:e.toLocaleString()," د.ع"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"حالة الدفع:"}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(F(S.payment_status)),children:J(S.payment_status)})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"معلومات المورد"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"الاسم:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:(null==(s=S.suppliers)?void 0:s.name)||S.supplier_name||"غير محدد"})]}),(null==(t=S.suppliers)?void 0:t.phone)&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"الهاتف:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:S.suppliers.phone})]}),(null==(j=S.suppliers)?void 0:j.address)&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"العنوان:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:S.suppliers.address})]})]})]})]}),S.purchase_invoice_items&&S.purchase_invoice_items.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"عناصر الفاتورة"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"الدواء"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"الكمية"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"السعر"}),(0,a.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المجموع"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:S.purchase_invoice_items.map((e,s)=>{var t,r,l;return(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(null==(t=e.medicines)?void 0:t.name)||"غير محدد"}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.quantity}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm text-gray-900",children:[null==(r=e.unit_cost)?void 0:r.toLocaleString()," د.ع"]}),(0,a.jsxs)("td",{className:"px-4 py-3 text-sm text-gray-900",children:[null==(l=e.total_cost)?void 0:l.toLocaleString()," د.ع"]})]},s)})})]})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t bg-gray-50",children:[(0,a.jsx)("button",{onClick:()=>E(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"إغلاق"}),(0,a.jsxs)("button",{onClick:()=>{B(S),E(!1)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"طباعة"]})]})]})}),q&&S&&(0,a.jsx)(u.Ay,{title:"فاتورة مشتريات",settings:O,onClose:()=>H(!1),onPrint:()=>(0,y.printInvoice)(S,"purchase",O),children:(0,a.jsx)(u.dt,{invoice:S,type:"purchase",settings:O})})]})})}},16785:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},19420:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},31854:(e,s,t)=>{Promise.resolve().then(t.bind(t,7156))},33109:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},44020:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},57434:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94498:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]])}},e=>{e.O(0,[705,6874,6543,5647,8080,1932,988,3363,2711,8441,5964,7358],()=>e(e.s=31854)),_N_E=e.O()}]);