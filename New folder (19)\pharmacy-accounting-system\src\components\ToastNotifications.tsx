'use client'

import { useState, useEffect } from 'react'
import { useNotifications } from '@/contexts/NotificationContext'
import {
  X,
  CheckCircle,
  AlertTriangle,
  Info,
  XCircle,
  ExternalLink
} from 'lucide-react'

interface ToastNotification {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message: string
  actionUrl?: string
  actionLabel?: string
  duration?: number
}

export default function ToastNotifications() {
  const [toasts, setToasts] = useState<ToastNotification[]>([])
  const { notifications } = useNotifications()

  // مراقبة التنبيهات الجديدة وعرضها كـ Toast
  useEffect(() => {
    const latestNotification = notifications[0]
    if (latestNotification && !latestNotification.isRead) {
      // عرض التنبيه كـ Toast فقط إذا كان حديث (أقل من دقيقة)
      const notificationTime = new Date(latestNotification.createdAt).getTime()
      const now = new Date().getTime()
      const diffInMinutes = (now - notificationTime) / (1000 * 60)
      
      if (diffInMinutes < 1) {
        showToast({
          id: latestNotification.id,
          type: latestNotification.type,
          title: latestNotification.title,
          message: latestNotification.message,
          actionUrl: latestNotification.actionUrl,
          actionLabel: latestNotification.actionLabel,
          duration: getDurationByPriority(latestNotification.priority)
        })
      }
    }
  }, [notifications])

  const getDurationByPriority = (priority: string): number => {
    switch (priority) {
      case 'critical': return 10000 // 10 ثواني
      case 'high': return 7000     // 7 ثواني
      case 'medium': return 5000   // 5 ثواني
      case 'low': return 3000      // 3 ثواني
      default: return 5000
    }
  }

  const showToast = (toast: ToastNotification) => {
    // تجنب التكرار
    if (toasts.find(t => t.id === toast.id)) return

    setToasts(prev => [...prev, toast])

    // إزالة التنبيه تلقائياً بعد المدة المحددة
    setTimeout(() => {
      removeToast(toast.id)
    }, toast.duration || 5000)
  }

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const getToastIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />
      default:
        return <Info className="h-5 w-5 text-gray-500" />
    }
  }

  const getToastStyles = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800'
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800'
    }
  }

  if (toasts.length === 0) return null

  return (
    <div className="fixed top-20 left-4 z-50 space-y-3">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`max-w-sm w-full shadow-lg rounded-lg border p-4 transition-all duration-300 transform animate-slide-in-left ${getToastStyles(toast.type)}`}
        >
          <div className="flex items-start gap-3">
            {/* Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {getToastIcon(toast.type)}
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium mb-1">
                {toast.title}
              </h4>
              <p className="text-sm opacity-90 line-clamp-2">
                {toast.message}
              </p>
              
              {/* Action Button */}
              {toast.actionUrl && (
                <a
                  href={toast.actionUrl}
                  className="inline-flex items-center gap-1 text-xs font-medium mt-2 hover:underline"
                >
                  <ExternalLink className="h-3 w-3" />
                  {toast.actionLabel || 'عرض التفاصيل'}
                </a>
              )}
            </div>
            
            {/* Close Button */}
            <button
              onClick={() => removeToast(toast.id)}
              className="flex-shrink-0 p-1 hover:bg-black hover:bg-opacity-10 rounded"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  )
}

// إضافة الأنيميشن إلى CSS
const toastStyles = `
  @keyframes slide-in-left {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .animate-slide-in-left {
    animation: slide-in-left 0.3s ease-out;
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`

// إضافة الأنيميشن إلى الصفحة
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = toastStyles
  document.head.appendChild(styleElement)
}
