# تحديث قالب الطباعة - مكتب لارين العلمي

## نظرة عامة
تم تحديث نظام إدارة الصيدلية ليتضمن قالب طباعة جديد مخصص لمكتب لارين العلمي، مع تصميم يشبه الفاتورة المرفقة في الصورة.

## التحديثات المنجزة

### 1. تحديث معلومات الشركة
- **الاسم العربي**: مكتب لارين العلمي
- **الاسم الإنجليزي**: LAREN SCIENTIFIC BUREAU
- **العنوان**: بغداد - شارع فلسطين
- **البريد الإلكتروني**: <EMAIL>

### 2. إنشاء قالب طباعة جديد
تم إنشاء ملف `src/utils/larenPrintTemplate.ts` يحتوي على:

#### أ. قالب الفواتير (`generateLarenInvoiceHTML`)
- تصميم يشبه الفاتورة المرفقة
- رأس يحتوي على اسم الشركة بالعربية والإنجليزية
- شعار الشركة (LAREN/لارين)
- جدول المواد مع الأعمدة التالية:
  - ت (رقم تسلسلي)
  - اسم المادة
  - الكمية
  - السعر
  - المجموع
  - EXP (تاريخ الانتهاء)
  - B.N (رقم الدفعة)
- قسم المجاميع مع الخصم والمجموع النهائي
- قسم الملاحظات
- تذييل مع ختم وتوقيع المسؤول

#### ب. قالب التقارير (`generateLarenReportHTML`)
- تصميم مشابه للفواتير
- ملخص إحصائي للبيانات
- جداول مرتبة حسب نوع التقرير

#### ج. قالب المرتجعات (`generateLarenReturnHTML`)
- تصميم خاص بسندات الإرجاع
- معلومات تفصيلية عن المرتجع
- جدول المواد المرتجعة مع أسباب الإرجاع
- إجمالي المبلغ المرتجع

### 3. تحديث إعدادات الطباعة
تم تحديث `src/hooks/usePrintSettings.ts`:
- إضافة حقل `companyNameEn` للاسم الإنجليزي
- تحديث الإعدادات الافتراضية لتتضمن معلومات لارين
- ربط القوالب الجديدة بدوال الطباعة

### 4. تحديث صفحة الإعدادات
تم تحديث `src/app/settings/page.tsx`:
- إضافة حقل إدخال للاسم الإنجليزي للشركة
- تحديث معلومات الشركة الافتراضية
- إضافة حقل الاسم الإنجليزي في إعدادات الطباعة

### 5. تحديث صفحة المرتجعات
تم تحديث `src/app/returns-records/page.tsx`:
- إضافة دالة `handlePrintSingleReturnWithLaren` لطباعة المرتجعات بالقالب الجديد
- تحديث زر الطباعة ليستخدم القالب الجديد
- إزالة الكود القديم للطباعة

## الميزات الجديدة

### 1. تصميم احترافي
- حدود سوداء واضحة
- تخطيط منظم ومرتب
- خطوط واضحة ومقروءة

### 2. معلومات شاملة
- اسم الشركة بالعربية والإنجليزية
- شعار مميز للشركة
- معلومات تفصيلية للعملاء/الموردين

### 3. جداول منظمة
- أعمدة واضحة ومحددة
- ترقيم تسلسلي للمواد
- معلومات الانتهاء ورقم الدفعة

### 4. قسم التوقيع
- مكان مخصص لختم وتوقيع المسؤول
- تصميم دائري احترافي

## كيفية الاستخدام

### طباعة الفواتير
```typescript
import { printInvoice } from '@/hooks/usePrintSettings'

// سيتم استخدام قالب لارين تلقائياً
printInvoice(invoice, 'sales', settings)
```

### طباعة المرتجعات
```typescript
// في صفحة المرتجعات، استخدم الزر الجديد
handlePrintSingleReturnWithLaren(returnRecord)
```

### طباعة التقارير
```typescript
import { printReport } from '@/hooks/usePrintSettings'

// سيتم استخدام قالب لارين تلقائياً
printReport(reportData, 'sales', 'تقرير المبيعات', settings)
```

## الملفات المحدثة

1. `src/utils/larenPrintTemplate.ts` - قوالب الطباعة الجديدة
2. `src/hooks/usePrintSettings.ts` - إعدادات الطباعة المحدثة
3. `src/app/settings/page.tsx` - صفحة الإعدادات المحدثة
4. `src/app/returns-records/page.tsx` - صفحة المرتجعات المحدثة

## ملاحظات مهمة

1. **التوافق**: القوالب الجديدة متوافقة مع جميع المتصفحات الحديثة
2. **الطباعة**: تم تحسين القوالب للطباعة على ورق A4
3. **الاستجابة**: القوالب تتكيف مع أحجام الشاشات المختلفة
4. **الأداء**: تم تحسين الكود لضمان سرعة التحميل والطباعة

## التطوير المستقبلي

1. إضافة المزيد من خيارات التخصيص
2. دعم أحجام ورق إضافية
3. إضافة قوالب لأنواع مستندات أخرى
4. تحسين التصميم بناءً على ملاحظات المستخدمين

---

تم إنجاز هذا التحديث في: ${new Date().toLocaleDateString('ar-EG')}
