(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9302],{43126:(e,t,a)=>{Promise.resolve().then(a.bind(a,77152))},77152:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var c=a(95155),n=a(12115),l=a(42099),i=a(10988);function s(){let[e,t]=(0,n.useState)([]),[a,s]=(0,n.useState)(!1),o=e=>{let a=new Date().toLocaleTimeString(),c="[".concat(a,"] ").concat(e);t(e=>[...e,c]),console.log(c)},u=()=>{t([])},r=async()=>{o("\uD83D\uDD04 اختبار اتصال Supabase...");try{let{data:e,error:t}=await l.N.from("medicines").select("id, name").limit(1);if(t)return o("❌ خطأ في Supabase: ".concat(t.message)),o("\uD83D\uDCCB تفاصيل الخطأ: ".concat(JSON.stringify(t))),!1;return o("✅ Supabase متصل بنجاح"),o("\uD83D\uDCCA عدد الأدوية المسترجعة: ".concat((null==e?void 0:e.length)||0)),e&&e.length>0&&o("\uD83D\uDCCB أول دواء: ".concat(JSON.stringify(e[0]))),!0}catch(e){return o("❌ خطأ في الاتصال: ".concat(e.message)),!1}},d=()=>{o("\uD83D\uDD04 فحص بيانات localStorage..."),["medicines","medicine_batches","sales_invoices","sales_invoice_items","customers"].forEach(e=>{try{let t=localStorage.getItem(e);if(t){let a=JSON.parse(t);o("\uD83D\uDCCA ".concat(e,": ").concat(a.length," عنصر")),"medicines"===e&&a.length>0&&o("\uD83D\uDCCB أول دواء في localStorage: ".concat(JSON.stringify(a[0]))),"sales_invoices"===e&&a.length>0&&o("\uD83D\uDCCB أول فاتورة في localStorage: ".concat(JSON.stringify(a[0])))}else o("\uD83D\uDCCA ".concat(e,": لا توجد بيانات"))}catch(t){o("❌ خطأ في قراءة ".concat(e,": ").concat(t.message))}})},D=async()=>{o("\uD83D\uDD04 اختبار إنشاء فاتورة...");try{var e,t;let a={invoice_number:"DEBUG-".concat(Date.now()),customer_name:"عميل تجريبي",total_amount:100,discount_amount:0,final_amount:100,payment_method:"cash",payment_status:"paid",notes:"فاتورة اختبار تشخيص"};o("\uD83D\uDCCB بيانات الفاتورة: ".concat(JSON.stringify(a)));let c=await (0,i.createSalesInvoice)(a);if(o("\uD83D\uDCCB نتيجة إنشاء الفاتورة: ".concat(JSON.stringify(c))),!c.success)return o("❌ فشل في إنشاء الفاتورة: ".concat(null==(t=c.error)?void 0:t.message)),null;{o("✅ تم إنشاء الفاتورة بنجاح. ID: ".concat(null==(e=c.data)?void 0:e.id));let t=JSON.parse(localStorage.getItem("sales_invoices")||"[]");return o("\uD83D\uDCCA عدد الفواتير في localStorage بعد الإنشاء: ".concat(t.length)),c.data}}catch(e){return o("❌ خطأ في إنشاء الفاتورة: ".concat(e.message)),o("\uD83D\uDCCB تفاصيل الخطأ: ".concat(JSON.stringify(e))),null}},g=async()=>{o("\uD83D\uDD04 اختبار معاملة كاملة...");try{var e,t,a;let c=await (0,i.getMedicines)();if(!c.success||!c.data||0===c.data.length)return o("❌ لا توجد أدوية متاحة للاختبار"),null;let n=c.data[0],l=(null==(e=n.batches)?void 0:e[0])||(null==(t=n.medicine_batches)?void 0:t[0]);if(!l)return o("❌ لا توجد دفعات متاحة للاختبار"),null;o("\uD83D\uDCE6 استخدام الدواء: ".concat(n.name)),o("\uD83D\uDCE6 استخدام الدفعة: ".concat(l.batch_code));let s={invoice_number:"COMPLETE-".concat(Date.now()),customer_name:"عميل اختبار كامل",total_amount:1500,discount_amount:0,final_amount:1500,payment_method:"cash",payment_status:"paid",notes:"اختبار معاملة كاملة"},u=[{medicine_batch_id:l.id,quantity:2,unit_price:750,total_price:1500,is_gift:!1,medicine_name:n.name,medicineName:n.name}];o("\uD83D\uDCCB بيانات المعاملة: ".concat(JSON.stringify(s))),o("\uD83D\uDCE6 عناصر المعاملة: ".concat(JSON.stringify(u)));let r=await (0,i.completeSalesTransaction)(s,u);if(o("\uD83D\uDCCB نتيجة المعاملة الكاملة: ".concat(JSON.stringify(r))),!r.success)return o("❌ فشلت المعاملة الكاملة: ".concat(null==(a=r.error)?void 0:a.message)),null;{o("✅ تمت المعاملة الكاملة بنجاح");let e=JSON.parse(localStorage.getItem("sales_invoices")||"[]"),t=JSON.parse(localStorage.getItem("sales_invoice_items")||"[]");return o("\uD83D\uDCCA عدد الفواتير في localStorage: ".concat(e.length)),o("\uD83D\uDCCA عدد العناصر في localStorage: ".concat(t.length)),r.data}}catch(e){return o("❌ خطأ في المعاملة الكاملة: ".concat(e.message)),o("\uD83D\uDCCB تفاصيل الخطأ: ".concat(JSON.stringify(e))),null}},m=async()=>{o("\uD83D\uDD04 اختبار استرجاع الفواتير...");try{var e,t,a,c;let n=await (0,i.getSalesInvoices)();if(o("\uD83D\uDCCB نتيجة استرجاع الفواتير: ".concat(JSON.stringify(n))),!n.success)return o("❌ فشل في استرجاع الفواتير: ".concat(null==(c=n.error)?void 0:c.message)),null;if(o("✅ تم استرجاع ".concat((null==(e=n.data)?void 0:e.length)||0," فاتورة")),n.data&&n.data.length>0){let e=n.data[0];o("\uD83D\uDCCB أول فاتورة: ".concat(JSON.stringify(e)));let c=e.sales_invoice_items||[];if(c.length>0){let e=c[0];o("\uD83D\uDCCB أول عنصر: ".concat(JSON.stringify(e)));let n=(null==(a=e.medicine_batches)||null==(t=a.medicines)?void 0:t.name)||e.medicine_name||e.medicineName;o("\uD83D\uDD0D اسم الدواء في أول عنصر: ".concat(n||"غير متوفر"))}}return n.data}catch(e){return o("❌ خطأ في استرجاع الفواتير: ".concat(e.message)),o("\uD83D\uDCCB تفاصيل الخطأ: ".concat(JSON.stringify(e))),null}},h=async()=>{o("\uD83D\uDD04 اختبار تحميل الأدوية...");try{var e,t;let a=await (0,i.getMedicines)();if(o("\uD83D\uDCCB نتيجة تحميل الأدوية: ".concat(JSON.stringify(a))),!a.success)return o("❌ فشل في تحميل الأدوية: ".concat(null==(t=a.error)?void 0:t.message)),null;if(o("✅ تم تحميل ".concat((null==(e=a.data)?void 0:e.length)||0," دواء")),a.data&&a.data.length>0){let e=a.data[0];o("\uD83D\uDCCB أول دواء: ".concat(JSON.stringify(e))),e.batches&&e.batches.length>0&&(o("\uD83D\uDCE6 عدد الدفعات للدواء الأول: ".concat(e.batches.length)),o("\uD83D\uDCE6 أول دفعة: ".concat(JSON.stringify(e.batches[0]))))}return a.data}catch(e){return o("❌ خطأ في تحميل الأدوية: ".concat(e.message)),o("\uD83D\uDCCB تفاصيل الخطأ: ".concat(JSON.stringify(e))),null}},C=async()=>{o("\uD83D\uDD04 اختبار بيانات الطباعة...");try{var e;let t=await (0,i.getSalesInvoices)();if(!t.success||!t.data||0===t.data.length)return o("❌ لا توجد فواتير للاختبار"),null;let a=t.data[0];o("\uD83D\uDCCB اختبار طباعة الفاتورة: ".concat(a.invoice_number));let c=await (0,i.getSalesInvoiceForPrint)(a.id);if(o("\uD83D\uDCCB نتيجة استرجاع بيانات الطباعة: ".concat(JSON.stringify(c))),c.success&&c.data){let e=c.data;o("\uD83D\uDCCB بيانات الفاتورة للطباعة: ".concat(JSON.stringify(e)));let t=e.sales_invoice_items||[];return o("\uD83D\uDCE6 عدد العناصر: ".concat(t.length)),t.forEach((e,t)=>{var a,c;let n=(null==(c=e.medicine_batches)||null==(a=c.medicines)?void 0:a.name)||e.medicine_name||e.medicineName||"غير متوفر";o("\uD83D\uDCE6 العنصر ".concat(t+1,": ").concat(n)),o("\uD83D\uDCE6 تفاصيل العنصر ".concat(t+1,": ").concat(JSON.stringify(e)))}),e}return o("❌ فشل في استرجاع بيانات الطباعة: ".concat(null==(e=c.error)?void 0:e.message)),null}catch(e){return o("❌ خطأ في اختبار الطباعة: ".concat(e.message)),o("\uD83D\uDCCB تفاصيل الخطأ: ".concat(JSON.stringify(e))),null}},b=async()=>{o("\uD83D\uDD04 اختبار تهيئة البيانات...");try{var e;let t=await (0,i.initializeSystemData)();if(o("\uD83D\uDCCB نتيجة التهيئة: ".concat(JSON.stringify(t))),t.success){o("✅ تمت تهيئة البيانات بنجاح");let e=JSON.parse(localStorage.getItem("medicines")||"[]"),a=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),c=JSON.parse(localStorage.getItem("customers")||"[]");return o("\uD83D\uDCCA الأدوية: ".concat(e.length)),o("\uD83D\uDCCA الدفعات: ".concat(a.length)),o("\uD83D\uDCCA العملاء: ".concat(c.length)),t.data}return o("❌ فشلت تهيئة البيانات: ".concat(null==(e=t.error)?void 0:e.message)),null}catch(e){return o("❌ خطأ في تهيئة البيانات: ".concat(e.message)),null}},y=async()=>{s(!0),u(),o("\uD83D\uDE80 بدء التشخيص الشامل..."),await b(),await r(),d(),await h(),await D(),await m(),await g(),await C(),o("\uD83C\uDF89 انتهى التشخيص الشامل!"),s(!1)};return(0,c.jsx)("div",{className:"container mx-auto p-6",children:(0,c.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,c.jsx)("h1",{className:"text-3xl font-bold mb-6 text-center",children:"\uD83D\uDD0D صفحة التشخيص"}),(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,c.jsx)("h2",{className:"text-xl font-bold mb-4",children:"أدوات التشخيص"}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsx)("button",{onClick:y,disabled:a,className:"w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:a?"جاري التشخيص...":"\uD83D\uDE80 تشخيص شامل"}),(0,c.jsx)("button",{onClick:b,disabled:a,className:"w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDD27 تهيئة البيانات"}),(0,c.jsx)("button",{onClick:r,disabled:a,className:"w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDD17 اختبار Supabase"}),(0,c.jsx)("button",{onClick:d,disabled:a,className:"w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDCBE فحص localStorage"}),(0,c.jsx)("button",{onClick:h,disabled:a,className:"w-full bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDC8A اختبار الأدوية"}),(0,c.jsx)("button",{onClick:g,disabled:a,className:"w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDD04 اختبار معاملة كاملة"}),(0,c.jsx)("button",{onClick:C,disabled:a,className:"w-full bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"\uD83D\uDDA8️ اختبار بيانات الطباعة"}),(0,c.jsx)("button",{onClick:()=>{["sales_invoices","sales_invoice_items","medicines","medicine_batches","customers"].forEach(e=>localStorage.removeItem(e)),o("\uD83D\uDDD1️ تم مسح جميع البيانات من localStorage")},className:"w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg",children:"\uD83D\uDDD1️ مسح localStorage"}),(0,c.jsx)("button",{onClick:u,className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"\uD83D\uDDD1️ مسح السجل"})]}),(0,c.jsx)("div",{className:"mt-6",children:(0,c.jsx)("a",{href:"/sales",className:"block w-full text-center bg-gray-700 hover:bg-gray-800 text-white px-4 py-2 rounded-lg",children:"← العودة إلى المبيعات"})})]}),(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,c.jsx)("h2",{className:"text-xl font-bold mb-4",children:"سجل التشخيص"}),(0,c.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm",children:0===e.length?(0,c.jsx)("p",{className:"text-gray-500",children:"لم يتم تشغيل أي اختبار بعد..."}):e.map((e,t)=>(0,c.jsx)("div",{className:"mb-1",children:e},t))})]})]})]})})}}},e=>{e.O(0,[5647,988,8441,5964,7358],()=>e(e.s=43126)),_N_E=e.O()}]);