"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3363],{53363:(n,t,e)=>{e.r(t),e.d(t,{printInvoice:()=>s,printReport:()=>l,usePrintSettings:()=>o});var a=e(12115);let i={companyName:"مكتب لارين العلمي",companyNameEn:"LAREN SCIENTIFIC BUREAU",companyAddress:"بغداد - شارع فلسطين",companyPhone:"+*********** 4567",companyEmail:"<EMAIL>",showLogo:!0,showHeader:!0,showFooter:!0,footerText:"شكراً لتعاملكم معنا",fontSize:"medium",paperSize:"A4",showBorders:!0,showColors:!1,includeBarcode:!1,includeQRCode:!1,showWatermark:!1,headerColor:"#1f2937",accentColor:"#3b82f6",textColor:"#374151",backgroundColor:"#ffffff"};function o(){let[n,t]=(0,a.useState)(i),[e,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{s()},[]);let s=()=>{try{let n=localStorage.getItem("printSettings");if(n){let e=JSON.parse(n);t({...i,...e})}}catch(n){console.error("Error loading print settings:",n)}finally{o(!1)}},l=e=>{let a={...n,...e};t(a);try{localStorage.setItem("printSettings",JSON.stringify(a))}catch(n){console.error("Error saving print settings:",n)}};return{settings:n,printSettings:n,loading:e,updateSettings:l,resetSettings:()=>{t(i);try{localStorage.removeItem("printSettings")}catch(n){console.error("Error resetting print settings:",n)}},exportSettings:()=>{let t=new Blob([JSON.stringify(n,null,2)],{type:"application/json"}),e=URL.createObjectURL(t),a=document.createElement("a");a.href=e,a.download="print-settings.json",a.click(),URL.revokeObjectURL(e)},importSettings:n=>new Promise((t,e)=>{let a=new FileReader;a.onload=n=>{try{var a;let e=JSON.parse(null==(a=n.target)?void 0:a.result);l(e),t()}catch(n){e(n)}},a.onerror=()=>e(Error("Failed to read file")),a.readAsText(n)})}}let s=async(n,t,a)=>{try{var i,o,s,l,d;console.log("\uD83D\uDDA8️ بدء طباعة الفاتورة:",n);let r=n;if(n.id&&"sales"===t){let{getSalesInvoiceForPrint:t}=e(10988),a=await t(n.id);a.success&&a.data&&(r={...a.data,customerName:n.customerName||a.data.customer_name,customerPhone:n.customerPhone||(null==(i=a.data.customers)?void 0:i.phone),customerAddress:n.customerAddress||(null==(o=a.data.customers)?void 0:o.address)},console.log("✅ تم استرجاع بيانات فاتورة المبيعات من قاعدة البيانات:",r))}else if(n.id&&"purchase"===t){let{getPurchaseInvoiceForPrint:t}=e(10988),a=await t(n.id);a.success&&a.data&&(r={...a.data,supplierName:n.supplierName||(null==(s=a.data.suppliers)?void 0:s.name),supplierPhone:n.supplierPhone||(null==(l=a.data.suppliers)?void 0:l.phone),supplierAddress:n.supplierAddress||(null==(d=a.data.suppliers)?void 0:d.address)},console.log("✅ تم استرجاع بيانات فاتورة المشتريات من قاعدة البيانات:",r))}else if(n.id&&"return"===t){let{getReturnForPrint:t}=e(10988),a=await t(n.id);a.success&&a.data&&(r={...a.data,customerName:n.customerName||a.data.customer_name,supplierName:n.supplierName||a.data.supplier_name},console.log("✅ تم استرجاع بيانات المرتجع من قاعدة البيانات:",r))}let c=window.open("","_blank");if(!c)return void console.error("❌ فشل في فتح نافذة الطباعة");let{generateLarenInvoiceHTML:p}=e(80214),m=p(r,t,a);c.document.write(m),c.document.close(),c.focus(),c.print(),c.close(),console.log("✅ تمت الطباعة بنجاح")}catch(l){console.error("❌ خطأ في الطباعة:",l);let i=window.open("","_blank");if(!i)return;let{generateLarenInvoiceHTML:o}=e(80214),s=o(n,t,a);i.document.write(s),i.document.close(),i.focus(),i.print(),i.close()}},l=(n,t,a,i)=>{let o=window.open("","_blank");if(!o)return;let{generateLarenReportHTML:s}=e(80214),l=s(n,t,a,i);o.document.write(l),o.document.close(),o.focus(),o.print(),o.close()}},80214:(n,t,e)=>{e.r(t),e.d(t,{generateLarenInvoiceHTML:()=>a,generateLarenReportHTML:()=>i,generateLarenReturnHTML:()=>o});let a=(n,t,e)=>{var a,i,o,s,l,d,r,c,p,m,u,h,v,g,b,x;let f,y,w,_;if("return"===t){f=n.return_invoice_items||n.sales_return_items||n.purchase_return_items||[];let t=n.type||n.return_type||"sales";y="sales"===t?(null==(v=n.customers)?void 0:v.name)||n.customer_name||n.customerName||"عميل نقدي":(null==(g=n.suppliers)?void 0:g.name)||n.supplier_name||n.supplierName||"غير محدد",w="sales"===t?"مرتجع مبيعات":"مرتجع مشتريات",_=n.return_number||n.invoice_number||"غير محدد"}else f="sales"===t?n.sales_invoice_items:n.purchase_invoice_items,y="sales"===t?(null==(b=n.customers)?void 0:b.name)||n.customer_name||n.customerName||"عميل نقدي":(null==(x=n.suppliers)?void 0:x.name)||n.supplier_name||n.supplierName||"غير محدد",w="sales"===t?"فاتورة مبيعات":"فاتورة مشتريات",_=n.invoice_number||"غير محدد";return'\n    <!DOCTYPE html>\n    <html dir="rtl" lang="ar">\n    <head>\n      <meta charset="UTF-8">\n      <meta name="viewport" content="width=device-width, initial-scale=1.0">\n      <title>'.concat(w," - ").concat(_,'</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: \'Arial\', sans-serif; \n          font-size: 14px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container { \n          max-width: 210mm; \n          margin: 0 auto; \n          padding: 10mm;\n          border: 2px solid #000;\n          min-height: 297mm;\n          position: relative;\n        }\n        \n        /* Header Section */\n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n        \n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n        \n        .company-name-ar {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        .company-name-en {\n          font-size: 18px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n        \n        .company-address {\n          font-size: 14px;\n          margin-bottom: 5px;\n        }\n        \n        .logo-section {\n          width: 120px;\n          height: 120px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n        \n        .logo-text {\n          font-size: 16px;\n          font-weight: bold;\n          text-align: center;\n          line-height: 1.2;\n        }\n        \n        /* Invoice Details Section */\n        .invoice-details {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n        }\n        \n        .invoice-info, .customer-info {\n          flex: 1;\n          padding: 10px;\n          border-right: 1px solid #000;\n        }\n        \n        .customer-info {\n          border-right: none;\n        }\n        \n        .detail-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n          padding: 2px 0;\n        }\n        \n        .detail-label {\n          font-weight: bold;\n          min-width: 80px;\n        }\n        \n        .detail-value {\n          text-align: left;\n        }\n        \n        /* Items Table */\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n        \n        .items-table th,\n        .items-table td {\n          border: 1px solid #000;\n          padding: 8px;\n          text-align: center;\n          font-size: 12px;\n        }\n        \n        .items-table th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n        }\n        \n        .items-table .item-name {\n          text-align: right;\n          padding-right: 10px;\n        }\n        \n        /* Totals Section */\n        .totals-section {\n          width: 300px;\n          margin-left: auto;\n          border: 2px solid #000;\n          margin-bottom: 20px;\n        }\n        \n        .totals-section table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n        \n        .totals-section td {\n          border: 1px solid #000;\n          padding: 8px;\n          font-size: 14px;\n        }\n        \n        .totals-section .total-label {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: right;\n        }\n        \n        .totals-section .total-value {\n          text-align: center;\n          font-weight: bold;\n        }\n        \n        /* Footer Section */\n        .footer {\n          position: absolute;\n          bottom: 10mm;\n          left: 10mm;\n          right: 10mm;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          border-top: 1px solid #000;\n          padding-top: 15px;\n        }\n        \n        .signature-section {\n          text-align: center;\n          flex: 1;\n        }\n        \n        .signature-box {\n          width: 150px;\n          height: 80px;\n          border: 2px solid #000;\n          border-radius: 50%;\n          margin: 0 auto 10px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          color: #666;\n        }\n        \n        .notes-section {\n          margin-bottom: 30px;\n          border: 1px solid #000;\n          padding: 10px;\n          min-height: 60px;\n        }\n        \n        .notes-label {\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .container { \n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class="container">\n        \x3c!-- Header --\x3e\n        <div class="header">\n          <div class="company-info">\n            <div class="company-name-ar">').concat(e.companyName,'</div>\n            <div class="company-name-en">').concat(e.companyNameEn,'</div>\n            <div class="company-address">').concat(e.companyAddress,'</div>\n          </div>\n          \n          <div class="logo-section">\n            <div class="logo-text">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        \x3c!-- Document Details --\x3e\n        <div class="invoice-details">\n          <div class="invoice-info">\n            <div class="detail-row">\n              <span class="detail-label">').concat("return"===t?"رقم المرتجع:":"رقم الفاتورة:",'</span>\n              <span class="detail-value">').concat(_,'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">التاريخ:</span>\n              <span class="detail-value">').concat(new Date(n.created_at).toLocaleDateString("ar-EG"),"</span>\n            </div>\n            ").concat("return"!==t?'\n            <div class="detail-row">\n              <span class="detail-label">طريقة الدفع:</span>\n              <span class="detail-value">'.concat("cash"===n.payment_method?"نقداً":"آجل","</span>\n            </div>"):"","\n            ").concat("return"===t?'\n            <div class="detail-row">\n              <span class="detail-label">سبب المرتجع:</span>\n              <span class="detail-value">'.concat(n.reason||"غير محدد",'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">الحالة:</span>\n              <span class="detail-value">').concat("approved"===n.status?"مقبول":"rejected"===n.status?"مرفوض":"قيد المراجعة","</span>\n            </div>"):"",'\n            </div>\n          </div>\n          \n          <div class="customer-info">\n            <div class="detail-row">\n              <span class="detail-label">').concat("return"===t?"purchase"===n.type||"purchase"===n.return_type?"المورد:":"العميل:":"sales"===t?"العميل:":"المورد:",'</span>\n              <span class="detail-value">').concat(y,'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">الهاتف:</span>\n              <span class="detail-value">').concat("return"===t?"purchase"===n.type||"purchase"===n.return_type?(null==(a=n.suppliers)?void 0:a.phone)||n.supplier_phone||n.supplierPhone||"":(null==(i=n.customers)?void 0:i.phone)||n.customer_phone||n.customerPhone||"":"sales"===t?(null==(o=n.customers)?void 0:o.phone)||n.customer_phone||n.customerPhone||"":(null==(s=n.suppliers)?void 0:s.phone)||n.supplier_phone||n.supplierPhone||"",'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">العنوان:</span>\n              <span class="detail-value">').concat("return"===t?"purchase"===n.type||"purchase"===n.return_type?(null==(l=n.suppliers)?void 0:l.address)||n.supplier_address||n.supplierAddress||"":(null==(d=n.customers)?void 0:d.address)||n.customer_address||n.customerAddress||"":"sales"===t?(null==(r=n.customers)?void 0:r.address)||n.customer_address||n.customerAddress||"":(null==(c=n.suppliers)?void 0:c.address)||n.supplier_address||n.supplierAddress||"",'</span>\n            </div>\n          </div>\n        </div>\n\n        \x3c!-- Items Table --\x3e\n        <table class="items-table">\n          <thead>\n            <tr>\n              <th style="width: 40px;">ت</th>\n              <th style="width: ').concat("return"===t?"150px":"200px",';">اسم المادة</th>\n              <th style="width: 60px;">الكمية</th>\n              <th style="width: 80px;">السعر</th>\n              <th style="width: 100px;">المجموع</th>\n              ').concat("return"===t?'<th style="width: 100px;">سبب المرتجع</th>':"",'\n              <th style="width: 60px;">EXP</th>\n              <th style="width: 60px;">B.N</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat((null==f?void 0:f.map((e,a)=>{var i,o,s,l,d,r,c,p,m,u;return"\n              <tr>\n                <td>".concat(a+1,'</td>\n                <td class="item-name">').concat("return"===t?e.medicine_name||e.medicineName||(null==(o=e.medicine_batches)||null==(i=o.medicines)?void 0:i.name)||(null==(s=e.medicines)?void 0:s.name)||"غير محدد":"sales"===t?e.medicine_name||e.medicineName||(null==(d=e.medicine_batches)||null==(l=d.medicines)?void 0:l.name)||"غير محدد":e.medicine_name||e.medicineName||(null==(r=e.medicines)?void 0:r.name)||e.name||"غير محدد","</td>\n                <td>").concat(e.quantity,"</td>\n                <td>").concat("return"===t?"purchase"===n.type||"purchase"===n.return_type?(e.unit_cost||e.unitCost||0).toLocaleString():(e.unit_price||e.unitPrice||0).toLocaleString():"sales"===t?(e.unit_price||0).toLocaleString():(e.unit_cost||e.unitCost||0).toLocaleString(),"</td>\n                <td>").concat("return"===t?"purchase"===n.type||"purchase"===n.return_type?(e.total_cost||e.totalCost||0).toLocaleString():(e.total_price||e.totalPrice||0).toLocaleString():"sales"===t?(e.total_price||0).toLocaleString():(e.total_cost||e.totalCost||0).toLocaleString(),"</td>\n                ").concat("return"===t?'<td style="font-size: 10px;">'.concat(e.return_reason||n.reason||"غير محدد","</td>"):"","\n                <td>").concat("return"===t?e.expiry_date||e.expiryDate||(null==(c=e.medicine_batches)?void 0:c.expiry_date)?new Date(e.expiry_date||e.expiryDate||e.medicine_batches.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"":"sales"===t?(null==(p=e.medicine_batches)?void 0:p.expiry_date)?new Date(e.medicine_batches.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"":e.expiry_date||e.expiryDate?new Date(e.expiry_date||e.expiryDate).toLocaleDateString("en-GB").replace(/\//g,"/"):"","</td>\n                <td>").concat("return"===t?e.batch_code||e.batchCode||(null==(m=e.medicine_batches)?void 0:m.batch_number)||"":"sales"===t?(null==(u=e.medicine_batches)?void 0:u.batch_number)||"":e.batch_code||e.batchCode||"","</td>\n              </tr>\n            ")}).join(""))||"","\n            \n            \x3c!-- Empty rows to fill space --\x3e\n            ").concat(Array.from({length:Math.max(0,10-((null==f?void 0:f.length)||0))},(n,e)=>"\n              <tr>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                ".concat("return"===t?"<td>&nbsp;</td>":"","\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n              </tr>\n            ")).join(""),'\n          </tbody>\n        </table>\n\n        \x3c!-- Totals --\x3e\n        <div class="totals-section">\n          <table>\n            ').concat("return"!==t?'\n            <tr>\n              <td class="total-label">المجموع الفرعي:</td>\n              <td class="total-value">'.concat((null==(p=n.total_amount)?void 0:p.toLocaleString())||0,'</td>\n            </tr>\n            <tr>\n              <td class="total-label">الخصم:</td>\n              <td class="total-value">').concat((null==(m=n.discount_amount)?void 0:m.toLocaleString())||0,'</td>\n            </tr>\n            <tr style="background-color: #f0f0f0;">\n              <td class="total-label">المجموع النهائي:</td>\n              <td class="total-value">').concat((null==(u=n.final_amount)?void 0:u.toLocaleString())||0,"</td>\n            </tr>"):'\n            <tr style="background-color: #f0f0f0;">\n              <td class="total-label">إجمالي المرتجع:</td>\n              <td class="total-value">'.concat((null==(h=n.total_amount)?void 0:h.toLocaleString())||0,"</td>\n            </tr>"),'\n          </table>\n        </div>\n\n        \x3c!-- Notes --\x3e\n        <div class="notes-section">\n          <div class="notes-label">ملاحظات: ').concat(n.notes||"","</div>\n          ").concat("return"===t&&n.rejection_reason?'\n          <div class="notes-label" style="margin-top: 10px; color: #dc2626;">سبب الرفض: '.concat(n.rejection_reason,"</div>"):"",'\n        </div>\n\n        \x3c!-- Footer --\x3e\n        <div class="footer">\n          <div style="font-size: 12px;">\n            صفحة 1 من 1\n          </div>\n          \n          <div class="signature-section">\n            <div class="signature-box">\n              ختم وتوقيع<br>\n              المسؤول\n            </div>\n          </div>\n          \n          <div style="font-size: 12px; text-align: left;">\n            ').concat(e.footerText||"شكراً لتعاملكم معنا","\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  ")},i=(n,t,e,a)=>'\n    <!DOCTYPE html>\n    <html dir="rtl" lang="ar">\n    <head>\n      <meta charset="UTF-8">\n      <meta name="viewport" content="width=device-width, initial-scale=1.0">\n      <title>'.concat(e,'</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: \'Arial\', sans-serif; \n          font-size: 12px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container { \n          max-width: 210mm; \n          margin: 0 auto; \n          padding: 10mm;\n          border: 2px solid #000;\n        }\n        \n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n        \n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n        \n        .company-name-ar {\n          font-size: 20px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        .company-name-en {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n        \n        .logo-section {\n          width: 100px;\n          height: 100px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n        \n        .report-title {\n          text-align: center;\n          font-size: 18px;\n          font-weight: bold;\n          margin: 20px 0;\n          padding: 10px;\n          border: 1px solid #000;\n          background-color: #f0f0f0;\n        }\n        \n        .report-date {\n          text-align: center;\n          margin-bottom: 20px;\n          font-size: 14px;\n        }\n        \n        table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n        \n        th, td {\n          border: 1px solid #000;\n          padding: 4px 6px;\n          text-align: right;\n          font-size: 10px;\n          vertical-align: top;\n          word-wrap: break-word;\n          max-width: 120px;\n        }\n\n        th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: center;\n          font-size: 9px;\n        }\n\n        tr:nth-child(even) {\n          background-color: #f9f9f9;\n        }\n\n        .number-cell {\n          text-align: center;\n          font-weight: bold;\n          width: 30px;\n        }\n        \n        .summary-section {\n          display: flex;\n          justify-content: space-around;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n          padding: 15px;\n        }\n        \n        .summary-item {\n          text-align: center;\n        }\n        \n        .summary-label {\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        \n        .summary-value {\n          font-size: 16px;\n          font-weight: bold;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .container { \n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class="container">\n        <div class="header">\n          <div class="company-info">\n            <div class="company-name-ar">').concat(a.companyName,'</div>\n            <div class="company-name-en">').concat(a.companyNameEn,"</div>\n            <div>").concat(a.companyAddress,'</div>\n          </div>\n          \n          <div class="logo-section">\n            <div style="font-size: 14px; font-weight: bold; text-align: center;">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        <div class="report-title">').concat(e,'</div>\n        <div class="report-date">تاريخ الطباعة: ').concat(new Date().toLocaleDateString("ar-EG"),"</div>\n\n        ").concat(Array.isArray(n)&&n.length>0?'\n          <div class="summary-section">\n            <div class="summary-item">\n              <div class="summary-label">عدد السجلات</div>\n              <div class="summary-value">'.concat(n.length,"</div>\n            </div>\n            ").concat(t.includes("sales")||t.includes("purchases")?'\n              <div class="summary-item">\n                <div class="summary-label">إجمالي المبلغ</div>\n                <div class="summary-value">'.concat(n.reduce((n,t)=>n+(t.final_amount||t.total_amount||0),0).toLocaleString(),' د.ع</div>\n              </div>\n              <div class="summary-item">\n                <div class="summary-label">متوسط المبلغ</div>\n                <div class="summary-value">').concat(Math.round(n.reduce((n,t)=>n+(t.final_amount||t.total_amount||0),0)/n.length).toLocaleString(),' د.ع</div>\n              </div>\n              <div class="summary-item">\n                <div class="summary-label">الفواتير المدفوعة</div>\n                <div class="summary-value">').concat(n.filter(n=>"paid"===n.payment_status).length,'</div>\n              </div>\n              <div class="summary-item">\n                <div class="summary-label">الفواتير المعلقة</div>\n                <div class="summary-value">').concat(n.filter(n=>"pending"===n.payment_status).length,"</div>\n              </div>\n            "):"","\n            ").concat("inventory"===t?'\n              <div class="summary-item">\n                <div class="summary-label">إجمالي الكمية</div>\n                <div class="summary-value">'.concat(n.reduce((n,t)=>n+(t.quantity||0),0).toLocaleString(),'</div>\n              </div>\n              <div class="summary-item">\n                <div class="summary-label">الأدوية منتهية الصلاحية</div>\n                <div class="summary-value">').concat(n.filter(n=>new Date(n.expiry_date)<new Date).length,'</div>\n              </div>\n              <div class="summary-item">\n                <div class="summary-label">الأدوية قليلة الكمية</div>\n                <div class="summary-value">').concat(n.filter(n=>10>(n.quantity||0)).length,"</div>\n              </div>\n            "):"",'\n          </div>\n\n          <table>\n            <thead>\n              <tr>\n                <th style="width: 30px;">ت</th>\n                ').concat(t.includes("sales")?"\n                  <th>رقم الفاتورة</th>\n                  <th>العميل</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ":"","\n                ").concat(t.includes("purchases")?"\n                  <th>رقم الفاتورة</th>\n                  <th>المورد</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ":"","\n                ").concat("inventory"===t?"\n                  <th>اسم الدواء</th>\n                  <th>الفئة</th>\n                  <th>الكمية</th>\n                  <th>تاريخ الانتهاء</th>\n                  <th>الحالة</th>\n                ":"","\n                ").concat("financial"===t?"\n                  <th>نوع العملية</th>\n                  <th>المبلغ</th>\n                  <th>الوصف</th>\n                  <th>التاريخ</th>\n                ":"","\n                ").concat("customers"===t?"\n                  <th>اسم العميل</th>\n                  <th>الهاتف</th>\n                  <th>العنوان</th>\n                  <th>إجمالي المشتريات</th>\n                ":"","\n                ").concat("suppliers"===t?"\n                  <th>اسم المورد</th>\n                  <th>الهاتف</th>\n                  <th>العنوان</th>\n                  <th>إجمالي المشتريات</th>\n                ":"","\n                ").concat(!t.includes("sales")&&!t.includes("purchases")&&"inventory"!==t&&"financial"!==t&&"customers"!==t&&"suppliers"!==t?"\n                  ".concat(Object.keys(n[0]||{}).slice(0,6).map(n=>"<th>".concat((n=>({id:"الرقم",name:"الاسم",invoice_number:"رقم الفاتورة",customer_name:"اسم العميل",supplier_name:"اسم المورد",total_amount:"المبلغ الإجمالي",final_amount:"المبلغ النهائي",payment_status:"حالة الدفع",created_at:"التاريخ",phone:"الهاتف",address:"العنوان",category:"الفئة",quantity:"الكمية",expiry_date:"تاريخ الانتهاء",medicine_name:"اسم الدواء",batch_code:"رقم الدفعة",unit_price:"سعر الوحدة",discount:"الخصم",notes:"ملاحظات"})[n]||n)(n),"</th>")).join(""),"\n                "):"","\n              </tr>\n            </thead>\n            <tbody>\n              ").concat(n.map((e,a)=>{var i,o,s,l,d,r,c,p,m;return'\n                <tr>\n                  <td class="number-cell">'.concat(a+1,"</td>\n                  ").concat(t.includes("sales")?"\n                    <td>".concat(e.invoice_number,"</td>\n                    <td>").concat((null==(i=e.customers)?void 0:i.name)||e.customer_name||"عميل نقدي","</td>\n                    <td>").concat(null==(o=e.final_amount)?void 0:o.toLocaleString()," د.ع</td>\n                    <td>").concat("paid"===e.payment_status?"مدفوع":"معلق","</td>\n                    <td>").concat(new Date(e.created_at).toLocaleDateString("ar-EG"),"</td>\n                  "):"","\n                  ").concat(t.includes("purchases")?"\n                    <td>".concat(e.invoice_number,"</td>\n                    <td>").concat((null==(s=e.suppliers)?void 0:s.name)||"غير محدد","</td>\n                    <td>").concat(null==(l=e.final_amount)?void 0:l.toLocaleString()," د.ع</td>\n                    <td>").concat("paid"===e.payment_status?"مدفوع":"معلق","</td>\n                    <td>").concat(new Date(e.created_at).toLocaleDateString("ar-EG"),"</td>\n                  "):"","\n                  ").concat("inventory"===t?"\n                    <td>".concat((null==(d=e.medicines)?void 0:d.name)||e.medicine_name||"غير محدد","</td>\n                    <td>").concat((null==(r=e.medicines)?void 0:r.category)||e.category||"غير محدد","</td>\n                    <td>").concat(e.quantity||0,"</td>\n                    <td>").concat(e.expiry_date?new Date(e.expiry_date).toLocaleDateString("ar-EG"):"غير محدد","</td>\n                    <td>").concat(10>(e.quantity||0)?"كمية قليلة":"طبيعي","</td>\n                  "):"","\n                  ").concat("financial"===t?"\n                    <td>".concat("income"===e.type?"دخل":"مصروف","</td>\n                    <td>").concat((null==(c=e.amount)?void 0:c.toLocaleString())||0," د.ع</td>\n                    <td>").concat(e.description||"غير محدد","</td>\n                    <td>").concat(e.created_at?new Date(e.created_at).toLocaleDateString("ar-EG"):"غير محدد","</td>\n                  "):"","\n                  ").concat("customers"===t?"\n                    <td>".concat(e.name||"غير محدد","</td>\n                    <td>").concat(e.phone||"غير محدد","</td>\n                    <td>").concat(e.address||"غير محدد","</td>\n                    <td>").concat((null==(p=e.total_purchases)?void 0:p.toLocaleString())||0," د.ع</td>\n                  "):"","\n                  ").concat("suppliers"===t?"\n                    <td>".concat(e.name||"غير محدد","</td>\n                    <td>").concat(e.phone||"غير محدد","</td>\n                    <td>").concat(e.address||"غير محدد","</td>\n                    <td>").concat((null==(m=e.total_purchases)?void 0:m.toLocaleString())||0," د.ع</td>\n                  "):"","\n                  ").concat(t.includes("sales")||t.includes("purchases")||"inventory"===t||"financial"===t||"customers"===t||"suppliers"===t?"":"\n                    ".concat(Object.keys(n[0]||{}).slice(0,6).map(n=>"\n                      <td>".concat(((n,t)=>{if(null==n)return"غير محدد";if(t.includes("date")||t.includes("created_at"))try{return new Date(n).toLocaleDateString("ar-EG")}catch(t){return n.toString()}if(t.includes("amount")||t.includes("price")||t.includes("cost")){let t=parseFloat(n);return isNaN(t)?n.toString():"".concat(t.toLocaleString()," د.ع")}return"payment_status"===t?"paid"===n?"مدفوع":"pending"===n?"معلق":n.toString():n.toString()})(e[n],n),"</td>\n                    ")).join(""),"\n                  "),"\n                </tr>\n              ")}).join(""),"\n            </tbody>\n          </table>\n        "):'\n          <div style="text-align: center; padding: 50px; border: 1px solid #000;">\n            لا توجد بيانات للعرض\n          </div>\n        ',"\n      </div>\n    </body>\n    </html>\n  "),o=(n,t)=>{var e,a,i,o,s,l,d;let r=n.return_items||[],c="purchase_return"===n.type,p=c?(null==(e=n.suppliers)?void 0:e.name)||"غير محدد":(null==(a=n.customers)?void 0:a.name)||n.customer_name||"عميل نقدي";return'\n    <!DOCTYPE html>\n    <html dir="rtl" lang="ar">\n    <head>\n      <meta charset="UTF-8">\n      <meta name="viewport" content="width=device-width, initial-scale=1.0">\n      <title>سند إرجاع - '.concat(n.return_number,'</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body {\n          font-family: \'Arial\', sans-serif;\n          font-size: 14px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container {\n          max-width: 210mm;\n          margin: 0 auto;\n          padding: 10mm;\n          border: 2px solid #000;\n          min-height: 297mm;\n          position: relative;\n        }\n\n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n\n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n\n        .company-name-ar {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .company-name-en {\n          font-size: 18px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n\n        .logo-section {\n          width: 120px;\n          height: 120px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n\n        .return-title {\n          text-align: center;\n          font-size: 20px;\n          font-weight: bold;\n          margin: 20px 0;\n          padding: 10px;\n          border: 2px solid #000;\n          background-color: #f0f0f0;\n        }\n\n        .return-details {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n        }\n\n        .return-info, .customer-info {\n          flex: 1;\n          padding: 10px;\n          border-right: 1px solid #000;\n        }\n\n        .customer-info {\n          border-right: none;\n        }\n\n        .detail-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n          padding: 2px 0;\n        }\n\n        .detail-label {\n          font-weight: bold;\n          min-width: 100px;\n        }\n\n        .detail-value {\n          text-align: left;\n        }\n\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n\n        .items-table th,\n        .items-table td {\n          border: 1px solid #000;\n          padding: 8px;\n          text-align: center;\n          font-size: 12px;\n        }\n\n        .items-table th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n        }\n\n        .items-table .item-name {\n          text-align: right;\n          padding-right: 10px;\n        }\n\n        .totals-section {\n          width: 300px;\n          margin-left: auto;\n          border: 2px solid #000;\n          margin-bottom: 20px;\n        }\n\n        .totals-section table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        .totals-section td {\n          border: 1px solid #000;\n          padding: 8px;\n          font-size: 14px;\n        }\n\n        .totals-section .total-label {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: right;\n        }\n\n        .totals-section .total-value {\n          text-align: center;\n          font-weight: bold;\n        }\n\n        .footer {\n          position: absolute;\n          bottom: 10mm;\n          left: 10mm;\n          right: 10mm;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          border-top: 1px solid #000;\n          padding-top: 15px;\n        }\n\n        .signature-section {\n          text-align: center;\n          flex: 1;\n        }\n\n        .signature-box {\n          width: 150px;\n          height: 80px;\n          border: 2px solid #000;\n          border-radius: 50%;\n          margin: 0 auto 10px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          color: #666;\n        }\n\n        .notes-section {\n          margin-bottom: 30px;\n          border: 1px solid #000;\n          padding: 10px;\n          min-height: 60px;\n        }\n\n        @media print {\n          body { margin: 0; }\n          .container {\n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class="container">\n        <div class="header">\n          <div class="company-info">\n            <div class="company-name-ar">').concat(t.companyName,'</div>\n            <div class="company-name-en">').concat(t.companyNameEn,'</div>\n            <div class="company-address">').concat(t.companyAddress,'</div>\n          </div>\n\n          <div class="logo-section">\n            <div style="font-size: 16px; font-weight: bold; text-align: center; line-height: 1.2;">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        <div class="return-title">\n          سند إرجاع ').concat(c?"مشتريات":"مبيعات",'\n        </div>\n\n        <div class="return-details">\n          <div class="return-info">\n            <div class="detail-row">\n              <span class="detail-label">رقم سند الإرجاع:</span>\n              <span class="detail-value">').concat(n.return_number,'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">التاريخ:</span>\n              <span class="detail-value">').concat(new Date(n.created_at).toLocaleDateString("ar-EG"),'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">رقم الفاتورة الأصلية:</span>\n              <span class="detail-value">').concat(n.original_invoice_number||"غير محدد",'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">نوع الإرجاع:</span>\n              <span class="detail-value">').concat(c?"إرجاع للمورد":"إرجاع من العميل",'</span>\n            </div>\n          </div>\n\n          <div class="customer-info">\n            <div class="detail-row">\n              <span class="detail-label">').concat(c?"المورد:":"العميل:",'</span>\n              <span class="detail-value">').concat(p,'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">الهاتف:</span>\n              <span class="detail-value">').concat(c?(null==(i=n.suppliers)?void 0:i.phone)||"":(null==(o=n.customers)?void 0:o.phone)||"",'</span>\n            </div>\n            <div class="detail-row">\n              <span class="detail-label">العنوان:</span>\n              <span class="detail-value">').concat(c?(null==(s=n.suppliers)?void 0:s.address)||"":(null==(l=n.customers)?void 0:l.address)||"",'</span>\n            </div>\n          </div>\n        </div>\n\n        <table class="items-table">\n          <thead>\n            <tr>\n              <th style="width: 40px;">ت</th>\n              <th style="width: 200px;">اسم المادة</th>\n              <th style="width: 60px;">الكمية</th>\n              <th style="width: 80px;">السعر</th>\n              <th style="width: 100px;">المجموع</th>\n              <th style="width: 100px;">سبب الإرجاع</th>\n              <th style="width: 60px;">EXP</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat((null==r?void 0:r.map((n,t)=>{var e,a;return"\n              <tr>\n                <td>".concat(t+1,'</td>\n                <td class="item-name">').concat(n.medicine_name||(null==(e=n.medicines)?void 0:e.name)||(null==(a=n.medicine)?void 0:a.name)||"غير محدد","</td>\n                <td>").concat(n.quantity,"</td>\n                <td>").concat((n.unit_price||0).toLocaleString(),"</td>\n                <td>").concat((n.total_amount||0).toLocaleString(),'</td>\n                <td style="font-size: 10px;">').concat(n.return_reason||"غير محدد","</td>\n                <td>").concat(n.expiry_date?new Date(n.expiry_date).toLocaleDateString("en-GB").replace(/\//g,"/"):"","</td>\n              </tr>\n            ")}).join(""))||"","\n\n            ").concat(Array.from({length:Math.max(0,8-((null==r?void 0:r.length)||0))},(n,t)=>"\n              <tr>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n              </tr>\n            ").join(""),'\n          </tbody>\n        </table>\n\n        <div class="totals-section">\n          <table>\n            <tr>\n              <td class="total-label">إجمالي المبلغ المرتجع:</td>\n              <td class="total-value">').concat((null==(d=n.total_amount)?void 0:d.toLocaleString())||0,' د.ع</td>\n            </tr>\n          </table>\n        </div>\n\n        <div class="notes-section">\n          <div style="font-weight: bold; margin-bottom: 5px;">ملاحظات:</div>\n          <div>').concat(n.notes||"",'</div>\n        </div>\n\n        <div class="footer">\n          <div style="font-size: 12px;">\n            صفحة 1 من 1\n          </div>\n\n          <div class="signature-section">\n            <div class="signature-box">\n              ختم وتوقيع<br>\n              المسؤول\n            </div>\n          </div>\n\n          <div style="font-size: 12px; text-align: left;">\n            ').concat(t.footerText||"شكراً لتعاملكم معنا","\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  ")}}}]);