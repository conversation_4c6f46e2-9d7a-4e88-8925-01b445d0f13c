(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5812],{4516:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},19420:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30304:(e,t,s)=>{Promise.resolve().then(s.bind(s,69022))},34592:(e,t,s)=>{"use strict";s.d(t,{KS:()=>r,P3:()=>n});let a=async e=>{try{let t="\n      <table>\n        <thead>\n          <tr>\n            ".concat(e.headers.map(e=>"<th>".concat(e,"</th>")).join(""),"\n          </tr>\n        </thead>\n        <tbody>\n          ").concat(e.rows.map(e=>"<tr>".concat(e.map(e=>"<td>".concat(e,"</td>")).join(""),"</tr>")).join(""),"\n        </tbody>\n      </table>\n    "),s=new Blob([t],{type:"application/vnd.ms-excel;charset=utf-8;"}),a=document.createElement("a"),n=URL.createObjectURL(s);return a.setAttribute("href",n),a.setAttribute("download","".concat(e.filename,".xls")),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a),!0}catch(e){return console.error("Error exporting to Excel:",e),!1}},n=e=>a({headers:["رقم الفاتورة","التاريخ","العميل","المجموع الفرعي","الخصم","المجموع النهائي","طريقة الدفع","ملاحظات"],rows:e.map(e=>[e.invoiceNumber||"",e.date||"",e.customerName||"",e.subtotal||0,e.discount||0,e.finalAmount||0,e.paymentMethod||"",e.notes||""]),filename:"مبيعات_".concat(new Date().toISOString().split("T")[0]),sheetName:"المبيعات"}),r=e=>a({headers:["اسم العميل","رقم الهاتف","البريد الإلكتروني","العنوان","ملاحظات","تاريخ الإضافة"],rows:e.map(e=>[e.name||"",e.phone||"",e.email||"",e.address||"",e.notes||"",e.created_at||""]),filename:"عملاء_".concat(new Date().toISOString().split("T")[0]),sheetName:"العملاء"})},57434:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69022:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(95155),n=s(12115),r=s(61932),l=s(91788),d=s(84616),i=s(47924),c=s(71007),o=s(19420),m=s(28883),x=s(4516),h=s(57434),p=s(13717),g=s(62525),u=s(54416),y=s(81304),b=s(55868),j=s(69074),f=s(34592);function N(){let[e,t]=(0,n.useState)([{id:"1",name:"أحمد محمد علي",phone:"07901111111",email:"<EMAIL>",address:"بغداد - الكرادة",notes:"عميل مميز",created_at:"2024-01-15"},{id:"2",name:"فاطمة حسن محمد",phone:"07802222222",address:"بغداد - الجادرية",created_at:"2024-02-10"},{id:"3",name:"محمد علي حسن",phone:"07703333333",email:"<EMAIL>",address:"بغداد - الأعظمية",created_at:"2024-03-05"},{id:"4",name:"سارة أحمد محمود",phone:"07604444444",address:"بغداد - الكاظمية",notes:"يفضل التواصل مساءً",created_at:"2024-03-20"},{id:"5",name:"علي محمود حسن",phone:"07505555555",address:"بغداد - المنصور",created_at:"2024-04-01"}]),[s,N]=(0,n.useState)(""),[v,w]=(0,n.useState)(!1),[k,A]=(0,n.useState)(null),[_,S]=(0,n.useState)(!1),[C,L]=(0,n.useState)(null),[M,D]=(0,n.useState)([]),[E,q]=(0,n.useState)({name:"",phone:"",email:"",address:"",notes:""}),z=e.filter(e=>{var t,a;return e.name.toLowerCase().includes(s.toLowerCase())||(null==(t=e.phone)?void 0:t.includes(s))||(null==(a=e.email)?void 0:a.toLowerCase().includes(s.toLowerCase()))}),O=()=>{w(!1),A(null),q({name:"",phone:"",email:"",address:"",notes:""})},H=e=>M.filter(t=>t.customer_name===e&&"pending"===t.payment_status).reduce((e,t)=>e+t.final_amount,0),I=e=>M.filter(t=>t.customer_name===e&&"paid"===t.payment_status).reduce((e,t)=>e+t.final_amount,0),G=async()=>{await (0,f.KS)(e)?alert("تم تصدير بيانات العملاء بنجاح!"):alert("حدث خطأ أثناء التصدير")};return(0,a.jsx)(r.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة العملاء"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة بيانات العملاء ومعلومات الاتصال"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("button",{onClick:G,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"تصدير للإكسل"]}),(0,a.jsxs)("button",{onClick:()=>{A(null),q({name:"",phone:"",email:"",address:"",notes:""}),w(!0)},className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),"إضافة عميل جديد"]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",placeholder:"البحث عن عميل...",value:s,onChange:e=>N(e.target.value),className:"w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["قائمة العملاء (",z.length,")"]})}),0===z.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد عملاء"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لم يتم العثور على عملاء تطابق معايير البحث"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:z.map(s=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,a.jsx)(c.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:s.name}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 mt-2 text-sm text-gray-600",children:[s.phone&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:s.phone})]}),s.email&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:s.email})]}),s.address&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:s.address})]})]}),s.notes&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1 text-sm text-gray-500",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:s.notes})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 mt-2",children:["تاريخ الإضافة: ",s.created_at]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>{L(s),(e=>{try{let t=JSON.parse(localStorage.getItem("sales_invoices")||"[]").filter(t=>t.customer_name===e);D(t)}catch(e){console.error("Error loading customer invoices:",e),D([])}})(s.name),S(!0)},className:"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg",title:"كشف الحساب",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{A(s),q({name:s.name,phone:s.phone||"",email:s.email||"",address:s.address||"",notes:s.notes||""}),w(!0)},className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg",title:"تعديل",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{var a;return a=s.id,void(confirm("هل أنت متأكد من حذف هذا العميل؟")&&(t(e.filter(e=>e.id!==a)),alert("تم حذف العميل بنجاح!")))},className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg",title:"حذف",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})]})},s.id))})]}),v&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:k?"تعديل العميل":"إضافة عميل جديد"}),(0,a.jsx)("button",{onClick:O,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(u.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),!E.name.trim())return void alert("يرجى إدخال اسم العميل");k?(t(e.map(e=>e.id===k.id?{...e,...E}:e)),alert("تم تحديث بيانات العميل بنجاح!")):(t([...e,{id:Date.now().toString(),...E,created_at:new Date().toISOString().split("T")[0]}]),alert("تم إضافة العميل بنجاح!")),w(!1),A(null),q({name:"",phone:"",email:"",address:"",notes:""})},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم العميل *"}),(0,a.jsx)("input",{type:"text",value:E.name,onChange:e=>q({...E,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"اسم العميل",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"رقم الهاتف"}),(0,a.jsx)("input",{type:"tel",value:E.phone,onChange:e=>q({...E,phone:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"07901234567"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البريد الإلكتروني"}),(0,a.jsx)("input",{type:"email",value:E.email,onChange:e=>q({...E,email:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"العنوان"}),(0,a.jsx)("input",{type:"text",value:E.address,onChange:e=>q({...E,address:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"العنوان"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات"}),(0,a.jsx)("textarea",{value:E.notes,onChange:e=>q({...E,notes:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ملاحظات إضافية",rows:3})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,a.jsx)("button",{type:"button",onClick:O,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"إلغاء"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:k?"تحديث":"إضافة"})]})]})]})}),_&&C&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["كشف حساب العميل - ",C.name]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("button",{onClick:()=>{if(!C)return;let e='\n      <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">\n        <div style="text-align: center; margin-bottom: 30px;">\n          <h1>صيدلية الشفاء</h1>\n          <h2>كشف حساب العميل</h2>\n        </div>\n\n        <div style="margin-bottom: 20px;">\n          <h3>معلومات العميل:</h3>\n          <p><strong>الاسم:</strong> '.concat(C.name,"</p>\n          <p><strong>الهاتف:</strong> ").concat(C.phone||"غير محدد","</p>\n          <p><strong>العنوان:</strong> ").concat(C.address||"غير محدد","</p>\n          <p><strong>تاريخ الطباعة:</strong> ").concat(new Date().toLocaleDateString("ar-EG"),'</p>\n        </div>\n\n        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">\n          <thead>\n            <tr style="background-color: #f5f5f5;">\n              <th style="border: 1px solid #ddd; padding: 8px;">رقم الفاتورة</th>\n              <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>\n              <th style="border: 1px solid #ddd; padding: 8px;">المبلغ</th>\n              <th style="border: 1px solid #ddd; padding: 8px;">حالة الدفع</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat(M.map(e=>'\n              <tr>\n                <td style="border: 1px solid #ddd; padding: 8px;">'.concat(e.invoice_number,'</td>\n                <td style="border: 1px solid #ddd; padding: 8px;">').concat(new Date(e.created_at).toLocaleDateString("ar-EG"),'</td>\n                <td style="border: 1px solid #ddd; padding: 8px;">').concat(e.final_amount.toLocaleString(),' د.ع</td>\n                <td style="border: 1px solid #ddd; padding: 8px;">').concat("paid"===e.payment_status?"مدفوع":"معلق","</td>\n              </tr>\n            ")).join(""),'\n          </tbody>\n        </table>\n\n        <div style="margin-top: 20px;">\n          <p><strong>إجمالي المبالغ المدفوعة:</strong> ').concat(I(C.name).toLocaleString()," د.ع</p>\n          <p><strong>إجمالي المبالغ المعلقة:</strong> ").concat(H(C.name).toLocaleString()," د.ع</p>\n          <p><strong>إجمالي المعاملات:</strong> ").concat((I(C.name)+H(C.name)).toLocaleString()," د.ع</p>\n        </div>\n      </div>\n    "),t=window.open("","_blank");t&&(t.document.write(e),t.document.close(),t.print())},className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),"طباعة"]}),(0,a.jsx)("button",{onClick:()=>{S(!1),L(null),D([])},className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(u.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"معلومات العميل"}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"الاسم:"})," ",C.name]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"الهاتف:"})," ",C.phone||"غير محدد"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"العنوان:"})," ",C.address||"غير محدد"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"ملخص الحساب"}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"إجمالي المبالغ المدفوعة:"})," ",(0,a.jsxs)("span",{className:"text-green-600",children:[I(C.name).toLocaleString()," د.ع"]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"إجمالي المبالغ المعلقة:"})," ",(0,a.jsxs)("span",{className:"text-red-600",children:[H(C.name).toLocaleString()," د.ع"]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"إجمالي المعاملات:"})," ",(0,a.jsxs)("span",{className:"text-blue-600",children:[(I(C.name)+H(C.name)).toLocaleString()," د.ع"]})]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["فواتير العميل (",M.length,")"]})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:0===M.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"لا توجد فواتير"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"لم يتم العثور على فواتير لهذا العميل."})]}):(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم الفاتورة"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ الإجمالي"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الخصم"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ النهائي"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"طريقة الدفع"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"حالة الدفع"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:M.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.invoice_number}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("ar-EG")}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.total_amount.toLocaleString()," د.ع"]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.discount_amount.toLocaleString()," د.ع"]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:[e.final_amount.toLocaleString()," د.ع"]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center",children:["cash"===e.payment_method?(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-500 mr-1"}):(0,a.jsx)(j.A,{className:"h-4 w-4 text-blue-500 mr-1"}),"cash"===e.payment_method?"نقداً":"آجل"]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("paid"===e.payment_status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"paid"===e.payment_status?"مدفوع":"معلق"})})]},e.id))})]})})]})]})})]})})}},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},91788:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}},e=>{e.O(0,[6874,6543,8080,1932,8441,5964,7358],()=>e(e.s=30304)),_N_E=e.O()}]);