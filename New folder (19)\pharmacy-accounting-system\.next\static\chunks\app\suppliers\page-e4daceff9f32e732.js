(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6772],{4516:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},19420:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},33048:(e,t,s)=>{Promise.resolve().then(s.bind(s,74702))},48136:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},57434:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},74702:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(95155),n=s(12115),r=s(61932),l=s(84616),d=s(47924),i=s(48136),c=s(55670),o=s(19420),m=s(28883),x=s(4516),h=s(57434),p=s(13717),g=s(62525),u=s(54416),y=s(81304),b=s(55868),j=s(69074);function f(){let[e,t]=(0,n.useState)([{id:"1",name:"شركة الأدوية العراقية",contact_person:"أحمد محمد",phone:"07901234567",email:"<EMAIL>",address:"بغداد - الكرادة",notes:"مورد رئيسي للمسكنات",created_at:"2024-01-10"},{id:"2",name:"شركة بغداد للأدوية",contact_person:"فاطمة علي",phone:"07801234567",email:"<EMAIL>",address:"بغداد - الجادرية",notes:"متخصص في المضادات الحيوية",created_at:"2024-01-15"},{id:"3",name:"شركة النهرين للأدوية",contact_person:"محمد حسن",phone:"07701234567",email:"<EMAIL>",address:"بغداد - الأعظمية",created_at:"2024-02-01"},{id:"4",name:"شركة دجلة للأدوية",contact_person:"سارة أحمد",phone:"07601234567",email:"<EMAIL>",address:"بغداد - الكاظمية",notes:"فيتامينات ومكملات غذائية",created_at:"2024-02-10"}]),[s,f]=(0,n.useState)(""),[N,v]=(0,n.useState)(!1),[w,k]=(0,n.useState)(null),[_,A]=(0,n.useState)(!1),[S,C]=(0,n.useState)(null),[M,L]=(0,n.useState)([]),[z,D]=(0,n.useState)({name:"",contact_person:"",phone:"",email:"",address:"",notes:""}),q=e.filter(e=>{var t,a,n;return e.name.toLowerCase().includes(s.toLowerCase())||(null==(t=e.contact_person)?void 0:t.toLowerCase().includes(s.toLowerCase()))||(null==(a=e.phone)?void 0:a.includes(s))||(null==(n=e.email)?void 0:n.toLowerCase().includes(s.toLowerCase()))}),E=()=>{v(!1),k(null),D({name:"",contact_person:"",phone:"",email:"",address:"",notes:""})},H=e=>M.filter(t=>t.supplier_name===e&&"pending"===t.payment_status).reduce((e,t)=>e+t.final_amount,0),O=e=>M.filter(t=>t.supplier_name===e&&"paid"===t.payment_status).reduce((e,t)=>e+t.final_amount,0);return(0,a.jsx)(r.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة الموردين"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة بيانات الموردين وشركات الأدوية"})]}),(0,a.jsxs)("button",{onClick:()=>{k(null),D({name:"",contact_person:"",phone:"",email:"",address:"",notes:""}),v(!0)},className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"إضافة مورد جديد"]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",placeholder:"البحث عن مورد...",value:s,onChange:e=>f(e.target.value),className:"w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["قائمة الموردين (",q.length,")"]})}),0===q.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(i.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد موردين"}),(0,a.jsx)("p",{className:"text-gray-500",children:"لم يتم العثور على موردين تطابق معايير البحث"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:q.map(s=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"bg-green-50 p-3 rounded-lg",children:(0,a.jsx)(i.A,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:s.name}),s.contact_person&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1 text-sm text-gray-600",children:[(0,a.jsx)(c.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:["جهة الاتصال: ",s.contact_person]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 mt-2 text-sm text-gray-600",children:[s.phone&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:s.phone})]}),s.email&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:s.email})]}),s.address&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:s.address})]})]}),s.notes&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1 text-sm text-gray-500",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:s.notes})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 mt-2",children:["تاريخ الإضافة: ",s.created_at]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>{C(s),(e=>{try{let t=JSON.parse(localStorage.getItem("purchase_invoices")||"[]").filter(t=>t.supplier_name===e);L(t)}catch(e){console.error("Error loading supplier invoices:",e),L([])}})(s.name),A(!0)},className:"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg",title:"كشف الحساب",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{k(s),D({name:s.name,contact_person:s.contact_person||"",phone:s.phone||"",email:s.email||"",address:s.address||"",notes:s.notes||""}),v(!0)},className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg",title:"تعديل",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{var a;return a=s.id,void(confirm("هل أنت متأكد من حذف هذا المورد؟")&&(t(e.filter(e=>e.id!==a)),alert("تم حذف المورد بنجاح!")))},className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg",title:"حذف",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})]})},s.id))})]}),N&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:w?"تعديل المورد":"إضافة مورد جديد"}),(0,a.jsx)("button",{onClick:E,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(u.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),!z.name.trim())return void alert("يرجى إدخال اسم المورد");w?(t(e.map(e=>e.id===w.id?{...e,...z}:e)),alert("تم تحديث بيانات المورد بنجاح!")):(t([...e,{id:Date.now().toString(),...z,created_at:new Date().toISOString().split("T")[0]}]),alert("تم إضافة المورد بنجاح!")),v(!1),k(null),D({name:"",contact_person:"",phone:"",email:"",address:"",notes:""})},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم المورد *"}),(0,a.jsx)("input",{type:"text",value:z.name,onChange:e=>D({...z,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"اسم الشركة أو المورد",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"جهة الاتصال"}),(0,a.jsx)("input",{type:"text",value:z.contact_person,onChange:e=>D({...z,contact_person:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"اسم الشخص المسؤول"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"رقم الهاتف"}),(0,a.jsx)("input",{type:"tel",value:z.phone,onChange:e=>D({...z,phone:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"07901234567"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البريد الإلكتروني"}),(0,a.jsx)("input",{type:"email",value:z.email,onChange:e=>D({...z,email:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"العنوان"}),(0,a.jsx)("input",{type:"text",value:z.address,onChange:e=>D({...z,address:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"عنوان الشركة"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ملاحظات"}),(0,a.jsx)("textarea",{value:z.notes,onChange:e=>D({...z,notes:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"ملاحظات حول المورد أو التخصص",rows:3})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,a.jsx)("button",{type:"button",onClick:E,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"إلغاء"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:w?"تحديث":"إضافة"})]})]})]})}),_&&S&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["كشف حساب المورد - ",S.name]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("button",{onClick:()=>{if(!S)return;let e='\n      <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">\n        <div style="text-align: center; margin-bottom: 30px;">\n          <h1>صيدلية الشفاء</h1>\n          <h2>كشف حساب المورد</h2>\n        </div>\n\n        <div style="margin-bottom: 20px;">\n          <h3>معلومات المورد:</h3>\n          <p><strong>اسم الشركة:</strong> '.concat(S.name,"</p>\n          <p><strong>الشخص المسؤول:</strong> ").concat(S.contact_person||"غير محدد","</p>\n          <p><strong>الهاتف:</strong> ").concat(S.phone||"غير محدد","</p>\n          <p><strong>العنوان:</strong> ").concat(S.address||"غير محدد","</p>\n          <p><strong>تاريخ الطباعة:</strong> ").concat(new Date().toLocaleDateString("ar-EG"),'</p>\n        </div>\n\n        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">\n          <thead>\n            <tr style="background-color: #f5f5f5;">\n              <th style="border: 1px solid #ddd; padding: 8px;">رقم الفاتورة</th>\n              <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>\n              <th style="border: 1px solid #ddd; padding: 8px;">المبلغ</th>\n              <th style="border: 1px solid #ddd; padding: 8px;">حالة الدفع</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat(M.map(e=>'\n              <tr>\n                <td style="border: 1px solid #ddd; padding: 8px;">'.concat(e.invoice_number,'</td>\n                <td style="border: 1px solid #ddd; padding: 8px;">').concat(new Date(e.created_at).toLocaleDateString("ar-EG"),'</td>\n                <td style="border: 1px solid #ddd; padding: 8px;">').concat(e.final_amount.toLocaleString(),' د.ع</td>\n                <td style="border: 1px solid #ddd; padding: 8px;">').concat("paid"===e.payment_status?"مدفوع":"معلق","</td>\n              </tr>\n            ")).join(""),'\n          </tbody>\n        </table>\n\n        <div style="margin-top: 20px;">\n          <p><strong>إجمالي المبالغ المدفوعة:</strong> ').concat(O(S.name).toLocaleString()," د.ع</p>\n          <p><strong>إجمالي المبالغ المعلقة:</strong> ").concat(H(S.name).toLocaleString()," د.ع</p>\n          <p><strong>إجمالي المعاملات:</strong> ").concat((O(S.name)+H(S.name)).toLocaleString()," د.ع</p>\n        </div>\n      </div>\n    "),t=window.open("","_blank");t&&(t.document.write(e),t.document.close(),t.print())},className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),"طباعة"]}),(0,a.jsx)("button",{onClick:()=>{A(!1),C(null),L([])},className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(u.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"معلومات المورد"}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"اسم الشركة:"})," ",S.name]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"الشخص المسؤول:"})," ",S.contact_person||"غير محدد"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"الهاتف:"})," ",S.phone||"غير محدد"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"العنوان:"})," ",S.address||"غير محدد"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"ملخص الحساب"}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"إجمالي المبالغ المدفوعة:"})," ",(0,a.jsxs)("span",{className:"text-green-600",children:[O(S.name).toLocaleString()," د.ع"]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"إجمالي المبالغ المعلقة:"})," ",(0,a.jsxs)("span",{className:"text-red-600",children:[H(S.name).toLocaleString()," د.ع"]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"إجمالي المعاملات:"})," ",(0,a.jsxs)("span",{className:"text-blue-600",children:[(O(S.name)+H(S.name)).toLocaleString()," د.ع"]})]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["فواتير المورد (",M.length,")"]})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:0===M.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"لا توجد فواتير"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"لم يتم العثور على فواتير لهذا المورد."})]}):(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم الفاتورة"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ الإجمالي"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الخصم"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ النهائي"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"طريقة الدفع"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"حالة الدفع"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:M.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.invoice_number}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("ar-EG")}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.total_amount.toLocaleString()," د.ع"]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.discount_amount.toLocaleString()," د.ع"]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:[e.final_amount.toLocaleString()," د.ع"]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center",children:["cash"===e.payment_method?(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-500 mr-1"}):(0,a.jsx)(j.A,{className:"h-4 w-4 text-blue-500 mr-1"}),"cash"===e.payment_method?"نقداً":"آجل"]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("paid"===e.payment_status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"paid"===e.payment_status?"مدفوع":"معلق"})})]},e.id))})]})})]})]})})]})})}},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}},e=>{e.O(0,[6874,6543,8080,1932,8441,5964,7358],()=>e(e.s=33048)),_N_E=e.O()}]);