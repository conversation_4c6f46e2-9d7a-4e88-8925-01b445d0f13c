'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import { 
  Plus, 
  Search, 
  RotateCcw, 
  FileText, 
  Calendar,
  DollarSign,
  User,
  Building,
  Eye,
  Filter,
  ArrowLeft,
  Package,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  ShoppingCart,
  Printer
} from 'lucide-react'
import PrintTemplate, { ReportPrint } from '@/components/PrintTemplate'
import { usePrintSettings, printReport } from '@/hooks/usePrintSettings'
import { getSalesInvoices, getPurchaseInvoices, processReturn, getReturns } from '@/lib/database'

interface ReturnRecord {
  id: string
  type: 'sales' | 'purchase'
  return_number: string
  original_invoice_id: string
  customer_name?: string
  supplier_name?: string
  total_amount: number
  reason: string
  notes?: string
  created_at: string
  status: 'pending' | 'approved' | 'rejected'
  customers?: { name: string; phone?: string }
  suppliers?: { name: string; contact_person?: string }
  sales_return_items?: any[]
  purchase_return_items?: any[]
}

interface InvoiceItem {
  id: string
  medicine_name: string
  batch_code: string
  quantity: number
  unit_price: number
  total_price: number
  medicine_batch_id?: string
  medicine_id?: string
  selected_quantity?: number
}

export default function ReturnsPage() {
  const [returns, setReturns] = useState<ReturnRecord[]>([])
  const [filteredReturns, setFilteredReturns] = useState<ReturnRecord[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'sales' | 'purchase'>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [currentStep, setCurrentStep] = useState<'select-type' | 'select-invoice' | 'select-items' | 'confirm'>('select-type')
  const [returnType, setReturnType] = useState<'sales' | 'purchase'>('sales')
  const [availableInvoices, setAvailableInvoices] = useState<any[]>([])
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)
  const [returnItems, setReturnItems] = useState<InvoiceItem[]>([])
  const [returnReason, setReturnReason] = useState('')
  const [returnNotes, setReturnNotes] = useState('')
  const [loading, setLoading] = useState(false)
  const [showPrintPreview, setShowPrintPreview] = useState(false)
  const { settings: printSettings } = usePrintSettings()

  useEffect(() => {
    loadReturns()
  }, [])

  useEffect(() => {
    console.log('Filtering returns. Total returns:', returns.length)
    filterReturns()
  }, [returns, searchTerm, filterType, filterStatus])

  const loadReturns = async () => {
    console.log('Loading returns...')
    const result = await getReturns()
    console.log('Returns result:', result)
    if (result.success && result.data) {
      console.log('Setting returns data:', result.data.length, 'items')
      setReturns(result.data)
    } else {
      console.log('No returns data or failed to load')
      setReturns([])
    }
  }

  const filterReturns = () => {
    let filtered = returns
    console.log('Starting filter with returns:', returns.length)

    if (searchTerm) {
      filtered = filtered.filter(returnItem =>
        returnItem.return_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        returnItem.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (returnItem.customers?.name || returnItem.suppliers?.name || '').toLowerCase().includes(searchTerm.toLowerCase())
      )
      console.log('After search filter:', filtered.length)
    }

    if (filterType !== 'all') {
      filtered = filtered.filter(returnItem => returnItem.type === filterType)
      console.log('After type filter:', filtered.length)
    }

    if (filterStatus !== 'all') {
      filtered = filtered.filter(returnItem => returnItem.status === filterStatus)
      console.log('After status filter:', filtered.length)
    }

    console.log('Final filtered returns:', filtered.length)
    setFilteredReturns(filtered)
  }

  const startNewReturn = () => {
    setShowAddModal(true)
    setCurrentStep('select-type')
    setReturnType('sales')
    setSelectedInvoice(null)
    setReturnItems([])
    setReturnReason('')
    setReturnNotes('')
  }

  const selectReturnType = async (type: 'sales' | 'purchase') => {
    setReturnType(type)
    setLoading(true)
    
    try {
      const result = type === 'sales' ? await getSalesInvoices() : await getPurchaseInvoices()
      if (result.success && result.data) {
        setAvailableInvoices(result.data)
        setCurrentStep('select-invoice')
      }
    } catch (error) {
      console.error('Error loading invoices:', error)
    } finally {
      setLoading(false)
    }
  }

  const selectInvoice = (invoice: any) => {
    setSelectedInvoice(invoice)
    
    // Prepare items for return selection
    const items = returnType === 'sales' 
      ? invoice.sales_invoice_items || []
      : invoice.purchase_invoice_items || []
    
    const returnableItems: InvoiceItem[] = items.map((item: any, index: number) => ({
      id: item.id || `item_${index}_${Date.now()}`,
      medicine_name: item.medicine_batches?.medicines?.name || item.medicines?.name || 'Unknown',
      batch_code: item.medicine_batches?.batch_code || item.batch_code || '',
      quantity: item.quantity,
      unit_price: item.unit_price || item.unit_cost || 0,
      total_price: item.total_price || item.total_cost || 0,
      medicine_batch_id: item.medicine_batch_id,
      medicine_id: item.medicine_id,
      selected_quantity: 0
    }))
    
    setReturnItems(returnableItems)
    setCurrentStep('select-items')
  }

  const updateReturnQuantity = (itemId: string, quantity: number) => {
    setReturnItems(items =>
      items.map(item =>
        item.id === itemId
          ? { ...item, selected_quantity: Math.min(Math.max(0, quantity), item.quantity) }
          : item
      )
    )
  }

  const calculateReturnTotal = () => {
    return returnItems.reduce((total, item) => 
      total + (item.selected_quantity || 0) * item.unit_price, 0
    )
  }

  const processReturnSubmission = async () => {
    if (!selectedInvoice || !returnReason) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const selectedItems = returnItems.filter(item => (item.selected_quantity || 0) > 0)
    if (selectedItems.length === 0) {
      alert('يرجى اختيار عناصر للإرجاع')
      return
    }

    setLoading(true)
    try {
      const returnNumber = `${returnType === 'sales' ? 'SR' : 'PR'}-${Date.now()}`
      
      const returnData = {
        return_number: returnNumber,
        original_invoice_id: selectedInvoice.id,
        total_amount: calculateReturnTotal(),
        reason: returnReason,
        status: 'pending',
        notes: returnNotes,
        ...(returnType === 'sales'
          ? {
              customer_id: selectedInvoice.customer_id,
              customer_name: selectedInvoice.customers?.name || selectedInvoice.customer_name,
              customers: { name: selectedInvoice.customers?.name || selectedInvoice.customer_name }
            }
          : {
              supplier_id: selectedInvoice.supplier_id,
              supplier_name: selectedInvoice.suppliers?.name || selectedInvoice.supplier_name,
              suppliers: { name: selectedInvoice.suppliers?.name || selectedInvoice.supplier_name }
            }
        )
      }

      const processItems = selectedItems.map(item => ({
        batchId: item.batchId || item.medicine_batch_id,
        medicineId: item.medicineId || item.medicine_id,
        medicine_name: item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || item.medicines?.name || 'غير محدد',
        batch_code: item.batch_code || item.batchCode || item.medicine_batches?.batch_number || '',
        expiry_date: item.expiry_date || item.expiryDate || item.medicine_batches?.expiry_date || '',
        quantity: item.selected_quantity || 0,
        unitPrice: item.unit_price || item.unitPrice || item.unit_cost || item.unitCost || 0,
        totalPrice: (item.selected_quantity || 0) * (item.unit_price || item.unitPrice || item.unit_cost || item.unitCost || 0),
        unit_price: item.unit_price || item.unitPrice || item.unit_cost || item.unitCost || 0,
        total_price: (item.selected_quantity || 0) * (item.unit_price || item.unitPrice || item.unit_cost || item.unitCost || 0),
        unit_cost: item.unit_cost || item.unitCost || item.unit_price || item.unitPrice || 0,
        total_cost: (item.selected_quantity || 0) * (item.unit_cost || item.unitCost || item.unit_price || item.unitPrice || 0),
        return_reason: returnReason
      }))

      console.log('Processing return:', { returnType, returnData, processItems })
      const result = await processReturn(returnType, returnData, processItems)
      console.log('Process return result:', result)

      if (result.success) {
        alert('تم إنشاء المرتجع بنجاح!')

        // Auto print return receipt
        const returnReceipt = {
          ...returnData,
          return_number: returnNumber,
          type: returnType,
          return_type: returnType,
          return_invoice_items: processItems,
          items: processItems,
          created_at: new Date().toISOString()
        }

        setTimeout(() => {
          printReport([returnReceipt], 'returns', 'إيصال مرتجع', printSettings)
        }, 500)

        setShowAddModal(false)
        console.log('Reloading returns after successful creation...')
        await loadReturns()
      } else {
        console.error('Failed to create return:', result)
        alert('حدث خطأ أثناء إنشاء المرتجع')
      }
    } catch (error) {
      console.error('Error processing return:', error)
      alert('حدث خطأ أثناء إنشاء المرتجع')
    } finally {
      setLoading(false)
    }
  }

  const handlePrintReturns = () => {
    if (filteredReturns.length > 0) {
      setShowPrintPreview(true)
    } else {
      alert('لا توجد مرتجعات للطباعة')
    }
  }

  const handleDirectPrint = () => {
    if (filteredReturns.length > 0) {
      printReport(filteredReturns, 'returns', 'تقرير المرتجعات', printSettings)
    } else {
      alert('لا توجد مرتجعات للطباعة')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'قيد المراجعة'
      case 'approved':
        return 'مقبول'
      case 'rejected':
        return 'مرفوض'
      default:
        return 'غير محدد'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleApproveReturn = async (returnId: string) => {
    if (!confirm('هل أنت متأكد من موافقة هذا المرتجع؟')) return

    try {
      // Update return status to approved
      const { updateReturnStatus } = await import('@/lib/database')
      const result = await updateReturnStatus(returnId, 'approved')

      if (result.success) {
        // Reload returns to show updated status
        await loadReturns()
        alert('تم قبول المرتجع بنجاح!')
      } else {
        alert('حدث خطأ أثناء قبول المرتجع')
      }
    } catch (error) {
      console.error('Error approving return:', error)
      alert('حدث خطأ أثناء قبول المرتجع')
    }
  }

  const handleRejectReturn = async (returnId: string) => {
    const reason = prompt('يرجى إدخال سبب رفض المرتجع:')
    if (!reason) return

    try {
      // Update return status to rejected
      const { updateReturnStatus } = await import('@/lib/database')
      const result = await updateReturnStatus(returnId, 'rejected', reason)

      if (result.success) {
        // Reload returns to show updated status
        await loadReturns()
        alert('تم رفض المرتجع!')
      } else {
        alert('حدث خطأ أثناء رفض المرتجع')
      }
    } catch (error) {
      console.error('Error rejecting return:', error)
      alert('حدث خطأ أثناء رفض المرتجع')
    }
  }

  const handlePrintReturn = async (returnItem: ReturnRecord) => {
    try {
      // Get full return details for printing
      const { getReturnForPrint } = await import('@/lib/database')
      const result = await getReturnForPrint(returnItem.id)

      if (result.success && result.data) {
        // Print the return using the same template as invoices
        const { printInvoice } = await import('@/hooks/usePrintSettings')
        await printInvoice(result.data, 'return', printSettings)
      } else {
        // Fallback to basic return data
        const returnData = {
          ...returnItem,
          return_invoice_items: returnItem.sales_return_items || returnItem.purchase_return_items || []
        }
        const { printInvoice } = await import('@/hooks/usePrintSettings')
        await printInvoice(returnData, 'return', printSettings)
      }
    } catch (error) {
      console.error('Error printing return:', error)
      alert('حدث خطأ أثناء طباعة المرتجع')
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المرتجعات</h1>
            <p className="text-gray-600 mt-1">إدارة مرتجعات المبيعات والمشتريات مع تأثير مباشر على الحسابات</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handlePrintReturns}
              disabled={filteredReturns.length === 0}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              معاينة وطباعة
            </button>
            <button
              onClick={handleDirectPrint}
              disabled={filteredReturns.length === 0}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              طباعة مباشرة
            </button>
            <button
              onClick={startNewReturn}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إنشاء مرتجع جديد
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">البحث</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="البحث في المرتجعات..."
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">نوع المرتجع</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأنواع</option>
                <option value="sales">مرتجعات المبيعات</option>
                <option value="purchase">مرتجعات المشتريات</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الحالات</option>
                <option value="pending">قيد المراجعة</option>
                <option value="approved">مقبول</option>
                <option value="rejected">مرفوض</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => {
                  setSearchTerm('')
                  setFilterType('all')
                  setFilterStatus('all')
                }}
                className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2"
              >
                <Filter className="h-4 w-4" />
                إعادة تعيين
              </button>
            </div>
          </div>
        </div>

        {/* Returns List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {filteredReturns.length === 0 ? (
            <div className="text-center py-12">
              <RotateCcw className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مرتجعات</h3>
              <p className="text-gray-500 mb-4">لم يتم إنشاء أي مرتجعات بعد</p>
              <button
                onClick={startNewReturn}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                إنشاء أول مرتجع
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رقم المرتجع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      النوع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      العميل/المورد
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المبلغ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      السبب
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      التاريخ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      إجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredReturns.map((returnItem, index) => (
                    <tr key={returnItem.id || `return_${index}_${Date.now()}`} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-sm font-medium text-gray-900">
                            {returnItem.return_number}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          returnItem.type === 'sales'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-purple-100 text-purple-800'
                        }`}>
                          {returnItem.type === 'sales' ? (
                            <>
                              <ShoppingCart className="h-3 w-3 mr-1" />
                              مرتجع مبيعات
                            </>
                          ) : (
                            <>
                              <Package className="h-3 w-3 mr-1" />
                              مرتجع مشتريات
                            </>
                          )}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {returnItem.type === 'sales' ? (
                            <User className="h-4 w-4 text-gray-400 mr-2" />
                          ) : (
                            <Building className="h-4 w-4 text-gray-400 mr-2" />
                          )}
                          <span className="text-sm text-gray-900">
                            {returnItem.customers?.name || returnItem.customer_name ||
                             returnItem.suppliers?.name || returnItem.supplier_name || 'غير محدد'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm font-medium text-gray-900">
                            {returnItem.total_amount.toLocaleString()} د.ع
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="text-sm text-gray-900 line-clamp-2">
                          {returnItem.reason}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(returnItem.status)}`}>
                          {getStatusIcon(returnItem.status)}
                          <span className="mr-1">{getStatusText(returnItem.status)}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-900">
                            {new Date(returnItem.created_at).toLocaleDateString('ar-EG')}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {/* Print Button */}
                          <button
                            onClick={() => handlePrintReturn(returnItem)}
                            className="text-gray-600 hover:text-gray-800 p-1 rounded-md hover:bg-gray-100"
                            title="طباعة المرتجع"
                          >
                            <Printer className="h-4 w-4" />
                          </button>

                          {/* Action buttons based on status */}
                          {returnItem.status === 'pending' && (
                            <>
                              {/* Approve Button */}
                              <button
                                onClick={() => handleApproveReturn(returnItem.id)}
                                className="text-green-600 hover:text-green-800 p-1 rounded-md hover:bg-green-50"
                                title="قبول المرتجع"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </button>

                              {/* Reject Button */}
                              <button
                                onClick={() => handleRejectReturn(returnItem.id)}
                                className="text-red-600 hover:text-red-800 p-1 rounded-md hover:bg-red-50"
                                title="رفض المرتجع"
                              >
                                <XCircle className="h-4 w-4" />
                              </button>
                            </>
                          )}

                          {/* View Details Button */}
                          <button
                            className="text-blue-600 hover:text-blue-800 p-1 rounded-md hover:bg-blue-50"
                            title="عرض التفاصيل"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Add Return Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-4xl max-h-screen overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-lg font-semibold text-gray-900">إنشاء مرتجع جديد</h2>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircle className="h-5 w-5" />
                </button>
              </div>

              <div className="p-6">
                {/* Step 1: Select Return Type */}
                {currentStep === 'select-type' && (
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-gray-900 mb-6">اختر نوع المرتجع</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <button
                        onClick={() => selectReturnType('sales')}
                        className="p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors"
                      >
                        <ShoppingCart className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                        <h4 className="text-lg font-medium text-gray-900 mb-2">مرتجع مبيعات</h4>
                        <p className="text-gray-600">إرجاع أدوية تم بيعها للعملاء</p>
                      </button>

                      <button
                        onClick={() => selectReturnType('purchase')}
                        className="p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors"
                      >
                        <Package className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                        <h4 className="text-lg font-medium text-gray-900 mb-2">مرتجع مشتريات</h4>
                        <p className="text-gray-600">إرجاع أدوية تم شراؤها من الموردين</p>
                      </button>
                    </div>
                  </div>
                )}

                {/* Step 2: Select Invoice */}
                {currentStep === 'select-invoice' && (
                  <div>
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-medium text-gray-900">
                        اختر الفاتورة الأصلية - {returnType === 'sales' ? 'فواتير المبيعات' : 'فواتير المشتريات'}
                      </h3>
                      <button
                        onClick={() => setCurrentStep('select-type')}
                        className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        رجوع
                      </button>
                    </div>

                    {loading ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="text-gray-500 mt-2">جاري تحميل الفواتير...</p>
                      </div>
                    ) : (
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {availableInvoices.map((invoice, index) => (
                          <button
                            key={invoice.id || `invoice_${index}_${Date.now()}`}
                            onClick={() => selectInvoice(invoice)}
                            className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">{invoice.invoice_number}</p>
                                <p className="text-sm text-gray-500">
                                  {returnType === 'sales'
                                    ? invoice.customers?.name || invoice.customer_name
                                    : invoice.suppliers?.name
                                  } • {invoice.final_amount.toLocaleString()} د.ع
                                </p>
                              </div>
                              <div className="text-sm text-gray-500">
                                {new Date(invoice.created_at).toLocaleDateString('ar-EG')}
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Step 3: Select Items */}
                {currentStep === 'select-items' && (
                  <div>
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-medium text-gray-900">اختر العناصر المراد إرجاعها</h3>
                      <button
                        onClick={() => setCurrentStep('select-invoice')}
                        className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        رجوع
                      </button>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <p className="text-blue-800">
                        <strong>الفاتورة:</strong> {selectedInvoice?.invoice_number} •
                        <strong> العميل/المورد:</strong> {
                          returnType === 'sales'
                            ? selectedInvoice?.customers?.name || selectedInvoice?.customer_name
                            : selectedInvoice?.suppliers?.name
                        }
                      </p>
                    </div>

                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {returnItems.map((item, index) => (
                        <div key={item.id || `return_item_${index}_${Date.now()}`} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900">{item.medicine_name}</h4>
                              <p className="text-sm text-gray-500">
                                وجبة: {item.batch_code} • سعر الوحدة: {item.unit_price.toLocaleString()} د.ع
                              </p>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="text-sm text-gray-500">
                                الكمية الأصلية: {item.quantity}
                              </div>
                              <div className="flex items-center gap-2">
                                <label className="text-sm text-gray-700">كمية الإرجاع:</label>
                                <input
                                  type="number"
                                  min="0"
                                  max={item.quantity}
                                  value={item.selected_quantity || 0}
                                  onChange={(e) => updateReturnQuantity(item.id, Number(e.target.value))}
                                  className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                      <div className="flex justify-between text-lg font-semibold">
                        <span>إجمالي مبلغ الإرجاع:</span>
                        <span className="text-blue-600">{calculateReturnTotal().toLocaleString()} د.ع</span>
                      </div>
                    </div>

                    <div className="mt-6 space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">سبب الإرجاع *</label>
                        <select
                          value={returnReason}
                          onChange={(e) => setReturnReason(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">اختر السبب</option>
                          <option value="دواء منتهي الصلاحية">دواء منتهي الصلاحية</option>
                          <option value="عيب في التصنيع">عيب في التصنيع</option>
                          <option value="عدم الحاجة للدواء">عدم الحاجة للدواء</option>
                          <option value="خطأ في الطلب">خطأ في الطلب</option>
                          <option value="تلف في التغليف">تلف في التغليف</option>
                          <option value="أخرى">أخرى</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">ملاحظات إضافية</label>
                        <textarea
                          value={returnNotes}
                          onChange={(e) => setReturnNotes(e.target.value)}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="أي ملاحظات إضافية حول المرتجع..."
                        />
                      </div>
                    </div>

                    <div className="flex justify-end gap-3 mt-6">
                      <button
                        onClick={() => setShowAddModal(false)}
                        className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                      >
                        إلغاء
                      </button>
                      <button
                        onClick={processReturnSubmission}
                        disabled={loading || calculateReturnTotal() === 0 || !returnReason}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                      >
                        {loading ? 'جاري الإنشاء...' : 'إنشاء المرتجع'}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Print Preview Modal */}
        {showPrintPreview && filteredReturns.length > 0 && (
          <PrintTemplate
            title="تقرير المرتجعات"
            data={filteredReturns}
            type="report"
            settings={printSettings}
            onClose={() => setShowPrintPreview(false)}
          >
            <ReportPrint
              reportData={filteredReturns}
              reportType="returns"
              title="تقرير المرتجعات"
              settings={printSettings}
            />
          </PrintTemplate>
        )}
      </div>
    </AppLayout>
  )
}
