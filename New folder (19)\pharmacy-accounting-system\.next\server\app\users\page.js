(()=>{var a={};a.id=9,a.ids=[9],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14103:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\users\\page.tsx","default")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>j});var d=c(60687);c(43210);var e=c(16189),f=c(63213),g=c(41862),h=c(43649),i=c(99891);function j({children:a,requiredPermissions:b=[],requiredRole:c,fallback:j}){let{isAuthenticated:l,isLoading:m,user:n}=(0,f.As)(),{hasPermission:o,hasAnyPermission:p}=(0,f.Sk)(),q=(0,e.useRouter)();return m?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(g.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"جاري التحقق من الصلاحيات..."})]})}):l?c&&n?.role!==c?j||(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,d.jsx)(h.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"غير مصرح لك"}),(0,d.jsxs)("p",{className:"text-gray-600 mb-6",children:["هذه الصفحة مخصصة للمستخدمين من نوع: ",c]}),(0,d.jsx)("button",{onClick:()=>q.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة"})]})}):b.length>0&&!p(b)?j||(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,d.jsx)(i.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"صلاحيات غير كافية"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة"}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,d.jsx)("p",{className:"text-sm text-gray-700 font-medium mb-2",children:"الصلاحيات المطلوبة:"}),(0,d.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:b.map(a=>(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"w-2 h-2 bg-red-400 rounded-full"}),k(a)]},a))})]}),(0,d.jsx)("button",{onClick:()=>q.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"العودة"})]})}):(0,d.jsx)(d.Fragment,{children:a}):null}let k=a=>({sales_view:"عرض المبيعات",sales_create:"إنشاء مبيعات",sales_edit:"تعديل المبيعات",sales_delete:"حذف المبيعات",sales_print:"طباعة المبيعات",sales_view_prices:"عرض الأسعار",purchases_view:"عرض المشتريات",purchases_create:"إنشاء مشتريات",purchases_edit:"تعديل المشتريات",purchases_delete:"حذف المشتريات",purchases_print:"طباعة المشتريات",inventory_view:"عرض المخزون",inventory_create:"إضافة للمخزون",inventory_edit:"تعديل المخزون",inventory_delete:"حذف من المخزون",inventory_print:"طباعة المخزون",customers_view:"عرض العملاء",customers_create:"إضافة عملاء",customers_edit:"تعديل العملاء",customers_delete:"حذف العملاء",suppliers_view:"عرض الموردين",suppliers_create:"إضافة موردين",suppliers_edit:"تعديل الموردين",suppliers_delete:"حذف الموردين",reports_view:"عرض التقارير",reports_financial:"التقارير المالية",reports_detailed:"التقارير المفصلة",reports_export:"تصدير التقارير",users_view:"عرض المستخدمين",users_create:"إضافة مستخدمين",users_edit:"تعديل المستخدمين",users_delete:"حذف المستخدمين",settings_view:"عرض الإعدادات",settings_edit:"تعديل الإعدادات",cashbox_view:"عرض الصندوق",cashbox_manage:"إدارة الصندوق",returns_view:"عرض المرتجعات",returns_create:"إنشاء مرتجعات",returns_edit:"تعديل المرتجعات",returns_delete:"حذف المرتجعات"})[a]||a},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29923:(a,b,c)=>{Promise.resolve().then(c.bind(c,55352))},33873:a=>{"use strict";a.exports=require("path")},34483:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,14103)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\users\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\users\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/users/page",pathname:"/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/users/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53899:(a,b,c)=>{Promise.resolve().then(c.bind(c,14103))},55352:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u});var d=c(60687),e=c(43210),f=c(21979),g=c(20769),h=c(63213),i=c(96474),j=c(99270),k=c(41312),l=c(99891),m=c(93508);let n=(0,c(62688).A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var o=c(40228),p=c(63143),q=c(12597),r=c(13861),s=c(88233),t=c(11860);function u(){let[a,b]=(0,e.useState)([]),[c,t]=(0,e.useState)(!0),[u,x]=(0,e.useState)(""),[y,z]=(0,e.useState)("all"),[A,B]=(0,e.useState)("all"),[C,D]=(0,e.useState)(!1),[E,F]=(0,e.useState)(null),[G,H]=(0,e.useState)(!1),{user:I}=(0,h.As)(),{canCreateUsers:J,canEditUsers:K,canDeleteUsers:L}=(0,h.Sk)(),{logUserActivity:M}=(0,h.Zy)(),N=a.filter(a=>{let b=a.full_name.toLowerCase().includes(u.toLowerCase())||a.username.toLowerCase().includes(u.toLowerCase())||a.email.toLowerCase().includes(u.toLowerCase()),c="all"===y||a.role===y,d="all"===A||"active"===A&&a.is_active||"inactive"===A&&!a.is_active;return b&&c&&d}),O=async(c,d)=>{try{b(a.map(a=>a.id===c?{...a,is_active:!d}:a));let e=a.find(a=>a.id===c);e&&await M(d?"DEACTIVATE_USER":"ACTIVATE_USER",`${d?"تعطيل":"تفعيل"} المستخدم: ${e.username}`,{table_name:"users",record_id:c,old_values:{is_active:d},new_values:{is_active:!d}})}catch(a){console.error("Error toggling user status:",a)}},P=async c=>{try{let d=btoa(c.password),e={id:Date.now().toString(),username:c.username,full_name:c.full_name,email:c.email,phone:c.phone,role:c.role,password_hash:d,is_active:!0,created_at:new Date().toISOString(),last_login:null},f=[...a,e];b(f),localStorage.setItem("users",JSON.stringify(f)),await M("CREATE_USER",`تم إنشاء مستخدم جديد: ${e.full_name}`,{table_name:"users",record_id:e.id,new_values:{username:e.username,role:e.role}}),alert("تم إضافة المستخدم بنجاح!")}catch(a){console.error("Error adding user:",a),alert("حدث خطأ أثناء إضافة المستخدم")}},Q=async c=>{try{let d=a.find(a=>a.id===c.id),e=a.map(a=>a.id===c.id?{...a,...c}:a);b(e),localStorage.setItem("users",JSON.stringify(e)),await M("UPDATE_USER",`تم تحديث بيانات المستخدم: ${c.full_name}`,{table_name:"users",record_id:c.id,old_values:{full_name:d?.full_name,email:d?.email,role:d?.role},new_values:{full_name:c.full_name,email:c.email,role:c.role}}),alert("تم تحديث بيانات المستخدم بنجاح!")}catch(a){console.error("Error updating user:",a),alert("حدث خطأ أثناء تحديث المستخدم")}},R=async c=>{if(confirm("هل أنت متأكد من حذف هذا المستخدم؟"))try{let d=a.find(a=>a.id===c),e=a.filter(a=>a.id!==c);b(e),localStorage.setItem("users",JSON.stringify(e)),await M("DELETE_USER",`تم حذف المستخدم: ${d?.full_name}`,{table_name:"users",record_id:c,old_values:{username:d?.username,role:d?.role}}),alert("تم حذف المستخدم بنجاح!")}catch(a){console.error("Error deleting user:",a),alert("حدث خطأ أثناء حذف المستخدم")}};return(0,d.jsx)(g.Ay,{requiredPermissions:["users_view"],children:(0,d.jsxs)(f.A,{children:[(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-darker",children:"إدارة المستخدمين"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة حسابات المستخدمين وصلاحياتهم"})]}),J&&(0,d.jsxs)("button",{onClick:()=>D(!0),className:"flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors w-full md:w-auto min-h-[44px]",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"font-semibold",children:"إضافة مستخدم جديد"})]})]}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(j.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",placeholder:"البحث في المستخدمين...",value:u,onChange:a=>x(a.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-dark font-medium min-h-[44px]"})]}),(0,d.jsx)("div",{children:(0,d.jsxs)("select",{value:y,onChange:a=>z(a.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-dark font-medium min-h-[44px]",children:[(0,d.jsx)("option",{value:"all",children:"جميع الأدوار"}),(0,d.jsx)("option",{value:"admin",children:"مدير النظام"}),(0,d.jsx)("option",{value:"manager",children:"مدير"}),(0,d.jsx)("option",{value:"pharmacist",children:"صيدلي"}),(0,d.jsx)("option",{value:"cashier",children:"كاشير"}),(0,d.jsx)("option",{value:"viewer",children:"مشاهد"})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("select",{value:A,onChange:a=>B(a.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-dark font-medium min-h-[44px]",children:[(0,d.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,d.jsx)("option",{value:"active",children:"نشط"}),(0,d.jsx)("option",{value:"inactive",children:"غير نشط"})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:(0,d.jsx)("div",{className:"overflow-x-auto table-responsive",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 text-sm md:text-base",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المستخدم"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الدور"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الحالة"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"آخر دخول"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"تاريخ الإنشاء"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c?(0,d.jsx)("tr",{children:(0,d.jsx)("td",{colSpan:6,className:"px-6 py-12 text-center",children:(0,d.jsxs)("div",{className:"flex items-center justify-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,d.jsx)("span",{className:"mr-3 text-gray-600",children:"جاري التحميل..."})]})})}):0===N.length?(0,d.jsx)("tr",{children:(0,d.jsxs)("td",{colSpan:6,className:"px-6 py-12 text-center",children:[(0,d.jsx)(k.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-500",children:"لا توجد مستخدمين"})]})}):N.map(a=>{var b;return(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,d.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,d.jsx)(k.A,{className:"h-5 w-5 text-blue-600"})})}),(0,d.jsxs)("div",{className:"mr-4",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.full_name}),(0,d.jsxs)("div",{className:"text-sm text-gray-500",children:["@",a.username," • ",a.email]})]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{admin:"bg-red-100 text-red-800",manager:"bg-purple-100 text-purple-800",pharmacist:"bg-blue-100 text-blue-800",cashier:"bg-green-100 text-green-800",viewer:"bg-gray-100 text-gray-800"}[a.role]||"bg-gray-100 text-gray-800"}`,children:[(0,d.jsx)(l.A,{className:"h-3 w-3 ml-1"}),{admin:"مدير النظام",manager:"مدير",pharmacist:"صيدلي",cashier:"كاشير",viewer:"مشاهد"}[b=a.role]||b]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${a.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:a.is_active?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(m.A,{className:"h-3 w-3 ml-1"}),"نشط"]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(n,{className:"h-3 w-3 ml-1"}),"غير نشط"]})})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.last_login?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 ml-1"}),new Date(a.last_login).toLocaleDateString("ar-EG")]}):"لم يسجل دخول"}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.created_at).toLocaleDateString("ar-EG")}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[K&&(0,d.jsx)("button",{onClick:()=>{F(a),H(!0)},className:"text-blue-600 hover:text-blue-900",title:"تعديل",children:(0,d.jsx)(p.A,{className:"h-4 w-4"})}),K&&a.id!==I?.id&&(0,d.jsx)("button",{onClick:()=>O(a.id,a.is_active),className:`${a.is_active?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"}`,title:a.is_active?"تعطيل":"تفعيل",children:a.is_active?(0,d.jsx)(q.A,{className:"h-4 w-4"}):(0,d.jsx)(r.A,{className:"h-4 w-4"})}),L&&a.id!==I?.id&&(0,d.jsx)("button",{onClick:()=>R(a.id),className:"text-red-600 hover:text-red-900",title:"حذف",children:(0,d.jsx)(s.A,{className:"h-4 w-4"})})]})})]},a.id)})})]})})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(k.A,{className:"h-8 w-8 text-blue-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"إجمالي المستخدمين"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.length})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(m.A,{className:"h-8 w-8 text-green-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"المستخدمين النشطين"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.filter(a=>a.is_active).length})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(n,{className:"h-8 w-8 text-red-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"المستخدمين المعطلين"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.filter(a=>!a.is_active).length})]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(l.A,{className:"h-8 w-8 text-purple-600"})}),(0,d.jsx)("div",{className:"mr-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"المديرين"}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.filter(a=>"admin"===a.role||"manager"===a.role).length})]})})]})})]})]}),C&&(0,d.jsx)(v,{onClose:()=>D(!1),onSave:P}),G&&E&&(0,d.jsx)(w,{user:E,onClose:()=>{H(!1),F(null)},onSave:Q})]})})}function v({onClose:a,onSave:b}){let[c,f]=(0,e.useState)({username:"",full_name:"",email:"",phone:"",role:"viewer",password:"",confirmPassword:""}),[g,h]=(0,e.useState)(!1),[i,j]=(0,e.useState)({}),k=async d=>{if(d.preventDefault(),(()=>{let a={};return c.username.trim()||(a.username="اسم المستخدم مطلوب"),c.full_name.trim()||(a.full_name="الاسم الكامل مطلوب"),c.email.trim()||(a.email="البريد الإلكتروني مطلوب"),c.password||(a.password="كلمة المرور مطلوبة"),c.password!==c.confirmPassword&&(a.confirmPassword="كلمات المرور غير متطابقة"),c.password&&c.password.length<6&&(a.password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"),j(a),0===Object.keys(a).length})()){h(!0);try{await b(c),a()}catch(a){console.error("Error adding user:",a)}finally{h(!1)}}};return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 md:p-4",children:(0,d.jsx)("div",{className:"bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto mobile-modal",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"إضافة مستخدم جديد"}),(0,d.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(t.A,{className:"h-6 w-6"})})]}),(0,d.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"اسم المستخدم *"}),(0,d.jsx)("input",{type:"text",value:c.username,onChange:a=>f({...c,username:a.target.value}),className:`w-full px-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-dark font-medium min-h-[44px] ${i.username?"border-red-500":"border-gray-300"}`,placeholder:"أدخل اسم المستخدم"}),i.username&&(0,d.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.username})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الاسم الكامل *"}),(0,d.jsx)("input",{type:"text",value:c.full_name,onChange:a=>f({...c,full_name:a.target.value}),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${i.full_name?"border-red-500":"border-gray-300"}`,placeholder:"أدخل الاسم الكامل"}),i.full_name&&(0,d.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.full_name})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البريد الإلكتروني *"}),(0,d.jsx)("input",{type:"email",value:c.email,onChange:a=>f({...c,email:a.target.value}),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${i.email?"border-red-500":"border-gray-300"}`,placeholder:"أدخل البريد الإلكتروني"}),i.email&&(0,d.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"رقم الهاتف"}),(0,d.jsx)("input",{type:"tel",value:c.phone,onChange:a=>f({...c,phone:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"أدخل رقم الهاتف"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الدور *"}),(0,d.jsxs)("select",{value:c.role,onChange:a=>f({...c,role:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"viewer",children:"مشاهد"}),(0,d.jsx)("option",{value:"cashier",children:"كاشير"}),(0,d.jsx)("option",{value:"pharmacist",children:"صيدلي"}),(0,d.jsx)("option",{value:"manager",children:"مدير"}),(0,d.jsx)("option",{value:"admin",children:"مدير النظام"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"كلمة المرور *"}),(0,d.jsx)("input",{type:"password",value:c.password,onChange:a=>f({...c,password:a.target.value}),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${i.password?"border-red-500":"border-gray-300"}`,placeholder:"أدخل كلمة المرور"}),i.password&&(0,d.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.password})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"تأكيد كلمة المرور *"}),(0,d.jsx)("input",{type:"password",value:c.confirmPassword,onChange:a=>f({...c,confirmPassword:a.target.value}),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${i.confirmPassword?"border-red-500":"border-gray-300"}`,placeholder:"أعد إدخال كلمة المرور"}),i.confirmPassword&&(0,d.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.confirmPassword})]}),(0,d.jsxs)("div",{className:"flex gap-3 pt-4 button-group-mobile",children:[(0,d.jsx)("button",{type:"button",onClick:a,className:"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors min-h-[44px]",children:"إلغاء"}),(0,d.jsx)("button",{type:"submit",disabled:g,className:"flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 min-h-[44px]",children:g?"جاري الحفظ...":"حفظ"})]})]})]})})})}function w({user:a,onClose:b,onSave:c}){let[f,g]=(0,e.useState)({full_name:a.full_name,email:a.email,phone:a.phone||"",role:a.role,is_active:a.is_active}),[h,i]=(0,e.useState)(!1),j=async d=>{d.preventDefault(),i(!0);try{await c({...a,...f}),b()}catch(a){console.error("Error updating user:",a)}finally{i(!1)}};return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsx)("div",{className:"bg-white rounded-xl shadow-2xl max-w-md w-full",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"تعديل المستخدم"}),(0,d.jsx)("button",{onClick:b,className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(t.A,{className:"h-6 w-6"})})]}),(0,d.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الاسم الكامل"}),(0,d.jsx)("input",{type:"text",value:f.full_name,onChange:a=>g({...f,full_name:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"البريد الإلكتروني"}),(0,d.jsx)("input",{type:"email",value:f.email,onChange:a=>g({...f,email:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"رقم الهاتف"}),(0,d.jsx)("input",{type:"tel",value:f.phone,onChange:a=>g({...f,phone:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الدور"}),(0,d.jsxs)("select",{value:f.role,onChange:a=>g({...f,role:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"viewer",children:"مشاهد"}),(0,d.jsx)("option",{value:"cashier",children:"كاشير"}),(0,d.jsx)("option",{value:"pharmacist",children:"صيدلي"}),(0,d.jsx)("option",{value:"manager",children:"مدير"}),(0,d.jsx)("option",{value:"admin",children:"مدير النظام"})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:"is_active",checked:f.is_active,onChange:a=>g({...f,is_active:a.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"is_active",className:"mr-2 block text-sm text-gray-900",children:"حساب نشط"})]}),(0,d.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,d.jsx)("button",{type:"button",onClick:b,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"إلغاء"}),(0,d.jsx)("button",{type:"submit",disabled:h,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:h?"جاري الحفظ...":"حفظ التغييرات"})]})]})]})})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,314,979],()=>b(b.s=34483));module.exports=c})();