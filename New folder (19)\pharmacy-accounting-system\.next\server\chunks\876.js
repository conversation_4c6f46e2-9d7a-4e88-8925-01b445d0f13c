exports.id=876,exports.ids=[876],exports.modules={11:(a,b,c)=>{"use strict";var d=c(15390),e=c(50969),f=c(68298),g=c(88875),h=c(91231),i=c(16424);d({target:"Promise",stat:!0,forced:c(99501)},{all:function(a){var b=this,c=g.f(b),d=c.resolve,j=c.reject,k=h(function(){var c=f(b.resolve),g=[],h=0,k=1;i(a,function(a){var f=h++,i=!1;k++,e(c,b,a).then(function(a){!i&&(i=!0,g[f]=a,--k||d(g))},j)}),--k||d(g)});return k.error&&j(k.value),c.promise}})},343:(a,b,c)=>{"use strict";var d=c(6532).navigator,e=d&&d.userAgent;a.exports=e?String(e):""},700:(a,b,c)=>{var d=c(21154).default,e=c(31062);a.exports=function(a){var b=e(a,"string");return"symbol"==d(b)?b:b+""},a.exports.__esModule=!0,a.exports.default=a.exports},1504:(a,b,c)=>{"use strict";var d=c(80387)("iterator"),e=!1;try{var f=0,g={next:function(){return{done:!!f++}},return:function(){e=!0}};g[d]=function(){return this},Array.from(g,function(){throw 2})}catch(a){}a.exports=function(a,b){try{if(!b&&!e)return!1}catch(a){return!1}var c=!1;try{var f={};f[d]=function(){return{next:function(){return{done:c=!0}}}},a(f)}catch(a){}return c}},2129:(a,b,c)=>{"use strict";var d,e,f,g=c(38246),h=c(6532),i=c(76514),j=c(94323),k=c(77525),l=c(72649),m=c(46815),n=c(91001),o="Object already initialized",p=h.TypeError,q=h.WeakMap;if(g||l.state){var r=l.state||(l.state=new q);r.get=r.get,r.has=r.has,r.set=r.set,d=function(a,b){if(r.has(a))throw new p(o);return b.facade=a,r.set(a,b),b},e=function(a){return r.get(a)||{}},f=function(a){return r.has(a)}}else{var s=m("state");n[s]=!0,d=function(a,b){if(k(a,s))throw new p(o);return b.facade=a,j(a,s,b),b},e=function(a){return k(a,s)?a[s]:{}},f=function(a){return k(a,s)}}a.exports={set:d,get:e,has:f,enforce:function(a){return f(a)?e(a):d(a,{})},getterFor:function(a){return function(b){var c;if(!i(b)||(c=e(b)).type!==a)throw new p("Incompatible receiver, "+a+" required");return c}}}},2870:(a,b,c)=>{"use strict";var d=c(5112),e=c(97039);a.exports=d&&e(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},3638:(a,b,c)=>{"use strict";var d=c(68298),e=c(60561);a.exports=function(a,b){var c=a[b];return e(c)?void 0:d(c)}},4074:(a,b,c)=>{"use strict";var d=c(50969),e=c(8991),f=c(17425),g=c(67420),h=c(85587),i=TypeError;a.exports=function(a,b){var c=a.exec;if(f(c)){var j=d(c,a,b);return null!==j&&e(j),j}if("RegExp"===g(a))return d(h,a,b);throw new i("RegExp#exec called on incompatible receiver")}},5112:(a,b,c)=>{"use strict";a.exports=!c(97039)(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},5502:(a,b,c)=>{"use strict";var d=c(97039),e=c(6532).RegExp;a.exports=d(function(){var a=e("(?<a>b)","g");return"b"!==a.exec("b").groups.a||"bc"!=="b".replace(a,"$<a>c")})},6532:function(a){"use strict";var b=function(a){return a&&a.Math===Math&&a};a.exports=b("object"==typeof globalThis&&globalThis)||b("object"==typeof window&&window)||b("object"==typeof self&&self)||b("object"==typeof global&&global)||b("object"==typeof this&&this)||function(){return this}()||Function("return this")()},7462:(a,b,c)=>{"use strict";var d=c(58627),e=Math.min;a.exports=function(a){var b=d(a);return b>0?e(b,0x1fffffffffffff):0}},7588:(a,b,c)=>{"use strict";var d=c(79892),e=Function.prototype,f=e.call,g=d&&e.bind.bind(f,f);a.exports=d?g:function(a){return function(){return f.apply(a,arguments)}}},7615:(a,b,c)=>{"use strict";var d=c(10397).f,e=c(77525),f=c(80387)("toStringTag");a.exports=function(a,b,c){a&&!c&&(a=a.prototype),a&&!e(a,f)&&d(a,f,{configurable:!0,value:b})}},8991:(a,b,c)=>{"use strict";var d=c(76514),e=String,f=TypeError;a.exports=function(a){if(d(a))return a;throw new f(e(a)+" is not an object")}},9108:(a,b,c)=>{"use strict";var d=c(15390),e=c(69416),f=c(86883).f,g=c(7462),h=c(83407),i=c(18023),j=c(10054),k=c(48072),l=c(60827),m=e("".slice),n=Math.min,o=k("startsWith");d({target:"String",proto:!0,forced:!(!l&&!o&&function(){var a=f(String.prototype,"startsWith");return a&&!a.writable}())&&!o},{startsWith:function(a){var b=h(j(this));i(a);var c=g(n(arguments.length>1?arguments[1]:void 0,b.length)),d=h(a);return m(b,c,c+d.length)===d}})},9145:a=>{"use strict";a.exports=function(a,b){try{1==arguments.length?console.error(a):console.error(a,b)}catch(a){}}},9285:(a,b,c)=>{"use strict";var d=c(80387),e=c(97601),f=d("iterator"),g=Array.prototype;a.exports=function(a){return void 0!==a&&(e.Array===a||g[f]===a)}},10054:(a,b,c)=>{"use strict";var d=c(60561),e=TypeError;a.exports=function(a){if(d(a))throw new e("Can't call method on "+a);return a}},10187:(a,b,c)=>{"use strict";var d=c(51067),e=c(3638),f=c(60561),g=c(97601),h=c(80387)("iterator");a.exports=function(a){if(!f(a))return e(a,h)||e(a,"@@iterator")||g[d(a)]}},10397:(a,b,c)=>{"use strict";var d=c(5112),e=c(68689),f=c(2870),g=c(8991),h=c(10421),i=TypeError,j=Object.defineProperty,k=Object.getOwnPropertyDescriptor,l="enumerable",m="configurable",n="writable";b.f=d?f?function(a,b,c){if(g(a),b=h(b),g(c),"function"==typeof a&&"prototype"===b&&"value"in c&&n in c&&!c[n]){var d=k(a,b);d&&d[n]&&(a[b]=c.value,c={configurable:m in c?c[m]:d[m],enumerable:l in c?c[l]:d[l],writable:!1})}return j(a,b,c)}:j:function(a,b,c){if(g(a),b=h(b),g(c),e)try{return j(a,b,c)}catch(a){}if("get"in c||"set"in c)throw new i("Accessors not supported");return"value"in c&&(a[b]=c.value),a}},10421:(a,b,c)=>{"use strict";var d=c(15013),e=c(89513);a.exports=function(a){var b=d(a,"string");return e(b)?b:b+""}},12005:(a,b,c)=>{"use strict";var d,e,f,g,h=c(6532),i=c(93229),j=c(65436),k=c(17425),l=c(77525),m=c(97039),n=c(45121),o=c(31132),p=c(23359),q=c(59592),r=c(97412),s=c(68773),t=h.setImmediate,u=h.clearImmediate,v=h.process,w=h.Dispatch,x=h.Function,y=h.MessageChannel,z=h.String,A=0,B={},C="onreadystatechange";m(function(){d=h.location});var D=function(a){if(l(B,a)){var b=B[a];delete B[a],b()}},E=function(a){return function(){D(a)}},F=function(a){D(a.data)},G=function(a){h.postMessage(z(a),d.protocol+"//"+d.host)};t&&u||(t=function(a){q(arguments.length,1);var b=k(a)?a:x(a),c=o(arguments,1);return B[++A]=function(){i(b,void 0,c)},e(A),A},u=function(a){delete B[a]},s?e=function(a){v.nextTick(E(a))}:w&&w.now?e=function(a){w.now(E(a))}:y&&!r?(g=(f=new y).port2,f.port1.onmessage=F,e=j(g.postMessage,g)):h.addEventListener&&k(h.postMessage)&&!h.importScripts&&d&&"file:"!==d.protocol&&!m(G)?(e=G,h.addEventListener("message",F,!1)):e=C in p("script")?function(a){n.appendChild(p("script"))[C]=function(){n.removeChild(this),D(a)}}:function(a){setTimeout(E(a),0)}),a.exports={set:t,clear:u}},12549:(a,b,c)=>{"use strict";var d,e,f,g=c(97039),h=c(17425),i=c(76514),j=c(65444),k=c(28907),l=c(59908),m=c(80387),n=c(60827),o=m("iterator"),p=!1;[].keys&&("next"in(f=[].keys())?(e=k(k(f)))!==Object.prototype&&(d=e):p=!0),!i(d)||g(function(){var a={};return d[o].call(a)!==a})?d={}:n&&(d=j(d)),h(d[o])||l(d,o,function(){return this}),a.exports={IteratorPrototype:d,BUGGY_SAFARI_ITERATORS:p}},13044:(a,b,c)=>{"use strict";var d=c(15390),e=c(42727),f=c(60827),g=c(99662),h=c(73040).CONSTRUCTOR,i=c(43086),j=e("Promise"),k=f&&!h;d({target:"Promise",stat:!0,forced:f||h},{resolve:function(a){return i(k&&this===j?g:this,a)}})},13209:(a,b,c)=>{"use strict";var d=c(76514);a.exports=function(a){return d(a)||null===a}},13314:(a,b,c)=>{"use strict";var d=c(74934).PROPER,e=c(97039),f=c(15480),g="​\x85᠎";a.exports=function(a){return e(function(){return!!f[a]()||g[a]()!==g||d&&f[a].name!==a})}},14704:(a,b,c)=>{"use strict";var d,e,f,g,h=c(15390),i=c(60827),j=c(68773),k=c(6532),l=c(81439),m=c(50969),n=c(59908),o=c(36751),p=c(7615),q=c(44757),r=c(68298),s=c(17425),t=c(76514),u=c(33127),v=c(84545),w=c(12005).set,x=c(82675),y=c(9145),z=c(91231),A=c(39173),B=c(2129),C=c(99662),D=c(73040),E=c(88875),F="Promise",G=D.CONSTRUCTOR,H=D.REJECTION_EVENT,I=D.SUBCLASSING,J=B.getterFor(F),K=B.set,L=C&&C.prototype,M=C,N=L,O=k.TypeError,P=k.document,Q=k.process,R=E.f,S=R,T=!!(P&&P.createEvent&&k.dispatchEvent),U="unhandledrejection",V=function(a){var b;return!!(t(a)&&s(b=a.then))&&b},W=function(a,b){var c,d,e,f=b.value,g=1===b.state,h=g?a.ok:a.fail,i=a.resolve,j=a.reject,k=a.domain;try{h?(g||(2===b.rejection&&_(b),b.rejection=1),!0===h?c=f:(k&&k.enter(),c=h(f),k&&(k.exit(),e=!0)),c===a.promise?j(new O("Promise-chain cycle")):(d=V(c))?m(d,c,i,j):i(c)):j(f)}catch(a){k&&!e&&k.exit(),j(a)}},X=function(a,b){a.notified||(a.notified=!0,x(function(){for(var c,d=a.reactions;c=d.get();)W(c,a);a.notified=!1,b&&!a.rejection&&Z(a)}))},Y=function(a,b,c){var d,e;T?((d=P.createEvent("Event")).promise=b,d.reason=c,d.initEvent(a,!1,!0),k.dispatchEvent(d)):d={promise:b,reason:c},!H&&(e=k["on"+a])?e(d):a===U&&y("Unhandled promise rejection",c)},Z=function(a){m(w,k,function(){var b,c=a.facade,d=a.value;if($(a)&&(b=z(function(){j?Q.emit("unhandledRejection",d,c):Y(U,c,d)}),a.rejection=j||$(a)?2:1,b.error))throw b.value})},$=function(a){return 1!==a.rejection&&!a.parent},_=function(a){m(w,k,function(){var b=a.facade;j?Q.emit("rejectionHandled",b):Y("rejectionhandled",b,a.value)})},aa=function(a,b,c){return function(d){a(b,d,c)}},ab=function(a,b,c){a.done||(a.done=!0,c&&(a=c),a.value=b,a.state=2,X(a,!0))},ac=function(a,b,c){if(!a.done){a.done=!0,c&&(a=c);try{if(a.facade===b)throw new O("Promise can't be resolved itself");var d=V(b);d?x(function(){var c={done:!1};try{m(d,b,aa(ac,c,a),aa(ab,c,a))}catch(b){ab(c,b,a)}}):(a.value=b,a.state=1,X(a,!1))}catch(b){ab({done:!1},b,a)}}};if(G&&(N=(M=function(a){u(this,N),r(a),m(d,this);var b=J(this);try{a(aa(ac,b),aa(ab,b))}catch(a){ab(b,a)}}).prototype,(d=function(a){K(this,{type:F,done:!1,notified:!1,parent:!1,reactions:new A,rejection:!1,state:0,value:null})}).prototype=n(N,"then",function(a,b){var c=J(this),d=R(v(this,M));return c.parent=!0,d.ok=!s(a)||a,d.fail=s(b)&&b,d.domain=j?Q.domain:void 0,0===c.state?c.reactions.add(d):x(function(){W(d,c)}),d.promise}),e=function(){var a=new d,b=J(a);this.promise=a,this.resolve=aa(ac,b),this.reject=aa(ab,b)},E.f=R=function(a){return a===M||a===f?new e(a):S(a)},!i&&s(C)&&L!==Object.prototype)){g=L.then,I||n(L,"then",function(a,b){var c=this;return new M(function(a,b){m(g,c,a,b)}).then(a,b)},{unsafe:!0});try{delete L.constructor}catch(a){}o&&o(L,N)}h({global:!0,constructor:!0,wrap:!0,forced:G},{Promise:M}),f=l.Promise,p(M,F,!1,!0),q(F)},14971:(a,b,c)=>{"use strict";a.exports=!c(97039)(function(){function a(){}return a.prototype.constructor=null,Object.getPrototypeOf(new a)!==a.prototype})},15013:(a,b,c)=>{"use strict";var d=c(50969),e=c(76514),f=c(89513),g=c(3638),h=c(79582),i=c(80387),j=TypeError,k=i("toPrimitive");a.exports=function(a,b){if(!e(a)||f(a))return a;var c,i=g(a,k);if(i){if(void 0===b&&(b="default"),!e(c=d(i,a,b))||f(c))return c;throw new j("Can't convert object to primitive value")}return void 0===b&&(b="number"),h(a,b)}},15193:(a,b,c)=>{"use strict";var d=c(97039),e=c(6532).RegExp,f=d(function(){var a=e("a","y");return a.lastIndex=2,null!==a.exec("abcd")}),g=f||d(function(){return!e("a","y").sticky});a.exports={BROKEN_CARET:f||d(function(){var a=e("^r","gy");return a.lastIndex=2,null!==a.exec("str")}),MISSED_STICKY:g,UNSUPPORTED_Y:f}},15199:(a,b,c)=>{"use strict";var d=c(15390),e=c(50969),f=c(68298),g=c(88875),h=c(91231),i=c(16424);d({target:"Promise",stat:!0,forced:c(99501)},{race:function(a){var b=this,c=g.f(b),d=c.reject,j=h(function(){var g=f(b.resolve);i(a,function(a){e(g,b,a).then(c.resolve,d)})});return j.error&&d(j.value),c.promise}})},15390:(a,b,c)=>{"use strict";var d=c(6532),e=c(86883).f,f=c(94323),g=c(59908),h=c(68077),i=c(59936),j=c(67008);a.exports=function(a,b){var c,k,l,m,n,o=a.target,p=a.global,q=a.stat;if(c=p?d:q?d[o]||h(o,{}):d[o]&&d[o].prototype)for(k in b){if(m=b[k],l=a.dontCallGetSet?(n=e(c,k))&&n.value:c[k],!j(p?k:o+(q?".":"#")+k,a.forced)&&void 0!==l){if(typeof m==typeof l)continue;i(m,l)}(a.sham||l&&l.sham)&&f(m,"sham",!0),g(c,k,m,a)}}},15480:a=>{"use strict";a.exports="	\n\v\f\r \xa0              　\u2028\u2029\uFEFF"},16424:(a,b,c)=>{"use strict";var d=c(65436),e=c(50969),f=c(8991),g=c(79815),h=c(9285),i=c(67414),j=c(43141),k=c(76901),l=c(10187),m=c(79219),n=TypeError,o=function(a,b){this.stopped=a,this.result=b},p=o.prototype;a.exports=function(a,b,c){var q,r,s,t,u,v,w,x=c&&c.that,y=!!(c&&c.AS_ENTRIES),z=!!(c&&c.IS_RECORD),A=!!(c&&c.IS_ITERATOR),B=!!(c&&c.INTERRUPTED),C=d(b,x),D=function(a){return q&&m(q,"normal"),new o(!0,a)},E=function(a){return y?(f(a),B?C(a[0],a[1],D):C(a[0],a[1])):B?C(a,D):C(a)};if(z)q=a.iterator;else if(A)q=a;else{if(!(r=l(a)))throw new n(g(a)+" is not iterable");if(h(r)){for(s=0,t=i(a);t>s;s++)if((u=E(a[s]))&&j(p,u))return u;return new o(!1)}q=k(a,r)}for(v=z?a.next:q.next;!(w=e(v,q)).done;){try{u=E(w.value)}catch(a){m(q,"throw",a)}if("object"==typeof u&&u&&j(p,u))return u}return new o(!1)}},17049:(a,b,c)=>{var d=c(700);a.exports=function(a,b,c){return(b=d(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a},a.exports.__esModule=!0,a.exports.default=a.exports},17425:a=>{"use strict";var b="object"==typeof document&&document.all;a.exports=void 0===b&&void 0!==b?function(a){return"function"==typeof a||a===b}:function(a){return"function"==typeof a}},18023:(a,b,c)=>{"use strict";var d=c(71472),e=TypeError;a.exports=function(a){if(d(a))throw new e("The method doesn't accept regular expressions");return a}},21651:(a,b,c)=>{"use strict";var d=c(97039),e=c(6532).RegExp;a.exports=d(function(){var a=e(".","s");return!(a.dotAll&&a.test("\n")&&"s"===a.flags)})},23315:(a,b,c)=>{"use strict";var d=c(15390),e=c(60827),f=c(73040).CONSTRUCTOR,g=c(99662),h=c(42727),i=c(17425),j=c(59908),k=g&&g.prototype;if(d({target:"Promise",proto:!0,forced:f,real:!0},{catch:function(a){return this.then(void 0,a)}}),!e&&i(g)){var l=h("Promise").prototype.catch;k.catch!==l&&j(k,"catch",l,{unsafe:!0})}},23359:(a,b,c)=>{"use strict";var d=c(6532),e=c(76514),f=d.document,g=e(f)&&e(f.createElement);a.exports=function(a){return g?f.createElement(a):{}}},23864:a=>{"use strict";a.exports=function(a,b){return{enumerable:!(1&a),configurable:!(2&a),writable:!(4&a),value:b}}},24991:(a,b,c)=>{for(var d=c(64798),e="undefined"==typeof window?global:window,f=["moz","webkit"],g="AnimationFrame",h=e["request"+g],i=e["cancel"+g]||e["cancelRequest"+g],j=0;!h&&j<f.length;j++)h=e[f[j]+"Request"+g],i=e[f[j]+"Cancel"+g]||e[f[j]+"CancelRequest"+g];if(!h||!i){var k=0,l=0,m=[],n=1e3/60;h=function(a){if(0===m.length){var b=d(),c=Math.max(0,n-(b-k));k=c+b,setTimeout(function(){var a=m.slice(0);m.length=0;for(var b=0;b<a.length;b++)if(!a[b].cancelled)try{a[b].callback(k)}catch(a){setTimeout(function(){throw a},0)}},Math.round(c))}return m.push({handle:++l,callback:a,cancelled:!1}),l},i=function(a){for(var b=0;b<m.length;b++)m[b].handle===a&&(m[b].cancelled=!0)}}a.exports=function(a){return h.call(e,a)},a.exports.cancel=function(){i.apply(e,arguments)},a.exports.polyfill=function(a){a||(a=e),a.requestAnimationFrame=h,a.cancelAnimationFrame=i}},25225:(a,b,c)=>{"use strict";var d=c(74934).PROPER,e=c(59908),f=c(8991),g=c(83407),h=c(97039),i=c(26602),j="toString",k=RegExp.prototype,l=k[j],m=h(function(){return"/a/b"!==l.call({source:"a",flags:"b"})}),n=d&&l.name!==j;(m||n)&&e(k,j,function(){var a=f(this);return"/"+g(a.source)+"/"+g(i(a))},{unsafe:!0})},26348:(a,b,c)=>{"use strict";var d=c(98064),e=c(67519);a.exports=Object.keys||function(a){return d(a,e)}},26602:(a,b,c)=>{"use strict";var d=c(50969),e=c(77525),f=c(43141),g=c(35121),h=c(53907),i=RegExp.prototype;a.exports=g.correct?function(a){return a.flags}:function(a){return!g.correct&&f(i,a)&&!e(a,"flags")?d(h,a):a.flags}},27199:(a,b,c)=>{"use strict";var d=c(7588),e=c(58627),f=c(83407),g=c(10054),h=d("".charAt),i=d("".charCodeAt),j=d("".slice),k=function(a){return function(b,c){var d,k,l=f(g(b)),m=e(c),n=l.length;return m<0||m>=n?a?"":void 0:(d=i(l,m))<55296||d>56319||m+1===n||(k=i(l,m+1))<56320||k>57343?a?h(l,m):d:a?j(l,m,m+2):(d-55296<<10)+(k-56320)+65536}};a.exports={codeAt:k(!1),charAt:k(!0)}},28090:(a,b,c)=>{"use strict";var d=c(15390),e=c(52554).trim;d({target:"String",proto:!0,forced:c(13314)("trim")},{trim:function(){return e(this)}})},28907:(a,b,c)=>{"use strict";var d=c(77525),e=c(17425),f=c(40377),g=c(46815),h=c(14971),i=g("IE_PROTO"),j=Object,k=j.prototype;a.exports=h?j.getPrototypeOf:function(a){var b=f(a);if(d(b,i))return b[i];var c=b.constructor;return e(c)&&b instanceof c?c.prototype:b instanceof j?k:null}},30052:(a,b,c)=>{"use strict";var d=c(45465),e=c(39529),f=c(97601),g=c(2129),h=c(10397).f,i=c(96516),j=c(43429),k=c(60827),l=c(5112),m="Array Iterator",n=g.set,o=g.getterFor(m);a.exports=i(Array,"Array",function(a,b){n(this,{type:m,target:d(a),index:0,kind:b})},function(){var a=o(this),b=a.target,c=a.index++;if(!b||c>=b.length)return a.target=null,j(void 0,!0);switch(a.kind){case"keys":return j(c,!1);case"values":return j(b[c],!1)}return j([c,b[c]],!1)},"values");var p=f.Arguments=f.Array;if(e("keys"),e("values"),e("entries"),!k&&l&&"values"!==p.name)try{h(p,"name",{value:"values"})}catch(a){}},30246:(a,b,c)=>{"use strict";var d=c(7588),e=c(40377),f=Math.floor,g=d("".charAt),h=d("".replace),i=d("".slice),j=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,k=/\$([$&'`]|\d{1,2})/g;a.exports=function(a,b,c,d,l,m){var n=c+a.length,o=d.length,p=k;return void 0!==l&&(l=e(l),p=j),h(m,p,function(e,h){var j;switch(g(h,0)){case"$":return"$";case"&":return a;case"`":return i(b,0,c);case"'":return i(b,n);case"<":j=l[i(h,1,-1)];break;default:var k=+h;if(0===k)return e;if(k>o){var m=f(k/10);if(0===m)return e;if(m<=o)return void 0===d[m-1]?g(h,1):d[m-1]+g(h,1);return e}j=d[k-1]}return void 0===j?"":j})}},31062:(a,b,c)=>{var d=c(21154).default;a.exports=function(a,b){if("object"!=d(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var e=c.call(a,b||"default");if("object"!=d(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)},a.exports.__esModule=!0,a.exports.default=a.exports},31132:(a,b,c)=>{"use strict";a.exports=c(7588)([].slice)},31429:(a,b,c)=>{"use strict";var d=c(72649);a.exports=function(a,b){return d[a]||(d[a]=b||{})}},33127:(a,b,c)=>{"use strict";var d=c(43141),e=TypeError;a.exports=function(a,b){if(d(b,a))return a;throw new e("Incorrect invocation")}},33748:(a,b,c)=>{"use strict";var d=c(50969),e=c(7588),f=c(57224),g=c(8991),h=c(76514),i=c(10054),j=c(84545),k=c(97361),l=c(7462),m=c(83407),n=c(3638),o=c(4074),p=c(15193),q=c(97039),r=p.UNSUPPORTED_Y,s=Math.min,t=e([].push),u=e("".slice),v=!q(function(){var a=/(?:)/,b=a.exec;a.exec=function(){return b.apply(this,arguments)};var c="ab".split(a);return 2!==c.length||"a"!==c[0]||"b"!==c[1]}),w="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;f("split",function(a,b,c){var e="0".split(void 0,0).length?function(a,c){return void 0===a&&0===c?[]:d(b,this,a,c)}:b;return[function(b,c){var f=i(this),g=h(b)?n(b,a):void 0;return g?d(g,b,f,c):d(e,m(f),b,c)},function(a,d){var f=g(this),h=m(a);if(!w){var i=c(e,f,h,d,e!==b);if(i.done)return i.value}var n=j(f,RegExp),p=f.unicode,q=(f.ignoreCase?"i":"")+(f.multiline?"m":"")+(f.unicode?"u":"")+(r?"g":"y"),v=new n(r?"^(?:"+f.source+")":f,q),x=void 0===d?0xffffffff:d>>>0;if(0===x)return[];if(0===h.length)return null===o(v,h)?[h]:[];for(var y=0,z=0,A=[];z<h.length;){v.lastIndex=r?0:z;var B,C=o(v,r?u(h,z):h);if(null===C||(B=s(l(v.lastIndex+(r?z:0)),h.length))===y)z=k(h,z,p);else{if(t(A,u(h,y,z)),A.length===x)return A;for(var D=1;D<=C.length-1;D++)if(t(A,C[D]),A.length===x)return A;z=y=B}}return t(A,u(h,y)),A}]},w||!v,r)},33989:(a,b,c)=>{"use strict";var d=c(343);a.exports=/ipad|iphone|ipod/i.test(d)&&"undefined"!=typeof Pebble},34357:(a,b,c)=>{"use strict";var d=c(50969),e=c(7588),f=c(57224),g=c(8991),h=c(76514),i=c(7462),j=c(83407),k=c(10054),l=c(3638),m=c(97361),n=c(26602),o=c(4074),p=e("".indexOf);f("match",function(a,b,c){return[function(b){var c=k(this),e=h(b)?l(b,a):void 0;return e?d(e,b,c):new RegExp(b)[a](j(c))},function(a){var d,e=g(this),f=j(a),h=c(b,e,f);if(h.done)return h.value;var k=j(n(e));if(-1===p(k,"g"))return o(e,f);var l=-1!==p(k,"u");e.lastIndex=0;for(var q=[],r=0;null!==(d=o(e,f));){var s=j(d[0]);q[r]=s,""===s&&(e.lastIndex=m(f,i(e.lastIndex),l)),r++}return 0===r?null:q}]})},35121:(a,b,c)=>{"use strict";var d=c(6532),e=c(97039),f=d.RegExp;a.exports={correct:!e(function(){var a=!0;try{f(".","d")}catch(b){a=!1}var b={},c="",d=a?"dgimsy":"gimsy",e=function(a,d){Object.defineProperty(b,a,{get:function(){return c+=d,!0}})},g={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var h in a&&(g.hasIndices="d"),g)e(h,g[h]);return Object.getOwnPropertyDescriptor(f.prototype,"flags").get.call(b)!==d||c!==d})}},36691:(a,b,c)=>{"use strict";var d=c(7588),e=c(97039),f=c(17425),g=c(77525),h=c(5112),i=c(74934).CONFIGURABLE,j=c(93930),k=c(2129),l=k.enforce,m=k.get,n=String,o=Object.defineProperty,p=d("".slice),q=d("".replace),r=d([].join),s=h&&!e(function(){return 8!==o(function(){},"length",{value:8}).length}),t=String(String).split("String"),u=a.exports=function(a,b,c){"Symbol("===p(n(b),0,7)&&(b="["+q(n(b),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),c&&c.getter&&(b="get "+b),c&&c.setter&&(b="set "+b),(!g(a,"name")||i&&a.name!==b)&&(h?o(a,"name",{value:b,configurable:!0}):a.name=b),s&&c&&g(c,"arity")&&a.length!==c.arity&&o(a,"length",{value:c.arity});try{c&&g(c,"constructor")&&c.constructor?h&&o(a,"prototype",{writable:!1}):a.prototype&&(a.prototype=void 0)}catch(a){}var d=l(a);return g(d,"source")||(d.source=r(t,"string"==typeof b?b:"")),a};Function.prototype.toString=u(function(){return f(this)&&m(this).source||j(this)},"toString")},36751:(a,b,c)=>{"use strict";var d=c(96482),e=c(76514),f=c(10054),g=c(99466);a.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var a,b=!1,c={};try{(a=d(Object.prototype,"__proto__","set"))(c,[]),b=c instanceof Array}catch(a){}return function(c,d){return f(c),g(d),e(c)&&(b?a(c,d):c.__proto__=d),c}}():void 0)},37812:(a,b,c)=>{"use strict";var d=c(23359)("span").classList,e=d&&d.constructor&&d.constructor.prototype;a.exports=e===Object.prototype?void 0:e},38246:(a,b,c)=>{"use strict";var d=c(6532),e=c(17425),f=d.WeakMap;a.exports=e(f)&&/native code/.test(String(f))},39173:a=>{"use strict";var b=function(){this.head=null,this.tail=null};b.prototype={add:function(a){var b={item:a,next:null},c=this.tail;c?c.next=b:this.head=b,this.tail=b},get:function(){var a=this.head;if(a)return null===(this.head=a.next)&&(this.tail=null),a.item}},a.exports=b},39529:(a,b,c)=>{"use strict";var d=c(80387),e=c(65444),f=c(10397).f,g=d("unscopables"),h=Array.prototype;void 0===h[g]&&f(h,g,{configurable:!0,value:e(null)}),a.exports=function(a){h[g][a]=!0}},40377:(a,b,c)=>{"use strict";var d=c(10054),e=Object;a.exports=function(a){return e(d(a))}},42727:(a,b,c)=>{"use strict";var d=c(6532),e=c(17425);a.exports=function(a,b){var c;return arguments.length<2?e(c=d[a])?c:void 0:d[a]&&d[a][b]}},43086:(a,b,c)=>{"use strict";var d=c(8991),e=c(76514),f=c(88875);a.exports=function(a,b){if(d(a),e(b)&&b.constructor===a)return b;var c=f.f(a);return(0,c.resolve)(b),c.promise}},43141:(a,b,c)=>{"use strict";a.exports=c(7588)({}.isPrototypeOf)},43429:a=>{"use strict";a.exports=function(a,b){return{value:a,done:b}}},44615:(a,b,c)=>{"use strict";var d=c(52359),e=c(97039),f=c(6532).String;a.exports=!!Object.getOwnPropertySymbols&&!e(function(){var a=Symbol("symbol detection");return!f(a)||!(Object(a)instanceof Symbol)||!Symbol.sham&&d&&d<41})},44757:(a,b,c)=>{"use strict";var d=c(42727),e=c(51170),f=c(80387),g=c(5112),h=f("species");a.exports=function(a){var b=d(a);g&&b&&!b[h]&&e(b,h,{configurable:!0,get:function(){return this}})}},45121:(a,b,c)=>{"use strict";a.exports=c(42727)("document","documentElement")},45452:(a,b,c)=>{"use strict";var d=c(15390),e=c(51206).left,f=c(57862),g=c(52359);d({target:"Array",proto:!0,forced:!c(68773)&&g>79&&g<83||!f("reduce")},{reduce:function(a){var b=arguments.length;return e(this,a,b,b>1?arguments[1]:void 0)}})},45465:(a,b,c)=>{"use strict";var d=c(76607),e=c(10054);a.exports=function(a){return d(e(a))}},46815:(a,b,c)=>{"use strict";var d=c(31429),e=c(88532),f=d("keys");a.exports=function(a){return f[a]||(f[a]=e(a))}},47908:(a,b,c)=>{"use strict";var d=c(93229),e=c(50969),f=c(7588),g=c(57224),h=c(97039),i=c(8991),j=c(17425),k=c(76514),l=c(58627),m=c(7462),n=c(83407),o=c(10054),p=c(97361),q=c(3638),r=c(30246),s=c(26602),t=c(4074),u=c(80387)("replace"),v=Math.max,w=Math.min,x=f([].concat),y=f([].push),z=f("".indexOf),A=f("".slice),B="$0"==="a".replace(/./,"$0"),C=!!/./[u]&&""===/./[u]("a","$0");g("replace",function(a,b,c){var f=C?"$":"$0";return[function(a,c){var d=o(this),f=k(a)?q(a,u):void 0;return f?e(f,a,d,c):e(b,n(d),a,c)},function(a,e){var g=i(this),h=n(a);if("string"==typeof e&&-1===z(e,f)&&-1===z(e,"$<")){var k=c(b,g,h,e);if(k.done)return k.value}var o=j(e);o||(e=n(e));var q=n(s(g)),u=-1!==z(q,"g");u&&(G=-1!==z(q,"u"),g.lastIndex=0);for(var B=[];null!==(I=t(g,h))&&(y(B,I),u);){;""===n(I[0])&&(g.lastIndex=p(h,m(g.lastIndex),G))}for(var C="",D=0,E=0;E<B.length;E++){for(var F,G,H,I=B[E],J=n(I[0]),K=v(w(l(I.index),h.length),0),L=[],M=1;M<I.length;M++)y(L,void 0===(F=I[M])?F:String(F));var N=I.groups;if(o){var O=x([J],L,K,h);void 0!==N&&y(O,N),H=n(d(e,void 0,O))}else H=r(J,h,K,L,N,e);K>=D&&(C+=A(h,D,K)+H,D=K+J.length)}return C+A(h,D)}]},!!h(function(){var a=/./;return a.exec=function(){var a=[];return a.groups={a:"7"},a},"7"!=="".replace(a,"$<a>")})||!B||C)},48072:(a,b,c)=>{"use strict";var d=c(80387)("match");a.exports=function(a){var b=/./;try{"/./"[a](b)}catch(c){try{return b[d]=!1,"/./"[a](b)}catch(a){}}return!1}},48324:(a,b,c)=>{"use strict";a.exports=c(44615)&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},50065:(a,b,c)=>{"use strict";var d=c(7588),e=c(97039),f=c(17425),g=c(51067),h=c(42727),i=c(93930),j=function(){},k=h("Reflect","construct"),l=/^\s*(?:class|function)\b/,m=d(l.exec),n=!l.test(j),o=function(a){if(!f(a))return!1;try{return k(j,[],a),!0}catch(a){return!1}},p=function(a){if(!f(a))return!1;switch(g(a)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return n||!!m(l,i(a))}catch(a){return!0}};p.sham=!0,a.exports=!k||e(function(){var a;return o(o.call)||!o(Object)||!o(function(){a=!0})||a})?p:o},50969:(a,b,c)=>{"use strict";var d=c(79892),e=Function.prototype.call;a.exports=d?e.bind(e):function(){return e.apply(e,arguments)}},51067:(a,b,c)=>{"use strict";var d=c(55744),e=c(17425),f=c(67420),g=c(80387)("toStringTag"),h=Object,i="Arguments"===f(function(){return arguments}()),j=function(a,b){try{return a[b]}catch(a){}};a.exports=d?f:function(a){var b,c,d;return void 0===a?"Undefined":null===a?"Null":"string"==typeof(c=j(b=h(a),g))?c:i?f(b):"Object"===(d=f(b))&&e(b.callee)?"Arguments":d}},51170:(a,b,c)=>{"use strict";var d=c(36691),e=c(10397);a.exports=function(a,b,c){return c.get&&d(c.get,b,{getter:!0}),c.set&&d(c.set,b,{setter:!0}),e.f(a,b,c)}},51206:(a,b,c)=>{"use strict";var d=c(68298),e=c(40377),f=c(76607),g=c(67414),h=TypeError,i="Reduce of empty array with no initial value",j=function(a){return function(b,c,j,k){var l=e(b),m=f(l),n=g(l);if(d(c),0===n&&j<2)throw new h(i);var o=a?n-1:0,p=a?-1:1;if(j<2)for(;;){if(o in m){k=m[o],o+=p;break}if(o+=p,a?o<0:n<=o)throw new h(i)}for(;a?o>=0:n>o;o+=p)o in m&&(k=c(k,m[o],o,l));return k}};a.exports={left:j(!1),right:j(!0)}},52359:(a,b,c)=>{"use strict";var d,e,f=c(6532),g=c(343),h=f.process,i=f.Deno,j=h&&h.versions||i&&i.version,k=j&&j.v8;k&&(e=(d=k.split("."))[0]>0&&d[0]<4?1:+(d[0]+d[1])),!e&&g&&(!(d=g.match(/Edge\/(\d+)/))||d[1]>=74)&&(d=g.match(/Chrome\/(\d+)/))&&(e=+d[1]),a.exports=e},52431:(a,b,c)=>{"use strict";var d=c(6532),e=c(343),f=c(67420),g=function(a){return e.slice(0,a.length)===a};a.exports=g("Bun/")?"BUN":g("Cloudflare-Workers")?"CLOUDFLARE":g("Deno/")?"DENO":g("Node.js/")?"NODE":d.Bun&&"string"==typeof Bun.version?"BUN":d.Deno&&"object"==typeof Deno.version?"DENO":"process"===f(d.process)?"NODE":d.window&&d.document?"BROWSER":"REST"},52554:(a,b,c)=>{"use strict";var d=c(7588),e=c(10054),f=c(83407),g=c(15480),h=d("".replace),i=RegExp("^["+g+"]+"),j=RegExp("(^|[^"+g+"])["+g+"]+$"),k=function(a){return function(b){var c=f(e(b));return 1&a&&(c=h(c,i,"")),2&a&&(c=h(c,j,"$1")),c}};a.exports={start:k(1),end:k(2),trim:k(3)}},53436:(a,b,c)=>{"use strict";var d=c(67420);a.exports=Array.isArray||function(a){return"Array"===d(a)}},53538:(a,b,c)=>{"use strict";var d=c(12549).IteratorPrototype,e=c(65444),f=c(23864),g=c(7615),h=c(97601),i=function(){return this};a.exports=function(a,b,c,j){var k=b+" Iterator";return a.prototype=e(d,{next:f(+!j,c)}),g(a,k,!1,!0),h[k]=i,a}},53907:(a,b,c)=>{"use strict";var d=c(8991);a.exports=function(){var a=d(this),b="";return a.hasIndices&&(b+="d"),a.global&&(b+="g"),a.ignoreCase&&(b+="i"),a.multiline&&(b+="m"),a.dotAll&&(b+="s"),a.unicode&&(b+="u"),a.unicodeSets&&(b+="v"),a.sticky&&(b+="y"),b}},55744:(a,b,c)=>{"use strict";var d=c(80387)("toStringTag"),e={};e[d]="z",a.exports="[object z]"===String(e)},57224:(a,b,c)=>{"use strict";c(79287);var d=c(50969),e=c(59908),f=c(85587),g=c(97039),h=c(80387),i=c(94323),j=h("species"),k=RegExp.prototype;a.exports=function(a,b,c,l){var m=h(a),n=!g(function(){var b={};return b[m]=function(){return 7},7!==""[a](b)}),o=n&&!g(function(){var b=!1,c=/a/;return"split"===a&&((c={}).constructor={},c.constructor[j]=function(){return c},c.flags="",c[m]=/./[m]),c.exec=function(){return b=!0,null},c[m](""),!b});if(!n||!o||c){var p=/./[m],q=b(m,""[a],function(a,b,c,e,g){var h=b.exec;return h===f||h===k.exec?n&&!g?{done:!0,value:d(p,b,c,e)}:{done:!0,value:d(a,c,b,e)}:{done:!1}});e(String.prototype,a,q[0]),e(k,m,q[1])}l&&i(k[m],"sham",!0)}},57862:(a,b,c)=>{"use strict";var d=c(97039);a.exports=function(a,b){var c=[][a];return!!c&&d(function(){c.call(null,b||function(){return 1},1)})}},58397:(a,b,c)=>{"use strict";var d=c(6532),e=c(59164),f=c(37812),g=c(30052),h=c(94323),i=c(7615),j=c(80387)("iterator"),k=g.values,l=function(a,b){if(a){if(a[j]!==k)try{h(a,j,k)}catch(b){a[j]=k}if(i(a,b,!0),e[b]){for(var c in g)if(a[c]!==g[c])try{h(a,c,g[c])}catch(b){a[c]=g[c]}}}};for(var m in e)l(d[m]&&d[m].prototype,m);l(f,"DOMTokenList")},58627:(a,b,c)=>{"use strict";var d=c(63585);a.exports=function(a){var b=+a;return b!=b||0===b?0:d(b)}},59164:a=>{"use strict";a.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},59592:a=>{"use strict";var b=TypeError;a.exports=function(a,c){if(a<c)throw new b("Not enough arguments");return a}},59908:(a,b,c)=>{"use strict";var d=c(17425),e=c(10397),f=c(36691),g=c(68077);a.exports=function(a,b,c,h){h||(h={});var i=h.enumerable,j=void 0!==h.name?h.name:b;if(d(c)&&f(c,j,h),h.global)i?a[b]=c:g(b,c);else{try{h.unsafe?a[b]&&(i=!0):delete a[b]}catch(a){}i?a[b]=c:e.f(a,b,{value:c,enumerable:!1,configurable:!h.nonConfigurable,writable:!h.nonWritable})}return a}},59936:(a,b,c)=>{"use strict";var d=c(77525),e=c(75351),f=c(86883),g=c(10397);a.exports=function(a,b,c){for(var h=e(b),i=g.f,j=f.f,k=0;k<h.length;k++){var l=h[k];d(a,l)||c&&d(c,l)||i(a,l,j(b,l))}}},60561:a=>{"use strict";a.exports=function(a){return null==a}},60827:a=>{"use strict";a.exports=!1},63345:(a,b)=>{"use strict";b.f=Object.getOwnPropertySymbols},63585:a=>{"use strict";var b=Math.ceil,c=Math.floor;a.exports=Math.trunc||function(a){var d=+a;return(d>0?c:b)(d)}},64798:function(a){(function(){var b,c,d,e;"undefined"!=typeof performance&&null!==performance&&performance.now?a.exports=function(){return performance.now()}:"undefined"!=typeof process&&null!==process&&process.hrtime?(a.exports=function(){return(b()-e)/1e6},c=process.hrtime,e=(b=function(){var a;return 1e9*(a=c())[0]+a[1]})()-1e9*process.uptime()):Date.now?(a.exports=function(){return Date.now()-d},d=Date.now()):(a.exports=function(){return new Date().getTime()-d},d=new Date().getTime())}).call(this)},65436:(a,b,c)=>{"use strict";var d=c(69416),e=c(68298),f=c(79892),g=d(d.bind);a.exports=function(a,b){return e(a),void 0===b?a:f?g(a,b):function(){return a.apply(b,arguments)}}},65444:(a,b,c)=>{"use strict";var d,e=c(8991),f=c(97269),g=c(67519),h=c(91001),i=c(45121),j=c(23359),k=c(46815),l="prototype",m="script",n=k("IE_PROTO"),o=function(){},p=function(a){return"<"+m+">"+a+"</"+m+">"},q=function(a){a.write(p("")),a.close();var b=a.parentWindow.Object;return a=null,b},r=function(){var a,b=j("iframe");return b.style.display="none",i.appendChild(b),b.src=String("java"+m+":"),(a=b.contentWindow.document).open(),a.write(p("document.F=Object")),a.close(),a.F},s=function(){try{d=new ActiveXObject("htmlfile")}catch(a){}s="undefined"!=typeof document?document.domain&&d?q(d):r():q(d);for(var a=g.length;a--;)delete s[l][g[a]];return s()};h[n]=!0,a.exports=Object.create||function(a,b){var c;return null!==a?(o[l]=e(a),c=new o,o[l]=null,c[n]=a):c=s(),void 0===b?c:f.f(c,b)}},65876:(a,b,c)=>{"use strict";c.r(b),c.d(b,{AElement:()=>a2,AnimateColorElement:()=>aY,AnimateElement:()=>aX,AnimateTransformElement:()=>aZ,BoundingBox:()=>aA,CB1:()=>Z,CB2:()=>$,CB3:()=>_,CB4:()=>aa,Canvg:()=>bt,CircleElement:()=>aK,ClipPathElement:()=>bg,DefsElement:()=>aR,DescElement:()=>bn,Document:()=>bq,Element:()=>aw,EllipseElement:()=>aL,FeColorMatrixElement:()=>bd,FeCompositeElement:()=>bk,FeDropShadowElement:()=>bi,FeGaussianBlurElement:()=>bl,FeMorphologyElement:()=>bj,FilterElement:()=>bh,Font:()=>az,FontElement:()=>a$,FontFaceElement:()=>a_,GElement:()=>aS,GlyphElement:()=>aE,GradientElement:()=>aT,ImageElement:()=>a7,LineElement:()=>aM,LinearGradientElement:()=>aU,MarkerElement:()=>aQ,MaskElement:()=>be,Matrix:()=>ar,MissingGlyphElement:()=>a0,Mouse:()=>ah,PSEUDO_ZERO:()=>V,Parser:()=>an,PathElement:()=>aD,PathParser:()=>aB,PatternElement:()=>aP,Point:()=>ag,PolygonElement:()=>aO,PolylineElement:()=>aN,Property:()=>ae,QB1:()=>ab,QB2:()=>ac,QB3:()=>ad,RadialGradientElement:()=>aV,RectElement:()=>aJ,RenderedElement:()=>aC,Rotate:()=>ap,SVGElement:()=>aI,SVGFontLoader:()=>a9,Scale:()=>aq,Screen:()=>ak,Skew:()=>as,SkewX:()=>at,SkewY:()=>au,StopElement:()=>aW,StyleElement:()=>ba,SymbolElement:()=>a8,TRefElement:()=>a1,TSpanElement:()=>aG,TextElement:()=>aF,TextPathElement:()=>a5,TitleElement:()=>bm,Transform:()=>av,Translate:()=>ao,UnknownElement:()=>ax,UseElement:()=>bb,ViewPort:()=>af,compressSpaces:()=>E,default:()=>bt,getSelectorSpecificity:()=>U,normalizeAttributeName:()=>J,normalizeColor:()=>L,parseExternalUrl:()=>K,presets:()=>D,toNumbers:()=>H,trimLeft:()=>F,trimRight:()=>G,vectorMagnitude:()=>W,vectorsAngle:()=>Y,vectorsRatio:()=>X}),c(98074);var d=c(69377);c(34357),c(47908),c(9108),c(30052),c(58397);var e=c(17049);c(45452),c(90845),c(33748);var f=c(24991);c(28090);var g=c(79064);c(68328),c(96595),c(73522);var h=function(a,b){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)Object.prototype.hasOwnProperty.call(b,c)&&(a[c]=b[c])})(a,b)};function i(a,b){if("function"!=typeof b&&null!==b)throw TypeError("Class extends value "+String(b)+" is not a constructor or null");function c(){this.constructor=a}h(a,b),a.prototype=null===b?Object.create(b):(c.prototype=b.prototype,new c)}function j(a,b){var c=a[0],d=a[1];return[c*Math.cos(b)-d*Math.sin(b),c*Math.sin(b)+d*Math.cos(b)]}function k(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];for(var c=0;c<a.length;c++)if("number"!=typeof a[c])throw Error("assertNumbers arguments["+c+"] is not a number. "+typeof a[c]+" == typeof "+a[c]);return!0}var l=Math.PI;function m(a,b,c){a.lArcFlag=+(0!==a.lArcFlag),a.sweepFlag=+(0!==a.sweepFlag);var d=a.rX,e=a.rY,f=a.x,g=a.y;d=Math.abs(a.rX),e=Math.abs(a.rY);var h=j([(b-f)/2,(c-g)/2],-a.xRot/180*l),i=h[0],k=h[1],m=Math.pow(i,2)/Math.pow(d,2)+Math.pow(k,2)/Math.pow(e,2);1<m&&(d*=Math.sqrt(m),e*=Math.sqrt(m)),a.rX=d,a.rY=e;var n=Math.pow(d,2)*Math.pow(k,2)+Math.pow(e,2)*Math.pow(i,2),o=(a.lArcFlag!==a.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(d,2)*Math.pow(e,2)-n)/n)),p=d*k/e*o,q=-e*i/d*o,r=j([p,q],a.xRot/180*l);a.cX=r[0]+(b+f)/2,a.cY=r[1]+(c+g)/2,a.phi1=Math.atan2((k-q)/e,(i-p)/d),a.phi2=Math.atan2((-k-q)/e,(-i-p)/d),0===a.sweepFlag&&a.phi2>a.phi1&&(a.phi2-=2*l),1===a.sweepFlag&&a.phi2<a.phi1&&(a.phi2+=2*l),a.phi1*=180/l,a.phi2*=180/l}function n(a,b,c){k(a,b,c);var d=a*a+b*b-c*c;if(0>d)return[];if(0===d)return[[a*c/(a*a+b*b),b*c/(a*a+b*b)]];var e=Math.sqrt(d);return[[(a*c+b*e)/(a*a+b*b),(b*c-a*e)/(a*a+b*b)],[(a*c-b*e)/(a*a+b*b),(b*c+a*e)/(a*a+b*b)]]}var o,p=Math.PI/180;function q(a,b,c,d){return a+Math.cos(d/180*l)*b+Math.sin(d/180*l)*c}function r(a,b,c,d){var e=b-a,f=c-b,g=3*e+3*(d-c)-6*f,h=6*(f-e),i=3*e;return 1e-6>Math.abs(g)?[-i/h]:function(a,b,c){void 0===c&&(c=1e-6);var d=a*a/4-b;if(d<-c)return[];if(d<=c)return[-a/2];var e=Math.sqrt(d);return[-a/2-e,-a/2+e]}(h/g,i/g,1e-6)}function s(a,b,c,d,e){var f=1-e;return f*f*f*a+3*f*f*e*b+3*f*e*e*c+e*e*e*d}!function(a){function b(){return e(function(a,b,c){return a.relative&&(void 0!==a.x1&&(a.x1+=b),void 0!==a.y1&&(a.y1+=c),void 0!==a.x2&&(a.x2+=b),void 0!==a.y2&&(a.y2+=c),void 0!==a.x&&(a.x+=b),void 0!==a.y&&(a.y+=c),a.relative=!1),a})}function c(){var a=NaN,b=NaN,c=NaN,d=NaN;return e(function(e,f,g){return e.type&x.SMOOTH_CURVE_TO&&(e.type=x.CURVE_TO,a=isNaN(a)?f:a,b=isNaN(b)?g:b,e.x1=e.relative?f-a:2*f-a,e.y1=e.relative?g-b:2*g-b),e.type&x.CURVE_TO?(a=e.relative?f+e.x2:e.x2,b=e.relative?g+e.y2:e.y2):(a=NaN,b=NaN),e.type&x.SMOOTH_QUAD_TO&&(e.type=x.QUAD_TO,c=isNaN(c)?f:c,d=isNaN(d)?g:d,e.x1=e.relative?f-c:2*f-c,e.y1=e.relative?g-d:2*g-d),e.type&x.QUAD_TO?(c=e.relative?f+e.x1:e.x1,d=e.relative?g+e.y1:e.y1):(c=NaN,d=NaN),e})}function d(){var a=NaN,b=NaN;return e(function(c,d,e){if(c.type&x.SMOOTH_QUAD_TO&&(c.type=x.QUAD_TO,a=isNaN(a)?d:a,b=isNaN(b)?e:b,c.x1=c.relative?d-a:2*d-a,c.y1=c.relative?e-b:2*e-b),c.type&x.QUAD_TO){a=c.relative?d+c.x1:c.x1,b=c.relative?e+c.y1:c.y1;var f=c.x1,g=c.y1;c.type=x.CURVE_TO,c.x1=((c.relative?0:d)+2*f)/3,c.y1=((c.relative?0:e)+2*g)/3,c.x2=(c.x+2*f)/3,c.y2=(c.y+2*g)/3}else a=NaN,b=NaN;return c})}function e(a){var b=0,c=0,d=NaN,e=NaN;return function(f){if(isNaN(d)&&!(f.type&x.MOVE_TO))throw Error("path must start with moveto");var g=a(f,b,c,d,e);return f.type&x.CLOSE_PATH&&(b=d,c=e),void 0!==f.x&&(b=f.relative?b+f.x:f.x),void 0!==f.y&&(c=f.relative?c+f.y:f.y),f.type&x.MOVE_TO&&(d=b,e=c),g}}function f(a,b,c,d,f,g){return k(a,b,c,d,f,g),e(function(e,h,i,j){var k=e.x1,l=e.x2,m=e.relative&&!isNaN(j),n=void 0!==e.x?e.x:m?0:h,o=void 0!==e.y?e.y:m?0:i;e.type&x.HORIZ_LINE_TO&&0!==b&&(e.type=x.LINE_TO,e.y=e.relative?0:i),e.type&x.VERT_LINE_TO&&0!==c&&(e.type=x.LINE_TO,e.x=e.relative?0:h),void 0!==e.x&&(e.x=e.x*a+o*c+(m?0:f)),void 0!==e.y&&(e.y=n*b+e.y*d+(m?0:g)),void 0!==e.x1&&(e.x1=e.x1*a+e.y1*c+(m?0:f)),void 0!==e.y1&&(e.y1=k*b+e.y1*d+(m?0:g)),void 0!==e.x2&&(e.x2=e.x2*a+e.y2*c+(m?0:f)),void 0!==e.y2&&(e.y2=l*b+e.y2*d+(m?0:g));var p=a*d-b*c;if(void 0!==e.xRot&&(1!==a||0!==b||0!==c||1!==d))if(0===p)delete e.rX,delete e.rY,delete e.xRot,delete e.lArcFlag,delete e.sweepFlag,e.type=x.LINE_TO;else{var q,r,s=e.xRot*Math.PI/180,t=Math.sin(s),u=Math.cos(s),v=1/((q=e.rX)*q),w=1/((r=e.rY)*r),y=u*u*v+t*t*w,z=2*t*u*(v-w),A=t*t*v+u*u*w,B=y*d*d-z*b*d+A*b*b,C=z*(a*d+b*c)-2*(y*c*d+A*a*b),D=y*c*c-z*a*c+A*a*a,E=(Math.atan2(C,B-D)+Math.PI)%Math.PI/2,F=Math.sin(E),G=Math.cos(E);e.rX=Math.abs(p)/Math.sqrt(G*G*B+C*F*G+F*F*D),e.rY=Math.abs(p)/Math.sqrt(F*F*B-C*F*G+G*G*D),e.xRot=180*E/Math.PI}return void 0!==e.sweepFlag&&0>p&&(e.sweepFlag=+!e.sweepFlag),e})}a.ROUND=function(a){function b(b){return Math.round(b*a)/a}return void 0===a&&(a=1e13),k(a),function(a){return void 0!==a.x1&&(a.x1=b(a.x1)),void 0!==a.y1&&(a.y1=b(a.y1)),void 0!==a.x2&&(a.x2=b(a.x2)),void 0!==a.y2&&(a.y2=b(a.y2)),void 0!==a.x&&(a.x=b(a.x)),void 0!==a.y&&(a.y=b(a.y)),void 0!==a.rX&&(a.rX=b(a.rX)),void 0!==a.rY&&(a.rY=b(a.rY)),a}},a.TO_ABS=b,a.TO_REL=function(){return e(function(a,b,c){return a.relative||(void 0!==a.x1&&(a.x1-=b),void 0!==a.y1&&(a.y1-=c),void 0!==a.x2&&(a.x2-=b),void 0!==a.y2&&(a.y2-=c),void 0!==a.x&&(a.x-=b),void 0!==a.y&&(a.y-=c),a.relative=!0),a})},a.NORMALIZE_HVZ=function(a,b,c){return void 0===a&&(a=!0),void 0===b&&(b=!0),void 0===c&&(c=!0),e(function(d,e,f,g,h){if(isNaN(g)&&!(d.type&x.MOVE_TO))throw Error("path must start with moveto");return b&&d.type&x.HORIZ_LINE_TO&&(d.type=x.LINE_TO,d.y=d.relative?0:f),c&&d.type&x.VERT_LINE_TO&&(d.type=x.LINE_TO,d.x=d.relative?0:e),a&&d.type&x.CLOSE_PATH&&(d.type=x.LINE_TO,d.x=d.relative?g-e:g,d.y=d.relative?h-f:h),d.type&x.ARC&&(0===d.rX||0===d.rY)&&(d.type=x.LINE_TO,delete d.rX,delete d.rY,delete d.xRot,delete d.lArcFlag,delete d.sweepFlag),d})},a.NORMALIZE_ST=c,a.QT_TO_C=d,a.INFO=e,a.SANITIZE=function(a){void 0===a&&(a=0),k(a);var b=NaN,c=NaN,d=NaN,f=NaN;return e(function(e,g,h,i,j){var k=Math.abs,l=!1,m=0,n=0;if(e.type&x.SMOOTH_CURVE_TO&&(m=isNaN(b)?0:g-b,n=isNaN(c)?0:h-c),e.type&(x.CURVE_TO|x.SMOOTH_CURVE_TO)?(b=e.relative?g+e.x2:e.x2,c=e.relative?h+e.y2:e.y2):(b=NaN,c=NaN),e.type&x.SMOOTH_QUAD_TO?(d=isNaN(d)?g:2*g-d,f=isNaN(f)?h:2*h-f):e.type&x.QUAD_TO?(d=e.relative?g+e.x1:e.x1,f=e.relative?h+e.y1:e.y2):(d=NaN,f=NaN),e.type&x.LINE_COMMANDS||e.type&x.ARC&&(0===e.rX||0===e.rY||!e.lArcFlag)||e.type&x.CURVE_TO||e.type&x.SMOOTH_CURVE_TO||e.type&x.QUAD_TO||e.type&x.SMOOTH_QUAD_TO){var o=void 0===e.x?0:e.relative?e.x:e.x-g,p=void 0===e.y?0:e.relative?e.y:e.y-h;m=isNaN(d)?void 0===e.x1?m:e.relative?e.x:e.x1-g:d-g,n=isNaN(f)?void 0===e.y1?n:e.relative?e.y:e.y1-h:f-h;var q=void 0===e.x2?0:e.relative?e.x:e.x2-g,r=void 0===e.y2?0:e.relative?e.y:e.y2-h;k(o)<=a&&k(p)<=a&&k(m)<=a&&k(n)<=a&&k(q)<=a&&k(r)<=a&&(l=!0)}return e.type&x.CLOSE_PATH&&k(g-i)<=a&&k(h-j)<=a&&(l=!0),l?[]:e})},a.MATRIX=f,a.ROTATE=function(a,b,c){void 0===b&&(b=0),void 0===c&&(c=0),k(a,b,c);var d=Math.sin(a),e=Math.cos(a);return f(e,d,-d,e,b-b*e+c*d,c-b*d-c*e)},a.TRANSLATE=function(a,b){return void 0===b&&(b=0),k(a,b),f(1,0,0,1,a,b)},a.SCALE=function(a,b){return void 0===b&&(b=a),k(a,b),f(a,0,0,b,0,0)},a.SKEW_X=function(a){return k(a),f(1,0,Math.atan(a),1,0,0)},a.SKEW_Y=function(a){return k(a),f(1,Math.atan(a),0,1,0,0)},a.X_AXIS_SYMMETRY=function(a){return void 0===a&&(a=0),k(a),f(-1,0,0,1,a,0)},a.Y_AXIS_SYMMETRY=function(a){return void 0===a&&(a=0),k(a),f(1,0,0,-1,0,a)},a.A_TO_C=function(){return e(function(a,b,c){return x.ARC===a.type?function(a,b,c){var d,e,f,g;a.cX||m(a,b,c);for(var h=Math.min(a.phi1,a.phi2),i=Math.max(a.phi1,a.phi2)-h,k=Math.ceil(i/90),l=Array(k),n=b,o=c,q=0;q<k;q++){var r,s,t,u,v,w,y=(r=a.phi1,s=a.phi2,(1-(t=q/k))*r+t*s),z=(u=a.phi1,v=a.phi2,(1-(w=(q+1)/k))*u+w*v),A=4/3*Math.tan((z-y)*p/4),B=[Math.cos(y*p)-A*Math.sin(y*p),Math.sin(y*p)+A*Math.cos(y*p)],C=B[0],D=B[1],E=[Math.cos(z*p),Math.sin(z*p)],F=E[0],G=E[1],H=[F+A*Math.sin(z*p),G-A*Math.cos(z*p)],I=H[0],J=H[1];l[q]={relative:a.relative,type:x.CURVE_TO};var K=function(b,c){var d=j([b*a.rX,c*a.rY],a.xRot),e=d[0],f=d[1];return[a.cX+e,a.cY+f]};d=K(C,D),l[q].x1=d[0],l[q].y1=d[1],e=K(I,J),l[q].x2=e[0],l[q].y2=e[1],f=K(F,G),l[q].x=f[0],l[q].y=f[1],a.relative&&(l[q].x1-=n,l[q].y1-=o,l[q].x2-=n,l[q].y2-=o,l[q].x-=n,l[q].y-=o),n=(g=[l[q].x,l[q].y])[0],o=g[1]}return l}(a,a.relative?0:b,a.relative?0:c):a})},a.ANNOTATE_ARCS=function(){return e(function(a,b,c){return a.relative&&(b=0,c=0),x.ARC===a.type&&m(a,b,c),a})},a.CLONE=function(){return function(a){var b={};for(var c in a)b[c]=a[c];return b}},a.CALCULATE_BOUNDS=function(){var a=function(a){var b={};for(var c in a)b[c]=a[c];return b},f=b(),g=d(),h=c(),i=e(function(b,c,d){var e=h(g(f(a(b))));function j(a){a>i.maxX&&(i.maxX=a),a<i.minX&&(i.minX=a)}function k(a){a>i.maxY&&(i.maxY=a),a<i.minY&&(i.minY=a)}if(e.type&x.DRAWING_COMMANDS&&(j(c),k(d)),e.type&x.HORIZ_LINE_TO&&j(e.x),e.type&x.VERT_LINE_TO&&k(e.y),e.type&x.LINE_TO&&(j(e.x),k(e.y)),e.type&x.CURVE_TO){j(e.x),k(e.y);for(var l=0,o=r(c,e.x1,e.x2,e.x);l<o.length;l++)0<(u=o[l])&&1>u&&j(s(c,e.x1,e.x2,e.x,u));for(var p=0,t=r(d,e.y1,e.y2,e.y);p<t.length;p++)0<(u=t[p])&&1>u&&k(s(d,e.y1,e.y2,e.y,u))}if(e.type&x.ARC){j(e.x),k(e.y),m(e,c,d);for(var u,v=e.xRot/180*Math.PI,w=Math.cos(v)*e.rX,y=Math.sin(v)*e.rX,z=-Math.sin(v)*e.rY,A=Math.cos(v)*e.rY,B=e.phi1<e.phi2?[e.phi1,e.phi2]:-180>e.phi2?[e.phi2+360,e.phi1+360]:[e.phi2,e.phi1],C=B[0],D=B[1],E=function(a){var b=a[0],c=180*Math.atan2(a[1],b)/Math.PI;return c<C?c+360:c},F=0,G=n(z,-w,0).map(E);F<G.length;F++)(u=G[F])>C&&u<D&&j(q(e.cX,w,z,u));for(var H=0,I=n(A,-y,0).map(E);H<I.length;H++)(u=I[H])>C&&u<D&&k(q(e.cY,y,A,u))}return b});return i.minX=1/0,i.maxX=-1/0,i.minY=1/0,i.maxY=-1/0,i}}(o||(o={}));var t,u=function(){function a(){}return a.prototype.round=function(a){return this.transform(o.ROUND(a))},a.prototype.toAbs=function(){return this.transform(o.TO_ABS())},a.prototype.toRel=function(){return this.transform(o.TO_REL())},a.prototype.normalizeHVZ=function(a,b,c){return this.transform(o.NORMALIZE_HVZ(a,b,c))},a.prototype.normalizeST=function(){return this.transform(o.NORMALIZE_ST())},a.prototype.qtToC=function(){return this.transform(o.QT_TO_C())},a.prototype.aToC=function(){return this.transform(o.A_TO_C())},a.prototype.sanitize=function(a){return this.transform(o.SANITIZE(a))},a.prototype.translate=function(a,b){return this.transform(o.TRANSLATE(a,b))},a.prototype.scale=function(a,b){return this.transform(o.SCALE(a,b))},a.prototype.rotate=function(a,b,c){return this.transform(o.ROTATE(a,b,c))},a.prototype.matrix=function(a,b,c,d,e,f){return this.transform(o.MATRIX(a,b,c,d,e,f))},a.prototype.skewX=function(a){return this.transform(o.SKEW_X(a))},a.prototype.skewY=function(a){return this.transform(o.SKEW_Y(a))},a.prototype.xSymmetry=function(a){return this.transform(o.X_AXIS_SYMMETRY(a))},a.prototype.ySymmetry=function(a){return this.transform(o.Y_AXIS_SYMMETRY(a))},a.prototype.annotateArcs=function(){return this.transform(o.ANNOTATE_ARCS())},a}(),v=function(a){return 48<=a.charCodeAt(0)&&57>=a.charCodeAt(0)},w=function(a){function b(){var b=a.call(this)||this;return b.curNumber="",b.curCommandType=-1,b.curCommandRelative=!1,b.canParseCommandOrComma=!0,b.curNumberHasExp=!1,b.curNumberHasExpDigits=!1,b.curNumberHasDecimal=!1,b.curArgs=[],b}return i(b,a),b.prototype.finish=function(a){if(void 0===a&&(a=[]),this.parse(" ",a),0!==this.curArgs.length||!this.canParseCommandOrComma)throw SyntaxError("Unterminated command at the path end.");return a},b.prototype.parse=function(a,b){var c=this;void 0===b&&(b=[]);for(var d=function(a){b.push(a),c.curArgs.length=0,c.canParseCommandOrComma=!0},e=0;e<a.length;e++){var f=a[e],g=this.curCommandType===x.ARC&&(3===this.curArgs.length||4===this.curArgs.length)&&1===this.curNumber.length&&("0"===this.curNumber||"1"===this.curNumber),h=v(f)&&("0"===this.curNumber&&"0"===f||g);if(!v(f)||h)if("e"!==f&&"E"!==f)if("-"!==f&&"+"!==f||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==f||this.curNumberHasExp||this.curNumberHasDecimal||g){if(this.curNumber&&-1!==this.curCommandType){var i=Number(this.curNumber);if(isNaN(i))throw SyntaxError("Invalid number ending at "+e);if(this.curCommandType===x.ARC){if(0===this.curArgs.length||1===this.curArgs.length){if(0>i)throw SyntaxError('Expected positive number, got "'+i+'" at index "'+e+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+e+'"')}this.curArgs.push(i),this.curArgs.length===y[this.curCommandType]&&(x.HORIZ_LINE_TO===this.curCommandType?d({type:x.HORIZ_LINE_TO,relative:this.curCommandRelative,x:i}):x.VERT_LINE_TO===this.curCommandType?d({type:x.VERT_LINE_TO,relative:this.curCommandRelative,y:i}):this.curCommandType===x.MOVE_TO||this.curCommandType===x.LINE_TO||this.curCommandType===x.SMOOTH_QUAD_TO?(d({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),x.MOVE_TO===this.curCommandType&&(this.curCommandType=x.LINE_TO)):this.curCommandType===x.CURVE_TO?d({type:x.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===x.SMOOTH_CURVE_TO?d({type:x.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===x.QUAD_TO?d({type:x.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===x.ARC&&d({type:x.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(" "!==f&&"	"!==f&&"\r"!==f&&"\n"!==f)if(","===f&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==f&&"-"!==f&&"."!==f)if(h)this.curNumber=f,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw SyntaxError("Unterminated command at index "+e+".");if(!this.canParseCommandOrComma)throw SyntaxError('Unexpected character "'+f+'" at index '+e+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==f&&"Z"!==f)if("h"===f||"H"===f)this.curCommandType=x.HORIZ_LINE_TO,this.curCommandRelative="h"===f;else if("v"===f||"V"===f)this.curCommandType=x.VERT_LINE_TO,this.curCommandRelative="v"===f;else if("m"===f||"M"===f)this.curCommandType=x.MOVE_TO,this.curCommandRelative="m"===f;else if("l"===f||"L"===f)this.curCommandType=x.LINE_TO,this.curCommandRelative="l"===f;else if("c"===f||"C"===f)this.curCommandType=x.CURVE_TO,this.curCommandRelative="c"===f;else if("s"===f||"S"===f)this.curCommandType=x.SMOOTH_CURVE_TO,this.curCommandRelative="s"===f;else if("q"===f||"Q"===f)this.curCommandType=x.QUAD_TO,this.curCommandRelative="q"===f;else if("t"===f||"T"===f)this.curCommandType=x.SMOOTH_QUAD_TO,this.curCommandRelative="t"===f;else{if("a"!==f&&"A"!==f)throw SyntaxError('Unexpected character "'+f+'" at index '+e+".");this.curCommandType=x.ARC,this.curCommandRelative="a"===f}else b.push({type:x.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=f,this.curNumberHasDecimal="."===f}else this.curNumber+=f,this.curNumberHasDecimal=!0;else this.curNumber+=f;else this.curNumber+=f,this.curNumberHasExp=!0;else this.curNumber+=f,this.curNumberHasExpDigits=this.curNumberHasExp}return b},b.prototype.transform=function(a){return Object.create(this,{parse:{value:function(b,c){void 0===c&&(c=[]);for(var d=0,e=Object.getPrototypeOf(this).parse.call(this,b);d<e.length;d++){var f=a(e[d]);Array.isArray(f)?c.push.apply(c,f):c.push(f)}return c}}})},b}(u),x=function(a){function b(c){var d=a.call(this)||this;return d.commands="string"==typeof c?b.parse(c):c,d}return i(b,a),b.prototype.encode=function(){return b.encode(this.commands)},b.prototype.getBounds=function(){var a=o.CALCULATE_BOUNDS();return this.transform(a),a},b.prototype.transform=function(a){for(var b=[],c=0,d=this.commands;c<d.length;c++){var e=a(d[c]);Array.isArray(e)?b.push.apply(b,e):b.push(e)}return this.commands=b,this},b.encode=function(a){var b=a,c="";Array.isArray(b)||(b=[b]);for(var d=0;d<b.length;d++){var e=b[d];if(e.type===x.CLOSE_PATH)c+="z";else if(e.type===x.HORIZ_LINE_TO)c+=(e.relative?"h":"H")+e.x;else if(e.type===x.VERT_LINE_TO)c+=(e.relative?"v":"V")+e.y;else if(e.type===x.MOVE_TO)c+=(e.relative?"m":"M")+e.x+" "+e.y;else if(e.type===x.LINE_TO)c+=(e.relative?"l":"L")+e.x+" "+e.y;else if(e.type===x.CURVE_TO)c+=(e.relative?"c":"C")+e.x1+" "+e.y1+" "+e.x2+" "+e.y2+" "+e.x+" "+e.y;else if(e.type===x.SMOOTH_CURVE_TO)c+=(e.relative?"s":"S")+e.x2+" "+e.y2+" "+e.x+" "+e.y;else if(e.type===x.QUAD_TO)c+=(e.relative?"q":"Q")+e.x1+" "+e.y1+" "+e.x+" "+e.y;else if(e.type===x.SMOOTH_QUAD_TO)c+=(e.relative?"t":"T")+e.x+" "+e.y;else{if(e.type!==x.ARC)throw Error('Unexpected command type "'+e.type+'" at index '+d+".");c+=(e.relative?"a":"A")+e.rX+" "+e.rY+" "+e.xRot+" "+ +e.lArcFlag+" "+ +e.sweepFlag+" "+e.x+" "+e.y}}return c},b.parse=function(a){var b=new w,c=[];return b.parse(a,c),b.finish(c),c},b.CLOSE_PATH=1,b.MOVE_TO=2,b.HORIZ_LINE_TO=4,b.VERT_LINE_TO=8,b.LINE_TO=16,b.CURVE_TO=32,b.SMOOTH_CURVE_TO=64,b.QUAD_TO=128,b.SMOOTH_QUAD_TO=256,b.ARC=512,b.LINE_COMMANDS=b.LINE_TO|b.HORIZ_LINE_TO|b.VERT_LINE_TO,b.DRAWING_COMMANDS=b.HORIZ_LINE_TO|b.VERT_LINE_TO|b.LINE_TO|b.CURVE_TO|b.SMOOTH_CURVE_TO|b.QUAD_TO|b.SMOOTH_QUAD_TO|b.ARC,b}(u),y=((t={})[x.MOVE_TO]=2,t[x.LINE_TO]=2,t[x.HORIZ_LINE_TO]=1,t[x.VERT_LINE_TO]=1,t[x.CLOSE_PATH]=0,t[x.QUAD_TO]=4,t[x.SMOOTH_QUAD_TO]=2,t[x.CURVE_TO]=6,t[x.SMOOTH_CURVE_TO]=4,t[x.ARC]=7,t);function z(a){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}c(25225);var A=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],B=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24],C=function a(){if(!(this instanceof a))throw TypeError("Cannot call a class as a function");this.r=0,this.g=0,this.b=0,this.a=0,this.next=null},D=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},b={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:a,createCanvas:(a,b)=>new OffscreenCanvas(a,b),createImage:a=>d(function*(){var b=yield fetch(a),c=yield b.blob();return yield createImageBitmap(c)})()};return("undefined"!=typeof DOMParser||void 0===a)&&Reflect.deleteProperty(b,"DOMParser"),b},node:function(a){var{DOMParser:b,canvas:c,fetch:d}=a;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:b,fetch:d,createCanvas:c.createCanvas,createImage:c.loadImage}}});function E(a){return a.replace(/(?!\u3000)\s+/gm," ")}function F(a){return a.replace(/^[\n \t]+/,"")}function G(a){return a.replace(/[\n \t]+$/,"")}function H(a){return((a||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var I=/^[A-Z-]+$/;function J(a){return I.test(a)?a.toLowerCase():a}function K(a){var b=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(a)||[];return b[2]||b[3]||b[4]}function L(a){if(!a.startsWith("rgb"))return a;var b=3;return a.replace(/\d+(\.\d+)?/g,(a,c)=>b--&&c?String(Math.round(parseFloat(a))):a)}var M=/(\[[^\]]+\])/g,N=/(#[^\s+>~.[:]+)/g,O=/(\.[^\s+>~.[:]+)/g,P=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,Q=/(:[\w-]+\([^)]*\))/gi,R=/(:[^\s+>~.[:]+)/g,S=/([^\s+>~.[:]+)/g;function T(a,b){var c=b.exec(a);return c?[a.replace(b," "),c.length]:[a,0]}function U(a){var b=[0,0,0],c=a.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),d=0;return[c,d]=T(c,M),b[1]+=d,[c,d]=T(c,N),b[0]+=d,[c,d]=T(c,O),b[1]+=d,[c,d]=T(c,P),b[2]+=d,[c,d]=T(c,Q),b[1]+=d,[c,d]=T(c,R),b[1]+=d,c=c.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[c,d]=T(c,S),b[2]+=d,b.join("")}var V=1e-8;function W(a){return Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2))}function X(a,b){return(a[0]*b[0]+a[1]*b[1])/(W(a)*W(b))}function Y(a,b){return(a[0]*b[1]<a[1]*b[0]?-1:1)*Math.acos(X(a,b))}function Z(a){return a*a*a}function $(a){return 3*a*a*(1-a)}function _(a){return 3*a*(1-a)*(1-a)}function aa(a){return(1-a)*(1-a)*(1-a)}function ab(a){return a*a}function ac(a){return 2*a*(1-a)}function ad(a){return(1-a)*(1-a)}class ae{constructor(a,b,c){this.document=a,this.name=b,this.value=c,this.isNormalizedColor=!1}static empty(a){return new ae(a,"EMPTY","")}split(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:b,name:c}=this;return E(this.getString()).trim().split(a).map(a=>new ae(b,c,a))}hasValue(a){var{value:b}=this;return null!==b&&""!==b&&(a||0!==b)&&void 0!==b}isString(a){var{value:b}=this,c="string"==typeof b;return c&&a?a.test(b):c}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var a=this.getString();switch(!0){case a.endsWith("px"):case/^[0-9]+$/.test(a):return!0;default:return!1}}setValue(a){return this.value=a,this}getValue(a){return void 0===a||this.hasValue()?this.value:a}getNumber(a){if(!this.hasValue())return void 0===a?0:parseFloat(a);var{value:b}=this,c=parseFloat(b);return this.isString(/%$/)&&(c/=100),c}getString(a){return void 0===a||this.hasValue()?void 0===this.value?"":String(this.value):String(a)}getColor(a){var b=this.getString(a);return this.isNormalizedColor||(this.isNormalizedColor=!0,b=L(b),this.value=b),b}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(a){var b=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[c,d]="boolean"==typeof a?[void 0,a]:[a],{viewPort:e}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(e.computeSize("x"),e.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(e.computeSize("x"),e.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*e.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*e.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&d:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*e.computeSize(c);default:var f=this.getNumber();if(b&&f<1)return f*e.computeSize(c);return f}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var a=this.getString(),b=/#([^)'"]+)/.exec(a);return b&&(b=b[1]),b||(b=a),this.document.definitions[b]}getFillStyleDefinition(a,b){var c=this.getDefinition();if(!c)return null;if("function"==typeof c.createGradient)return c.createGradient(this.document.ctx,a,b);if("function"==typeof c.createPattern){if(c.getHrefAttribute().hasValue()){var d=c.getAttribute("patternTransform");c=c.getHrefAttribute().getDefinition(),d.hasValue()&&c.getAttribute("patternTransform",!0).setValue(d.value)}return c.createPattern(this.document.ctx,a,b)}return null}getTextBaseline(){return this.hasValue()?ae.textBaselineMapping[this.getString()]:null}addOpacity(a){for(var b=this.getColor(),c=b.length,d=0,e=0;e<c&&(","===b[e]&&d++,3!==d);e++);if(a.hasValue()&&this.isString()&&3!==d){var f=new g(b);f.ok&&(f.alpha=a.getNumber(),b=f.toRGBA())}return new ae(this.document,this.name,b)}}ae.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class af{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(a,b){this.viewPorts.push({width:a,height:b})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:a}=this;return a[a.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(a){return"number"==typeof a?a:"x"===a?this.width:"y"===a?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class ag{constructor(a,b){this.x=a,this.y=b}static parse(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[c=b,d=b]=H(a);return new ag(c,d)}static parseScale(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[c=b,d=c]=H(a);return new ag(c,d)}static parsePath(a){for(var b=H(a),c=b.length,d=[],e=0;e<c;e+=2)d.push(new ag(b[e],b[e+1]));return d}angleTo(a){return Math.atan2(a.y-this.y,a.x-this.x)}applyTransform(a){var{x:b,y:c}=this,d=b*a[0]+c*a[2]+a[4],e=b*a[1]+c*a[3]+a[5];this.x=d,this.y=e}}class ah{constructor(a){this.screen=a,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:a,onClick:b,onMouseMove:c}=this,d=a.ctx.canvas;d.onclick=b,d.onmousemove=c,this.working=!0}}stop(){if(this.working){var a=this.screen.ctx.canvas;this.working=!1,a.onclick=null,a.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:a,events:b,eventElements:c}=this,{style:d}=a.ctx.canvas;d&&(d.cursor=""),b.forEach((a,b)=>{for(var{run:d}=a,e=c[b];e;)d(e),e=e.parent}),this.events=[],this.eventElements=[]}}checkPath(a,b){if(this.working&&b){var{events:c,eventElements:d}=this;c.forEach((c,e)=>{var{x:f,y:g}=c;!d[e]&&b.isPointInPath&&b.isPointInPath(f,g)&&(d[e]=a)})}}checkBoundingBox(a,b){if(this.working&&b){var{events:c,eventElements:d}=this;c.forEach((c,e)=>{var{x:f,y:g}=c;!d[e]&&b.isPointInBox(f,g)&&(d[e]=a)})}}mapXY(a,b){for(var{window:c,ctx:d}=this.screen,e=new ag(a,b),f=d.canvas;f;)e.x-=f.offsetLeft,e.y-=f.offsetTop,f=f.offsetParent;return c.scrollX&&(e.x+=c.scrollX),c.scrollY&&(e.y+=c.scrollY),e}onClick(a){var{x:b,y:c}=this.mapXY(a.clientX,a.clientY);this.events.push({type:"onclick",x:b,y:c,run(a){a.onClick&&a.onClick()}})}onMouseMove(a){var{x:b,y:c}=this.mapXY(a.clientX,a.clientY);this.events.push({type:"onmousemove",x:b,y:c,run(a){a.onMouseMove&&a.onMouseMove()}})}}var ai="undefined"!=typeof window?window:null,aj="undefined"!=typeof fetch?fetch.bind(void 0):null;class ak{constructor(a){var{fetch:b=aj,window:c=ai}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=a,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new af,this.mouse=new ah(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=c,this.fetch=b}wait(a){this.waits.push(a)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var a=this.waits.every(a=>a());return a&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=a,a}setDefaults(a){a.strokeStyle="rgba(0,0,0,0)",a.lineCap="butt",a.lineJoin="miter",a.miterLimit=4}setViewBox(a){var{document:b,ctx:c,aspectRatio:d,width:e,desiredWidth:f,height:g,desiredHeight:h,minX:i=0,minY:j=0,refX:k,refY:l,clip:m=!1,clipX:n=0,clipY:o=0}=a,[p,q]=E(d).replace(/^defer\s/,"").split(" "),r=p||"xMidYMid",s=q||"meet",t=e/f,u=g/h,v=Math.min(t,u),w=Math.max(t,u),x=f,y=h;"meet"===s&&(x*=v,y*=v),"slice"===s&&(x*=w,y*=w);var z=new ae(b,"refX",k),A=new ae(b,"refY",l),B=z.hasValue()&&A.hasValue();if(B&&c.translate(-v*z.getPixels("x"),-v*A.getPixels("y")),m){var C=v*n,D=v*o;c.beginPath(),c.moveTo(C,D),c.lineTo(e,D),c.lineTo(e,g),c.lineTo(C,g),c.closePath(),c.clip()}if(!B){var F="meet"===s&&v===u,G="slice"===s&&w===u,H="meet"===s&&v===t,I="slice"===s&&w===t;r.startsWith("xMid")&&(F||G)&&c.translate(e/2-x/2,0),r.endsWith("YMid")&&(H||I)&&c.translate(0,g/2-y/2),r.startsWith("xMax")&&(F||G)&&c.translate(e-x,0),r.endsWith("YMax")&&(H||I)&&c.translate(0,g-y)}switch(!0){case"none"===r:c.scale(t,u);break;case"meet"===s:c.scale(v,v);break;case"slice"===s:c.scale(w,w)}c.translate(-i,-j)}start(a){var{enableRedraw:b=!1,ignoreMouse:c=!1,ignoreAnimation:d=!1,ignoreDimensions:e=!1,ignoreClear:g=!1,forceRedraw:h,scaleWidth:i,scaleHeight:j,offsetX:k,offsetY:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:m,mouse:n}=this,o=1e3/m;if(this.frameDuration=o,this.readyPromise=new Promise(a=>{this.resolveReady=a}),this.isReady()&&this.render(a,e,g,i,j,k,l),b){var p=Date.now(),q=p,r=0,s=()=>{(r=(p=Date.now())-q)>=o&&(q=p-r%o,this.shouldUpdate(d,h)&&(this.render(a,e,g,i,j,k,l),n.runEvents())),this.intervalId=f(s)};c||n.start(),this.intervalId=f(s)}}stop(){this.intervalId&&(f.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(a,b){if(!a){var{frameDuration:c}=this;if(this.animations.reduce((a,b)=>b.update(c)||a,!1))return!0}return!!("function"==typeof b&&b()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(a,b,c,d,e,f,g){var{CLIENT_WIDTH:h,CLIENT_HEIGHT:i,viewPort:j,ctx:k,isFirstRender:l}=this,m=k.canvas;j.clear(),m.width&&m.height?j.setCurrent(m.width,m.height):j.setCurrent(h,i);var n=a.getStyle("width"),o=a.getStyle("height");!b&&(l||"number"!=typeof d&&"number"!=typeof e)&&(n.hasValue()&&(m.width=n.getPixels("x"),m.style&&(m.style.width="".concat(m.width,"px"))),o.hasValue()&&(m.height=o.getPixels("y"),m.style&&(m.style.height="".concat(m.height,"px"))));var p=m.clientWidth||m.width,q=m.clientHeight||m.height;if(b&&n.hasValue()&&o.hasValue()&&(p=n.getPixels("x"),q=o.getPixels("y")),j.setCurrent(p,q),"number"==typeof f&&a.getAttribute("x",!0).setValue(f),"number"==typeof g&&a.getAttribute("y",!0).setValue(g),"number"==typeof d||"number"==typeof e){var r=H(a.getAttribute("viewBox").getString()),s=0,t=0;if("number"==typeof d){var u=a.getStyle("width");u.hasValue()?s=u.getPixels("x")/d:isNaN(r[2])||(s=r[2]/d)}if("number"==typeof e){var v=a.getStyle("height");v.hasValue()?t=v.getPixels("y")/e:isNaN(r[3])||(t=r[3]/e)}s||(s=t),t||(t=s),a.getAttribute("width",!0).setValue(d),a.getAttribute("height",!0).setValue(e);var w=a.getStyle("transform",!0,!0);w.setValue("".concat(w.getString()," scale(").concat(1/s,", ").concat(1/t,")"))}c||k.clearRect(0,0,p,q),a.render(k),l&&(this.isFirstRender=!1)}}ak.defaultWindow=ai,ak.defaultFetch=aj;var{defaultFetch:al}=ak,am="undefined"!=typeof DOMParser?DOMParser:null;class an{constructor(){var{fetch:a=al,DOMParser:b=am}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=a,this.DOMParser=b}parse(a){var b=this;return d(function*(){return a.startsWith("<")?b.parseFromString(a):b.load(a)})()}parseFromString(a){var b=new this.DOMParser;try{return this.checkDocument(b.parseFromString(a,"image/svg+xml"))}catch(c){return this.checkDocument(b.parseFromString(a,"text/xml"))}}checkDocument(a){var b=a.getElementsByTagName("parsererror")[0];if(b)throw Error(b.textContent);return a}load(a){var b=this;return d(function*(){var c=yield b.fetch(a),d=yield c.text();return b.parseFromString(d)})()}}class ao{constructor(a,b){this.type="translate",this.point=null,this.point=ag.parse(b)}apply(a){var{x:b,y:c}=this.point;a.translate(b||0,c||0)}unapply(a){var{x:b,y:c}=this.point;a.translate(-1*b||0,-1*c||0)}applyToPoint(a){var{x:b,y:c}=this.point;a.applyTransform([1,0,0,1,b||0,c||0])}}class ap{constructor(a,b,c){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var d=H(b);this.angle=new ae(a,"angle",d[0]),this.originX=c[0],this.originY=c[1],this.cx=d[1]||0,this.cy=d[2]||0}apply(a){var{cx:b,cy:c,originX:d,originY:e,angle:f}=this,g=b+d.getPixels("x"),h=c+e.getPixels("y");a.translate(g,h),a.rotate(f.getRadians()),a.translate(-g,-h)}unapply(a){var{cx:b,cy:c,originX:d,originY:e,angle:f}=this,g=b+d.getPixels("x"),h=c+e.getPixels("y");a.translate(g,h),a.rotate(-1*f.getRadians()),a.translate(-g,-h)}applyToPoint(a){var{cx:b,cy:c,angle:d}=this,e=d.getRadians();a.applyTransform([1,0,0,1,b||0,c||0]),a.applyTransform([Math.cos(e),Math.sin(e),-Math.sin(e),Math.cos(e),0,0]),a.applyTransform([1,0,0,1,-b||0,-c||0])}}class aq{constructor(a,b,c){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var d=ag.parseScale(b);(0===d.x||0===d.y)&&(d.x=V,d.y=V),this.scale=d,this.originX=c[0],this.originY=c[1]}apply(a){var{scale:{x:b,y:c},originX:d,originY:e}=this,f=d.getPixels("x"),g=e.getPixels("y");a.translate(f,g),a.scale(b,c||b),a.translate(-f,-g)}unapply(a){var{scale:{x:b,y:c},originX:d,originY:e}=this,f=d.getPixels("x"),g=e.getPixels("y");a.translate(f,g),a.scale(1/b,1/c||b),a.translate(-f,-g)}applyToPoint(a){var{x:b,y:c}=this.scale;a.applyTransform([b||0,0,0,c||0,0,0])}}class ar{constructor(a,b,c){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=H(b),this.originX=c[0],this.originY=c[1]}apply(a){var{originX:b,originY:c,matrix:d}=this,e=b.getPixels("x"),f=c.getPixels("y");a.translate(e,f),a.transform(d[0],d[1],d[2],d[3],d[4],d[5]),a.translate(-e,-f)}unapply(a){var{originX:b,originY:c,matrix:d}=this,e=d[0],f=d[2],g=d[4],h=d[1],i=d[3],j=d[5],k=1/(e*(i-0*j)-f*(h-0*j)+g*(0*h-0*i)),l=b.getPixels("x"),m=c.getPixels("y");a.translate(l,m),a.transform(k*(i-0*j),k*(0*j-h),k*(0*g-f),k*(e-0*g),k*(f*j-g*i),k*(g*h-e*j)),a.translate(-l,-m)}applyToPoint(a){a.applyTransform(this.matrix)}}class as extends ar{constructor(a,b,c){super(a,b,c),this.type="skew",this.angle=null,this.angle=new ae(a,"angle",b)}}class at extends as{constructor(a,b,c){super(a,b,c),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class au extends as{constructor(a,b,c){super(a,b,c),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class av{constructor(a,b,c){this.document=a,this.transforms=[],E(b).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/).forEach(a=>{if("none"!==a){var[b,d]=function(a){var[b,c]=a.split("(");return[b.trim(),c.trim().replace(")","")]}(a),e=av.transformTypes[b];void 0!==e&&this.transforms.push(new e(this.document,d,c))}})}static fromElement(a,b){var c=b.getStyle("transform",!1,!0),[d,e=d]=b.getStyle("transform-origin",!1,!0).split();return c.hasValue()?new av(a,c.getString(),[d,e]):null}apply(a){for(var{transforms:b}=this,c=b.length,d=0;d<c;d++)b[d].apply(a)}unapply(a){for(var{transforms:b}=this,c=b.length,d=c-1;d>=0;d--)b[d].unapply(a)}applyToPoint(a){for(var{transforms:b}=this,c=b.length,d=0;d<c;d++)b[d].applyToPoint(a)}}av.transformTypes={translate:ao,rotate:ap,scale:aq,matrix:ar,skewX:at,skewY:au};class aw{constructor(a,b){var c=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=a,this.node=b,this.captureTextNodes=c,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],!b||1!==b.nodeType)return;Array.from(b.attributes).forEach(b=>{var c=J(b.nodeName);this.attributes[c]=new ae(a,c,b.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()&&this.getAttribute("style").getString().split(";").map(a=>a.trim()).forEach(b=>{if(b){var[c,d]=b.split(":").map(a=>a.trim());this.styles[c]=new ae(a,c,d)}});var{definitions:d}=a,e=this.getAttribute("id");e.hasValue()&&!d[e.getString()]&&(d[e.getString()]=this),Array.from(b.childNodes).forEach(b=>{if(1===b.nodeType)this.addChild(b);else if(c&&(3===b.nodeType||4===b.nodeType)){var d=a.createTextNode(b);d.getText().length>0&&this.addChild(d)}})}getAttribute(a){var b=arguments.length>1&&void 0!==arguments[1]&&arguments[1],c=this.attributes[a];if(!c&&b){var d=new ae(this.document,a,"");return this.attributes[a]=d,d}return c||ae.empty(this.document)}getHrefAttribute(){for(var a in this.attributes)if("href"===a||a.endsWith(":href"))return this.attributes[a];return ae.empty(this.document)}getStyle(a){var b=arguments.length>1&&void 0!==arguments[1]&&arguments[1],c=arguments.length>2&&void 0!==arguments[2]&&arguments[2],d=this.styles[a];if(d)return d;var e=this.getAttribute(a);if(null!=e&&e.hasValue())return this.styles[a]=e,e;if(!c){var{parent:f}=this;if(f){var g=f.getStyle(a);if(null!=g&&g.hasValue())return g}}if(b){var h=new ae(this.document,a,"");return this.styles[a]=h,h}return d||ae.empty(this.document)}render(a){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(a.save(),this.getStyle("mask").hasValue()){var b=this.getStyle("mask").getDefinition();b&&(this.applyEffects(a),b.apply(a,this))}else if("none"!==this.getStyle("filter").getValue("none")){var c=this.getStyle("filter").getDefinition();c&&(this.applyEffects(a),c.apply(a,this))}else this.setContext(a),this.renderChildren(a),this.clearContext(a);a.restore()}}setContext(a){}applyEffects(a){var b=av.fromElement(this.document,this);b&&b.apply(a);var c=this.getStyle("clip-path",!1,!0);if(c.hasValue()){var d=c.getDefinition();d&&d.apply(a)}}clearContext(a){}renderChildren(a){this.children.forEach(b=>{b.render(a)})}addChild(a){var b=a instanceof aw?a:this.document.createElement(a);b.parent=this,aw.ignoreChildTypes.includes(b.type)||this.children.push(b)}matchesSelector(a){var b,{node:c}=this;if("function"==typeof c.matches)return c.matches(a);var d=null==(b=c.getAttribute)?void 0:b.call(c,"class");return!!d&&""!==d&&d.split(" ").some(b=>".".concat(b)===a)}addStylesFromStyleDefinition(){var{styles:a,stylesSpecificity:b}=this.document;for(var c in a)if(!c.startsWith("@")&&this.matchesSelector(c)){var d=a[c],e=b[c];if(d)for(var f in d){var g=this.stylesSpecificity[f];void 0===g&&(g="000"),e>=g&&(this.styles[f]=d[f],this.stylesSpecificity[f]=e)}}}removeStyles(a,b){return b.reduce((b,c)=>{var d=a.getStyle(c);if(!d.hasValue())return b;var e=d.getString();return d.setValue(""),[...b,[c,e]]},[])}restoreStyles(a,b){b.forEach(b=>{var[c,d]=b;a.getStyle(c,!0).setValue(d)})}isFirstChild(){var a;return(null==(a=this.parent)?void 0:a.children.indexOf(this))===0}}aw.ignoreChildTypes=["title"];class ax extends aw{constructor(a,b,c){super(a,b,c)}}function ay(a){var b=a.trim();return/^('|")/.test(b)?b:'"'.concat(b,'"')}class az{constructor(a,b,c,d,e,f){var g=f?"string"==typeof f?az.parse(f):f:{};this.fontFamily=e||g.fontFamily,this.fontSize=d||g.fontSize,this.fontStyle=a||g.fontStyle,this.fontWeight=c||g.fontWeight,this.fontVariant=b||g.fontVariant}static parse(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",b=arguments.length>1?arguments[1]:void 0,c="",d="",e="",f="",g="",h=E(a).trim().split(" "),i={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return h.forEach(a=>{switch(!0){case!i.fontStyle&&az.styles.includes(a):"inherit"!==a&&(c=a),i.fontStyle=!0;break;case!i.fontVariant&&az.variants.includes(a):"inherit"!==a&&(d=a),i.fontStyle=!0,i.fontVariant=!0;break;case!i.fontWeight&&az.weights.includes(a):"inherit"!==a&&(e=a),i.fontStyle=!0,i.fontVariant=!0,i.fontWeight=!0;break;case!i.fontSize:"inherit"!==a&&([f]=a.split("/")),i.fontStyle=!0,i.fontVariant=!0,i.fontWeight=!0,i.fontSize=!0;break;default:"inherit"!==a&&(g+=a)}}),new az(c,d,e,f,g,b)}toString(){var a;return[function(a){if(!a)return"";var b=a.trim().toLowerCase();switch(b){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return b;default:if(/^oblique\s+(-|)\d+deg$/.test(b))return b;return""}}(this.fontStyle),this.fontVariant,function(a){if(!a)return"";var b=a.trim().toLowerCase();switch(b){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return b;default:if(/^[\d.]+$/.test(b))return b;return""}}(this.fontWeight),this.fontSize,(a=this.fontFamily,"undefined"==typeof process?a:a.trim().split(",").map(ay).join(","))].join(" ").trim()}}az.styles="normal|italic|oblique|inherit",az.variants="normal|small-caps|inherit",az.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class aA{constructor(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:NaN,b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:NaN,c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:NaN,d=arguments.length>3&&void 0!==arguments[3]?arguments[3]:NaN;this.x1=a,this.y1=b,this.x2=c,this.y2=d,this.addPoint(a,b),this.addPoint(c,d)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(a,b){void 0!==a&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=a,this.x2=a),a<this.x1&&(this.x1=a),a>this.x2&&(this.x2=a)),void 0!==b&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=b,this.y2=b),b<this.y1&&(this.y1=b),b>this.y2&&(this.y2=b))}addX(a){this.addPoint(a,null)}addY(a){this.addPoint(null,a)}addBoundingBox(a){if(a){var{x1:b,y1:c,x2:d,y2:e}=a;this.addPoint(b,c),this.addPoint(d,e)}}sumCubic(a,b,c,d,e){return Math.pow(1-a,3)*b+3*Math.pow(1-a,2)*a*c+3*(1-a)*Math.pow(a,2)*d+Math.pow(a,3)*e}bezierCurveAdd(a,b,c,d,e){var f=6*b-12*c+6*d,g=-3*b+9*c-9*d+3*e,h=3*c-3*b;if(0===g){if(0===f)return;var i=-h/f;0<i&&i<1&&(a?this.addX(this.sumCubic(i,b,c,d,e)):this.addY(this.sumCubic(i,b,c,d,e)));return}var j=Math.pow(f,2)-4*h*g;if(!(j<0)){var k=(-f+Math.sqrt(j))/(2*g);0<k&&k<1&&(a?this.addX(this.sumCubic(k,b,c,d,e)):this.addY(this.sumCubic(k,b,c,d,e)));var l=(-f-Math.sqrt(j))/(2*g);0<l&&l<1&&(a?this.addX(this.sumCubic(l,b,c,d,e)):this.addY(this.sumCubic(l,b,c,d,e)))}}addBezierCurve(a,b,c,d,e,f,g,h){this.addPoint(a,b),this.addPoint(g,h),this.bezierCurveAdd(!0,a,c,e,g),this.bezierCurveAdd(!1,b,d,f,h)}addQuadraticCurve(a,b,c,d,e,f){var g=a+2/3*(c-a),h=b+2/3*(d-b);this.addBezierCurve(a,b,g,g+1/3*(e-a),h,h+1/3*(f-b),e,f)}isPointInBox(a,b){var{x1:c,y1:d,x2:e,y2:f}=this;return c<=a&&a<=e&&d<=b&&b<=f}}class aB extends x{constructor(a){super(a.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new ag(0,0),this.control=new ag(0,0),this.current=new ag(0,0),this.points=[],this.angles=[]}isEnd(){var{i:a,commands:b}=this;return a>=b.length-1}next(){var a=this.commands[++this.i];return this.previousCommand=this.command,this.command=a,a}getPoint(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",c=new ag(this.command[a],this.command[b]);return this.makeAbsolute(c)}getAsControlPoint(a,b){var c=this.getPoint(a,b);return this.control=c,c}getAsCurrentPoint(a,b){var c=this.getPoint(a,b);return this.current=c,c}getReflectedControlPoint(){var a=this.previousCommand.type;if(a!==x.CURVE_TO&&a!==x.SMOOTH_CURVE_TO&&a!==x.QUAD_TO&&a!==x.SMOOTH_QUAD_TO)return this.current;var{current:{x:b,y:c},control:{x:d,y:e}}=this;return new ag(2*b-d,2*c-e)}makeAbsolute(a){if(this.command.relative){var{x:b,y:c}=this.current;a.x+=b,a.y+=c}return a}addMarker(a,b,c){var{points:d,angles:e}=this;c&&e.length>0&&!e[e.length-1]&&(e[e.length-1]=d[d.length-1].angleTo(c)),this.addMarkerAngle(a,b?b.angleTo(a):null)}addMarkerAngle(a,b){this.points.push(a),this.angles.push(b)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:a}=this,b=a.length,c=0;c<b;c++)if(!a[c]){for(var d=c+1;d<b;d++)if(a[d]){a[c]=a[d];break}}return a}}class aC extends aw{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var a=1,b=this;b;){var c=b.getStyle("opacity",!1,!0);c.hasValue(!0)&&(a*=c.getNumber()),b=b.parent}return a}setContext(a){var b=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!b){var c=this.getStyle("fill"),d=this.getStyle("fill-opacity"),e=this.getStyle("stroke"),f=this.getStyle("stroke-opacity");if(c.isUrlDefinition()){var g=c.getFillStyleDefinition(this,d);g&&(a.fillStyle=g)}else if(c.hasValue()){"currentColor"===c.getString()&&c.setValue(this.getStyle("color").getColor());var h=c.getColor();"inherit"!==h&&(a.fillStyle="none"===h?"rgba(0,0,0,0)":h)}if(d.hasValue()){var i=new ae(this.document,"fill",a.fillStyle).addOpacity(d).getColor();a.fillStyle=i}if(e.isUrlDefinition()){var j=e.getFillStyleDefinition(this,f);j&&(a.strokeStyle=j)}else if(e.hasValue()){"currentColor"===e.getString()&&e.setValue(this.getStyle("color").getColor());var k=e.getString();"inherit"!==k&&(a.strokeStyle="none"===k?"rgba(0,0,0,0)":k)}if(f.hasValue()){var l=new ae(this.document,"stroke",a.strokeStyle).addOpacity(f).getString();a.strokeStyle=l}var m=this.getStyle("stroke-width");m.hasValue()&&(a.lineWidth=m.getPixels()||V);var n=this.getStyle("stroke-linecap"),o=this.getStyle("stroke-linejoin"),p=this.getStyle("stroke-miterlimit"),q=this.getStyle("stroke-dasharray"),r=this.getStyle("stroke-dashoffset");if(n.hasValue()&&(a.lineCap=n.getString()),o.hasValue()&&(a.lineJoin=o.getString()),p.hasValue()&&(a.miterLimit=p.getNumber()),q.hasValue()&&"none"!==q.getString()){var s=H(q.getString());void 0!==a.setLineDash?a.setLineDash(s):void 0!==a.webkitLineDash?a.webkitLineDash=s:void 0!==a.mozDash&&(1!==s.length||0!==s[0])&&(a.mozDash=s);var t=r.getPixels();void 0!==a.lineDashOffset?a.lineDashOffset=t:void 0!==a.webkitLineDashOffset?a.webkitLineDashOffset=t:void 0!==a.mozDashOffset&&(a.mozDashOffset=t)}}if(this.modifiedEmSizeStack=!1,void 0!==a.font){var u=this.getStyle("font"),v=this.getStyle("font-style"),w=this.getStyle("font-variant"),x=this.getStyle("font-weight"),y=this.getStyle("font-size"),z=this.getStyle("font-family"),A=new az(v.getString(),w.getString(),x.getString(),y.hasValue()?"".concat(y.getPixels(!0),"px"):"",z.getString(),az.parse(u.getString(),a.font));v.setValue(A.fontStyle),w.setValue(A.fontVariant),x.setValue(A.fontWeight),y.setValue(A.fontSize),z.setValue(A.fontFamily),a.font=A.toString(),y.isPixels()&&(this.document.emSize=y.getPixels(),this.modifiedEmSizeStack=!0)}b||(this.applyEffects(a),a.globalAlpha=this.calculateOpacity())}clearContext(a){super.clearContext(a),this.modifiedEmSizeStack&&this.document.popEmSize()}}class aD extends aC{constructor(a,b,c){super(a,b,c),this.type="path",this.pathParser=null,this.pathParser=new aB(this.getAttribute("d").getString())}path(a){var{pathParser:b}=this,c=new aA;for(b.reset(),a&&a.beginPath();!b.isEnd();)switch(b.next().type){case aB.MOVE_TO:this.pathM(a,c);break;case aB.LINE_TO:this.pathL(a,c);break;case aB.HORIZ_LINE_TO:this.pathH(a,c);break;case aB.VERT_LINE_TO:this.pathV(a,c);break;case aB.CURVE_TO:this.pathC(a,c);break;case aB.SMOOTH_CURVE_TO:this.pathS(a,c);break;case aB.QUAD_TO:this.pathQ(a,c);break;case aB.SMOOTH_QUAD_TO:this.pathT(a,c);break;case aB.ARC:this.pathA(a,c);break;case aB.CLOSE_PATH:this.pathZ(a,c)}return c}getBoundingBox(a){return this.path()}getMarkers(){var{pathParser:a}=this,b=a.getMarkerPoints(),c=a.getMarkerAngles();return b.map((a,b)=>[a,c[b]])}renderChildren(a){this.path(a),this.document.screen.mouse.checkPath(this,a);var b=this.getStyle("fill-rule");""!==a.fillStyle&&("inherit"!==b.getString("inherit")?a.fill(b.getString()):a.fill()),""!==a.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(a.save(),a.setTransform(1,0,0,1,0,0),a.stroke(),a.restore()):a.stroke());var c=this.getMarkers();if(c){var d=c.length-1,e=this.getStyle("marker-start"),f=this.getStyle("marker-mid"),g=this.getStyle("marker-end");if(e.isUrlDefinition()){var h=e.getDefinition(),[i,j]=c[0];h.render(a,i,j)}if(f.isUrlDefinition())for(var k=f.getDefinition(),l=1;l<d;l++){var[m,n]=c[l];k.render(a,m,n)}if(g.isUrlDefinition()){var o=g.getDefinition(),[p,q]=c[d];o.render(a,p,q)}}}static pathM(a){var b=a.getAsCurrentPoint();return a.start=a.current,{point:b}}pathM(a,b){var{pathParser:c}=this,{point:d}=aD.pathM(c),{x:e,y:f}=d;c.addMarker(d),b.addPoint(e,f),a&&a.moveTo(e,f)}static pathL(a){var{current:b}=a;return{current:b,point:a.getAsCurrentPoint()}}pathL(a,b){var{pathParser:c}=this,{current:d,point:e}=aD.pathL(c),{x:f,y:g}=e;c.addMarker(e,d),b.addPoint(f,g),a&&a.lineTo(f,g)}static pathH(a){var{current:b,command:c}=a,d=new ag((c.relative?b.x:0)+c.x,b.y);return a.current=d,{current:b,point:d}}pathH(a,b){var{pathParser:c}=this,{current:d,point:e}=aD.pathH(c),{x:f,y:g}=e;c.addMarker(e,d),b.addPoint(f,g),a&&a.lineTo(f,g)}static pathV(a){var{current:b,command:c}=a,d=new ag(b.x,(c.relative?b.y:0)+c.y);return a.current=d,{current:b,point:d}}pathV(a,b){var{pathParser:c}=this,{current:d,point:e}=aD.pathV(c),{x:f,y:g}=e;c.addMarker(e,d),b.addPoint(f,g),a&&a.lineTo(f,g)}static pathC(a){var{current:b}=a,c=a.getPoint("x1","y1");return{current:b,point:c,controlPoint:a.getAsControlPoint("x2","y2"),currentPoint:a.getAsCurrentPoint()}}pathC(a,b){var{pathParser:c}=this,{current:d,point:e,controlPoint:f,currentPoint:g}=aD.pathC(c);c.addMarker(g,f,e),b.addBezierCurve(d.x,d.y,e.x,e.y,f.x,f.y,g.x,g.y),a&&a.bezierCurveTo(e.x,e.y,f.x,f.y,g.x,g.y)}static pathS(a){var{current:b}=a,c=a.getReflectedControlPoint();return{current:b,point:c,controlPoint:a.getAsControlPoint("x2","y2"),currentPoint:a.getAsCurrentPoint()}}pathS(a,b){var{pathParser:c}=this,{current:d,point:e,controlPoint:f,currentPoint:g}=aD.pathS(c);c.addMarker(g,f,e),b.addBezierCurve(d.x,d.y,e.x,e.y,f.x,f.y,g.x,g.y),a&&a.bezierCurveTo(e.x,e.y,f.x,f.y,g.x,g.y)}static pathQ(a){var{current:b}=a;return{current:b,controlPoint:a.getAsControlPoint("x1","y1"),currentPoint:a.getAsCurrentPoint()}}pathQ(a,b){var{pathParser:c}=this,{current:d,controlPoint:e,currentPoint:f}=aD.pathQ(c);c.addMarker(f,e,e),b.addQuadraticCurve(d.x,d.y,e.x,e.y,f.x,f.y),a&&a.quadraticCurveTo(e.x,e.y,f.x,f.y)}static pathT(a){var{current:b}=a,c=a.getReflectedControlPoint();return a.control=c,{current:b,controlPoint:c,currentPoint:a.getAsCurrentPoint()}}pathT(a,b){var{pathParser:c}=this,{current:d,controlPoint:e,currentPoint:f}=aD.pathT(c);c.addMarker(f,e,e),b.addQuadraticCurve(d.x,d.y,e.x,e.y,f.x,f.y),a&&a.quadraticCurveTo(e.x,e.y,f.x,f.y)}static pathA(a){var{current:b,command:c}=a,{rX:d,rY:e,xRot:f,lArcFlag:g,sweepFlag:h}=c,i=Math.PI/180*f,j=a.getAsCurrentPoint(),k=new ag(Math.cos(i)*(b.x-j.x)/2+Math.sin(i)*(b.y-j.y)/2,-Math.sin(i)*(b.x-j.x)/2+Math.cos(i)*(b.y-j.y)/2),l=Math.pow(k.x,2)/Math.pow(d,2)+Math.pow(k.y,2)/Math.pow(e,2);l>1&&(d*=Math.sqrt(l),e*=Math.sqrt(l));var m=(g===h?-1:1)*Math.sqrt((Math.pow(d,2)*Math.pow(e,2)-Math.pow(d,2)*Math.pow(k.y,2)-Math.pow(e,2)*Math.pow(k.x,2))/(Math.pow(d,2)*Math.pow(k.y,2)+Math.pow(e,2)*Math.pow(k.x,2)));isNaN(m)&&(m=0);var n=new ag(m*d*k.y/e,-(m*e)*k.x/d),o=new ag((b.x+j.x)/2+Math.cos(i)*n.x-Math.sin(i)*n.y,(b.y+j.y)/2+Math.sin(i)*n.x+Math.cos(i)*n.y),p=Y([1,0],[(k.x-n.x)/d,(k.y-n.y)/e]),q=[(k.x-n.x)/d,(k.y-n.y)/e],r=[(-k.x-n.x)/d,(-k.y-n.y)/e],s=Y(q,r);return -1>=X(q,r)&&(s=Math.PI),X(q,r)>=1&&(s=0),{currentPoint:j,rX:d,rY:e,sweepFlag:h,xAxisRotation:i,centp:o,a1:p,ad:s}}pathA(a,b){var{pathParser:c}=this,{currentPoint:d,rX:e,rY:f,sweepFlag:g,xAxisRotation:h,centp:i,a1:j,ad:k}=aD.pathA(c),l=1-g?1:-1,m=j+k/2*l,n=new ag(i.x+e*Math.cos(m),i.y+f*Math.sin(m));if(c.addMarkerAngle(n,m-l*Math.PI/2),c.addMarkerAngle(d,m-l*Math.PI),b.addPoint(d.x,d.y),a&&!isNaN(j)&&!isNaN(k)){var o=e>f?1:e/f,p=e>f?f/e:1;a.translate(i.x,i.y),a.rotate(h),a.scale(o,p),a.arc(0,0,e>f?e:f,j,j+k,!!(1-g)),a.scale(1/o,1/p),a.rotate(-h),a.translate(-i.x,-i.y)}}static pathZ(a){a.current=a.start}pathZ(a,b){aD.pathZ(this.pathParser),a&&b.x1!==b.x2&&b.y1!==b.y2&&a.closePath()}}class aE extends aD{constructor(a,b,c){super(a,b,c),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class aF extends aC{constructor(a,b,c){super(a,b,new.target===aF||c),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(a){var b=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(a,b);var c=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();c&&(a.textBaseline=c)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=1/0,this.maxX=-1/0}getBoundingBox(a){if("text"!==this.type)return this.getTElementBoundingBox(a);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(a);var b=null;return this.children.forEach((c,d)=>{var e=this.getChildBoundingBox(a,this,this,d);b?b.addBoundingBox(e):b=e}),b}getFontSize(){var{document:a,parent:b}=this,c=az.parse(a.ctx.font).fontSize;return b.getStyle("font-size").getNumber(c)}getTElementBoundingBox(a){var b=this.getFontSize();return new aA(this.x,this.y-b,this.x+this.measureText(a),this.y)}getGlyph(a,b,c){var d=b[c],e=null;if(a.isArabic){var f=b.length,g=b[c-1],h=b[c+1],i="isolated";if((0===c||" "===g)&&c<f-1&&" "!==h&&(i="terminal"),c>0&&" "!==g&&c<f-1&&" "!==h&&(i="medial"),c>0&&" "!==g&&(c===f-1||" "===h)&&(i="initial"),void 0!==a.glyphs[d]){var j=a.glyphs[d];e=j instanceof aE?j:j[i]}}else e=a.glyphs[d];return e||(e=a.missingGlyph),e}getText(){return""}getTextFromNode(a){var b=a||this.node,c=Array.from(b.parentNode.childNodes),d=c.indexOf(b),e=c.length-1,f=E(b.textContent||"");return 0===d&&(f=F(f)),d===e&&(f=G(f)),f}renderChildren(a){if("text"!==this.type)return void this.renderTElementChildren(a);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(a),this.children.forEach((b,c)=>{this.renderChild(a,this,this,c)});var{mouse:b}=this.document.screen;b.isWorking()&&b.checkBoundingBox(this,this.getBoundingBox(a))}renderTElementChildren(a){var{document:b,parent:c}=this,d=this.getText(),e=c.getStyle("font-family").getDefinition();if(e){for(var{unitsPerEm:f}=e.fontFace,g=az.parse(b.ctx.font),h=c.getStyle("font-size").getNumber(g.fontSize),i=c.getStyle("font-style").getString(g.fontStyle),j=h/f,k=e.isRTL?d.split("").reverse().join(""):d,l=H(c.getAttribute("dx").getString()),m=k.length,n=0;n<m;n++){var o=this.getGlyph(e,k,n);a.translate(this.x,this.y),a.scale(j,-j);var p=a.lineWidth;a.lineWidth=a.lineWidth*f/h,"italic"===i&&a.transform(1,0,.4,1,0,0),o.render(a),"italic"===i&&a.transform(1,0,-.4,1,0,0),a.lineWidth=p,a.scale(1/j,-1/j),a.translate(-this.x,-this.y),this.x+=h*(o.horizAdvX||e.horizAdvX)/f,void 0===l[n]||isNaN(l[n])||(this.x+=l[n])}return}var{x:q,y:r}=this;a.fillStyle&&a.fillText(d,q,r),a.strokeStyle&&a.strokeText(d,q,r)}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var a=this.leafTexts[this.textChunkStart],b=a.getStyle("text-anchor").getString("start"),c=0;c="start"===b?a.x-this.minX:"end"===b?a.x-this.maxX:a.x-(this.minX+this.maxX)/2;for(var d=this.textChunkStart;d<this.leafTexts.length;d++)this.leafTexts[d].x+=c;this.minX=1/0,this.maxX=-1/0,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(a){this.children.forEach((b,c)=>{this.adjustChildCoordinatesRecursiveCore(a,this,this,c)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(a,b,c,d){var e=c.children[d];e.children.length>0?e.children.forEach((c,d)=>{b.adjustChildCoordinatesRecursiveCore(a,b,e,d)}):this.adjustChildCoordinates(a,b,c,d)}adjustChildCoordinates(a,b,c,d){var e=c.children[d];if("function"!=typeof e.measureText)return e;a.save(),e.setContext(a,!0);var f=e.getAttribute("x"),g=e.getAttribute("y"),h=e.getAttribute("dx"),i=e.getAttribute("dy"),j=e.getStyle("font-family").getDefinition(),k=!!j&&j.isRTL;0===d&&(f.hasValue()||f.setValue(e.getInheritedAttribute("x")),g.hasValue()||g.setValue(e.getInheritedAttribute("y")),h.hasValue()||h.setValue(e.getInheritedAttribute("dx")),i.hasValue()||i.setValue(e.getInheritedAttribute("dy")));var l=e.measureText(a);return k&&(b.x-=l),f.hasValue()?(b.applyAnchoring(),e.x=f.getPixels("x"),h.hasValue()&&(e.x+=h.getPixels("x"))):(h.hasValue()&&(b.x+=h.getPixels("x")),e.x=b.x),b.x=e.x,k||(b.x+=l),g.hasValue()?(e.y=g.getPixels("y"),i.hasValue()&&(e.y+=i.getPixels("y"))):(i.hasValue()&&(b.y+=i.getPixels("y")),e.y=b.y),b.y=e.y,b.leafTexts.push(e),b.minX=Math.min(b.minX,e.x,e.x+l),b.maxX=Math.max(b.maxX,e.x,e.x+l),e.clearContext(a),a.restore(),e}getChildBoundingBox(a,b,c,d){var e=c.children[d];if("function"!=typeof e.getBoundingBox)return null;var f=e.getBoundingBox(a);return f?(e.children.forEach((c,d)=>{var g=b.getChildBoundingBox(a,b,e,d);f.addBoundingBox(g)}),f):null}renderChild(a,b,c,d){var e=c.children[d];e.render(a),e.children.forEach((c,d)=>{b.renderChild(a,b,e,d)})}measureText(a){var{measureCache:b}=this;if(~b)return b;var c=this.getText(),d=this.measureTargetText(a,c);return this.measureCache=d,d}measureTargetText(a,b){if(!b.length)return 0;var{parent:c}=this,d=c.getStyle("font-family").getDefinition();if(d){for(var e=this.getFontSize(),f=d.isRTL?b.split("").reverse().join(""):b,g=H(c.getAttribute("dx").getString()),h=f.length,i=0,j=0;j<h;j++)i+=(this.getGlyph(d,f,j).horizAdvX||d.horizAdvX)*e/d.fontFace.unitsPerEm,void 0===g[j]||isNaN(g[j])||(i+=g[j]);return i}if(!a.measureText)return 10*b.length;a.save(),this.setContext(a,!0);var{width:k}=a.measureText(b);return this.clearContext(a),a.restore(),k}getInheritedAttribute(a){for(var b=this;b instanceof aF&&b.isFirstChild();){var c=b.parent.getAttribute(a);if(c.hasValue(!0))return c.getValue("0");b=b.parent}return null}}class aG extends aF{constructor(a,b,c){super(a,b,new.target===aG||c),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class aH extends aG{constructor(){super(...arguments),this.type="textNode"}}class aI extends aC{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(a){var b,{document:c}=this,{screen:d,window:e}=c,f=a.canvas;if(d.setDefaults(a),f.style&&void 0!==a.font&&e&&void 0!==e.getComputedStyle){a.font=e.getComputedStyle(f).getPropertyValue("font");var g=new ae(c,"fontSize",az.parse(a.font).fontSize);g.hasValue()&&(c.rootEmSize=g.getPixels("y"),c.emSize=c.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:h,height:i}=d.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var j=this.getAttribute("refX"),k=this.getAttribute("refY"),l=this.getAttribute("viewBox"),m=l.hasValue()?H(l.getString()):null,n=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),o=0,p=0,q=0,r=0;m&&(o=m[0],p=m[1]),this.root||(h=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y"),"marker"===this.type&&(q=o,r=p,o=0,p=0)),d.viewPort.setCurrent(h,i),this.node&&(!this.parent||(null==(b=this.node.parentNode)?void 0:b.nodeName)==="foreignObject")&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(a),a.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),m&&(h=m[2],i=m[3]),c.setViewBox({ctx:a,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:d.viewPort.width,desiredWidth:h,height:d.viewPort.height,desiredHeight:i,minX:o,minY:p,refX:j.getValue(),refY:k.getValue(),clip:n,clipX:q,clipY:r}),m&&(d.viewPort.removeCurrent(),d.viewPort.setCurrent(h,i))}clearContext(a){super.clearContext(a),this.document.screen.viewPort.removeCurrent()}resize(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,c=arguments.length>2&&void 0!==arguments[2]&&arguments[2],d=this.getAttribute("width",!0),e=this.getAttribute("height",!0),f=this.getAttribute("viewBox"),g=this.getAttribute("style"),h=d.getNumber(0),i=e.getNumber(0);if(c)if("string"==typeof c)this.getAttribute("preserveAspectRatio",!0).setValue(c);else{var j=this.getAttribute("preserveAspectRatio");j.hasValue()&&j.setValue(j.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(d.setValue(a),e.setValue(b),f.hasValue()||f.setValue("0 0 ".concat(h||a," ").concat(i||b)),g.hasValue()){var k=this.getStyle("width"),l=this.getStyle("height");k.hasValue()&&k.setValue("".concat(a,"px")),l.hasValue()&&l.setValue("".concat(b,"px"))}}}class aJ extends aD{constructor(){super(...arguments),this.type="rect"}path(a){var b=this.getAttribute("x").getPixels("x"),c=this.getAttribute("y").getPixels("y"),d=this.getStyle("width",!1,!0).getPixels("x"),e=this.getStyle("height",!1,!0).getPixels("y"),f=this.getAttribute("rx"),g=this.getAttribute("ry"),h=f.getPixels("x"),i=g.getPixels("y");if(f.hasValue()&&!g.hasValue()&&(i=h),g.hasValue()&&!f.hasValue()&&(h=i),h=Math.min(h,d/2),i=Math.min(i,e/2),a){var j=(Math.sqrt(2)-1)/3*4;a.beginPath(),e>0&&d>0&&(a.moveTo(b+h,c),a.lineTo(b+d-h,c),a.bezierCurveTo(b+d-h+j*h,c,b+d,c+i-j*i,b+d,c+i),a.lineTo(b+d,c+e-i),a.bezierCurveTo(b+d,c+e-i+j*i,b+d-h+j*h,c+e,b+d-h,c+e),a.lineTo(b+h,c+e),a.bezierCurveTo(b+h-j*h,c+e,b,c+e-i+j*i,b,c+e-i),a.lineTo(b,c+i),a.bezierCurveTo(b,c+i-j*i,b+h-j*h,c,b+h,c),a.closePath())}return new aA(b,c,b+d,c+e)}getMarkers(){return null}}class aK extends aD{constructor(){super(...arguments),this.type="circle"}path(a){var b=this.getAttribute("cx").getPixels("x"),c=this.getAttribute("cy").getPixels("y"),d=this.getAttribute("r").getPixels();return a&&d>0&&(a.beginPath(),a.arc(b,c,d,0,2*Math.PI,!1),a.closePath()),new aA(b-d,c-d,b+d,c+d)}getMarkers(){return null}}class aL extends aD{constructor(){super(...arguments),this.type="ellipse"}path(a){var b=(Math.sqrt(2)-1)/3*4,c=this.getAttribute("rx").getPixels("x"),d=this.getAttribute("ry").getPixels("y"),e=this.getAttribute("cx").getPixels("x"),f=this.getAttribute("cy").getPixels("y");return a&&c>0&&d>0&&(a.beginPath(),a.moveTo(e+c,f),a.bezierCurveTo(e+c,f+b*d,e+b*c,f+d,e,f+d),a.bezierCurveTo(e-b*c,f+d,e-c,f+b*d,e-c,f),a.bezierCurveTo(e-c,f-b*d,e-b*c,f-d,e,f-d),a.bezierCurveTo(e+b*c,f-d,e+c,f-b*d,e+c,f),a.closePath()),new aA(e-c,f-d,e+c,f+d)}getMarkers(){return null}}class aM extends aD{constructor(){super(...arguments),this.type="line"}getPoints(){return[new ag(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new ag(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(a){var[{x:b,y:c},{x:d,y:e}]=this.getPoints();return a&&(a.beginPath(),a.moveTo(b,c),a.lineTo(d,e)),new aA(b,c,d,e)}getMarkers(){var[a,b]=this.getPoints(),c=a.angleTo(b);return[[a,c],[b,c]]}}class aN extends aD{constructor(a,b,c){super(a,b,c),this.type="polyline",this.points=[],this.points=ag.parsePath(this.getAttribute("points").getString())}path(a){var{points:b}=this,[{x:c,y:d}]=b,e=new aA(c,d);return a&&(a.beginPath(),a.moveTo(c,d)),b.forEach(b=>{var{x:c,y:d}=b;e.addPoint(c,d),a&&a.lineTo(c,d)}),e}getMarkers(){var{points:a}=this,b=a.length-1,c=[];return a.forEach((d,e)=>{e!==b&&c.push([d,d.angleTo(a[e+1])])}),c.length>0&&c.push([a[a.length-1],c[c.length-1][1]]),c}}class aO extends aN{constructor(){super(...arguments),this.type="polygon"}path(a){var b=super.path(a),[{x:c,y:d}]=this.points;return a&&(a.lineTo(c,d),a.closePath()),b}}class aP extends aw{constructor(){super(...arguments),this.type="pattern"}createPattern(a,b,c){var d=this.getStyle("width").getPixels("x",!0),e=this.getStyle("height").getPixels("y",!0),f=new aI(this.document,null);f.attributes.viewBox=new ae(this.document,"viewBox",this.getAttribute("viewBox").getValue()),f.attributes.width=new ae(this.document,"width","".concat(d,"px")),f.attributes.height=new ae(this.document,"height","".concat(e,"px")),f.attributes.transform=new ae(this.document,"transform",this.getAttribute("patternTransform").getValue()),f.children=this.children;var g=this.document.createCanvas(d,e),h=g.getContext("2d"),i=this.getAttribute("x"),j=this.getAttribute("y");i.hasValue()&&j.hasValue()&&h.translate(i.getPixels("x",!0),j.getPixels("y",!0)),c.hasValue()?this.styles["fill-opacity"]=c:Reflect.deleteProperty(this.styles,"fill-opacity");for(var k=-1;k<=1;k++)for(var l=-1;l<=1;l++)h.save(),f.attributes.x=new ae(this.document,"x",k*g.width),f.attributes.y=new ae(this.document,"y",l*g.height),f.render(h),h.restore();return a.createPattern(g,"repeat")}}class aQ extends aw{constructor(){super(...arguments),this.type="marker"}render(a,b,c){if(b){var{x:d,y:e}=b,f=this.getAttribute("orient").getString("auto"),g=this.getAttribute("markerUnits").getString("strokeWidth");a.translate(d,e),"auto"===f&&a.rotate(c),"strokeWidth"===g&&a.scale(a.lineWidth,a.lineWidth),a.save();var h=new aI(this.document,null);h.type=this.type,h.attributes.viewBox=new ae(this.document,"viewBox",this.getAttribute("viewBox").getValue()),h.attributes.refX=new ae(this.document,"refX",this.getAttribute("refX").getValue()),h.attributes.refY=new ae(this.document,"refY",this.getAttribute("refY").getValue()),h.attributes.width=new ae(this.document,"width",this.getAttribute("markerWidth").getValue()),h.attributes.height=new ae(this.document,"height",this.getAttribute("markerHeight").getValue()),h.attributes.overflow=new ae(this.document,"overflow",this.getAttribute("overflow").getValue()),h.attributes.fill=new ae(this.document,"fill",this.getAttribute("fill").getColor("black")),h.attributes.stroke=new ae(this.document,"stroke",this.getAttribute("stroke").getValue("none")),h.children=this.children,h.render(a),a.restore(),"strokeWidth"===g&&a.scale(1/a.lineWidth,1/a.lineWidth),"auto"===f&&a.rotate(-c),a.translate(-d,-e)}}}class aR extends aw{constructor(){super(...arguments),this.type="defs"}render(){}}class aS extends aC{constructor(){super(...arguments),this.type="g"}getBoundingBox(a){var b=new aA;return this.children.forEach(c=>{b.addBoundingBox(c.getBoundingBox(a))}),b}}class aT extends aw{constructor(a,b,c){super(a,b,c),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:d,children:e}=this;e.forEach(a=>{"stop"===a.type&&d.push(a)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(a,b,c){var d=this;this.getHrefAttribute().hasValue()&&(d=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(d));var{stops:e}=d,f=this.getGradient(a,b);if(!f)return this.addParentOpacity(c,e[e.length-1].color);if(e.forEach(a=>{f.addColorStop(a.offset,this.addParentOpacity(c,a.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:g}=this,{MAX_VIRTUAL_PIXELS:h,viewPort:i}=g.screen,[j]=i.viewPorts,k=new aJ(g,null);k.attributes.x=new ae(g,"x",-h/3),k.attributes.y=new ae(g,"y",-h/3),k.attributes.width=new ae(g,"width",h),k.attributes.height=new ae(g,"height",h);var l=new aS(g,null);l.attributes.transform=new ae(g,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[k];var m=new aI(g,null);m.attributes.x=new ae(g,"x",0),m.attributes.y=new ae(g,"y",0),m.attributes.width=new ae(g,"width",j.width),m.attributes.height=new ae(g,"height",j.height),m.children=[l];var n=g.createCanvas(j.width,j.height),o=n.getContext("2d");return o.fillStyle=f,m.render(o),o.createPattern(n,"no-repeat")}return f}inheritStopContainer(a){this.attributesToInherit.forEach(b=>{!this.getAttribute(b).hasValue()&&a.getAttribute(b).hasValue()&&this.getAttribute(b,!0).setValue(a.getAttribute(b).getValue())})}addParentOpacity(a,b){return a.hasValue()?new ae(this.document,"color",b).addOpacity(a).getColor():b}}class aU extends aT{constructor(a,b,c){super(a,b,c),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(a,b){var c="objectBoundingBox"===this.getGradientUnits(),d=c?b.getBoundingBox(a):null;if(c&&!d)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var e=c?d.x+d.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),f=c?d.y+d.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),g=c?d.x+d.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),h=c?d.y+d.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return e===g&&f===h?null:a.createLinearGradient(e,f,g,h)}}class aV extends aT{constructor(a,b,c){super(a,b,c),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(a,b){var c="objectBoundingBox"===this.getGradientUnits(),d=b.getBoundingBox(a);if(c&&!d)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var e=c?d.x+d.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),f=c?d.y+d.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),g=e,h=f;this.getAttribute("fx").hasValue()&&(g=c?d.x+d.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(h=c?d.y+d.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var i=c?(d.width+d.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),j=this.getAttribute("fr").getPixels();return a.createRadialGradient(g,h,j,e,f,i)}}class aW extends aw{constructor(a,b,c){super(a,b,c),this.type="stop";var d=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),e=this.getStyle("stop-opacity"),f=this.getStyle("stop-color",!0);""===f.getString()&&f.setValue("#000"),e.hasValue()&&(f=f.addOpacity(e)),this.offset=d,this.color=f.getColor()}}class aX extends aw{constructor(a,b,c){super(a,b,c),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,a.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new ae(a,"values",null);var d=this.getAttribute("values");d.hasValue()&&this.values.setValue(d.getString().split(";"))}getProperty(){var a=this.getAttribute("attributeType").getString(),b=this.getAttribute("attributeName").getString();return"CSS"===a?this.parent.getStyle(b,!0):this.parent.getAttribute(b,!0)}calcValue(){var{initialUnits:a}=this,{progress:b,from:c,to:d}=this.getProgress(),e=c.getNumber()+(d.getNumber()-c.getNumber())*b;return"%"===a&&(e*=100),"".concat(e).concat(a)}update(a){var{parent:b}=this,c=this.getProperty();if(this.initialValue||(this.initialValue=c.getString(),this.initialUnits=c.getUnits()),this.duration>this.maxDuration){var d=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==d||this.frozen){if("remove"===d&&!this.removed)return this.removed=!0,c.setValue(b.animationFrozen?b.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,b.animationFrozen=!0,b.animationFrozenValue=c.getString();return!1}this.duration+=a;var e=!1;if(this.begin<this.duration){var f=this.calcValue(),g=this.getAttribute("type");if(g.hasValue()){var h=g.getString();f="".concat(h,"(").concat(f,")")}c.setValue(f),e=!0}return e}getProgress(){var{document:a,values:b}=this,c={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(b.hasValue()){var d=c.progress*(b.getValue().length-1),e=Math.floor(d),f=Math.ceil(d);c.from=new ae(a,"from",parseFloat(b.getValue()[e])),c.to=new ae(a,"to",parseFloat(b.getValue()[f])),c.progress=(d-e)/(f-e)}else c.from=this.from,c.to=this.to;return c}}class aY extends aX{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:a,from:b,to:c}=this.getProgress(),d=new g(b.getColor()),e=new g(c.getColor());if(d.ok&&e.ok){var f=d.r+(e.r-d.r)*a,h=d.g+(e.g-d.g)*a,i=d.b+(e.b-d.b)*a;return"rgb(".concat(Math.floor(f),", ").concat(Math.floor(h),", ").concat(Math.floor(i),")")}return this.getAttribute("from").getColor()}}class aZ extends aX{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:a,from:b,to:c}=this.getProgress(),d=H(b.getString()),e=H(c.getString());return d.map((b,c)=>b+(e[c]-b)*a).join(" ")}}class a$ extends aw{constructor(a,b,c){super(a,b,c),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:d}=a,{children:e}=this;for(var f of e)switch(f.type){case"font-face":this.fontFace=f;var g=f.getStyle("font-family");g.hasValue()&&(d[g.getString()]=this);break;case"missing-glyph":this.missingGlyph=f;break;case"glyph":f.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[f.unicode]&&(this.glyphs[f.unicode]=Object.create(null)),this.glyphs[f.unicode][f.arabicForm]=f):this.glyphs[f.unicode]=f}}render(){}}class a_ extends aw{constructor(a,b,c){super(a,b,c),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class a0 extends aD{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class a1 extends aF{constructor(){super(...arguments),this.type="tref"}getText(){var a=this.getHrefAttribute().getDefinition();if(a){var b=a.children[0];if(b)return b.getText()}return""}}class a2 extends aF{constructor(a,b,c){super(a,b,c),this.type="a";var{childNodes:d}=b,e=d[0],f=d.length>0&&Array.from(d).every(a=>3===a.nodeType);this.hasText=f,this.text=f?this.getTextFromNode(e):""}getText(){return this.text}renderChildren(a){if(this.hasText){super.renderChildren(a);var{document:b,x:c,y:d}=this,{mouse:e}=b.screen,f=new ae(b,"fontSize",az.parse(b.ctx.font).fontSize);e.isWorking()&&e.checkBoundingBox(this,new aA(c,d-f.getPixels("y"),c+this.measureText(a),d))}else if(this.children.length>0){var g=new aS(this.document,null);g.children=this.children,g.parent=this,g.render(a)}}onClick(){var{window:a}=this.document;a&&a.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function a3(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function a4(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?a3(Object(c),!0).forEach(function(b){e(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):a3(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}class a5 extends aF{constructor(a,b,c){super(a,b,c),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var d=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(d)}getText(){return this.text}path(a){var{dataArray:b}=this;a&&a.beginPath(),b.forEach(b=>{var{type:c,points:d}=b;switch(c){case aB.LINE_TO:a&&a.lineTo(d[0],d[1]);break;case aB.MOVE_TO:a&&a.moveTo(d[0],d[1]);break;case aB.CURVE_TO:a&&a.bezierCurveTo(d[0],d[1],d[2],d[3],d[4],d[5]);break;case aB.QUAD_TO:a&&a.quadraticCurveTo(d[0],d[1],d[2],d[3]);break;case aB.ARC:var[e,f,g,h,i,j,k,l]=d,m=g>h?1:g/h,n=g>h?h/g:1;a&&(a.translate(e,f),a.rotate(k),a.scale(m,n),a.arc(0,0,g>h?g:h,i,i+j,!!(1-l)),a.scale(1/m,1/n),a.rotate(-k),a.translate(-e,-f));break;case aB.CLOSE_PATH:a&&a.closePath()}})}renderChildren(a){this.setTextData(a),a.save();var b=this.parent.getStyle("text-decoration").getString(),c=this.getFontSize(),{glyphInfo:d}=this,e=a.fillStyle;"underline"===b&&a.beginPath(),d.forEach((d,e)=>{var{p0:f,p1:g,rotation:h,text:i}=d;a.save(),a.translate(f.x,f.y),a.rotate(h),a.fillStyle&&a.fillText(i,0,0),a.strokeStyle&&a.strokeText(i,0,0),a.restore(),"underline"===b&&(0===e&&a.moveTo(f.x,f.y+c/8),a.lineTo(g.x,g.y+c/5))}),"underline"===b&&(a.lineWidth=c/20,a.strokeStyle=e,a.stroke(),a.closePath()),a.restore()}getLetterSpacingAt(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[a]||0}findSegmentToFitChar(a,b,c,d,e,f,g,h,i){var j=f,k=this.measureText(a,h);" "===h&&"justify"===b&&c<d&&(k+=(d-c)/e),i>-1&&(j+=this.getLetterSpacingAt(i));var l=this.textHeight/20,m=this.getEquidistantPointOnPath(j,l,0),n=this.getEquidistantPointOnPath(j+k,l,0),o={p0:m,p1:n},p=m&&n?Math.atan2(n.y-m.y,n.x-m.x):0;if(g){var q=Math.cos(Math.PI/2+p)*g,r=Math.cos(-p)*g;o.p0=a4(a4({},m),{},{x:m.x+q,y:m.y+r}),o.p1=a4(a4({},n),{},{x:n.x+q,y:n.y+r})}return{offset:j+=k,segment:o,rotation:p}}measureText(a,b){var{measuresCache:c}=this,d=b||this.getText();if(c.has(d))return c.get(d);var e=this.measureTargetText(a,d);return c.set(d,e),e}setTextData(a){if(!this.glyphInfo){var b=this.getText(),c=b.split(""),d=b.split(" ").length-1,e=this.parent.getAttribute("dx").split().map(a=>a.getPixels("x")),f=this.parent.getAttribute("dy").getPixels("y"),g=this.parent.getStyle("text-anchor").getString("start"),h=this.getStyle("letter-spacing"),i=this.parent.getStyle("letter-spacing"),j=0;h.hasValue()&&"inherit"!==h.getValue()?h.hasValue()&&"initial"!==h.getValue()&&"unset"!==h.getValue()&&(j=h.getPixels()):j=i.getPixels();var k=[],l=b.length;this.letterSpacingCache=k;for(var m=0;m<l;m++)k.push(void 0!==e[m]?e[m]:j);var n=k.reduce((a,b,c)=>0===c?0:a+b||0,0),o=this.measureText(a),p=Math.max(o+n,0);this.textWidth=o,this.textHeight=this.getFontSize(),this.glyphInfo=[];var q=this.getPathLength(),r=this.getStyle("startOffset").getNumber(0)*q,s=0;("middle"===g||"center"===g)&&(s=-p/2),("end"===g||"right"===g)&&(s=-p),s+=r,c.forEach((b,e)=>{var{offset:h,segment:i,rotation:j}=this.findSegmentToFitChar(a,g,p,q,d,s,f,b,e);s=h,i.p0&&i.p1&&this.glyphInfo.push({text:c[e],p0:i.p0,p1:i.p1,rotation:j})})}}parsePathData(a){if(this.pathLength=-1,!a)return[];var b=[],{pathParser:c}=a;for(c.reset();!c.isEnd();){var{current:d}=c,e=d?d.x:0,f=d?d.y:0,g=c.next(),h=g.type,i=[];switch(g.type){case aB.MOVE_TO:this.pathM(c,i);break;case aB.LINE_TO:h=this.pathL(c,i);break;case aB.HORIZ_LINE_TO:h=this.pathH(c,i);break;case aB.VERT_LINE_TO:h=this.pathV(c,i);break;case aB.CURVE_TO:this.pathC(c,i);break;case aB.SMOOTH_CURVE_TO:h=this.pathS(c,i);break;case aB.QUAD_TO:this.pathQ(c,i);break;case aB.SMOOTH_QUAD_TO:h=this.pathT(c,i);break;case aB.ARC:i=this.pathA(c);break;case aB.CLOSE_PATH:aD.pathZ(c)}g.type!==aB.CLOSE_PATH?b.push({type:h,points:i,start:{x:e,y:f},pathLength:this.calcLength(e,f,h,i)}):b.push({type:aB.CLOSE_PATH,points:[],pathLength:0})}return b}pathM(a,b){var{x:c,y:d}=aD.pathM(a).point;b.push(c,d)}pathL(a,b){var{x:c,y:d}=aD.pathL(a).point;return b.push(c,d),aB.LINE_TO}pathH(a,b){var{x:c,y:d}=aD.pathH(a).point;return b.push(c,d),aB.LINE_TO}pathV(a,b){var{x:c,y:d}=aD.pathV(a).point;return b.push(c,d),aB.LINE_TO}pathC(a,b){var{point:c,controlPoint:d,currentPoint:e}=aD.pathC(a);b.push(c.x,c.y,d.x,d.y,e.x,e.y)}pathS(a,b){var{point:c,controlPoint:d,currentPoint:e}=aD.pathS(a);return b.push(c.x,c.y,d.x,d.y,e.x,e.y),aB.CURVE_TO}pathQ(a,b){var{controlPoint:c,currentPoint:d}=aD.pathQ(a);b.push(c.x,c.y,d.x,d.y)}pathT(a,b){var{controlPoint:c,currentPoint:d}=aD.pathT(a);return b.push(c.x,c.y,d.x,d.y),aB.QUAD_TO}pathA(a){var{rX:b,rY:c,sweepFlag:d,xAxisRotation:e,centp:f,a1:g,ad:h}=aD.pathA(a);return 0===d&&h>0&&(h-=2*Math.PI),1===d&&h<0&&(h+=2*Math.PI),[f.x,f.y,b,c,g,h,e,d]}calcLength(a,b,c,d){var e=0,f=null,g=null,h=0;switch(c){case aB.LINE_TO:return this.getLineLength(a,b,d[0],d[1]);case aB.CURVE_TO:for(h=.01,e=0,f=this.getPointOnCubicBezier(0,a,b,d[0],d[1],d[2],d[3],d[4],d[5]);h<=1;h+=.01)g=this.getPointOnCubicBezier(h,a,b,d[0],d[1],d[2],d[3],d[4],d[5]),e+=this.getLineLength(f.x,f.y,g.x,g.y),f=g;return e;case aB.QUAD_TO:for(h=.01,e=0,f=this.getPointOnQuadraticBezier(0,a,b,d[0],d[1],d[2],d[3]);h<=1;h+=.01)g=this.getPointOnQuadraticBezier(h,a,b,d[0],d[1],d[2],d[3]),e+=this.getLineLength(f.x,f.y,g.x,g.y),f=g;return e;case aB.ARC:e=0;var i=d[4],j=d[5],k=d[4]+j,l=Math.PI/180;if(Math.abs(i-k)<l&&(l=Math.abs(i-k)),f=this.getPointOnEllipticalArc(d[0],d[1],d[2],d[3],i,0),j<0)for(h=i-l;h>k;h-=l)g=this.getPointOnEllipticalArc(d[0],d[1],d[2],d[3],h,0),e+=this.getLineLength(f.x,f.y,g.x,g.y),f=g;else for(h=i+l;h<k;h+=l)g=this.getPointOnEllipticalArc(d[0],d[1],d[2],d[3],h,0),e+=this.getLineLength(f.x,f.y,g.x,g.y),f=g;return g=this.getPointOnEllipticalArc(d[0],d[1],d[2],d[3],k,0),e+=this.getLineLength(f.x,f.y,g.x,g.y)}return 0}getPointOnLine(a,b,c,d,e){var f=arguments.length>5&&void 0!==arguments[5]?arguments[5]:b,g=arguments.length>6&&void 0!==arguments[6]?arguments[6]:c,h=(e-c)/(d-b+V),i=Math.sqrt(a*a/(1+h*h));d<b&&(i*=-1);var j=h*i,k=null;if(d===b)k={x:f,y:g+j};else if((g-c)/(f-b+V)===h)k={x:f+i,y:g+j};else{var l=0,m=0,n=this.getLineLength(b,c,d,e);if(n<V)return null;var o=(f-b)*(d-b)+(g-c)*(e-c);o/=n*n,l=b+o*(d-b),m=c+o*(e-c);var p=this.getLineLength(f,g,l,m),q=Math.sqrt(a*a-p*p);i=Math.sqrt(q*q/(1+h*h)),d<b&&(i*=-1),j=h*i,k={x:l+i,y:m+j}}return k}getPointOnPath(a){var b=this.getPathLength(),c=0,d=null;if(a<-5e-5||a-5e-5>b)return null;var{dataArray:e}=this;for(var f of e){if(f&&(f.pathLength<5e-5||c+f.pathLength+5e-5<a)){c+=f.pathLength;continue}var g=a-c,h=0;switch(f.type){case aB.LINE_TO:d=this.getPointOnLine(g,f.start.x,f.start.y,f.points[0],f.points[1],f.start.x,f.start.y);break;case aB.ARC:var i=f.points[4],j=f.points[5],k=f.points[4]+j;if(h=i+g/f.pathLength*j,j<0&&h<k||j>=0&&h>k)break;d=this.getPointOnEllipticalArc(f.points[0],f.points[1],f.points[2],f.points[3],h,f.points[6]);break;case aB.CURVE_TO:(h=g/f.pathLength)>1&&(h=1),d=this.getPointOnCubicBezier(h,f.start.x,f.start.y,f.points[0],f.points[1],f.points[2],f.points[3],f.points[4],f.points[5]);break;case aB.QUAD_TO:(h=g/f.pathLength)>1&&(h=1),d=this.getPointOnQuadraticBezier(h,f.start.x,f.start.y,f.points[0],f.points[1],f.points[2],f.points[3])}if(d)return d;break}return null}getLineLength(a,b,c,d){return Math.sqrt((c-a)*(c-a)+(d-b)*(d-b))}getPathLength(){return -1===this.pathLength&&(this.pathLength=this.dataArray.reduce((a,b)=>b.pathLength>0?a+b.pathLength:a,0)),this.pathLength}getPointOnCubicBezier(a,b,c,d,e,f,g,h,i){return{x:h*Z(a)+f*$(a)+d*_(a)+b*aa(a),y:i*Z(a)+g*$(a)+e*_(a)+c*aa(a)}}getPointOnQuadraticBezier(a,b,c,d,e,f,g){return{x:a*a*f+d*ac(a)+b*ad(a),y:a*a*g+e*ac(a)+c*ad(a)}}getPointOnEllipticalArc(a,b,c,d,e,f){var g=Math.cos(f),h=Math.sin(f),i={x:c*Math.cos(e),y:d*Math.sin(e)};return{x:a+(i.x*g-i.y*h),y:b+(i.x*h+i.y*g)}}buildEquidistantCache(a,b){var c=this.getPathLength(),d=b||.25,e=a||c/100;if(!this.equidistantCache||this.equidistantCache.step!==e||this.equidistantCache.precision!==d){this.equidistantCache={step:e,precision:d,points:[]};for(var f=0,g=0;g<=c;g+=d){var h=this.getPointOnPath(g),i=this.getPointOnPath(g+d);h&&i&&(f+=this.getLineLength(h.x,h.y,i.x,i.y))>=e&&(this.equidistantCache.points.push({x:h.x,y:h.y,distance:g}),f-=e)}}}getEquidistantPointOnPath(a,b,c){if(this.buildEquidistantCache(b,c),a<0||a-this.getPathLength()>5e-5)return null;var d=Math.round(a/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[d]||null}}var a6=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class a7 extends aC{constructor(a,b,c){super(a,b,c),this.type="image",this.loaded=!1;var d=this.getHrefAttribute().getString();if(!d)return;var e=d.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(d);a.images.push(this),e?this.loadSvg(d):this.loadImage(d),this.isSvg=e}loadImage(a){var b=this;return d(function*(){try{var c=yield b.document.createImage(a);b.image=c}catch(b){console.error('Error while loading image "'.concat(a,'":'),b)}b.loaded=!0})()}loadSvg(a){var b=this;return d(function*(){var c=a6.exec(a);if(c){var d=c[5];"base64"===c[4]?b.image=atob(d):b.image=decodeURIComponent(d)}else try{var e=yield b.document.fetch(a);b.image=yield e.text()}catch(b){console.error('Error while loading image "'.concat(a,'":'),b)}b.loaded=!0})()}renderChildren(a){var{document:b,image:c,loaded:d}=this,e=this.getAttribute("x").getPixels("x"),f=this.getAttribute("y").getPixels("y"),g=this.getStyle("width").getPixels("x"),h=this.getStyle("height").getPixels("y");if(d&&c&&g&&h){if(a.save(),a.translate(e,f),this.isSvg){var i=b.canvg.forkString(a,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:g,scaleHeight:h});i.document.documentElement.parent=this,i.render()}else{var j=this.image;b.setViewBox({ctx:a,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:g,desiredWidth:j.width,height:h,desiredHeight:j.height}),this.loaded&&(void 0===j.complete||j.complete)&&a.drawImage(j,0,0)}a.restore()}}getBoundingBox(){var a=this.getAttribute("x").getPixels("x"),b=this.getAttribute("y").getPixels("y");return new aA(a,b,a+this.getStyle("width").getPixels("x"),b+this.getStyle("height").getPixels("y"))}}class a8 extends aC{constructor(){super(...arguments),this.type="symbol"}render(a){}}class a9{constructor(a){this.document=a,this.loaded=!1,a.fonts.push(this)}load(a,b){var c=this;return d(function*(){try{var{document:d}=c,e=(yield d.canvg.parser.load(b)).getElementsByTagName("font");Array.from(e).forEach(b=>{var c=d.createElement(b);d.definitions[a]=c})}catch(a){console.error('Error while loading font "'.concat(b,'":'),a)}c.loaded=!0})()}}class ba extends aw{constructor(a,b,c){super(a,b,c),this.type="style",E(Array.from(b.childNodes).map(a=>a.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(b=>{var c=b.trim();if(c){var d=c.split("{"),e=d[0].split(","),f=d[1].split(";");e.forEach(b=>{var c=b.trim();if(c){var d=a.styles[c]||{};if(f.forEach(b=>{var c=b.indexOf(":"),e=b.substr(0,c).trim(),f=b.substr(c+1,b.length-c).trim();e&&f&&(d[e]=new ae(a,e,f))}),a.styles[c]=d,a.stylesSpecificity[c]=U(c),"@font-face"===c){var e=d["font-family"].getString().replace(/"|'/g,"");d.src.getString().split(",").forEach(b=>{if(b.indexOf('format("svg")')>0){var c=K(b);c&&new a9(a).load(e,c)}})}}})}})}}ba.parseExternalUrl=K;class bb extends aC{constructor(){super(...arguments),this.type="use"}setContext(a){super.setContext(a);var b=this.getAttribute("x"),c=this.getAttribute("y");b.hasValue()&&a.translate(b.getPixels("x"),0),c.hasValue()&&a.translate(0,c.getPixels("y"))}path(a){var{element:b}=this;b&&b.path(a)}renderChildren(a){var{document:b,element:c}=this;if(c){var d=c;if("symbol"===c.type&&((d=new aI(b,null)).attributes.viewBox=new ae(b,"viewBox",c.getAttribute("viewBox").getString()),d.attributes.preserveAspectRatio=new ae(b,"preserveAspectRatio",c.getAttribute("preserveAspectRatio").getString()),d.attributes.overflow=new ae(b,"overflow",c.getAttribute("overflow").getString()),d.children=c.children,c.styles.opacity=new ae(b,"opacity",this.calculateOpacity())),"svg"===d.type){var e=this.getStyle("width",!1,!0),f=this.getStyle("height",!1,!0);e.hasValue()&&(d.attributes.width=new ae(b,"width",e.getString())),f.hasValue()&&(d.attributes.height=new ae(b,"height",f.getString()))}var g=d.parent;d.parent=this,d.render(a),d.parent=g}}getBoundingBox(a){var{element:b}=this;return b?b.getBoundingBox(a):null}elementTransform(){var{document:a,element:b}=this;return av.fromElement(a,b)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function bc(a,b,c,d){return b+Math.cos(a)*c+Math.sin(a)*d}class bd extends aw{constructor(a,b,c){super(a,b,c),this.type="feColorMatrix";var d=H(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var e=d[0];d=[.213+.787*e,.715-.715*e,.072-.072*e,0,0,.213-.213*e,.715+.285*e,.072-.072*e,0,0,.213-.213*e,.715-.715*e,.072+.928*e,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var f=d[0]*Math.PI/180;d=[bc(f,.213,.787,-.213),bc(f,.715,-.715,-.715),bc(f,.072,-.072,.928),0,0,bc(f,.213,-.213,.143),bc(f,.715,.285,.14),bc(f,.072,-.072,-.283),0,0,bc(f,.213,-.213,-.787),bc(f,.715,-.715,.715),bc(f,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=d,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(a,b,c,d,e){for(var{includeOpacity:f,matrix:g}=this,h=a.getImageData(0,0,d,e),i=0;i<e;i++)for(var j=0;j<d;j++){var k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E=(k=h.data,k[i*d*4+4*j+0]),F=(l=h.data,l[i*d*4+4*j+1]),G=(m=h.data,m[i*d*4+4*j+2]),H=(n=h.data,n[i*d*4+4*j+3]),I=g[0]*E+g[1]*F+g[2]*G+g[3]*H+ +g[4],J=g[5]*E+g[6]*F+g[7]*G+g[8]*H+ +g[9],K=g[10]*E+g[11]*F+g[12]*G+g[13]*H+ +g[14],L=g[15]*E+g[16]*F+g[17]*G+g[18]*H+ +g[19];f&&(I=0,J=0,K=0,L*=H/255),o=h.data,p=j,q=i,r=I,o[q*d*4+4*p+0]=r,s=h.data,t=j,u=i,v=J,s[u*d*4+4*t+1]=v,w=h.data,x=j,y=i,z=K,w[y*d*4+4*x+2]=z,A=h.data,B=j,C=i,D=L,A[C*d*4+4*B+3]=D}a.clearRect(0,0,d,e),a.putImageData(h,0,0)}}class be extends aw{constructor(){super(...arguments),this.type="mask"}apply(a,b){var{document:c}=this,d=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),f=this.getStyle("width").getPixels("x"),g=this.getStyle("height").getPixels("y");if(!f&&!g){var h=new aA;this.children.forEach(b=>{h.addBoundingBox(b.getBoundingBox(a))}),d=Math.floor(h.x1),e=Math.floor(h.y1),f=Math.floor(h.width),g=Math.floor(h.height)}var i=this.removeStyles(b,be.ignoreStyles),j=c.createCanvas(d+f,e+g),k=j.getContext("2d");c.screen.setDefaults(k),this.renderChildren(k),new bd(c,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(k,0,0,d+f,e+g);var l=c.createCanvas(d+f,e+g),m=l.getContext("2d");c.screen.setDefaults(m),b.render(m),m.globalCompositeOperation="destination-in",m.fillStyle=k.createPattern(j,"no-repeat"),m.fillRect(0,0,d+f,e+g),a.fillStyle=m.createPattern(l,"no-repeat"),a.fillRect(0,0,d+f,e+g),this.restoreStyles(b,i)}render(a){}}be.ignoreStyles=["mask","transform","clip-path"];var bf=()=>{};class bg extends aw{constructor(){super(...arguments),this.type="clipPath"}apply(a){var{document:b}=this,c=Reflect.getPrototypeOf(a),{beginPath:d,closePath:e}=a;c&&(c.beginPath=bf,c.closePath=bf),Reflect.apply(d,a,[]),this.children.forEach(d=>{if(void 0!==d.path){var f=void 0!==d.elementTransform?d.elementTransform():null;f||(f=av.fromElement(b,d)),f&&f.apply(a),d.path(a),c&&(c.closePath=e),f&&f.unapply(a)}}),Reflect.apply(e,a,[]),a.clip(),c&&(c.beginPath=d,c.closePath=e)}render(a){}}class bh extends aw{constructor(){super(...arguments),this.type="filter"}apply(a,b){var{document:c,children:d}=this,e=b.getBoundingBox(a);if(e){var f=0,g=0;d.forEach(a=>{var b=a.extraFilterDistance||0;f=Math.max(f,b),g=Math.max(g,b)});var h=Math.floor(e.width),i=Math.floor(e.height),j=h+2*f,k=i+2*g;if(!(j<1)&&!(k<1)){var l=Math.floor(e.x),m=Math.floor(e.y),n=this.removeStyles(b,bh.ignoreStyles),o=c.createCanvas(j,k),p=o.getContext("2d");c.screen.setDefaults(p),p.translate(-l+f,-m+g),b.render(p),d.forEach(a=>{"function"==typeof a.apply&&a.apply(p,0,0,j,k)}),a.drawImage(o,0,0,j,k,l-f,m-g,j,k),this.restoreStyles(b,n)}}}render(a){}}bh.ignoreStyles=["filter","transform","clip-path"];class bi extends aw{constructor(a,b,c){super(a,b,c),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(a,b,c,d,e){}}class bj extends aw{constructor(){super(...arguments),this.type="feMorphology"}apply(a,b,c,d,e){}}class bk extends aw{constructor(){super(...arguments),this.type="feComposite"}apply(a,b,c,d,e){}}class bl extends aw{constructor(a,b,c){super(a,b,c),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(a,b,c,d,e){var{document:f,blurRadius:g}=this,h=f.window?f.window.document.body:null,i=a.canvas;i.id=f.getUniqueId(),h&&(i.style.display="none",h.appendChild(i));var j=g;if(!isNaN(j)&&!(j<1)){j|=0;var k=function(a,b,c,d,e){if("string"==typeof a&&(a=document.getElementById(a)),!a||"object"!==z(a)||!("getContext"in a))throw TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var f=a.getContext("2d");try{return f.getImageData(b,c,d,e)}catch(a){throw Error("unable to access image data: "+a)}}(i,b,c,d,e);k=function(a,b,c,d,e,f){for(var g,h=a.data,i=2*f+1,j=d-1,k=e-1,l=f+1,m=l*(l+1)/2,n=new C,o=n,p=1;p<i;p++)o=o.next=new C,p===l&&(g=o);o.next=n;for(var q=null,r=null,s=0,t=0,u=A[f],v=B[f],w=0;w<e;w++){o=n;for(var x=h[t],y=h[t+1],z=h[t+2],D=h[t+3],E=0;E<l;E++)o.r=x,o.g=y,o.b=z,o.a=D,o=o.next;for(var F=0,G=0,H=0,I=0,J=l*x,K=l*y,L=l*z,M=l*D,N=m*x,O=m*y,P=m*z,Q=m*D,R=1;R<l;R++){var S=t+((j<R?j:R)<<2),T=h[S],U=h[S+1],V=h[S+2],W=h[S+3],X=l-R;N+=(o.r=T)*X,O+=(o.g=U)*X,P+=(o.b=V)*X,Q+=(o.a=W)*X,F+=T,G+=U,H+=V,I+=W,o=o.next}q=n,r=g;for(var Y=0;Y<d;Y++){var Z=Q*u>>>v;if(h[t+3]=Z,0!==Z){var $=255/Z;h[t]=(N*u>>>v)*$,h[t+1]=(O*u>>>v)*$,h[t+2]=(P*u>>>v)*$}else h[t]=h[t+1]=h[t+2]=0;N-=J,O-=K,P-=L,Q-=M,J-=q.r,K-=q.g,L-=q.b,M-=q.a;var _=Y+f+1;_=s+(_<j?_:j)<<2,F+=q.r=h[_],G+=q.g=h[_+1],H+=q.b=h[_+2],I+=q.a=h[_+3],N+=F,O+=G,P+=H,Q+=I,q=q.next;var aa=r,ab=aa.r,ac=aa.g,ad=aa.b,ae=aa.a;J+=ab,K+=ac,L+=ad,M+=ae,F-=ab,G-=ac,H-=ad,I-=ae,r=r.next,t+=4}s+=d}for(var af=0;af<d;af++){var ag=h[t=af<<2],ah=h[t+1],ai=h[t+2],aj=h[t+3],ak=l*ag,al=l*ah,am=l*ai,an=l*aj,ao=m*ag,ap=m*ah,aq=m*ai,ar=m*aj;o=n;for(var as=0;as<l;as++)o.r=ag,o.g=ah,o.b=ai,o.a=aj,o=o.next;for(var at=d,au=0,av=0,aw=0,ax=0,ay=1;ay<=f;ay++){t=at+af<<2;var az=l-ay;ao+=(o.r=ag=h[t])*az,ap+=(o.g=ah=h[t+1])*az,aq+=(o.b=ai=h[t+2])*az,ar+=(o.a=aj=h[t+3])*az,ax+=ag,au+=ah,av+=ai,aw+=aj,o=o.next,ay<k&&(at+=d)}t=af,q=n,r=g;for(var aA=0;aA<e;aA++){var aB=t<<2;h[aB+3]=aj=ar*u>>>v,aj>0?(aj=255/aj,h[aB]=(ao*u>>>v)*aj,h[aB+1]=(ap*u>>>v)*aj,h[aB+2]=(aq*u>>>v)*aj):h[aB]=h[aB+1]=h[aB+2]=0,ao-=ak,ap-=al,aq-=am,ar-=an,ak-=q.r,al-=q.g,am-=q.b,an-=q.a,aB=af+((aB=aA+l)<k?aB:k)*d<<2,ao+=ax+=q.r=h[aB],ap+=au+=q.g=h[aB+1],aq+=av+=q.b=h[aB+2],ar+=aw+=q.a=h[aB+3],q=q.next,ak+=ag=r.r,al+=ah=r.g,am+=ai=r.b,an+=aj=r.a,ax-=ag,au-=ah,av-=ai,aw-=aj,r=r.next,t+=d}}return a}(k,0,0,d,e,j),i.getContext("2d").putImageData(k,b,c)}h&&h.removeChild(i)}}class bm extends aw{constructor(){super(...arguments),this.type="title"}}class bn extends aw{constructor(){super(...arguments),this.type="desc"}}function bo(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function bp(){return(bp=d(function*(a){var b=arguments.length>1&&void 0!==arguments[1]&&arguments[1],c=document.createElement("img");return b&&(c.crossOrigin="Anonymous"),new Promise((b,d)=>{c.onload=()=>{b(c)},c.onerror=(a,b,c,e,f)=>{d(f)},c.src=a})})).apply(this,arguments)}class bq{constructor(a){var{rootEmSize:b=12,emSize:c=12,createCanvas:d=bq.createCanvas,createImage:e=bq.createImage,anonymousCrossOrigin:f}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=a,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=a.screen,this.rootEmSize=b,this.emSize=c,this.createCanvas=d,this.createImage=this.bindCreateImage(e,f),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(a,b){return"boolean"==typeof b?(c,d)=>a(c,"boolean"==typeof d?d:b):a}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:a}=this;return a[a.length-1]}set emSize(a){var{emSizeStack:b}=this;b.push(a)}popEmSize(){var{emSizeStack:a}=this;a.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(a=>a.loaded)}isFontsLoaded(){return this.fonts.every(a=>a.loaded)}createDocumentElement(a){var b=this.createElement(a.documentElement);return b.root=!0,b.addStylesFromStyleDefinition(),this.documentElement=b,b}createElement(a){var b=a.nodeName.replace(/^[^:]+:/,""),c=bq.elementTypes[b];return void 0!==c?new c(this,a):new ax(this,a)}createTextNode(a){return new aH(this,a)}setViewBox(a){this.screen.setViewBox(function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?bo(Object(c),!0).forEach(function(b){e(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):bo(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({document:this},a))}}function br(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function bs(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?br(Object(c),!0).forEach(function(b){e(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):br(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}bq.createCanvas=function(a,b){var c=document.createElement("canvas");return c.width=a,c.height=b,c},bq.createImage=function(a){return bp.apply(this,arguments)},bq.elementTypes={svg:aI,rect:aJ,circle:aK,ellipse:aL,line:aM,polyline:aN,polygon:aO,path:aD,pattern:aP,marker:aQ,defs:aR,linearGradient:aU,radialGradient:aV,stop:aW,animate:aX,animateColor:aY,animateTransform:aZ,font:a$,"font-face":a_,"missing-glyph":a0,glyph:aE,text:aF,tspan:aG,tref:a1,a:a2,textPath:a5,image:a7,g:aS,symbol:a8,style:ba,use:bb,mask:be,clipPath:bg,filter:bh,feDropShadow:bi,feMorphology:bj,feComposite:bk,feColorMatrix:bd,feGaussianBlur:bl,title:bm,desc:bn};class bt{constructor(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new an(c),this.screen=new ak(a,c),this.options=c;var d=new bq(this,c),e=d.createDocumentElement(b);this.document=d,this.documentElement=e}static from(a,b){var c=arguments;return d(function*(){var d=c.length>2&&void 0!==c[2]?c[2]:{},e=new an(d);return new bt(a,(yield e.parse(b)),d)})()}static fromString(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new bt(a,new an(c).parseFromString(b),c)}fork(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return bt.from(a,b,bs(bs({},this.options),c))}forkString(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return bt.fromString(a,b,bs(bs({},this.options),c))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var a=arguments,b=this;return d(function*(){var c=a.length>0&&void 0!==a[0]?a[0]:{};b.start(bs({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},c)),yield b.ready(),b.stop()})()}start(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:b,screen:c,options:d}=this;c.start(b,bs(bs({enableRedraw:!0},d),a))}stop(){this.screen.stop()}resize(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,c=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(a,b,c)}}},67008:(a,b,c)=>{"use strict";var d=c(97039),e=c(17425),f=/#|\.prototype\./,g=function(a,b){var c=i[h(a)];return c===k||c!==j&&(e(b)?d(b):!!b)},h=g.normalize=function(a){return String(a).replace(f,".").toLowerCase()},i=g.data={},j=g.NATIVE="N",k=g.POLYFILL="P";a.exports=g},67414:(a,b,c)=>{"use strict";var d=c(7462);a.exports=function(a){return d(a.length)}},67420:(a,b,c)=>{"use strict";var d=c(7588),e=d({}.toString),f=d("".slice);a.exports=function(a){return f(e(a),8,-1)}},67519:a=>{"use strict";a.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},68077:(a,b,c)=>{"use strict";var d=c(6532),e=Object.defineProperty;a.exports=function(a,b){try{e(d,a,{value:b,configurable:!0,writable:!0})}catch(c){d[a]=b}return b}},68298:(a,b,c)=>{"use strict";var d=c(17425),e=c(79815),f=TypeError;a.exports=function(a){if(d(a))return a;throw new f(e(a)+" is not a function")}},68328:(a,b,c)=>{"use strict";var d=c(15390),e=c(69416),f=c(71381).indexOf,g=c(57862),h=e([].indexOf),i=!!h&&1/h([1],1,-0)<0;d({target:"Array",proto:!0,forced:i||!g("indexOf")},{indexOf:function(a){var b=arguments.length>1?arguments[1]:void 0;return i?h(this,a,b)||0:f(this,a,b)}})},68689:(a,b,c)=>{"use strict";var d=c(5112),e=c(97039),f=c(23359);a.exports=!d&&!e(function(){return 7!==Object.defineProperty(f("div"),"a",{get:function(){return 7}}).a})},68773:(a,b,c)=>{"use strict";a.exports="NODE"===c(52431)},69377:a=>{function b(a,b,c,d,e,f,g){try{var h=a[f](g),i=h.value}catch(a){return void c(a)}h.done?b(i):Promise.resolve(i).then(d,e)}a.exports=function(a){return function(){var c=this,d=arguments;return new Promise(function(e,f){var g=a.apply(c,d);function h(a){b(g,e,f,h,i,"next",a)}function i(a){b(g,e,f,h,i,"throw",a)}h(void 0)})}},a.exports.__esModule=!0,a.exports.default=a.exports},69416:(a,b,c)=>{"use strict";var d=c(67420),e=c(7588);a.exports=function(a){if("Function"===d(a))return e(a)}},71381:(a,b,c)=>{"use strict";var d=c(45465),e=c(84890),f=c(67414),g=function(a){return function(b,c,g){var h,i=d(b),j=f(i);if(0===j)return!a&&-1;var k=e(g,j);if(a&&c!=c){for(;j>k;)if((h=i[k++])!=h)return!0}else for(;j>k;k++)if((a||k in i)&&i[k]===c)return a||k||0;return!a&&-1}};a.exports={includes:g(!0),indexOf:g(!1)}},71472:(a,b,c)=>{"use strict";var d=c(76514),e=c(67420),f=c(80387)("match");a.exports=function(a){var b;return d(a)&&(void 0!==(b=a[f])?!!b:"RegExp"===e(a))}},72649:(a,b,c)=>{"use strict";var d=c(60827),e=c(6532),f=c(68077),g="__core-js_shared__",h=a.exports=e[g]||f(g,{});(h.versions||(h.versions=[])).push({version:"3.44.0",mode:d?"pure":"global",copyright:"\xa9 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"})},73040:(a,b,c)=>{"use strict";var d=c(6532),e=c(99662),f=c(17425),g=c(67008),h=c(93930),i=c(80387),j=c(52431),k=c(60827),l=c(52359),m=e&&e.prototype,n=i("species"),o=!1,p=f(d.PromiseRejectionEvent);a.exports={CONSTRUCTOR:g("Promise",function(){var a=h(e),b=a!==String(e);if(!b&&66===l||k&&!(m.catch&&m.finally))return!0;if(!l||l<51||!/native code/.test(a)){var c=new e(function(a){a(1)}),d=function(a){a(function(){},function(){})};if((c.constructor={})[n]=d,!(o=c.then(function(){})instanceof d))return!0}return!b&&("BROWSER"===j||"DENO"===j)&&!p}),REJECTION_EVENT:p,SUBCLASSING:o}},73522:(a,b,c)=>{"use strict";var d=c(15390),e=c(7588),f=c(53436),g=e([].reverse),h=[1,2];d({target:"Array",proto:!0,forced:String(h)===String(h.reverse())},{reverse:function(){return f(this)&&(this.length=this.length),g(this)}})},74934:(a,b,c)=>{"use strict";var d=c(5112),e=c(77525),f=Function.prototype,g=d&&Object.getOwnPropertyDescriptor,h=e(f,"name"),i=h&&(!d||d&&g(f,"name").configurable);a.exports={EXISTS:h,PROPER:h&&"something"===(function(){}).name,CONFIGURABLE:i}},75351:(a,b,c)=>{"use strict";var d=c(42727),e=c(7588),f=c(77660),g=c(63345),h=c(8991),i=e([].concat);a.exports=d("Reflect","ownKeys")||function(a){var b=f.f(h(a)),c=g.f;return c?i(b,c(a)):b}},76514:(a,b,c)=>{"use strict";var d=c(17425);a.exports=function(a){return"object"==typeof a?null!==a:d(a)}},76607:(a,b,c)=>{"use strict";var d=c(7588),e=c(97039),f=c(67420),g=Object,h=d("".split);a.exports=e(function(){return!g("z").propertyIsEnumerable(0)})?function(a){return"String"===f(a)?h(a,""):g(a)}:g},76901:(a,b,c)=>{"use strict";var d=c(50969),e=c(68298),f=c(8991),g=c(79815),h=c(10187),i=TypeError;a.exports=function(a,b){var c=arguments.length<2?h(a):b;if(e(c))return f(d(c,a));throw new i(g(a)+" is not iterable")}},77525:(a,b,c)=>{"use strict";var d=c(7588),e=c(40377),f=d({}.hasOwnProperty);a.exports=Object.hasOwn||function(a,b){return f(e(a),b)}},77660:(a,b,c)=>{"use strict";var d=c(98064),e=c(67519).concat("length","prototype");b.f=Object.getOwnPropertyNames||function(a){return d(a,e)}},79064:a=>{a.exports=function(a){this.ok=!1,this.alpha=1,"#"==a.charAt(0)&&(a=a.substr(1,6));var b={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};a=b[a=(a=a.replace(/ /g,"")).toLowerCase()]||a;for(var c=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(a){return[parseInt(a[1]),parseInt(a[2]),parseInt(a[3]),parseFloat(a[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(a){return[parseInt(a[1]),parseInt(a[2]),parseInt(a[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(a){return[parseInt(a[1],16),parseInt(a[2],16),parseInt(a[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(a){return[parseInt(a[1]+a[1],16),parseInt(a[2]+a[2],16),parseInt(a[3]+a[3],16)]}}],d=0;d<c.length;d++){var e=c[d].re,f=c[d].process,g=e.exec(a);if(g){var h=f(g);this.r=h[0],this.g=h[1],this.b=h[2],h.length>3&&(this.alpha=h[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var a=this.r.toString(16),b=this.g.toString(16),c=this.b.toString(16);return 1==a.length&&(a="0"+a),1==b.length&&(b="0"+b),1==c.length&&(c="0"+c),"#"+a+b+c},this.getHelpXML=function(){for(var a=[],d=0;d<c.length;d++)for(var e=c[d].example,f=0;f<e.length;f++)a[a.length]=e[f];for(var g in b)a[a.length]=g;var h=document.createElement("ul");h.setAttribute("id","rgbcolor-examples");for(var d=0;d<a.length;d++)try{var i=document.createElement("li"),j=new RGBColor(a[d]),k=document.createElement("div");k.style.cssText="margin: 3px; border: 1px solid black; background:"+j.toHex()+"; color:"+j.toHex(),k.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+a[d]+" -> "+j.toRGB()+" -> "+j.toHex());i.appendChild(k),i.appendChild(l),h.appendChild(i)}catch(a){}return h}}},79219:(a,b,c)=>{"use strict";var d=c(50969),e=c(8991),f=c(3638);a.exports=function(a,b,c){var g,h;e(a);try{if(!(g=f(a,"return"))){if("throw"===b)throw c;return c}g=d(g,a)}catch(a){h=!0,g=a}if("throw"===b)throw c;if(h)throw g;return e(g),c}},79287:(a,b,c)=>{"use strict";var d=c(15390),e=c(85587);d({target:"RegExp",proto:!0,forced:/./.exec!==e},{exec:e})},79582:(a,b,c)=>{"use strict";var d=c(50969),e=c(17425),f=c(76514),g=TypeError;a.exports=function(a,b){var c,h;if("string"===b&&e(c=a.toString)&&!f(h=d(c,a))||e(c=a.valueOf)&&!f(h=d(c,a))||"string"!==b&&e(c=a.toString)&&!f(h=d(c,a)))return h;throw new g("Can't convert object to primitive value")}},79815:a=>{"use strict";var b=String;a.exports=function(a){try{return b(a)}catch(a){return"Object"}}},79892:(a,b,c)=>{"use strict";a.exports=!c(97039)(function(){var a=(function(){}).bind();return"function"!=typeof a||a.hasOwnProperty("prototype")})},80387:(a,b,c)=>{"use strict";var d=c(6532),e=c(31429),f=c(77525),g=c(88532),h=c(44615),i=c(48324),j=d.Symbol,k=e("wks"),l=i?j.for||j:j&&j.withoutSetter||g;a.exports=function(a){return f(k,a)||(k[a]=h&&f(j,a)?j[a]:l("Symbol."+a)),k[a]}},81439:(a,b,c)=>{"use strict";a.exports=c(6532)},82675:(a,b,c)=>{"use strict";var d,e,f,g,h,i=c(6532),j=c(84849),k=c(65436),l=c(12005).set,m=c(39173),n=c(97412),o=c(33989),p=c(82800),q=c(68773),r=i.MutationObserver||i.WebKitMutationObserver,s=i.document,t=i.process,u=i.Promise,v=j("queueMicrotask");if(!v){var w=new m,x=function(){var a,b;for(q&&(a=t.domain)&&a.exit();b=w.get();)try{b()}catch(a){throw w.head&&d(),a}a&&a.enter()};n||q||p||!r||!s?!o&&u&&u.resolve?((g=u.resolve(void 0)).constructor=u,h=k(g.then,g),d=function(){h(x)}):q?d=function(){t.nextTick(x)}:(l=k(l,i),d=function(){l(x)}):(e=!0,f=s.createTextNode(""),new r(x).observe(f,{characterData:!0}),d=function(){f.data=e=!e}),v=function(a){w.head||d(),w.add(a)}}a.exports=v},82800:(a,b,c)=>{"use strict";var d=c(343);a.exports=/web0s(?!.*chrome)/i.test(d)},83407:(a,b,c)=>{"use strict";var d=c(51067),e=String;a.exports=function(a){if("Symbol"===d(a))throw TypeError("Cannot convert a Symbol value to a string");return e(a)}},84545:(a,b,c)=>{"use strict";var d=c(8991),e=c(85152),f=c(60561),g=c(80387)("species");a.exports=function(a,b){var c,h=d(a).constructor;return void 0===h||f(c=d(h)[g])?b:e(c)}},84849:(a,b,c)=>{"use strict";var d=c(6532),e=c(5112),f=Object.getOwnPropertyDescriptor;a.exports=function(a){if(!e)return d[a];var b=f(d,a);return b&&b.value}},84890:(a,b,c)=>{"use strict";var d=c(58627),e=Math.max,f=Math.min;a.exports=function(a,b){var c=d(a);return c<0?e(c+b,0):f(c,b)}},85152:(a,b,c)=>{"use strict";var d=c(50065),e=c(79815),f=TypeError;a.exports=function(a){if(d(a))return a;throw new f(e(a)+" is not a constructor")}},85587:(a,b,c)=>{"use strict";var d=c(50969),e=c(7588),f=c(83407),g=c(53907),h=c(15193),i=c(31429),j=c(65444),k=c(2129).get,l=c(21651),m=c(5502),n=i("native-string-replace",String.prototype.replace),o=RegExp.prototype.exec,p=o,q=e("".charAt),r=e("".indexOf),s=e("".replace),t=e("".slice),u=function(){var a=/a/,b=/b*/g;return d(o,a,"a"),d(o,b,"a"),0!==a.lastIndex||0!==b.lastIndex}(),v=h.BROKEN_CARET,w=void 0!==/()??/.exec("")[1];(u||w||v||l||m)&&(p=function(a){var b,c,e,h,i,l,m,x=k(this),y=f(a),z=x.raw;if(z)return z.lastIndex=this.lastIndex,b=d(p,z,y),this.lastIndex=z.lastIndex,b;var A=x.groups,B=v&&this.sticky,C=d(g,this),D=this.source,E=0,F=y;if(B&&(-1===r(C=s(C,"y",""),"g")&&(C+="g"),F=t(y,this.lastIndex),this.lastIndex>0&&(!this.multiline||this.multiline&&"\n"!==q(y,this.lastIndex-1))&&(D="(?: "+D+")",F=" "+F,E++),c=RegExp("^(?:"+D+")",C)),w&&(c=RegExp("^"+D+"$(?!\\s)",C)),u&&(e=this.lastIndex),h=d(o,B?c:this,F),B?h?(h.input=t(h.input,E),h[0]=t(h[0],E),h.index=this.lastIndex,this.lastIndex+=h[0].length):this.lastIndex=0:u&&h&&(this.lastIndex=this.global?h.index+h[0].length:e),w&&h&&h.length>1&&d(n,h[0],c,function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(h[i]=void 0)}),h&&A)for(i=0,h.groups=l=j(null);i<A.length;i++)l[(m=A[i])[0]]=h[m[1]];return h}),a.exports=p},86883:(a,b,c)=>{"use strict";var d=c(5112),e=c(50969),f=c(89273),g=c(23864),h=c(45465),i=c(10421),j=c(77525),k=c(68689),l=Object.getOwnPropertyDescriptor;b.f=d?l:function(a,b){if(a=h(a),b=i(b),k)try{return l(a,b)}catch(a){}if(j(a,b))return g(!e(f.f,a,b),a[b])}},88532:(a,b,c)=>{"use strict";var d=c(7588),e=0,f=Math.random(),g=d(1.1.toString);a.exports=function(a){return"Symbol("+(void 0===a?"":a)+")_"+g(++e+f,36)}},88875:(a,b,c)=>{"use strict";var d=c(68298),e=TypeError,f=function(a){var b,c;this.promise=new a(function(a,d){if(void 0!==b||void 0!==c)throw new e("Bad Promise constructor");b=a,c=d}),this.resolve=d(b),this.reject=d(c)};a.exports.f=function(a){return new f(a)}},89273:(a,b)=>{"use strict";var c={}.propertyIsEnumerable,d=Object.getOwnPropertyDescriptor;b.f=d&&!c.call({1:2},1)?function(a){var b=d(this,a);return!!b&&b.enumerable}:c},89513:(a,b,c)=>{"use strict";var d=c(42727),e=c(17425),f=c(43141),g=c(48324),h=Object;a.exports=g?function(a){return"symbol"==typeof a}:function(a){var b=d("Symbol");return e(b)&&f(b.prototype,h(a))}},90845:(a,b,c)=>{"use strict";var d=c(15390),e=c(69416),f=c(86883).f,g=c(7462),h=c(83407),i=c(18023),j=c(10054),k=c(48072),l=c(60827),m=e("".slice),n=Math.min,o=k("endsWith");d({target:"String",proto:!0,forced:!(!l&&!o&&function(){var a=f(String.prototype,"endsWith");return a&&!a.writable}())&&!o},{endsWith:function(a){var b=h(j(this));i(a);var c=arguments.length>1?arguments[1]:void 0,d=b.length,e=void 0===c?d:n(g(c),d),f=h(a);return m(b,e-f.length,e)===f}})},91001:a=>{"use strict";a.exports={}},91231:a=>{"use strict";a.exports=function(a){try{return{error:!1,value:a()}}catch(a){return{error:!0,value:a}}}},93229:(a,b,c)=>{"use strict";var d=c(79892),e=Function.prototype,f=e.apply,g=e.call;a.exports="object"==typeof Reflect&&Reflect.apply||(d?g.bind(f):function(){return g.apply(f,arguments)})},93930:(a,b,c)=>{"use strict";var d=c(7588),e=c(17425),f=c(72649),g=d(Function.toString);e(f.inspectSource)||(f.inspectSource=function(a){return g(a)}),a.exports=f.inspectSource},94323:(a,b,c)=>{"use strict";var d=c(5112),e=c(10397),f=c(23864);a.exports=d?function(a,b,c){return e.f(a,b,f(1,c))}:function(a,b,c){return a[b]=c,a}},95941:(a,b,c)=>{"use strict";var d=c(15390),e=c(88875);d({target:"Promise",stat:!0,forced:c(73040).CONSTRUCTOR},{reject:function(a){var b=e.f(this);return(0,b.reject)(a),b.promise}})},96482:(a,b,c)=>{"use strict";var d=c(7588),e=c(68298);a.exports=function(a,b,c){try{return d(e(Object.getOwnPropertyDescriptor(a,b)[c]))}catch(a){}}},96516:(a,b,c)=>{"use strict";var d=c(15390),e=c(50969),f=c(60827),g=c(74934),h=c(17425),i=c(53538),j=c(28907),k=c(36751),l=c(7615),m=c(94323),n=c(59908),o=c(80387),p=c(97601),q=c(12549),r=g.PROPER,s=g.CONFIGURABLE,t=q.IteratorPrototype,u=q.BUGGY_SAFARI_ITERATORS,v=o("iterator"),w="keys",x="values",y="entries",z=function(){return this};a.exports=function(a,b,c,g,o,q,A){i(c,b,g);var B,C,D,E=function(a){if(a===o&&J)return J;if(!u&&a&&a in H)return H[a];switch(a){case w:case x:case y:return function(){return new c(this,a)}}return function(){return new c(this)}},F=b+" Iterator",G=!1,H=a.prototype,I=H[v]||H["@@iterator"]||o&&H[o],J=!u&&I||E(o),K="Array"===b&&H.entries||I;if(K&&(B=j(K.call(new a)))!==Object.prototype&&B.next&&(!f&&j(B)!==t&&(k?k(B,t):h(B[v])||n(B,v,z)),l(B,F,!0,!0),f&&(p[F]=z)),r&&o===x&&I&&I.name!==x&&(!f&&s?m(H,"name",x):(G=!0,J=function(){return e(I,this)})),o)if(C={values:E(x),keys:q?J:E(w),entries:E(y)},A)for(D in C)!u&&!G&&D in H||n(H,D,C[D]);else d({target:b,proto:!0,forced:u||G},C);return(!f||A)&&H[v]!==J&&n(H,v,J,{name:o}),p[b]=J,C}},96595:(a,b,c)=>{"use strict";var d=c(15390),e=c(7588),f=c(18023),g=c(10054),h=c(83407),i=c(48072),j=e("".indexOf);d({target:"String",proto:!0,forced:!i("includes")},{includes:function(a){return!!~j(h(g(this)),h(f(a)),arguments.length>1?arguments[1]:void 0)}})},97039:a=>{"use strict";a.exports=function(a){try{return!!a()}catch(a){return!0}}},97269:(a,b,c)=>{"use strict";var d=c(5112),e=c(2870),f=c(10397),g=c(8991),h=c(45465),i=c(26348);b.f=d&&!e?Object.defineProperties:function(a,b){g(a);for(var c,d=h(b),e=i(b),j=e.length,k=0;j>k;)f.f(a,c=e[k++],d[c]);return a}},97361:(a,b,c)=>{"use strict";var d=c(27199).charAt;a.exports=function(a,b,c){return b+(c?d(a,b).length:1)}},97412:(a,b,c)=>{"use strict";var d=c(343);a.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(d)},97601:a=>{"use strict";a.exports={}},98064:(a,b,c)=>{"use strict";var d=c(7588),e=c(77525),f=c(45465),g=c(71381).indexOf,h=c(91001),i=d([].push);a.exports=function(a,b){var c,d=f(a),j=0,k=[];for(c in d)!e(h,c)&&e(d,c)&&i(k,c);for(;b.length>j;)e(d,c=b[j++])&&(~g(k,c)||i(k,c));return k}},98074:(a,b,c)=>{"use strict";c(14704),c(11),c(23315),c(15199),c(95941),c(13044)},99466:(a,b,c)=>{"use strict";var d=c(13209),e=String,f=TypeError;a.exports=function(a){if(d(a))return a;throw new f("Can't set "+e(a)+" as a prototype")}},99501:(a,b,c)=>{"use strict";var d=c(99662),e=c(1504);a.exports=c(73040).CONSTRUCTOR||!e(function(a){d.all(a).then(void 0,function(){})})},99662:(a,b,c)=>{"use strict";a.exports=c(6532).Promise}};