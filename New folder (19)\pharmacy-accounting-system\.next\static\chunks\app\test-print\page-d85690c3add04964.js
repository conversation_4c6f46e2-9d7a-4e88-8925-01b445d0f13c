(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2957],{31615:(e,t,n)=>{Promise.resolve().then(n.bind(n,39003))},39003:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});var a=n(95155),s=n(12115),i=n(61932),c=n(10988),l=n(81304),d=n(92657);function r(){var e,t,n;let[r,o]=(0,s.useState)([]),[m,h]=(0,s.useState)(!0),[x,p]=(0,s.useState)(null),[u,g]=(0,s.useState)(null);(0,s.useEffect)(()=>{v()},[]);let v=async()=>{try{let e=await (0,c.getSalesInvoices)();e.success&&o(e.data||[])}catch(e){console.error("خطأ في تحميل الفواتير:",e)}finally{h(!1)}},b=async e=>{try{console.log("\uD83D\uDD0D جلب بيانات الفاتورة للطباعة:",e);let n=await (0,c.getSalesInvoiceForPrint)(e);if(n.success){var t;console.log("✅ بيانات الفاتورة:",n.data),p(n.data),g(n.data),console.log("\uD83D\uDD0D تحليل مفصل لعناصر الفاتورة:"),null==(t=n.data.sales_invoice_items)||t.forEach((e,t)=>{var n,a;console.log("\n--- العنصر ".concat(t+1," ---")),console.log("البيانات الكاملة:",e),console.log("أسماء الأدوية المختلفة:",{medicine_name:e.medicine_name,medicineName:e.medicineName,medicine_batches_name:null==(a=e.medicine_batches)||null==(n=a.medicines)?void 0:n.name,batch_id:e.medicine_batch_id,batch_data:e.medicine_batches});let s=JSON.parse(localStorage.getItem("medicine_batches")||"[]"),i=JSON.parse(localStorage.getItem("medicines")||"[]"),c=s.find(t=>t.id===e.medicine_batch_id),l=i.find(e=>e.id===(null==c?void 0:c.medicine_id));console.log("بيانات من localStorage:",{batch_found:!!c,medicine_found:!!l,batch_data:c,medicine_data:l,calculated_name:(null==l?void 0:l.name)||"غير موجود"})})}else console.error("❌ فشل في جلب بيانات الفاتورة:",n.error)}catch(e){console.error("❌ خطأ في جلب بيانات الفاتورة:",e)}},y=()=>{var e,t,n,a,s,i;if(!u)return;let c=window.open("","_blank");if(!c)return;let l='\n      <!DOCTYPE html>\n      <html dir="rtl" lang="ar">\n      <head>\n        <meta charset="UTF-8">\n        <title>فاتورة مبيعات - '.concat(u.invoice_number,'</title>\n        <style>\n          body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n          .header { text-align: center; margin-bottom: 20px; }\n          .invoice-info { margin-bottom: 20px; }\n          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }\n          .items-table th { background-color: #f5f5f5; }\n          .total { text-align: left; font-weight: bold; }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>فاتورة مبيعات</h1>\n          <h2>رقم الفاتورة: ').concat(u.invoice_number,'</h2>\n        </div>\n        \n        <div class="invoice-info">\n          <p><strong>العميل:</strong> ').concat((null==(e=u.customers)?void 0:e.name)||"عميل نقدي","</p>\n          <p><strong>التاريخ:</strong> ").concat(new Date(u.created_at).toLocaleDateString("ar-EG"),"</p>\n          <p><strong>طريقة الدفع:</strong> ").concat("cash"===u.payment_method?"نقدي":"آجل",'</p>\n        </div>\n        \n        <table class="items-table">\n          <thead>\n            <tr>\n              <th>اسم الدواء</th>\n              <th>الكمية</th>\n              <th>سعر الوحدة</th>\n              <th>الإجمالي</th>\n              <th>هدية</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat((null==(t=u.sales_invoice_items)?void 0:t.map(e=>{var t,n;let a=(null==(n=e.medicine_batches)||null==(t=n.medicines)?void 0:t.name)||e.medicine_name||e.medicineName||"غير محدد";return"\n                <tr>\n                  <td>".concat(a,"</td>\n                  <td>").concat(e.quantity,"</td>\n                  <td>").concat(e.unit_price.toFixed(2),"</td>\n                  <td>").concat(e.total_price.toFixed(2),"</td>\n                  <td>").concat(e.is_gift?"نعم":"لا","</td>\n                </tr>\n              ")}).join(""))||'<tr><td colspan="5">لا توجد عناصر</td></tr>','\n          </tbody>\n        </table>\n        \n        <div class="total">\n          <p><strong>المجموع الفرعي:</strong> ').concat((null==(n=u.subtotal)?void 0:n.toFixed(2))||"0.00"," جنيه</p>\n          <p><strong>الخصم:</strong> ").concat((null==(a=u.discount)?void 0:a.toFixed(2))||"0.00"," جنيه</p>\n          <p><strong>الضريبة:</strong> ").concat((null==(s=u.tax)?void 0:s.toFixed(2))||"0.00"," جنيه</p>\n          <p><strong>المجموع النهائي:</strong> ").concat((null==(i=u.final_amount)?void 0:i.toFixed(2))||"0.00"," جنيه</p>\n        </div>\n      </body>\n      </html>\n    ");c.document.write(l),c.document.close(),c.print()};return m?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"text-lg",children:"جاري التحميل..."})})}):(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,a.jsx)(l.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"اختبار طباعة الفواتير"}),(0,a.jsx)("p",{className:"text-gray-600",children:"اختبار عرض أسماء الأدوية في الفواتير المطبوعة"})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم الفاتورة"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"العميل"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:r.map(e=>{var t,n;return(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.invoice_number}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(t=e.customers)?void 0:t.name)||"عميل نقدي"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("ar-EG")}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[null==(n=e.final_amount)?void 0:n.toFixed(2)," جنيه"]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,a.jsx)("button",{onClick:()=>b(e.id),className:"text-blue-600 hover:text-blue-900 ml-2",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})}),(null==x?void 0:x.id)===e.id&&(0,a.jsx)("button",{onClick:y,className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})]})]},e.id)})})]})}),x&&(0,a.jsxs)("div",{className:"mt-6 bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"تفاصيل الفاتورة المحددة:"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"رقم الفاتورة:"})," ",x.invoice_number]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"العميل:"})," ",(null==(e=x.customers)?void 0:e.name)||"عميل نقدي"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"عدد العناصر:"})," ",(null==(t=x.sales_invoice_items)?void 0:t.length)||0]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"عناصر الفاتورة:"}),null==(n=x.sales_invoice_items)?void 0:n.map((e,t)=>{var n,s;let i=(null==(s=e.medicine_batches)||null==(n=s.medicines)?void 0:n.name)||e.medicine_name||e.medicineName||"غير محدد";return(0,a.jsxs)("div",{className:"bg-white p-2 rounded border",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"اسم الدواء:"})," ",i]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"الكمية:"})," ",e.quantity]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"سعر الوحدة:"})," ",e.unit_price," جنيه"]})]},t)})]})]})]})]})})})}},92657:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{e.O(0,[6874,6543,5647,8080,1932,988,8441,5964,7358],()=>e(e.s=31615)),_N_E=e.O()}]);