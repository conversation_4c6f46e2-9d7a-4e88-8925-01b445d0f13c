exports.id=314,exports.ids=[314],exports.modules={9530:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},18434:(a,b,c)=>{Promise.resolve().then(c.bind(c,29131)),Promise.resolve().then(c.bind(c,81326))},29131:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\contexts\\AuthContext.tsx","useAuth");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call usePermissions() from the server but usePermissions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\contexts\\AuthContext.tsx","usePermissions"),(0,d.registerClientReference)(function(){throw Error("Attempted to call useActivityLogger() from the server but useActivityLogger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\contexts\\AuthContext.tsx","useActivityLogger")},33891:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23))},36586:(a,b,c)=>{Promise.resolve().then(c.bind(c,63213)),Promise.resolve().then(c.bind(c,46952))},46322:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},46952:(a,b,c)=>{"use strict";c.d(b,{E:()=>g,NotificationProvider:()=>h});var d=c(60687),e=c(43210);let f=(0,e.createContext)(void 0),g=()=>{let a=(0,e.useContext)(f);if(void 0===a)throw Error("useNotifications must be used within a NotificationProvider");return a},h=({children:a})=>{let[b,c]=(0,e.useState)([]);(0,e.useEffect)(()=>{g();let a=setInterval(g,6e4);return()=>clearInterval(a)},[]);let g=async()=>{try{let a=await h();c(a)}catch(a){console.error("Error loading notifications:",a)}},h=async()=>{let a=[],b=new Date;return a.push({id:"1",type:"warning",title:"أدوية قاربت على الانتهاء",message:"يوجد 5 أدوية ستنتهي صلاحيتها خلال 30 يوم",category:"inventory",priority:"high",isRead:!1,actionUrl:"/inventory?filter=expiring",actionLabel:"عرض الأدوية",createdAt:new Date(b.getTime()-72e5).toISOString()}),a.push({id:"2",type:"error",title:"نفاد مخزون",message:"باراسيتامول 500mg - الكمية المتبقية: 0",category:"inventory",priority:"critical",isRead:!1,actionUrl:"/inventory?search=باراسيتامول",actionLabel:"إضافة مخزون",createdAt:new Date(b.getTime()-18e5).toISOString()}),a.push({id:"3",type:"warning",title:"مخزون منخفض",message:"يوجد 8 أدوية كميتها أقل من الحد الأدنى",category:"inventory",priority:"medium",isRead:!1,actionUrl:"/inventory?filter=low-stock",actionLabel:"عرض التفاصيل",createdAt:new Date(b.getTime()-144e5).toISOString()}),a.push({id:"4",type:"info",title:"مبيعات اليوم",message:"تم تحقيق 2,450,000 د.ع من المبيعات اليوم",category:"sales",priority:"low",isRead:!0,actionUrl:"/sales-records",actionLabel:"عرض التفاصيل",createdAt:new Date(b.getTime()-216e5).toISOString()}),a.push({id:"5",type:"success",title:"تحديث النظام",message:"تم تحديث النظام بنجاح إلى الإصدار 1.0.1",category:"system",priority:"low",isRead:!1,createdAt:new Date(b.getTime()-864e5).toISOString()}),a.push({id:"6",type:"info",title:"مستخدم جديد",message:"تم إضافة مستخدم جديد: أحمد الصيدلي",category:"user",priority:"low",isRead:!0,actionUrl:"/users",actionLabel:"إدارة المستخدمين",createdAt:new Date(b.getTime()-432e5).toISOString()}),a.push({id:"7",type:"warning",title:"فواتير معلقة",message:"يوجد 12 فاتورة معلقة الدفع بقيمة 850,000 د.ع",category:"financial",priority:"high",isRead:!1,actionUrl:"/sales-records?filter=pending",actionLabel:"عرض الفواتير",createdAt:new Date(b.getTime()-288e5).toISOString()}),a.sort((a,b)=>new Date(b.createdAt).getTime()-new Date(a.createdAt).getTime())},i=async()=>{await g()},j=b.filter(a=>!a.isRead).length;return(0,d.jsx)(f.Provider,{value:{notifications:b,unreadCount:j,addNotification:a=>{let b={...a,id:Date.now().toString(),isRead:!1,createdAt:new Date().toISOString()};c(a=>[b,...a])},markAsRead:a=>{c(b=>b.map(b=>b.id===a?{...b,isRead:!0}:b))},markAllAsRead:()=>{c(a=>a.map(a=>({...a,isRead:!0})))},removeNotification:a=>{c(b=>b.filter(b=>b.id!==a))},clearAll:()=>{c([])},getNotificationsByCategory:a=>b.filter(b=>b.category===a),getNotificationsByPriority:a=>b.filter(b=>b.priority===a),refreshNotifications:i},children:a})}},54413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(37413),e=c(4536),f=c.n(e),g=c(78768),h=c(18898),i=c(36440);function j(){return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center px-4",children:(0,d.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("div",{className:"text-8xl font-bold text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 bg-clip-text mb-4",children:"404"}),(0,d.jsx)("div",{className:"w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-700 mx-auto rounded-full"})]}),(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20 p-8 mb-8",children:[(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)(g.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"الصفحة غير موجودة"}),(0,d.jsx)("p",{className:"text-gray-600",children:"عذراً، لا يمكن العثور على الصفحة التي تبحث عنها"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)(f(),{href:"/",className:"inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg",children:[(0,d.jsx)(h.A,{className:"h-5 w-5"}),"العودة للرئيسية",(0,d.jsx)(i.A,{className:"h-4 w-4"})]}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"أو يمكنك استخدام القائمة الجانبية للتنقل"})]})]}),(0,d.jsx)("div",{className:"text-center text-sm text-gray-500",children:"\xa9 2024 مكتب لارين العلمي - نظام إدارة الصيدلية"})]})})}},61135:()=>{},63213:(a,b,c)=>{"use strict";c.d(b,{As:()=>k,AuthProvider:()=>l,Sk:()=>m,Zy:()=>n});var d=c(60687),e=c(43210);let f=async(a,b,c=!1)=>"admin"===a&&"admin123"===b?{success:!0,user:{id:"1",username:"admin",email:"<EMAIL>",full_name:"مدير النظام",role:"admin",permissions:{sales_view:!0,sales_create:!0,sales_edit:!0,sales_delete:!0,sales_print:!0,sales_view_prices:!0,purchases_view:!0,purchases_create:!0,purchases_edit:!0,purchases_delete:!0,purchases_print:!0,inventory_view:!0,inventory_create:!0,inventory_edit:!0,inventory_delete:!0,inventory_print:!0,customers_view:!0,customers_create:!0,customers_edit:!0,customers_delete:!0,suppliers_view:!0,suppliers_create:!0,suppliers_edit:!0,suppliers_delete:!0,reports_view:!0,reports_financial:!0,reports_detailed:!0,reports_export:!0,users_view:!0,users_create:!0,users_edit:!0,users_delete:!0,settings_view:!0,settings_edit:!0,cashbox_view:!0,cashbox_manage:!0,returns_view:!0,returns_create:!0,returns_edit:!0,returns_delete:!0},is_active:!0,last_login:new Date().toISOString(),created_at:"2024-01-01T00:00:00Z"},session:{token:"mock-session-token"}}:{success:!1,error:"اسم المستخدم أو كلمة المرور غير صحيحة"},g=async a=>({success:!0}),h=async a=>"mock-session-token"===a?{success:!0,user:{id:"1",username:"admin",email:"<EMAIL>",full_name:"مدير النظام",role:"admin",permissions:{sales_view:!0,sales_create:!0,sales_edit:!0,sales_delete:!0,sales_print:!0,sales_view_prices:!0,purchases_view:!0,purchases_create:!0,purchases_edit:!0,purchases_delete:!0,purchases_print:!0,inventory_view:!0,inventory_create:!0,inventory_edit:!0,inventory_delete:!0,inventory_print:!0,customers_view:!0,customers_create:!0,customers_edit:!0,customers_delete:!0,suppliers_view:!0,suppliers_create:!0,suppliers_edit:!0,suppliers_delete:!0,reports_view:!0,reports_financial:!0,reports_detailed:!0,reports_export:!0,users_view:!0,users_create:!0,users_edit:!0,users_delete:!0,settings_view:!0,settings_edit:!0,cashbox_view:!0,cashbox_manage:!0,returns_view:!0,returns_create:!0,returns_edit:!0,returns_delete:!0},is_active:!0}}:{success:!1,error:"جلسة غير صالحة"},i=async a=>(console.log("Activity logged:",a),{success:!0}),j=(0,e.createContext)(void 0),k=()=>{let a=(0,e.useContext)(j);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a},l=({children:a})=>{let[b,c]=(0,e.useState)(null),[i,k]=(0,e.useState)(null),[l,m]=(0,e.useState)(!0);(0,e.useEffect)(()=>{n()},[]);let n=async()=>{try{let a=localStorage.getItem("sessionToken");if(!a)return void m(!1);let b=await h(a);b.success&&b.user?(c(b.user),k(b.user.permissions)):localStorage.removeItem("sessionToken")}catch(a){console.error("Error checking session:",a),localStorage.removeItem("sessionToken")}finally{m(!1)}},o=async(a,b,d=!1)=>{try{m(!0);let e=await f(a,b,d);if(e.success&&e.user&&e.session)return c(e.user),k(e.user.permissions),localStorage.setItem("sessionToken",e.session.token),{success:!0};return{success:!1,error:e.error||"فشل في تسجيل الدخول"}}catch(a){return{success:!1,error:a.message||"حدث خطأ غير متوقع"}}finally{m(!1)}},p=async()=>{try{let a=localStorage.getItem("sessionToken");a&&await g(a),c(null),k(null),localStorage.removeItem("sessionToken")}catch(a){console.error("Error during logout:",a),c(null),k(null),localStorage.removeItem("sessionToken")}},q=async()=>{await n()};return(0,d.jsx)(j.Provider,{value:{user:b,permissions:i,isLoading:l,isAuthenticated:!!b,login:o,logout:p,hasPermission:a=>!!i&&!0===i[a],hasAnyPermission:a=>!!i&&a.some(a=>!0===i[a]),hasRole:a=>!!b&&b.role===a,refreshUser:q},children:a})},m=()=>{let{permissions:a,hasPermission:b,hasAnyPermission:c}=k();return{permissions:a,hasPermission:b,hasAnyPermission:c,canViewSales:b("sales_view"),canCreateSales:b("sales_create"),canEditSales:b("sales_edit"),canDeleteSales:b("sales_delete"),canPrintSales:b("sales_print"),canViewPrices:b("sales_view_prices"),canViewPurchases:b("purchases_view"),canCreatePurchases:b("purchases_create"),canEditPurchases:b("purchases_edit"),canDeletePurchases:b("purchases_delete"),canPrintPurchases:b("purchases_print"),canViewInventory:b("inventory_view"),canCreateInventory:b("inventory_create"),canEditInventory:b("inventory_edit"),canDeleteInventory:b("inventory_delete"),canPrintInventory:b("inventory_print"),canViewCustomers:b("customers_view"),canCreateCustomers:b("customers_create"),canEditCustomers:b("customers_edit"),canDeleteCustomers:b("customers_delete"),canViewSuppliers:b("suppliers_view"),canCreateSuppliers:b("suppliers_create"),canEditSuppliers:b("suppliers_edit"),canDeleteSuppliers:b("suppliers_delete"),canViewReports:b("reports_view"),canViewFinancialReports:b("reports_financial"),canViewDetailedReports:b("reports_detailed"),canExportReports:b("reports_export"),canViewUsers:b("users_view"),canCreateUsers:b("users_create"),canEditUsers:b("users_edit"),canDeleteUsers:b("users_delete"),canViewSettings:b("settings_view"),canEditSettings:b("settings_edit"),canViewCashbox:b("cashbox_view"),canManageCashbox:b("cashbox_manage"),canViewReturns:b("returns_view"),canCreateReturns:b("returns_create"),canEditReturns:b("returns_edit"),canDeleteReturns:b("returns_delete"),canAccessSalesModule:c(["sales_view","sales_create","sales_edit"]),canAccessPurchasesModule:c(["purchases_view","purchases_create","purchases_edit"]),canAccessInventoryModule:c(["inventory_view","inventory_create","inventory_edit"]),canAccessReportsModule:b("reports_view"),canAccessUsersModule:c(["users_view","users_create","users_edit"]),canAccessSettingsModule:c(["settings_view","settings_edit"])}},n=()=>{let{user:a}=k();return{logUserActivity:async(b,c,d)=>{if(a)try{await i({user_id:a.id,action:b,description:c,...d})}catch(a){console.error("Error logging activity:",a)}}}}},67393:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(6867),f=c(63420);function g(){return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,d.jsx)(e.A,{className:"h-10 w-10 text-white"})}),(0,d.jsx)("div",{className:"absolute -inset-2 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl opacity-20 animate-ping"})]})}),(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20 p-8 max-w-sm mx-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,d.jsx)(f.A,{className:"h-6 w-6 text-blue-600 animate-spin"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"جاري التحميل..."})]}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"نظام إدارة الصيدلية - مكتب لارين العلمي"}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-4",children:(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-700 h-2 rounded-full animate-pulse",style:{width:"60%"}})}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"يرجى الانتظار..."})]}),(0,d.jsxs)("div",{className:"flex justify-center gap-2 mt-8",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded-full animate-bounce"}),(0,d.jsx)("div",{className:"w-3 h-3 bg-indigo-600 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-3 h-3 bg-purple-600 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})})}},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},78335:()=>{},81326:(a,b,c)=>{"use strict";c.d(b,{NotificationProvider:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\contexts\\NotificationContext.tsx","useNotifications");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\contexts\\NotificationContext.tsx","NotificationProvider")},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(37413);c(61135);var e=c(29131),f=c(81326);let g={title:"نظام حسابات الصيدلية - مكتب لارين العلمي",description:"نظام إدارة حسابات وصيدلية احترافي مع نظام مصادقة متقدم",viewport:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"};function h({children:a}){return(0,d.jsx)("html",{lang:"ar",dir:"rtl",children:(0,d.jsx)("body",{className:"antialiased bg-gray-50 font-sans",children:(0,d.jsx)(e.AuthProvider,{children:(0,d.jsx)(f.NotificationProvider,{children:a})})})})}},96487:()=>{},99547:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23))}};