'use client'

import React from 'react'

interface InvoiceItem {
  medicineName: string
  batchCode: string
  quantity: number
  unitPrice: number
  totalPrice: number
  isGift?: boolean
  expiryDate?: string
}

interface PrintableInvoiceProps {
  type: 'sales' | 'purchase'
  invoiceNumber: string
  date: string
  customerOrSupplier: {
    name: string
    phone?: string
    address?: string
  }
  items: InvoiceItem[]
  subtotal: number
  discount: number
  finalAmount: number
  notes?: string
}

export default function PrintableInvoice({
  type,
  invoiceNumber,
  date,
  customerOrSupplier,
  items,
  subtotal,
  discount,
  finalAmount,
  notes
}: PrintableInvoiceProps) {
  return (
    <div className="max-w-4xl mx-auto bg-white p-8 print:p-4" id="printable-invoice">
      {/* Header */}
      <div className="text-center border-b-2 border-gray-300 pb-6 mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">صيدلية الشفاء</h1>
        <p className="text-gray-600">بغداد - الكرادة - شارع الرئيسي</p>
        <p className="text-gray-600">هاتف: 07901234567 | البريد الإلكتروني: <EMAIL></p>
        <p className="text-gray-600">رخصة رقم: PH-2024-001</p>
      </div>

      {/* Invoice Info */}
      <div className="grid grid-cols-2 gap-8 mb-6">
        <div>
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            {type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'}
          </h2>
          <div className="space-y-2">
            <p><strong>رقم الفاتورة:</strong> {invoiceNumber}</p>
            <p><strong>التاريخ:</strong> {date}</p>
            <p><strong>الوقت:</strong> {new Date().toLocaleTimeString('ar-EG')}</p>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {type === 'sales' ? 'بيانات العميل' : 'بيانات المورد'}
          </h3>
          <div className="space-y-1">
            <p><strong>الاسم:</strong> {customerOrSupplier.name}</p>
            {customerOrSupplier.phone && (
              <p><strong>الهاتف:</strong> {customerOrSupplier.phone}</p>
            )}
            {customerOrSupplier.address && (
              <p><strong>العنوان:</strong> {customerOrSupplier.address}</p>
            )}
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-6">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-300 px-4 py-2 text-right">م</th>
              <th className="border border-gray-300 px-4 py-2 text-right">اسم الدواء</th>
              <th className="border border-gray-300 px-4 py-2 text-right">كود الوجبة</th>
              {type === 'sales' && (
                <th className="border border-gray-300 px-4 py-2 text-right">تاريخ الانتهاء</th>
              )}
              <th className="border border-gray-300 px-4 py-2 text-right">الكمية</th>
              <th className="border border-gray-300 px-4 py-2 text-right">سعر الوحدة</th>
              <th className="border border-gray-300 px-4 py-2 text-right">المجموع</th>
              {type === 'sales' && (
                <th className="border border-gray-300 px-4 py-2 text-right">ملاحظات</th>
              )}
            </tr>
          </thead>
          <tbody>
            {items.map((item, index) => (
              <tr key={index}>
                <td className="border border-gray-300 px-4 py-2 text-center">{index + 1}</td>
                <td className="border border-gray-300 px-4 py-2">{item.medicineName}</td>
                <td className="border border-gray-300 px-4 py-2 text-center">{item.batchCode}</td>
                {type === 'sales' && (
                  <td className="border border-gray-300 px-4 py-2 text-center">{item.expiryDate}</td>
                )}
                <td className="border border-gray-300 px-4 py-2 text-center">{item.quantity}</td>
                <td className="border border-gray-300 px-4 py-2 text-center">
                  {item.isGift ? 'هدية' : `${item.unitPrice.toLocaleString()} د.ع`}
                </td>
                <td className="border border-gray-300 px-4 py-2 text-center">
                  {item.isGift ? 'هدية' : `${item.totalPrice.toLocaleString()} د.ع`}
                </td>
                {type === 'sales' && (
                  <td className="border border-gray-300 px-4 py-2 text-center">
                    {item.isGift ? 'هدية' : ''}
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-6">
        <div className="w-64">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>المجموع الفرعي:</span>
              <span>{subtotal.toLocaleString()} د.ع</span>
            </div>
            {discount > 0 && (
              <div className="flex justify-between">
                <span>الخصم:</span>
                <span>-{discount.toLocaleString()} د.ع</span>
              </div>
            )}
            <div className="flex justify-between font-bold text-lg border-t pt-2">
              <span>المجموع النهائي:</span>
              <span>{finalAmount.toLocaleString()} د.ع</span>
            </div>
          </div>
        </div>
      </div>

      {/* Notes */}
      {notes && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">ملاحظات:</h3>
          <p className="text-gray-700">{notes}</p>
        </div>
      )}

      {/* Footer */}
      <div className="border-t-2 border-gray-300 pt-6 mt-8">
        <div className="grid grid-cols-3 gap-8 text-center">
          <div>
            <div className="border-b border-gray-400 mb-2 pb-1">
              <span className="text-sm text-gray-600">توقيع العميل</span>
            </div>
          </div>
          <div>
            <div className="border-b border-gray-400 mb-2 pb-1">
              <span className="text-sm text-gray-600">توقيع الصيدلي</span>
            </div>
          </div>
          <div>
            <div className="border-b border-gray-400 mb-2 pb-1">
              <span className="text-sm text-gray-600">ختم الصيدلية</span>
            </div>
          </div>
        </div>
        
        <div className="text-center mt-6 text-sm text-gray-500">
          <p>شكراً لثقتكم بنا | نتمنى لكم الشفاء العاجل</p>
          <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة الصيدلية</p>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          body * {
            visibility: hidden;
          }
          #printable-invoice,
          #printable-invoice * {
            visibility: visible;
          }
          #printable-invoice {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          .print\\:p-4 {
            padding: 1rem;
          }
        }
      `}</style>
    </div>
  )
}
