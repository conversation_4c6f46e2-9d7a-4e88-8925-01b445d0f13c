(()=>{var a={};a.id=977,a.ids=[977],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19467:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["returns-records",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,81703)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\returns-records\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\returns-records\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/returns-records/page",pathname:"/returns-records",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/returns-records/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},23843:(a,b,c)=>{Promise.resolve().then(c.bind(c,98913))},24523:(a,b,c)=>{Promise.resolve().then(c.bind(c,81703))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},37911:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},81703:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\مجلد جديد (8)\\\\New folder (19)\\\\pharmacy-accounting-system\\\\src\\\\app\\\\returns-records\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\مجلد جديد (8)\\New folder (19)\\pharmacy-accounting-system\\src\\app\\returns-records\\page.tsx","default")},81904:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},98913:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(60687),e=c(43210),f=c(21979),g=c(5336),h=c(35071),i=c(48730),j=c(43649),k=c(13943),l=c(71444),m=c(37911),n=c(19080),o=c(78122),p=c(99270),q=c(10022),r=c(23928),s=c(40228),t=c(81904),u=c(13861),v=c(13964),w=c(11860),x=c(31836);function y(){let[a,b]=(0,e.useState)([]),[y,z]=(0,e.useState)([]),[A,B]=(0,e.useState)(""),[C,D]=(0,e.useState)("all"),[E,F]=(0,e.useState)("all"),[G,H]=(0,e.useState)(null),[I,J]=(0,e.useState)(!1),[K,L]=(0,e.useState)(!0),[M,N]=(0,e.useState)(null),[O,P]=(0,e.useState)(null),Q=(a=!1)=>{a&&(localStorage.removeItem("sales_returns"),localStorage.removeItem("purchase_returns"),localStorage.removeItem("customers"),localStorage.removeItem("suppliers"));let b=[{id:"sr-001",return_number:"SR-2024-001",customer_id:"cust-001",customer_name:"أحمد محمد علي",total_amount:15e4,status:"pending",reason:"دواء منتهي الصلاحية",created_at:new Date().toISOString(),return_items:[{id:"sri-001",medicine_name:"باراسيتامول 500 مجم",quantity:2,unit_price:25e3,total_price:5e4,reason:"منتهي الصلاحية",expiry_date:"2024-01-15"},{id:"sri-002",medicine_name:"أموكسيسيلين 250 مجم",quantity:4,unit_price:25e3,total_price:1e5,reason:"عيب في التصنيع",expiry_date:"2024-06-20"}]},{id:"sr-002",return_number:"SR-2024-002",customer_id:"cust-002",customer_name:"فاطمة أحمد حسن",total_amount:75e3,status:"approved",reason:"رد فعل تحسسي",created_at:new Date(Date.now()-864e5).toISOString(),return_items:[{id:"sri-003",medicine_name:"إيبوبروفين 400 مجم",quantity:3,unit_price:25e3,total_price:75e3,reason:"رد فعل تحسسي",expiry_date:"2024-12-30"}]}],c=[{id:"pr-001",return_number:"PR-2024-001",supplier_id:"supp-001",supplier_name:"شركة الأدوية المتحدة",total_amount:5e5,status:"pending",reason:"شحنة تالفة",created_at:new Date(Date.now()-1728e5).toISOString(),return_items:[{id:"pri-001",medicine_name:"أسبرين 100 مجم",quantity:10,unit_cost:2e4,total_cost:2e5,reason:"عبوات مكسورة",expiry_date:"2025-03-15"},{id:"pri-002",medicine_name:"فيتامين د 1000 وحدة",quantity:15,unit_cost:2e4,total_cost:3e5,reason:"تاريخ انتهاء قريب",expiry_date:"2024-02-28"}]}];localStorage.setItem("sales_returns",JSON.stringify(b)),localStorage.setItem("purchase_returns",JSON.stringify(c)),localStorage.setItem("customers",JSON.stringify([{id:"cust-001",name:"أحمد محمد علي",phone:"07901234567",address:"بغداد - الكرادة"},{id:"cust-002",name:"فاطمة أحمد حسن",phone:"07907654321",address:"بغداد - المنصور"}])),localStorage.setItem("suppliers",JSON.stringify([{id:"supp-001",name:"شركة الأدوية المتحدة",phone:"07801234567",address:"بغداد - المنطقة الصناعية"}])),console.log("Sample returns data created:",{salesReturns:b.length,purchaseReturns:c.length,salesItems:b.reduce((a,b)=>a+(b.return_items?.length||0),0),purchaseItems:c.reduce((a,b)=>a+(b.return_items?.length||0),0)})},R=async()=>{try{L(!0);let a=localStorage.getItem("sales_returns");a&&0!==JSON.parse(a).length||Q();let c=await (0,x.getReturns)();c.success&&c.data&&(console.log("Loaded return records:",c.data),c.data.forEach((a,b)=>{console.log(`Return ${b+1} (${a.return_number}):`,{id:a.id,return_items:a.return_items,items_count:a.return_items?.length||0})}),b(c.data))}catch(a){console.error("Error loading return records:",a)}finally{L(!1)}},S=async a=>{try{let b=a,c=await (0,x.getReturnById)(a.id);c.success&&c.data&&(b=c.data,console.log("Full record for details:",b)),H(b),J(!0),N(null)}catch(b){console.error("Error loading return details:",b),H(a),J(!0),N(null)}},T=async a=>{try{P(a.id),(await (0,x.updateReturn)(a.id,{status:"approved"})).success?(await R(),alert("تم قبول المرتجع بنجاح")):alert("حدث خطأ أثناء قبول المرتجع")}catch(a){console.error("Error approving return:",a),alert("حدث خطأ أثناء قبول المرتجع")}finally{P(null),N(null)}},U=async a=>{let b=prompt("يرجى إدخال سبب رفض المرتجع:");if(b)try{P(a.id),(await (0,x.updateReturn)(a.id,{status:"rejected",rejection_reason:b})).success?(await R(),alert("تم رفض المرتجع")):alert("حدث خطأ أثناء رفض المرتجع")}catch(a){console.error("Error rejecting return:",a),alert("حدث خطأ أثناء رفض المرتجع")}finally{P(null),N(null)}},V=async a=>{console.log("Printing return record with Laren template:",a);let b=a;try{let c=await (0,x.getReturnById)(a.id);c.success&&c.data&&(b=c.data,console.log("Full return record with items:",b))}catch(a){console.error("Error fetching full return details:",a)}let{generateLarenReturnHTML:d}=c(84622),e=window.open("","_blank");if(!e)return;let f=d(b,{companyName:"مكتب لارين العلمي",companyNameEn:"LAREN SCIENTIFIC BUREAU",companyAddress:"بغداد - شارع فلسطين",companyPhone:"+*********** 4567",companyEmail:"<EMAIL>",footerText:"شكراً لتعاملكم معنا"});e.document.write(f),e.document.close(),e.focus(),setTimeout(()=>{e.print()},500)},W=async()=>{try{let a=await c.e(103).then(c.bind(c,33103)),b=a.utils.book_new(),d=[["تقرير المرتجعات الشامل"],["نظام إدارة الصيدلية"],[""],["تاريخ التقرير:",new Date().toLocaleDateString("ar-EG")],["وقت التقرير:",new Date().toLocaleTimeString("ar-EG")],[""],["الملخص الإحصائي"],["المؤشر","العدد"],["إجمالي المرتجعات",y.length],["في الانتظار",y.filter(a=>"pending"===a.status).length],["مقبولة",y.filter(a=>"approved"===a.status).length],["مرفوضة",y.filter(a=>"rejected"===a.status).length]],e=a.utils.aoa_to_sheet(d);a.utils.book_append_sheet(b,e,"الملخص");let f=y.map(a=>({"رقم المرتجع":a.return_number,النوع:"sales"===a.return_type?"مرتجع مبيعات":"مرتجع مشتريات","العميل/المورد":"sales"===a.return_type?a.customers?.name||a.customer_name||"غير محدد":a.suppliers?.name||a.supplier_name||"غير محدد","المبلغ الإجمالي":a.total_amount,الحالة:"approved"===a.status?"مقبول":"rejected"===a.status?"مرفوض":"في الانتظار","سبب المرتجع":a.reason||"غير محدد",التاريخ:new Date(a.created_at).toLocaleDateString("ar-EG"),الهاتف:"sales"===a.return_type?a.customers?.phone||"غير محدد":a.suppliers?.phone||"غير محدد",العنوان:"sales"===a.return_type?a.customers?.address||"غير محدد":a.suppliers?.address||"غير محدد"})),g=a.utils.json_to_sheet(f);a.utils.book_append_sheet(b,g,"البيانات التفصيلية");let h=[];if(y.forEach(a=>{a.return_items&&a.return_items.length>0&&a.return_items.forEach(b=>{h.push({"رقم المرتجع":a.return_number,"اسم الدواء":b.medicine_name||b.medicines?.name||"غير محدد",الكمية:b.quantity||0,"سعر الوحدة":b.unit_price||b.unit_cost||0,المجموع:b.total_price||b.total_cost||0,"تاريخ الانتهاء":b.expiry_date?new Date(b.expiry_date).toLocaleDateString("ar-EG"):"غير محدد"})})}),h.length>0){let c=a.utils.json_to_sheet(h);a.utils.book_append_sheet(b,c,"المواد المرتجعة")}let i=`تقرير_المرتجعات_${new Date().toISOString().split("T")[0]}.xlsx`;a.writeFile(b,i)}catch(a){console.error("Error exporting Excel:",a),alert("حدث خطأ أثناء تصدير Excel")}},X=a=>{switch(a){case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},Y=a=>{switch(a){case"approved":return"مقبول";case"rejected":return"مرفوض";case"pending":return"في الانتظار";default:return"غير محدد"}},Z=a=>{switch(a){case"approved":return(0,d.jsx)(g.A,{className:"h-4 w-4"});case"rejected":return(0,d.jsx)(h.A,{className:"h-4 w-4"});case"pending":return(0,d.jsx)(i.A,{className:"h-4 w-4"});default:return(0,d.jsx)(j.A,{className:"h-4 w-4"})}},$=a=>{switch(a){case"sales":return"مرتجع مبيعات";case"purchase":return"مرتجع مشتريات";default:return"غير محدد"}};return(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center gap-3",children:[(0,d.jsx)(k.A,{className:"h-8 w-8 text-blue-600"}),"سجل المرتجعات"]}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"عرض وإدارة جميع المرتجعات مع إمكانيات طباعة وتصدير متقدمة"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:()=>{let a=window.open("","_blank");if(!a)return;let b=`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تقرير المرتجعات</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2D3748;
            background: #FFFFFF;
            direction: rtl;
            padding: 20px;
          }

          .print-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
          }

          .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 12px 12px 0 0;
            margin-bottom: 30px;
          }

          .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
          }

          .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
          }

          .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }

          .summary-card {
            background: #F7FAFC;
            border: 1px solid #E2E8F0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border-right: 4px solid #667eea;
          }

          .summary-card h3 {
            font-size: 14px;
            font-weight: 600;
            color: #4A5568;
            margin-bottom: 8px;
          }

          .summary-card p {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
          }

          .returns-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }

          .return-card {
            background: white;
            border: 1px solid #E2E8F0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            break-inside: avoid;
            margin-bottom: 20px;
          }

          .return-header {
            background: linear-gradient(135deg, #F7FAFC 0%, #EDF2F7 100%);
            padding: 15px 20px;
            border-bottom: 1px solid #E2E8F0;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .return-number h3 {
            font-size: 18px;
            font-weight: 700;
            color: #2D3748;
            margin-bottom: 5px;
          }

          .return-type {
            font-size: 12px;
            color: #718096;
            background: #EDF2F7;
            padding: 4px 8px;
            border-radius: 6px;
          }

          .return-status {
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
          }

          .return-info {
            padding: 20px;
          }

          .info-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
          }

          .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
            padding: 15px;
            background: #F7FAFC;
            border-radius: 8px;
          }

          .info-item {
            display: flex;
            flex-direction: column;
          }

          .info-item label {
            font-size: 12px;
            font-weight: 600;
            color: #4A5568;
            margin-bottom: 4px;
          }

          .info-item span {
            font-size: 14px;
            color: #2D3748;
          }

          .reason-section {
            margin-bottom: 15px;
          }

          .reason-section label {
            font-size: 12px;
            font-weight: 600;
            color: #4A5568;
            margin-bottom: 8px;
            display: block;
          }

          .reason-text {
            background: #FFF5F5;
            border: 1px solid #FED7D7;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            color: #2D3748;
          }

          .items-section h4 {
            font-size: 14px;
            font-weight: 600;
            color: #4A5568;
            margin-bottom: 12px;
          }

          .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
          }

          .item-card {
            background: #F0FFF4;
            border: 1px solid #C6F6D5;
            border-radius: 8px;
            padding: 12px;
          }

          .item-name {
            font-weight: 600;
            color: #2D3748;
            margin-bottom: 8px;
            font-size: 13px;
          }

          .item-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
          }

          .item-details span {
            font-size: 11px;
            color: #4A5568;
            display: block;
            margin-bottom: 2px;
          }

          .item-number {
            background: #667eea;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-left: 5px;
          }

          .items-total {
            margin-top: 15px;
            padding: 10px;
            background: #EDF2F7;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            color: #2D3748;
          }

          .no-items {
            text-align: center;
            color: #718096;
            padding: 20px;
            background: #F7FAFC;
            border-radius: 8px;
            border: 2px dashed #CBD5E0;
          }

          .no-items-icon {
            font-size: 24px;
            margin-bottom: 8px;
          }

          .footer {
            background: #F7FAFC;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #E2E8F0;
            color: #718096;
            font-size: 12px;
            margin-top: 30px;
          }

          @media print {
            body {
              margin: 0;
              padding: 10px;
            }

            .returns-grid {
              grid-template-columns: 1fr;
            }

            .return-card {
              break-inside: avoid;
              margin-bottom: 15px;
            }

            .summary {
              grid-template-columns: repeat(4, 1fr);
            }
          }

          @page {
            margin: 1cm;
            size: A4;
          }
        </style>
      </head>
      <body>
        <div class="print-container">
          <div class="header">
            <h1>تقرير المرتجعات الشامل</h1>
            <div class="subtitle">نظام إدارة الصيدلية الاحترافي</div>
          </div>

          <div class="summary">
            <div class="summary-card">
              <h3>إجمالي المرتجعات</h3>
              <p>${y.length}</p>
            </div>
            <div class="summary-card">
              <h3>في الانتظار</h3>
              <p>${y.filter(a=>"pending"===a.status).length}</p>
            </div>
            <div class="summary-card">
              <h3>مقبولة</h3>
              <p>${y.filter(a=>"approved"===a.status).length}</p>
            </div>
            <div class="summary-card">
              <h3>مرفوضة</h3>
              <p>${y.filter(a=>"rejected"===a.status).length}</p>
            </div>
          </div>

          <div class="returns-grid">
            ${y.map(a=>(a=>{let b="approved"===a.status?"#10B981":"rejected"===a.status?"#EF4444":"#F59E0B",c="approved"===a.status?"مقبول":"rejected"===a.status?"مرفوض":"في الانتظار",d="sales"===a.return_type?"مرتجع مبيعات":"مرتجع مشتريات",e="sales"===a.return_type?a.customers?.name||a.customer_name||"عميل غير محدد":a.suppliers?.name||a.supplier_name||"مورد غير محدد",f=a.return_items||[],g=f.length>0?`
        <div class="items-section">
          <h4>المواد المرتجعة (${f.length} مادة):</h4>
          <div class="items-grid">
            ${f.map((a,b)=>`
              <div class="item-card">
                <div class="item-name">
                  <span class="item-number">${b+1}.</span>
                  ${a.medicine_name||a.medicines?.name||a.medicine?.name||`دواء ${b+1}`}
                </div>
                <div class="item-details">
                  <span>📦 الكمية: ${a.quantity||0}</span>
                  <span>💰 السعر: ${(a.unit_price||a.unit_cost||a.price||0).toLocaleString()} د.ع</span>
                  <span>💵 المجموع: ${(a.total_price||a.total_cost||a.total||(a.quantity||0)*(a.unit_price||a.unit_cost||a.price||0)).toLocaleString()} د.ع</span>
                  ${a.expiry_date?`<span>📅 انتهاء: ${new Date(a.expiry_date).toLocaleDateString("ar-EG")}</span>`:""}
                  ${a.reason||a.return_reason?`<span>📝 السبب: ${a.reason||a.return_reason}</span>`:""}
                </div>
              </div>
            `).join("")}
          </div>
          <div class="items-total">
            <strong>المجموع الكلي: ${a.total_amount?.toLocaleString()||0} د.ع</strong>
          </div>
        </div>
      `:`
        <div class="no-items">
          <div class="no-items-icon">📦</div>
          <div>لا توجد مواد مرتجعة</div>
        </div>
      `;return`
        <div class="return-card">
          <div class="return-header">
            <div class="return-number">
              <h3>${a.return_number}</h3>
              <span class="return-type">${d}</span>
            </div>
            <div class="return-status" style="background: ${b};">
              ${c}
            </div>
          </div>

          <div class="return-info">
            <div class="info-row">
              <div class="info-item">
                <label>التاريخ:</label>
                <span>${new Date(a.created_at).toLocaleDateString("ar-EG")}</span>
              </div>
              <div class="info-item">
                <label>${"sales"===a.return_type?"العميل":"المورد"}:</label>
                <span>${e}</span>
              </div>
              <div class="info-item">
                <label>المبلغ الإجمالي:</label>
                <span>${a.total_amount.toLocaleString()} د.ع</span>
              </div>
            </div>

            ${"sales"===a.return_type&&a.customers?`
              <div class="contact-info">
                <div class="info-item">
                  <label>الهاتف:</label>
                  <span>${a.customers.phone||"غير محدد"}</span>
                </div>
                <div class="info-item">
                  <label>العنوان:</label>
                  <span>${a.customers.address||"غير محدد"}</span>
                </div>
              </div>
            `:""}

            ${"purchase"===a.return_type&&a.suppliers?`
              <div class="contact-info">
                <div class="info-item">
                  <label>الهاتف:</label>
                  <span>${a.suppliers.phone||"غير محدد"}</span>
                </div>
                <div class="info-item">
                  <label>العنوان:</label>
                  <span>${a.suppliers.address||"غير محدد"}</span>
                </div>
              </div>
            `:""}

            <div class="reason-section">
              <label>سبب المرتجع:</label>
              <div class="reason-text">${a.reason||"غير محدد"}</div>
            </div>

            ${g}
          </div>
        </div>
      `})(a)).join("")}
          </div>

          <div class="footer">
            <div>تم إنشاء هذا التقرير في: ${new Date().toLocaleString("ar-EG")}</div>
            <div>نظام إدارة الصيدلية - تقرير المرتجعات</div>
          </div>
        </div>
      </body>
      </html>
    `;a.document.write(b),a.document.close(),a.focus(),setTimeout(()=>{a.print()},500)},className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2 transition-colors",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),"طباعة التقرير"]}),(0,d.jsxs)("button",{onClick:W,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"تصدير Excel"]}),(0,d.jsxs)("button",{onClick:()=>{Q(!0),setTimeout(()=>R(),100)},className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2 transition-colors",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),"إعادة إنشاء البيانات"]}),(0,d.jsxs)("button",{onClick:R,className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2 transition-colors",children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),"تحديث"]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي المرتجعات"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:y.length})]}),(0,d.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,d.jsx)(k.A,{className:"h-6 w-6 text-blue-600"})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"في الانتظار"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:y.filter(a=>"pending"===a.status).length})]}),(0,d.jsx)("div",{className:"p-3 bg-yellow-100 rounded-full",children:(0,d.jsx)(i.A,{className:"h-6 w-6 text-yellow-600"})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"مقبولة"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-green-600",children:y.filter(a=>"approved"===a.status).length})]}),(0,d.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,d.jsx)(g.A,{className:"h-6 w-6 text-green-600"})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"مرفوضة"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-red-600",children:y.filter(a=>"rejected"===a.status).length})]}),(0,d.jsx)("div",{className:"p-3 bg-red-100 rounded-full",children:(0,d.jsx)(h.A,{className:"h-6 w-6 text-red-600"})})]})})]}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)("input",{type:"text",placeholder:"البحث برقم المرتجع أو اسم العميل...",value:A,onChange:a=>B(a.target.value),className:"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,d.jsxs)("select",{value:C,onChange:a=>D(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"all",children:"جميع الأنواع"}),(0,d.jsx)("option",{value:"sales",children:"مرتجع مبيعات"}),(0,d.jsx)("option",{value:"purchase",children:"مرتجع مشتريات"})]}),(0,d.jsxs)("select",{value:E,onChange:a=>F(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,d.jsx)("option",{value:"pending",children:"في الانتظار"}),(0,d.jsx)("option",{value:"approved",children:"مقبول"}),(0,d.jsx)("option",{value:"rejected",children:"مرفوض"})]}),(0,d.jsx)("div",{className:"flex items-center justify-center bg-yellow-50 rounded-lg px-4 py-2",children:(0,d.jsxs)("span",{className:"text-sm text-yellow-800",children:["في الانتظار: ",a.filter(a=>"pending"===a.status).length]})}),(0,d.jsx)("div",{className:"flex items-center justify-center bg-gray-50 rounded-lg px-4 py-2",children:(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[y.length," من ",a.length," مرتجع"]})})]})}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:K?(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,d.jsx)("p",{className:"text-gray-600 mt-2",children:"جاري تحميل السجلات..."})]}):0===y.length?(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsx)(q.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"لا توجد مرتجعات"})]}):(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"رقم المرتجع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النوع"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"العميل/المورد"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المواد المرتجعة"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"المبلغ"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"السبب"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الحالة"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ"}),(0,d.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:y.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 text-purple-500 mr-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900",children:a.return_number})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"sales"===a.return_type?"bg-blue-100 text-blue-800":"bg-orange-100 text-orange-800"}`,children:$(a.return_type)})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"sales"===a.return_type?a.customers?.name||a.customer_name||"عميل نقدي":a.suppliers?.name||a.supplier_name||"غير محدد"}),("sales"===a.return_type&&a.customers?.phone||"purchase"===a.return_type&&a.suppliers?.phone)&&(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"sales"===a.return_type?a.customers?.phone:a.suppliers?.phone})]})}),(0,d.jsx)("td",{className:"px-6 py-4",children:(0,d.jsx)("div",{className:"max-w-xs",children:(()=>{console.log(`Rendering items for ${a.return_number}:`,a.return_items);let b=a.return_items||[];return b.length>0?(0,d.jsxs)("div",{className:"space-y-1",children:[b.slice(0,2).map((a,b)=>{let c=a.medicine_name||a.medicines?.name||a.medicine?.name||`دواء ${b+1}`,e=a.quantity||0;return(0,d.jsxs)("div",{className:"flex items-center text-xs text-gray-600 bg-gray-50 rounded px-2 py-1",children:[(0,d.jsx)(n.A,{className:"h-3 w-3 text-blue-500 mr-1"}),(0,d.jsxs)("span",{className:"truncate",title:`${c} (${e})`,children:[c," (",e,")"]})]},b)}),b.length>2&&(0,d.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["+",b.length-2," مواد أخرى"]})]}):(0,d.jsxs)("div",{className:"flex items-center text-xs text-gray-400 italic",children:[(0,d.jsx)(n.A,{className:"h-3 w-3 text-gray-400 mr-1"}),(0,d.jsx)("span",{children:"لا توجد مواد"})]})})()})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 text-red-500 mr-1"}),(0,d.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[a.total_amount?.toLocaleString()," د.ع"]})]})}),(0,d.jsx)("td",{className:"px-6 py-4",children:(0,d.jsx)("div",{className:"text-sm text-gray-900 max-w-xs truncate",title:a.reason,children:a.reason})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)("span",{className:`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${X(a.status)}`,children:[Z(a.status),(0,d.jsx)("span",{className:"mr-1",children:Y(a.status)})]})})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:new Date(a.created_at).toLocaleDateString("ar-EG")})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("button",{onClick:()=>N(M===a.id?null:a.id),className:"text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100",disabled:O===a.id,children:O===a.id?(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"}):(0,d.jsx)(t.A,{className:"h-4 w-4"})}),M===a.id&&(0,d.jsx)("div",{className:"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200",children:(0,d.jsxs)("div",{className:"py-1",children:[(0,d.jsxs)("button",{onClick:()=>S(a),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"عرض التفاصيل"]}),(0,d.jsxs)("button",{onClick:()=>V(a),className:"flex items-center w-full px-4 py-2 text-sm text-blue-700 hover:bg-blue-50",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"طباعة المرتجع (قالب لارين)"]}),"pending"===a.status&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("button",{onClick:()=>T(a),className:"flex items-center w-full px-4 py-2 text-sm text-green-700 hover:bg-green-50",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"قبول المرتجع"]}),(0,d.jsxs)("button",{onClick:()=>U(a),className:"flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50",children:[(0,d.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"رفض المرتجع"]})]})]})})]})})]},a.id))})]})})}),I&&G&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["تفاصيل المرتجع ",G.return_number]}),(0,d.jsx)("button",{onClick:()=>J(!1),className:"text-gray-400 hover:text-gray-600",children:"\xd7"})]}),(0,d.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"معلومات المرتجع"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"رقم المرتجع:"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:G.return_number})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"النوع:"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:$(G.return_type)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"التاريخ:"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:new Date(G.created_at).toLocaleDateString("ar-EG")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"المبلغ الإجمالي:"}),(0,d.jsxs)("p",{className:"text-sm text-gray-900",children:[G.total_amount?.toLocaleString()," د.ع"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"الحالة:"}),(0,d.jsxs)("span",{className:`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${X(G.status)}`,children:[Z(G.status),(0,d.jsx)("span",{className:"mr-1",children:Y(G.status)})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"سبب المرتجع:"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:G.reason})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"sales"===G.return_type?"معلومات العميل":"معلومات المورد"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"الاسم:"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:"sales"===G.return_type?G.customers?.name||G.customer_name||"عميل نقدي":G.suppliers?.name||G.supplier_name||"غير محدد"})]}),("sales"===G.return_type&&G.customers?.phone||"purchase"===G.return_type&&G.suppliers?.phone)&&(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"الهاتف:"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:"sales"===G.return_type?G.customers?.phone:G.suppliers?.phone})]}),("sales"===G.return_type&&G.customers?.address||"purchase"===G.return_type&&G.suppliers?.address)&&(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"العنوان:"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:"sales"===G.return_type?G.customers?.address:G.suppliers?.address})]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-blue-600"}),"المواد المرتجعة (",G.return_items?.length||0," مادة)"]}),G.return_items&&G.return_items.length>0?(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"#"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"اسم الدواء"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"الكمية"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"سعر الوحدة"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"المجموع"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"تاريخ الانتهاء"}),(0,d.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase",children:"سبب الإرجاع"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:G.return_items.map((a,b)=>{let c=a.medicine_name||a.medicines?.name||a.medicine?.name||`دواء ${b+1}`,e=a.quantity||0,f=a.unit_price||a.unit_cost||a.price||0,g=a.total_price||a.total_cost||a.total||e*f;return(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-4 py-3 text-sm font-medium text-gray-900",children:b+1}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 text-blue-500"}),(0,d.jsx)("span",{className:"font-medium",children:c})]})}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,d.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:e})}),(0,d.jsxs)("td",{className:"px-4 py-3 text-sm text-gray-900",children:[f.toLocaleString()," د.ع"]}),(0,d.jsxs)("td",{className:"px-4 py-3 text-sm font-medium text-gray-900",children:[g.toLocaleString()," د.ع"]}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:a.expiry_date?new Date(a.expiry_date).toLocaleDateString("ar-EG"):"غير محدد"}),(0,d.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,d.jsx)("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:a.reason||a.return_reason||"غير محدد"})})]},b)})}),(0,d.jsx)("tfoot",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{colSpan:4,className:"px-4 py-3 text-sm font-medium text-gray-900 text-right",children:"المجموع الكلي:"}),(0,d.jsxs)("td",{className:"px-4 py-3 text-sm font-bold text-gray-900",children:[G.total_amount?.toLocaleString()||0," د.ع"]}),(0,d.jsx)("td",{colSpan:2})]})})]})}):(0,d.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,d.jsx)(n.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد مواد مرتجعة"}),(0,d.jsx)("p",{className:"text-gray-500",children:"لم يتم تسجيل أي مواد لهذا المرتجع"})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t bg-gray-50",children:[(0,d.jsx)("button",{onClick:()=>J(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"إغلاق"}),"pending"===G.status&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("button",{onClick:()=>{T(G),J(!1)},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,d.jsx)(v.A,{className:"h-4 w-4"}),"قبول المرتجع"]}),(0,d.jsxs)("button",{onClick:()=>{U(G),J(!1)},className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center gap-2",children:[(0,d.jsx)(w.A,{className:"h-4 w-4"}),"رفض المرتجع"]})]})]})]})})]})})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,360,991,463,314,979,31,711],()=>b(b.s=19467));module.exports=c})();